SECRET_KEY=Ni2Q/BV6dOMi7EMuReSrLVa7vextHbGQfIX0tXv
DB_TYPE=mysql
MYSQL_SERVER=************
MYSQL_USERNAME=dev
MYSQL_PASSWORD=ycjf_2018
MYSQL_DB=yunzhiwen_dev
MYSQL_PORT=3306

# 数据库连接池配置
SQLALCHEMY_POOL_SIZE=20
SQLALCHEMY_MAX_OVERFLOW=10  
SQLALCHEMY_POOL_RECYCLE=1800
SQLALCHEMY_POOL_PRE_PING=True

# S3兼容服务配置
S3_ENDPOINT_URL=https://s3.ycfin.net  # MinIO本地测试环境

# 认证信息
S3_ACCESS_KEY=pCZGYtPyIpB9qacQKe4z
S3_SECRET_KEY=XfeeZcntsMcwQnby9JSuTI6wdpbeA8eU98ByU9XM
S3_REGION=us-east-1  # 区域名称

# 存储桶配置
S3_BUCKET_NAME=knowledge-base  # 知识库文件的存储桶名称
S3_ICON_BUCKET_NAME=agent-icon

# 其他可选配置
S3_FILE_PREFIX=uploads/  # 文件存储前缀
S3_URL_EXPIRE_SECONDS=3600  # 预签名URL有效期 

# 向量存储配置
VECTOR_STORE_TYPE=elasticsearch
VECTOR_STORE_DATA_DIR=data/vector_stores

MONGODB_URL=*****************************************************
MONGODB_DATABASE=agent_service

ES_URL=http://***********:9200
ES_USERNAME=elastic
ES_PASSWORD=Hz&11_djook

# 日志
LOG_LEVEL=INFO 