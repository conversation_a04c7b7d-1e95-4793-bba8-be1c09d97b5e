"""initdb

Revision ID: 3c56217ce608
Revises: 
Create Date: 2025-06-10 16:21:36.136762

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '3c56217ce608'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('access_credential',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('tenant_id', sa.String(length=32), nullable=False),
    sa.Column('agent_id', sa.String(length=32), nullable=False),
    sa.Column('app_id', sa.String(length=64), nullable=False),
    sa.Column('app_secret', sa.String(length=256), nullable=False),
    sa.Column('allowed_domains', sa.JSON(), nullable=True),
    sa.Column('allowed_ips', sa.JSON(), nullable=True),
    sa.Column('rate_limit', sa.Integer(), nullable=True),
    sa.Column('token_expire_hours', sa.Integer(), nullable=True),
    sa.Column('sign_type', sa.String(length=32), nullable=True),
    sa.Column('sign_version', sa.String(length=16), nullable=True),
    sa.Column('nonce_check', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], use_alter=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('app_id')
    )
    op.create_index(op.f('ix_access_credential_id'), 'access_credential', ['id'], unique=False)
    op.create_table('access_token',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('credential_id', sa.String(length=32), nullable=True),
    sa.Column('token', sa.String(length=256), nullable=False),
    sa.Column('expired_at', sa.DateTime(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['credential_id'], ['access_credential.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token')
    )
    op.create_index(op.f('ix_access_token_id'), 'access_token', ['id'], unique=False)
    op.create_table('agent',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('tenant_id', sa.String(length=32), nullable=True),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('agent_type', sa.Enum('SUPERVISOR', 'GENERAL', name='agenttype'), nullable=True),
    sa.Column('open_id', sa.String(length=64), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('icon_url', sa.String(length=512), nullable=True, comment='图标S3存储路径'),
    sa.Column('icon_metadata', sa.JSON(), nullable=True, comment='图标元数据(文件大小、格式等)'),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('open_id')
    )
    op.create_index(op.f('ix_agent_id'), 'agent', ['id'], unique=False)
    op.create_table('agent_tool',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('agent_id', sa.String(length=32), nullable=True),
    sa.Column('agent_version', sa.String(length=32), nullable=True),
    sa.Column('tool_id', sa.String(length=32), nullable=True),
    sa.Column('tenant_tool_id', sa.String(length=32), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], use_alter=True),
    sa.ForeignKeyConstraint(['tenant_tool_id'], ['tenant_tool.id'], use_alter=True),
    sa.ForeignKeyConstraint(['tool_id'], ['tool.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_agent_tool_id'), 'agent_tool', ['id'], unique=False)
    op.create_table('agent_version',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('agent_id', sa.String(length=32), nullable=True),
    sa.Column('version_number', sa.String(length=16), nullable=False),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('prompt', sa.Text(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('model_id', sa.String(length=32), nullable=True),
    sa.Column('knowledge_base_ids', sa.JSON(), nullable=True),
    sa.Column('tool_ids', sa.JSON(), nullable=True),
    sa.Column('mcp_server_ids', sa.JSON(), nullable=True),
    sa.Column('is_current', sa.Boolean(), nullable=True),
    sa.Column('is_published', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], use_alter=True),
    sa.ForeignKeyConstraint(['model_id'], ['model.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('agent_id', 'version_number', name='uq_agent_version')
    )
    op.create_index(op.f('ix_agent_version_id'), 'agent_version', ['id'], unique=False)
    op.create_table('knowledge_base',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('tenant_id', sa.String(length=32), nullable=True),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_base_id'), 'knowledge_base', ['id'], unique=False)
    op.create_table('knowledge_chunk',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('file_id', sa.String(length=32), nullable=True),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('meta_data', sa.JSON(), nullable=True),
    sa.Column('embedding', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['file_id'], ['knowledge_file.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_chunk_id'), 'knowledge_chunk', ['id'], unique=False)
    op.create_table('knowledge_file',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('knowledge_base_id', sa.String(length=32), nullable=True),
    sa.Column('file_name', sa.String(length=256), nullable=False),
    sa.Column('file_path', sa.String(length=512), nullable=False),
    sa.Column('file_type', sa.String(length=128), nullable=True),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=32), nullable=True),
    sa.Column('chunk_count', sa.Integer(), nullable=True),
    sa.Column('meta_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['knowledge_base_id'], ['knowledge_base.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_file_id'), 'knowledge_file', ['id'], unique=False)
    op.create_table('knowledge_search',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('knowledge_base_id', sa.String(length=32), nullable=True),
    sa.Column('session_id', sa.String(length=32), nullable=True),
    sa.Column('query', sa.Text(), nullable=False),
    sa.Column('results', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['knowledge_base_id'], ['knowledge_base.id'], use_alter=True),
    sa.ForeignKeyConstraint(['session_id'], ['session.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_search_id'), 'knowledge_search', ['id'], unique=False)
    op.create_table('mcp_server',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('tenant_id', sa.String(length=32), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('endpoint', sa.String(length=256), nullable=False),
    sa.Column('transport_type', sa.String(length=32), nullable=False),
    sa.Column('status', sa.String(length=32), nullable=True),
    sa.Column('last_heartbeat', sa.DateTime(), nullable=True),
    sa.Column('meta_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_mcp_server_id'), 'mcp_server', ['id'], unique=False)
    op.create_table('mcp_tool',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('server_id', sa.String(length=32), nullable=True),
    sa.Column('tool_id', sa.String(length=32), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(length=32), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['server_id'], ['mcp_server.id'], use_alter=True),
    sa.ForeignKeyConstraint(['tool_id'], ['tool.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_mcp_tool_id'), 'mcp_tool', ['id'], unique=False)
    op.create_table('model',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('tenant_id', sa.String(length=32), nullable=False),
    sa.Column('provider_id', sa.String(length=32), nullable=False),
    sa.Column('provider_access_credential_id', sa.String(length=32), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('key', sa.String(length=64), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('model_type', sa.String(length=32), nullable=False),
    sa.Column('extra_config', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['provider_access_credential_id'], ['provider_access_credential.id'], use_alter=True),
    sa.ForeignKeyConstraint(['provider_id'], ['provider.id'], use_alter=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_model_id'), 'model', ['id'], unique=False)
    op.create_index(op.f('ix_model_provider_id'), 'model', ['provider_id'], unique=False)
    op.create_index(op.f('ix_model_tenant_id'), 'model', ['tenant_id'], unique=False)
    op.create_table('provider',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('key', sa.String(length=64), nullable=False),
    sa.Column('provider_type', sa.Enum('BUILTIN', 'CUSTOM_OPENAI', name='providertype'), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('icon', sa.String(length=128), nullable=True),
    sa.Column('website', sa.String(length=256), nullable=True),
    sa.Column('api_base', sa.String(length=256), nullable=True),
    sa.Column('api_compatible', sa.String(length=64), nullable=True),
    sa.Column('auth_type', sa.Enum('API_KEY', 'BEARER_TOKEN', 'BASIC_AUTH', 'OAUTH', name='authtype'), nullable=False),
    sa.Column('config_schema', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('key')
    )
    op.create_index(op.f('ix_provider_id'), 'provider', ['id'], unique=False)
    op.create_table('provider_access_credential',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('tenant_id', sa.String(length=32), nullable=True),
    sa.Column('provider_id', sa.String(length=32), nullable=True),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('credentials', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['provider_id'], ['provider.id'], use_alter=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_provider_access_credential_id'), 'provider_access_credential', ['id'], unique=False)
    op.create_table('tenant_invitation',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('tenant_id', sa.String(length=32), nullable=True),
    sa.Column('inviter_id', sa.String(length=32), nullable=True),
    sa.Column('invitee_email', sa.String(length=128), nullable=False),
    sa.Column('role_key', sa.String(length=32), nullable=False),
    sa.Column('invitation_token', sa.String(length=128), nullable=True),
    sa.Column('status', sa.String(length=32), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['inviter_id'], ['user.id'], use_alter=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tenant_invitation_id'), 'tenant_invitation', ['id'], unique=False)
    op.create_index(op.f('ix_tenant_invitation_invitation_token'), 'tenant_invitation', ['invitation_token'], unique=True)
    op.create_index(op.f('ix_tenant_invitation_invitee_email'), 'tenant_invitation', ['invitee_email'], unique=False)
    op.create_table('tenant_tool',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('tenant_id', sa.String(length=32), nullable=True),
    sa.Column('tool_id', sa.String(length=32), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], use_alter=True),
    sa.ForeignKeyConstraint(['tool_id'], ['tool.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tenant_tool_id'), 'tenant_tool', ['id'], unique=False)
    op.create_table('tool',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('key', sa.String(length=64), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('param_schema', sa.JSON(), nullable=True),
    sa.Column('config_schema', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('tag', sa.String(length=256), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('key')
    )
    op.create_index(op.f('ix_tool_id'), 'tool', ['id'], unique=False)
    op.create_table('tool_execution_log',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('session_id', sa.String(length=32), nullable=True),
    sa.Column('tool_id', sa.String(length=32), nullable=True),
    sa.Column('agent_tool_id', sa.String(length=32), nullable=True),
    sa.Column('input_params', sa.JSON(), nullable=True),
    sa.Column('output_result', sa.JSON(), nullable=True),
    sa.Column('execution_time', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=32), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['agent_tool_id'], ['agent_tool.id'], use_alter=True),
    sa.ForeignKeyConstraint(['session_id'], ['session.id'], use_alter=True),
    sa.ForeignKeyConstraint(['tool_id'], ['tool.id'], use_alter=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tool_execution_log_id'), 'tool_execution_log', ['id'], unique=False)
    op.create_table('user',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('username', sa.String(length=64), nullable=False),
    sa.Column('email', sa.String(length=128), nullable=False),
    sa.Column('password_hash', sa.String(length=256), nullable=False),
    sa.Column('display_name', sa.String(length=64), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=True),
    sa.Column('verification_token', sa.String(length=128), nullable=True),
    sa.Column('verification_token_expires', sa.DateTime(), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('extra', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('username')
    )
    op.create_index(op.f('ix_user_id'), 'user', ['id'], unique=False)
    op.create_table('user_tenant_association',
    sa.Column('user_id', sa.String(length=32), nullable=False),
    sa.Column('tenant_id', sa.String(length=32), nullable=False),
    sa.Column('role_key', sa.String(length=32), nullable=False),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], use_alter=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], use_alter=True),
    sa.PrimaryKeyConstraint('user_id', 'tenant_id')
    )
    op.create_table('tenant',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_personal', sa.Boolean(), nullable=True),
    sa.Column('owner_id', sa.String(length=32), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_tenant_id'), 'tenant', ['id'], unique=False)
    op.drop_index('ix_tasks_id', table_name='tasks')
    op.drop_index('ix_tasks_priority', table_name='tasks')
    op.drop_index('ix_tasks_status', table_name='tasks')
    op.drop_index('ix_tasks_task_type', table_name='tasks')
    op.drop_index('ix_tasks_tenant_id', table_name='tasks')
    op.drop_table('tasks')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tasks',
    sa.Column('id', mysql.VARCHAR(length=36), nullable=False),
    sa.Column('tenant_id', mysql.VARCHAR(length=36), nullable=False),
    sa.Column('task_type', mysql.ENUM('INDEX_DOCUMENT', 'DELETE_FILE_INDEX', 'REINDEX_KNOWLEDGE_BASE'), nullable=False),
    sa.Column('status', mysql.ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'RETRY'), nullable=True),
    sa.Column('params', mysql.JSON(), nullable=False),
    sa.Column('result', mysql.JSON(), nullable=True),
    sa.Column('error', mysql.TEXT(), nullable=True),
    sa.Column('retry_count', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('max_retries', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('priority', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('created_at', mysql.DATETIME(), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.Column('started_at', mysql.DATETIME(), nullable=True),
    sa.Column('completed_at', mysql.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_tasks_tenant_id', 'tasks', ['tenant_id'], unique=False)
    op.create_index('ix_tasks_task_type', 'tasks', ['task_type'], unique=False)
    op.create_index('ix_tasks_status', 'tasks', ['status'], unique=False)
    op.create_index('ix_tasks_priority', 'tasks', ['priority'], unique=False)
    op.create_index('ix_tasks_id', 'tasks', ['id'], unique=False)
    op.drop_index(op.f('ix_tenant_id'), table_name='tenant')
    op.drop_table('tenant')
    op.drop_table('user_tenant_association')
    op.drop_index(op.f('ix_user_id'), table_name='user')
    op.drop_table('user')
    op.drop_index(op.f('ix_tool_execution_log_id'), table_name='tool_execution_log')
    op.drop_table('tool_execution_log')
    op.drop_index(op.f('ix_tool_id'), table_name='tool')
    op.drop_table('tool')
    op.drop_index(op.f('ix_tenant_tool_id'), table_name='tenant_tool')
    op.drop_table('tenant_tool')
    op.drop_index(op.f('ix_tenant_invitation_invitee_email'), table_name='tenant_invitation')
    op.drop_index(op.f('ix_tenant_invitation_invitation_token'), table_name='tenant_invitation')
    op.drop_index(op.f('ix_tenant_invitation_id'), table_name='tenant_invitation')
    op.drop_table('tenant_invitation')
    op.drop_index(op.f('ix_provider_access_credential_id'), table_name='provider_access_credential')
    op.drop_table('provider_access_credential')
    op.drop_index(op.f('ix_provider_id'), table_name='provider')
    op.drop_table('provider')
    op.drop_index(op.f('ix_model_tenant_id'), table_name='model')
    op.drop_index(op.f('ix_model_provider_id'), table_name='model')
    op.drop_index(op.f('ix_model_id'), table_name='model')
    op.drop_table('model')
    op.drop_index(op.f('ix_mcp_tool_id'), table_name='mcp_tool')
    op.drop_table('mcp_tool')
    op.drop_index(op.f('ix_mcp_server_id'), table_name='mcp_server')
    op.drop_table('mcp_server')
    op.drop_index(op.f('ix_knowledge_search_id'), table_name='knowledge_search')
    op.drop_table('knowledge_search')
    op.drop_index(op.f('ix_knowledge_file_id'), table_name='knowledge_file')
    op.drop_table('knowledge_file')
    op.drop_index(op.f('ix_knowledge_chunk_id'), table_name='knowledge_chunk')
    op.drop_table('knowledge_chunk')
    op.drop_index(op.f('ix_knowledge_base_id'), table_name='knowledge_base')
    op.drop_table('knowledge_base')
    op.drop_index(op.f('ix_agent_version_id'), table_name='agent_version')
    op.drop_table('agent_version')
    op.drop_index(op.f('ix_agent_tool_id'), table_name='agent_tool')
    op.drop_table('agent_tool')
    op.drop_index(op.f('ix_agent_id'), table_name='agent')
    op.drop_table('agent')
    op.drop_index(op.f('ix_access_token_id'), table_name='access_token')
    op.drop_table('access_token')
    op.drop_index(op.f('ix_access_credential_id'), table_name='access_credential')
    op.drop_table('access_credential')
    # ### end Alembic commands ###
