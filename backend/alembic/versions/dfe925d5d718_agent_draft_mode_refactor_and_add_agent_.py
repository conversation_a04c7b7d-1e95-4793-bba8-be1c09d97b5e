"""agent_draft_mode_refactor_and_add_agent_version_updated_at

Revision ID: dfe925d5d718
Revises: 3c56217ce608
Create Date: 2025-06-18 15:19:19.562500

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'dfe925d5d718'
down_revision: Union[str, None] = '3c56217ce608'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_tasks_id', table_name='tasks')
    op.drop_index('ix_tasks_priority', table_name='tasks')
    op.drop_index('ix_tasks_status', table_name='tasks')
    op.drop_index('ix_tasks_task_type', table_name='tasks')
    op.drop_index('ix_tasks_tenant_id', table_name='tasks')
    op.drop_table('tasks')
    op.create_foreign_key(None, 'access_credential', 'tenant', ['tenant_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'access_credential', 'agent', ['agent_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'access_token', 'access_credential', ['credential_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'agent', 'tenant', ['tenant_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'agent_tool', 'agent', ['agent_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'agent_tool', 'tool', ['tool_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'agent_tool', 'tenant_tool', ['tenant_tool_id'], ['id'], use_alter=True)
    op.add_column('agent_version', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.create_foreign_key(None, 'agent_version', 'agent', ['agent_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'agent_version', 'model', ['model_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'knowledge_base', 'tenant', ['tenant_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'knowledge_chunk', 'knowledge_file', ['file_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'knowledge_file', 'knowledge_base', ['knowledge_base_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'knowledge_search', 'knowledge_base', ['knowledge_base_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'mcp_server', 'tenant', ['tenant_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'mcp_tool', 'mcp_server', ['server_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'mcp_tool', 'tool', ['tool_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'model', 'tenant', ['tenant_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'model', 'provider', ['provider_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'model', 'provider_access_credential', ['provider_access_credential_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'provider_access_credential', 'provider', ['provider_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'provider_access_credential', 'tenant', ['tenant_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'tenant_invitation', 'user', ['inviter_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'tenant_invitation', 'tenant', ['tenant_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'tenant_tool', 'tenant', ['tenant_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'tenant_tool', 'tool', ['tool_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'tool_execution_log', 'agent_tool', ['agent_tool_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'tool_execution_log', 'tool', ['tool_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'user_tenant_association', 'user', ['user_id'], ['id'], use_alter=True)
    op.create_foreign_key(None, 'user_tenant_association', 'tenant', ['tenant_id'], ['id'], use_alter=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user_tenant_association', type_='foreignkey')
    op.drop_constraint(None, 'user_tenant_association', type_='foreignkey')
    op.drop_constraint(None, 'tool_execution_log', type_='foreignkey')
    op.drop_constraint(None, 'tool_execution_log', type_='foreignkey')
    op.drop_constraint(None, 'tenant_tool', type_='foreignkey')
    op.drop_constraint(None, 'tenant_tool', type_='foreignkey')
    op.drop_constraint(None, 'tenant_invitation', type_='foreignkey')
    op.drop_constraint(None, 'tenant_invitation', type_='foreignkey')
    op.drop_constraint(None, 'provider_access_credential', type_='foreignkey')
    op.drop_constraint(None, 'provider_access_credential', type_='foreignkey')
    op.drop_constraint(None, 'model', type_='foreignkey')
    op.drop_constraint(None, 'model', type_='foreignkey')
    op.drop_constraint(None, 'model', type_='foreignkey')
    op.drop_constraint(None, 'mcp_tool', type_='foreignkey')
    op.drop_constraint(None, 'mcp_tool', type_='foreignkey')
    op.drop_constraint(None, 'mcp_server', type_='foreignkey')
    op.drop_constraint(None, 'knowledge_search', type_='foreignkey')
    op.drop_constraint(None, 'knowledge_file', type_='foreignkey')
    op.drop_constraint(None, 'knowledge_chunk', type_='foreignkey')
    op.drop_constraint(None, 'knowledge_base', type_='foreignkey')
    op.drop_constraint(None, 'agent_version', type_='foreignkey')
    op.drop_constraint(None, 'agent_version', type_='foreignkey')
    op.drop_column('agent_version', 'updated_at')
    op.drop_constraint(None, 'agent_tool', type_='foreignkey')
    op.drop_constraint(None, 'agent_tool', type_='foreignkey')
    op.drop_constraint(None, 'agent_tool', type_='foreignkey')
    op.drop_constraint(None, 'agent', type_='foreignkey')
    op.drop_constraint(None, 'access_token', type_='foreignkey')
    op.drop_constraint(None, 'access_credential', type_='foreignkey')
    op.drop_constraint(None, 'access_credential', type_='foreignkey')
    op.create_table('tasks',
    sa.Column('id', mysql.VARCHAR(length=36), nullable=False),
    sa.Column('tenant_id', mysql.VARCHAR(length=36), nullable=False),
    sa.Column('task_type', mysql.ENUM('INDEX_DOCUMENT', 'DELETE_FILE_INDEX', 'REINDEX_KNOWLEDGE_BASE'), nullable=False),
    sa.Column('status', mysql.ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'RETRY'), nullable=True),
    sa.Column('params', mysql.JSON(), nullable=False),
    sa.Column('result', mysql.JSON(), nullable=True),
    sa.Column('error', mysql.TEXT(), nullable=True),
    sa.Column('retry_count', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('max_retries', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('priority', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('created_at', mysql.DATETIME(), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.Column('started_at', mysql.DATETIME(), nullable=True),
    sa.Column('completed_at', mysql.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_tasks_tenant_id', 'tasks', ['tenant_id'], unique=False)
    op.create_index('ix_tasks_task_type', 'tasks', ['task_type'], unique=False)
    op.create_index('ix_tasks_status', 'tasks', ['status'], unique=False)
    op.create_index('ix_tasks_priority', 'tasks', ['priority'], unique=False)
    op.create_index('ix_tasks_id', 'tasks', ['id'], unique=False)
    # ### end Alembic commands ###
