[pytest]
# 设置测试目录和文件模式
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 标记定义
markers =
    integration: tests that require external services or actual files
    unit: unit tests that mock external dependencies
    
# 显示详细的测试输出
addopts = -v

# 允许使用asyncio进行测试
asyncio_mode = auto 
# 设置异步事件循环作用域
asyncio_default_fixture_loop_scope = function
asyncio_default_test_loop_scope = function 

# 设置FAISS存储目录
env =
    RAG_VECTOR_DB_FAISS_STORE_DIR=./backend/tests/faiss_test_vectors 

