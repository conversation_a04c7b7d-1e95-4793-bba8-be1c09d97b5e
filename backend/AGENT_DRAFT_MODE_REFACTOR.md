# Agent 创建接口草稿模式重构

## 概述

将 Agent 统一创建接口重构为只支持草稿模式，简化创建流程，确保所有 Agent 在发布前都经过完整的配置和验证。

## 重构目标

1. **统一创建流程**：所有 Agent 创建后都处于草稿状态
2. **提高 Agent 质量**：强制验证配置后才能发布
3. **简化接口复杂度**：移除多种创建模式的复杂性
4. **增强用户体验**：提供清晰的配置 → 验证 → 发布流程

## 主要变更

### 1. Schema 变更 (`app/schemas/agent.py`)

**移除的字段：**
- `create_draft_only`: 不再需要，始终创建草稿
- `auto_publish`: 不再需要，不支持自动发布

**新增的 Schema：**
- `AgentBasicInfoUpdate`: 专门用于基本信息更新

**保留的功能：**
- 模板创建和自定义创建
- 所有配置参数和优先级规则
- 验证逻辑

### 2. 服务层重构 (`app/services/agent.py`)

**简化的方法：**
- `create_agent_unified()`: 只调用草稿创建流程
- 移除 `_create_initial_version()` 方法
- 保留 `_create_draft_version()` 方法

**新的创建流程：**
```python
async def create_agent_unified(self, tenant_id: str, agent_data: AgentCreateRequest):
    """统一的Agent创建方法，只支持草稿模式"""
    # 1. 验证租户和基础信息
    # 2. 处理模板配置
    # 3. 创建 Agent 基础记录
    # 4. 创建草稿版本
    # 5. 返回草稿响应
```

### 3. API 层更新 (`app/api/v1/agent.py`)

**新增接口：**
- `PUT /agents/{id}/basic-info` - 基本信息更新（立即生效）

**重新设计的接口文档：**
- 明确区分基本信息和配置信息
- 提供清晰的操作流程指导
- 增加前端开发示例

**接口分类：**
```
基本信息管理（立即生效）：
- PUT /agents/{id}/basic-info     # 更新基本信息
- POST /agents/{id}/icon          # 上传图标
- DELETE /agents/{id}/icon        # 删除图标

配置信息管理（草稿模式）：
- GET /agents/{id}/draft          # 获取草稿配置
- PUT /agents/{id}/draft          # 更新草稿配置  
- POST /agents/{id}/validate      # 验证配置
- POST /agents/{id}/publish       # 发布配置
```

## 📋 前端理解指南

### 🎯 信息分类原则

为了让前端开发者更容易理解，我们将 Agent 信息明确分为两大类：

#### **基本信息**（立即生效）
```javascript
// 这些信息修改后立即生效，无需发布
const basicInfo = {
  name: "Agent名称",        // ✅ 用户看到的显示名称
  description: "Agent描述", // ✅ 用户看到的描述信息
  is_active: true,          // ✅ 是否启用Agent
  icon: "图标文件"          // ✅ Agent头像/图标
};

// API调用：立即保存，立即生效
PUT /agents/{id}/basic-info
```

#### **配置信息**（草稿模式）
```javascript  
// 这些信息影响Agent行为，需要通过草稿→发布流程
const configInfo = {
  model_id: "gpt-4",                 // 🔄 LLM模型选择
  prompt: "你是一个专业助手...",      // 🔄 Agent行为定义
  knowledge_base_ids: ["kb_001"],    // 🔄 知识库关联
  tool_ids: ["tool_001"],            // 🔄 工具能力
  llm_model_config: {                // 🔄 模型参数
    temperature: 0.7,
    max_tokens: 2000
  }
};

// API调用：草稿保存 → 验证 → 发布生效
PUT /agents/{id}/draft → POST /agents/{id}/publish
```

### 🎨 前端界面设计建议

```jsx
const AgentEditPage = ({ agentId }) => {
  return (
    <div className="agent-edit">
      {/* 第一区域：基本信息 - 立即生效 */}
      <Card title="基本信息" className="basic-info-card">
        <Form onFinish={handleBasicInfoSave}>
          <Form.Item name="name" label="Agent名称">
            <Input placeholder="输入Agent名称" />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <TextArea placeholder="输入Agent描述" />
          </Form.Item>
          <Form.Item name="is_active" label="状态">
            <Switch checkedChildren="启用" unCheckedChildren="停用" />
          </Form.Item>
          
          <Button type="primary" htmlType="submit">
            💾 保存基本信息（立即生效）
          </Button>
        </Form>
      </Card>
      
      {/* 第二区域：配置信息 - 草稿模式 */}
      <Card 
        title="配置信息" 
        className="config-info-card"
        extra={
          <Badge 
            status={hasChanges ? "processing" : "success"} 
            text={hasChanges ? "有未发布更改" : "配置已同步"}
          />
        }
      >
        <Form onChange={handleConfigChange}> {/* 自动保存草稿 */}
          <Form.Item name="model_id" label="LLM模型">
            <Select placeholder="选择模型" />
          </Form.Item>
          <Form.Item name="prompt" label="提示词">
            <TextArea rows={6} placeholder="定义Agent行为..." />
          </Form.Item>
          <Form.Item name="knowledge_base_ids" label="知识库">
            <Select mode="multiple" placeholder="选择知识库" />
          </Form.Item>
        </Form>
        
        <div className="config-actions">
          <Button onClick={handleValidate}>
            ✅ 验证配置
          </Button>
          <Button 
            type="primary" 
            onClick={handlePublish}
            disabled={!isReadyToPublish}
          >
            🚀 发布配置（生效）
          </Button>
        </div>
      </Card>
    </div>
  );
};
```

### 🔄 状态管理最佳实践

```javascript
// 推荐的状态管理结构
const useAgentEdit = (agentId) => {
  const [agent, setAgent] = useState(null);           // 基本信息
  const [draftConfig, setDraftConfig] = useState(null); // 草稿配置
  const [hasChanges, setHasChanges] = useState(false);   // 是否有未发布更改
  
  // 基本信息更新 - 立即生效
  const updateBasicInfo = async (basicInfo) => {
    const updated = await api.updateBasicInfo(agentId, basicInfo);
    setAgent(updated); // 立即更新UI状态
    showSuccessMessage("基本信息已保存"); // 立即反馈
  };
  
  // 配置信息更新 - 自动保存草稿
  const updateConfig = async (config) => {
    const draft = await api.updateDraft(agentId, config);
    setDraftConfig(draft);
    setHasChanges(draft.has_changes);
    // 不显示保存成功提示，因为是自动保存
  };
  
  // 发布配置 - 草稿生效
  const publishConfig = async () => {
    await api.publishConfig(agentId);
    await refreshAgent(); // 重新加载完整数据
    setHasChanges(false);
    showSuccessMessage("配置已发布并生效"); // 重要操作反馈
  };
  
  return {
    agent,           // 基本信息
    draftConfig,     // 草稿配置
    hasChanges,      // 状态指示
    updateBasicInfo, // 立即生效操作
    updateConfig,    // 草稿操作
    publishConfig    // 发布操作
  };
};
```

### 💡 用户体验原则

1. **即时反馈 vs 安全发布**：
   - 基本信息：修改→保存→立即显示效果
   - 配置信息：修改→草稿保存→验证→发布→生效

2. **视觉区分**：
   - 基本信息区域：使用绿色/立即生效的视觉样式
   - 配置信息区域：使用橙色/需要发布的视觉样式

3. **状态提示**：
   - 基本信息：保存后显示"✅ 已保存"
   - 配置信息：显示"🔄 草稿已保存，需要发布才能生效"

4. **错误处理**：
   - 基本信息验证失败：字段级即时错误提示
   - 配置验证失败：阻止发布，显示详细验证结果

### 🚀 开发效率提升

通过这种设计，前端开发可以：

1. **明确职责**：清楚哪些操作立即生效，哪些需要发布
2. **简化逻辑**：不用复杂的状态判断，按信息类型选择API
3. **提升体验**：用户操作更符合直觉，减少混淆
4. **降低错误**：类型化的接口减少参数错误

## 迁移指南

### 原有代码迁移

```javascript
// 旧方式：统一更新接口
PUT /agents/{id} {
  name: "新名称",
  prompt: "新提示词"
}

// 新方式：分类更新
PUT /agents/{id}/basic-info {
  name: "新名称"      // 立即生效
}

PUT /agents/{id}/draft {
  prompt: "新提示词"  // 保存草稿
}

POST /agents/{id}/publish // 发布配置
```

## 总结

新的设计通过明确区分基本信息和配置信息，让前端开发者能够：

1. **直观理解**：哪些修改立即生效，哪些需要发布
2. **合理设计**：UI界面分区明确，用户操作符合预期  
3. **简化开发**：API调用清晰，状态管理简单
4. **提升体验**：用户获得即时反馈，同时保证Agent质量

这种设计平衡了用户体验和系统质量，让前端开发更加简单高效。 