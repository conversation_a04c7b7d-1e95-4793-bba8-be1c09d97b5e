# LangGraph引擎重构说明

## 概述

本次重构的目标是将数据库访问逻辑从 `LangGraphEngine` 中移除，使引擎更加纯净，所有数据都从外部传入。

## 主要修改

### 1. 移除数据库访问逻辑

**之前**: `LangGraphEngine` 内部包含 `_get_model_info` 方法，直接访问数据库获取模型信息。

**现在**: 移除了 `_get_model_info` 方法，所有模型信息都从外部传入。

### 2. 修改方法签名

#### `build_agent_graph` 方法
```python
# 之前
def build_agent_graph(self, agent: Agent, version: AgentVersion) -> StateGraph:

# 现在  
def build_agent_graph(self, agent: Agent, version: AgentVersion, metadata: Optional[Dict[str, Any]] = None, model_info: Optional[Tuple[Model, Provider, ProviderAccessCredential]] = None) -> StateGraph:
```

#### `execute_agent` 方法
```python
# 之前
async def execute_agent(
    self, 
    agent: Agent, 
    version: AgentVersion, 
    prompt: str, 
    history: Optional[List[Dict[str, str]]] = None,
    stream: bool = False,
    metadata: Optional[Dict[str, Any]] = None
) -> Union[Dict[str, Any], Any]:

# 现在
async def execute_agent(
    self, 
    agent: Agent, 
    version: AgentVersion, 
    prompt: str, 
    history: Optional[List[Dict[str, str]]] = None,
    stream: bool = False,
    metadata: Optional[Dict[str, Any]] = None,
    model_info: Optional[Tuple[Model, Provider, ProviderAccessCredential]] = None
) -> Union[Dict[str, Any], Any]:
```

#### `_build_llm_node` 方法
```python
# 之前
def _build_llm_node(self, agent: Agent, version: AgentVersion, parsed_config: Dict[str, Any], system_message: SystemMessage) -> Callable[[AgentState], AgentState]:

# 现在
def _build_llm_node(self, agent: Agent, version: AgentVersion, parsed_config: Dict[str, Any], system_message: SystemMessage, model_info: Optional[Tuple[Model, Provider, ProviderAccessCredential]] = None) -> Callable[[AgentState], AgentState]:
```

### 3. AgentService 中的修改

#### 新增 `_get_model_info` 方法
在 `AgentService` 中添加了 `_get_model_info` 方法，负责获取模型的完整信息：

```python
def _get_model_info(self, model_id: str, tenant_id: int) -> Optional[Tuple[Model, Provider, ProviderAccessCredential]]:
    """
    获取模型的完整信息，包括模型、提供商和访问凭证
    
    Args:
        model_id: 模型ID
        tenant_id: 租户ID
        
    Returns:
        (Model, Provider, ProviderAccessCredential) 元组，如果获取失败返回None
    """
```

#### 修改调用逻辑
在 `invoke_agent` 和 `invoke_agent_stream` 方法中：

1. 首先获取模型信息
2. 将模型信息传递给 `execute_agent` 方法

```python
# 获取模型信息
model_info = None
if version.model_id:
    model_info = self._get_model_info(version.model_id, tenant_id)
elif agent.model_id:
    model_info = self._get_model_info(agent.model_id, tenant_id)

# 使用LangGraph执行Agent推理
result = await self.langgraph_engine.execute_agent(
    agent=agent,
    version=version,
    prompt=request_data.get("prompt", ""),
    history=request_data.get("history"),
    stream=False,
    metadata=metadata,
    model_info=model_info  # 新增参数
)
```

### 4. ModelFactory 增强

#### 新增 `create_model_with_info` 方法
```python
def create_model_with_info(
    self,
    model: Model,
    provider: Provider,
    credential: ProviderAccessCredential,
    extra_config: Optional[Dict[str, Any]] = None
) -> Optional[BaseChatModel]:
    """
    使用已有的模型、提供商和凭证信息创建模型实例
    """
```

#### 修复 `create_model` 方法
修复了原有方法中的数据库查询逻辑，正确处理模型与提供商的关联关系。

## 优势

1. **关注点分离**: `LangGraphEngine` 专注于图构建和执行逻辑，不再处理数据库访问
2. **可测试性**: 更容易进行单元测试，可以直接传入模拟数据
3. **灵活性**: 支持从不同来源获取模型信息，不局限于数据库
4. **性能优化**: 可以在上层进行缓存和批量查询优化

## 向后兼容性

所有新增的参数都是可选的，保持了向后兼容性。如果不传入 `model_info`，系统会回退到原有的逻辑。

## 使用示例

```python
# 在AgentService中
model_info = self._get_model_info(version.model_id, tenant_id)

# 调用LangGraph引擎
result = await self.langgraph_engine.execute_agent(
    agent=agent,
    version=version,
    prompt="你好",
    model_info=model_info
)
``` 