from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
from contextlib import asynccontextmanager
import logging

# 导入配置
from .core.config import settings
from .core.logging_config import logging_config

# 导入数据库连接
from .db.session import SessionLocal, engine, get_db
from .db.base import Base

# 导入API路由
from .api.v1 import tenant, user, role, agent, auth, tool, model, provider, admin, knowledge, agent_execution

# 导入模型提供商检查器和初始化器
from .core.model_provider_initializer import model_provider_initializer

# 导入任务队列服务
from .services.task_queue import task_queue_service

# 导入初始化平台工具
from app.services.tool import ToolService,MCPServerService

# 配置日志
logging_config.configure_from_env()
logger = logging_config.get_logger(__name__)

# 创建数据库表（如果不存在）
# 实际生产环境建议使用Alembic进行数据库迁移
Base.metadata.create_all(bind=engine)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动事件：可以在这里添加应用启动时需要执行的逻辑
    logger.info("应用启动，开始初始化...")
    
    # 使用数据库会话
    db = SessionLocal()
    try:
        # 初始化默认模型提供商
        logger.info("开始初始化默认模型提供商...")
        provider_results = model_provider_initializer.initialize_providers(db)
        
        success_count = provider_results.get("success", 0)
        total_count = provider_results.get("total", 0)
        logger.info(f"模型提供商初始化完成: {success_count}/{total_count} 个提供商成功")
                # 启动任务队列工作线程
        logger.info("Starting task queue worker...")
        task_queue_service.start_worker()

        # 初始化平台工具
        logger.info("开始初始化平台工具...")
        ToolService.init_platform_tools(db)
        logger.info("平台工具初始化完成")

        # 初始化MCP服务器
        logger.info("开始初始化MCP服务器...")
        MCPServerService.start_mcp_status_monitor()
        logger.info("MCP服务器初始化完成")
    except Exception as e:
        logger.error(f"初始化时出错: {str(e)}")
    finally:
        db.close()
    
    yield
    
    # 关闭事件：可以在这里添加应用关闭时需要执行的逻辑
    logger.info("应用关闭，执行清理操作...")
    # 停止任务队列工作线程
    logger.info("Stopping task queue worker...")
    task_queue_service.stop_worker()
# 创建FastAPI应用
app = FastAPI(
    title="云知问智能Agent平台API",
    description="云知问多租户智能Agent平台的后端API服务",
    version="0.1.0",
    lifespan=lifespan,
    swagger_ui_parameters={"syntaxHighlight": {"theme": "obsidian"}},
    openapi_url="/openapi.json"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(auth.router, prefix=settings.API_V1_STR + "/auth", tags=["认证"])
app.include_router(tenant.router, prefix=settings.API_V1_STR, tags=["租户管理"])
app.include_router(user.router, prefix=settings.API_V1_STR, tags=["用户管理"])
app.include_router(role.router, prefix=settings.API_V1_STR, tags=["角色管理"])
app.include_router(agent.router, prefix=settings.API_V1_STR, tags=["Agent管理"])
app.include_router(tool.router, prefix=settings.API_V1_STR)
app.include_router(model.router, prefix=settings.API_V1_STR, tags=["模型管理"])
app.include_router(provider.router, prefix=settings.API_V1_STR, tags=["提供商管理"])
app.include_router(admin.router, prefix=settings.API_V1_STR, tags=["超级管理员"])
app.include_router(knowledge.router, prefix=settings.API_V1_STR, tags=["知识库管理"])
app.include_router(agent_execution.router, prefix=settings.API_V1_STR, tags=["Agent执行"])

# 健康检查端点
@app.get("/health", tags=["健康检查"])
def health_check():
    return {
        "status": "healthy",
        "db_type": settings.DB_TYPE
    }

# 主函数
if __name__ == "__main__":
    # 从环境变量获取配置，如果没有则使用默认值
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", 8000))
    
    # 启动服务器
    uvicorn.run("app.main:app", host=host, port=port, reload=True)
