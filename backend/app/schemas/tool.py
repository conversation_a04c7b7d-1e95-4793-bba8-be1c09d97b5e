from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator


# 基础工具Schema
class ToolBase(BaseModel):
    name: str = Field(..., description="工具名称", max_length=64)
    key: str = Field(..., description="工具标识", max_length=64)
    description: Optional[str] = Field(None, description="工具描述")
    config_schema: Optional[Dict[str, Any]] = Field(None, description="配置架构") 
    param_schema: Optional[Dict[str, Any]] = Field(None, description="参数架构")
    tag: Optional[str] = Field(None, description="工具分类")
    is_active: bool = Field(True, description="是否激活")

    class Config:
        from_attributes = True

# 创建工具
class ToolCreate(ToolBase):
    @validator('name')
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('工具名称不能为空')
        return v.strip()
    
    @validator('key')
    def key_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('工具标识不能为空')
        return v.strip()

# 更新工具
class ToolUpdate(BaseModel):
    name: Optional[str] = Field(None, description="工具名称", max_length=64)
    description: Optional[str] = Field(None, description="工具描述")
    config_schema: Optional[List[Dict[str, Any]]] = Field(None, description="配置架构")  # 修改：Dict -> List[Dict]
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    class Config:
        from_attributes = True

# 响应模型
class Tool(ToolBase):
    id: str
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 工具详情
class ToolDetail(Tool):
    pass

# 租户工具配置
class TenantToolBase(BaseModel):
    tenant_id: str = Field(..., description="租户ID")
    tool_id: str = Field(..., description="工具ID")
    config: Optional[Dict[str, Any]] = Field(None, description="配置信息")
    is_active: Optional[bool] = Field(True, description="是否激活")
    
    class Config:
        from_attributes = True

# 创建租户工具配置
class TenantToolCreate(TenantToolBase):
    pass

# 更新租户工具配置
class TenantToolUpdate(BaseModel):
    config: Optional[Dict[str, Any]] = Field(None, description="配置信息")
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    class Config:
        from_attributes = True

# 租户工具响应
class TenantTool(TenantToolBase):
    id: str
    created_at: datetime
    updated_at: datetime
    tool: Optional[Tool] = None
    
    class Config:
        from_attributes = True

# Agent工具配置
class AgentToolBase(BaseModel):
    agent_id: str = Field(..., description="Agent ID")
    tool_id: str = Field(..., description="工具ID")
    tenant_tool_id: Optional[str] = Field(None, description="租户工具ID")
    config: Optional[Dict[str, Any]] = Field(None, description="配置信息")
    is_active: Optional[bool] = Field(True, description="是否激活")
    
    class Config:
        from_attributes = True

# 创建Agent工具配置
class AgentToolCreate(AgentToolBase):
    pass

# 更新Agent工具配置
class AgentToolUpdate(BaseModel):
    config: Optional[Dict[str, Any]] = Field(None, description="配置信息")
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    class Config:
        from_attributes = True

# Agent工具响应
class AgentTool(AgentToolBase):
    id: str
    created_at: datetime
    updated_at: datetime
    tool: Optional[Tool] = None
    
    class Config:
        from_attributes = True

# MCP服务器
class MCPServerBase(BaseModel):
    name: str = Field(..., description="服务器名称",max_length=64)
    endpoint: str = Field(..., description="服务器地址",max_length=256)
    transport_type: str = Field(..., description="传输类型")

    status: Optional[str] = Field("active", description="状态")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")
    
    class Config:
        from_attributes = True

# 创建MCP服务器
class MCPServerCreate(MCPServerBase):
    pass

# 更新MCP服务器
class MCPServerUpdate(BaseModel):
    name: Optional[str] = Field(None, description="服务器名称", max_length=64)
    endpoint: Optional[str] = Field(None, description="服务器地址", max_length=256)

    transport_type: Optional[str] = Field(None, description="传输类型")
   
    status: Optional[str] = Field(None, description="状态")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")
    
    class Config:
        from_attributes = True

# MCP服务器响应
class MCPServer(MCPServerBase):
    id: str
    last_heartbeat: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 工具执行记录
class ToolExecutionLogCreate(BaseModel):
    session_id: str = Field(..., description="会话ID")
    tool_id: str = Field(..., description="工具ID")
    agent_tool_id: str = Field(..., description="Agent工具ID")
    input_params: Dict[str, Any] = Field(..., description="输入参数")
    output_result: Optional[Dict[str, Any]] = Field(None, description="输出结果")
    execution_time: int = Field(..., description="执行时间(毫秒)")
    status: str = Field(..., description="状态")
    error_message: Optional[str] = Field(None, description="错误信息")
        
# 工具执行记录响应
class ToolExecutionLog(ToolExecutionLogCreate):
    id: str
    created_at: datetime
    
    class Config:
        from_attributes = True

# 列表响应
class ToolListResponse(BaseModel):
    total: int
    items: List[Tool]

class TenantToolListResponse(BaseModel):
    total: int
    items: List[TenantTool]

class AgentToolListResponse(BaseModel):
    total: int
    items: List[AgentTool]

class MCPServerListResponse(BaseModel):
    total: int
    items: List[MCPServer]

class ToolExecutionLogListResponse(BaseModel):
    total: int
    items: List[ToolExecutionLog]