from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator, computed_field
from app.models.agent import AgentType as AgentTypeEnum  # 导入枚举类型

# 基础Schema定义
class AgentBase(BaseModel):
    name: str = Field(..., description="Agent名称", max_length=64)
    agent_type: Optional[AgentTypeEnum] = Field(None, description="Agent类型")
    description: Optional[str] = Field(None, description="Agent描述")
    icon_url: Optional[str] = Field(None, description="图标S3存储路径")
    
    class Config:
        from_attributes = True
        use_enum_values = True  # 使用枚举值


# Agent模型配置
class AgentLLMModelConfig(BaseModel):
    """
    LLM模型配置
    """
    temperature: Optional[float] = Field(0.5, description="温度", ge=0, le=1)
    top_p: Optional[float] = Field(0.5, description="Top-P", ge=0, le=1)
    max_tokens: Optional[int] = Field(2048, description="最大Token数", ge=0)
    max_retries: Optional[int] = Field(3, description="最大重试次数", ge=0, le=10)
    timeout: Optional[int] = Field(10, description="超时时间", ge=0)


class AgentKnowledgeBaseConfig(BaseModel):
    """
    知识库配置
    """
    max_results: Optional[int] = Field(5, description="最大返回结果数", ge=0)
    min_score: Optional[float] = Field(0.2, description="最小得分", ge=0, le=1)


# 创建Agent
class AgentCreate(AgentBase):
    tenant_id: str = Field(..., description="所属租户ID")
    prompt: Optional[str] = Field(None, description="初始提示词配置")
    # 将llm_model_config和knowledge_base_config作为独立字段，在service中合并到config
    llm_model_config: Optional[AgentLLMModelConfig] = Field(None, description="LLM模型配置")
    knowledge_base_config: Optional[AgentKnowledgeBaseConfig] = Field(None, description="知识库配置")
    model_id: Optional[str] = Field(None, description="LLM模型ID")
    knowledge_base_ids: Optional[List[str]] = Field(None, description="关联的知识库ID列表")
    tool_ids: Optional[List[str]] = Field(None, description="关联的工具ID列表")
    mcp_server_ids: Optional[List[str]] = Field(None, description="关联的MCP服务器ID列表")
    
    @field_validator('name')
    @classmethod
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('名称不能为空')
        return v
    
    class Config:
        from_attributes = True


# 更新Agent
class AgentUpdate(AgentBase):
    is_active: Optional[bool] = Field(None, description="是否激活")
        
    class Config:
        from_attributes = True

# 基本信息更新 - 立即生效
class AgentBasicInfoUpdate(BaseModel):
    """Agent基本信息更新 - 立即生效，无需发布"""
    name: Optional[str] = Field(None, description="Agent名称", max_length=64)
    description: Optional[str] = Field(None, description="Agent描述")
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    @field_validator('name')
    @classmethod
    def name_not_empty(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('名称不能为空')
        return v
    
    class Config:
        from_attributes = True

# 版本相关模型
class AgentVersionBase(BaseModel):
    version_number: str = Field(..., description="版本号", max_length=32)
    description: Optional[str] = Field(None, description="版本描述")
    model_id: Optional[str] = Field(None, description="LLM模型ID")
    knowledge_base_ids: Optional[List[str]] = Field(None, description="关联的知识库ID列表")
    tool_ids: Optional[List[str]] = Field(None, description="关联的工具ID列表")
    mcp_server_ids: Optional[List[str]] = Field(None, description="关联的MCP服务器ID列表")
    
    class Config:
        from_attributes = True

# 响应模型
class AgentVersion(AgentVersionBase):
    id: str
    agent_id: str
    config: Optional[Dict[str, Any]]
    prompt: Optional[str]
    is_current: bool
    is_published: bool
    created_at: datetime
    
    @computed_field
    @property
    def llm_model_config(self) -> Optional[AgentLLMModelConfig]:
        """从config中提取模型配置"""
        if not self.config or 'model_config' not in self.config:
            return None
        try:
            return AgentLLMModelConfig(**self.config['model_config'])
        except (TypeError, ValueError):
            return None
    
    @computed_field
    @property
    def knowledge_base_config(self) -> Optional[AgentKnowledgeBaseConfig]:
        """从config中提取知识库配置"""
        if not self.config or 'knowledge_base_config' not in self.config:
            return None
        try:
            return AgentKnowledgeBaseConfig(**self.config['knowledge_base_config'])
        except (TypeError, ValueError):
            return None
    
    class Config:
        from_attributes = True

class Agent(AgentBase):
    id: str
    tenant_id: str
    open_id: str
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    # 从当前版本获取的动态属性
    config: Optional[Dict[str, Any]] = Field(None, description="当前版本的配置")
    model_id: Optional[str] = Field(None, description="当前版本的模型ID")
    prompt: Optional[str] = Field(None, description="当前版本的提示词")
    
    # 图标访问URL（通过property自动计算）
    icon_access_url: Optional[str] = Field(None, description="图标访问URL")
    icon_metadata: Optional[Dict[str, Any]] = Field(None, description="图标元数据")
    
    @computed_field
    @property
    def is_supervisor(self) -> bool:
        """是否为监督者类型"""
        return self.agent_type == AgentTypeEnum.SUPERVISOR
    
    @computed_field
    @property
    def llm_model_config(self) -> Optional[AgentLLMModelConfig]:
        """从config中提取模型配置"""
        if not self.config or 'model_config' not in self.config:
            return None
        try:
            return AgentLLMModelConfig(**self.config['model_config'])
        except (TypeError, ValueError):
            return None
    
    @computed_field
    @property
    def knowledge_base_config(self) -> Optional[AgentKnowledgeBaseConfig]:
        """从config中提取知识库配置"""
        if not self.config or 'knowledge_base_config' not in self.config:
            return None
        try:
            return AgentKnowledgeBaseConfig(**self.config['knowledge_base_config'])
        except (TypeError, ValueError):
            return None
    
    class Config:
        from_attributes = True

class AgentDetail(Agent):
    versions: List[AgentVersion]
    
    class Config:
        from_attributes = True
    
# 页面响应
class AgentListResponse(BaseModel):
    total: int
    items: List[Agent]

# API调用
class AgentAPIRequest(BaseModel):
    prompt: str = Field(..., description="用户提示词")
    history: Optional[List[Dict[str, str]]] = Field(None, description="对话历史")
    user_info: Optional[Dict[str, Any]] = Field(None, description="用户信息")
    session_id: Optional[str] = Field(None, description="会话ID")
    stream: Optional[bool] = Field(False, description="是否流式响应")
    
class AgentAPIResponse(BaseModel):
    response: str = Field(..., description="Agent响应内容")
    session_id: str = Field(..., description="会话ID")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据，包含agent_id、agent_name、model_id等信息") 

# 图标上传相关Schema
class AgentIconUploadResponse(BaseModel):
    """图标上传响应"""
    success: bool = Field(..., description="是否上传成功")
    message: str = Field(..., description="响应消息")
    icon_url: Optional[str] = Field(None, description="图标S3存储路径")
    icon_access_url: Optional[str] = Field(None, description="图标访问URL")
    metadata: Optional[Dict[str, Any]] = Field(None, description="图标元数据")

class AgentIconDeleteResponse(BaseModel):
    """图标删除响应"""
    success: bool = Field(..., description="是否删除成功")
    message: str = Field(..., description="响应消息")

# ================= 简化版本管理方案 Schema =================

# Agent创建模板
class AgentTemplate(BaseModel):
    """Agent创建模板"""
    template_id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    agent_type: AgentTypeEnum = Field(..., description="Agent类型")
    description: str = Field(..., description="模板描述")
    icon_url: Optional[str] = Field(None, description="模板图标")
    
    # 与AgentCreateRequest字段保持一致的默认配置
    default_config: Dict[str, Any] = Field(default_factory=dict, description="默认配置，包含model_id、prompt、knowledge_base_ids等")
    
    # 智能推荐的资源ID列表 - 使用实际的ID而不是key
    suggested_model_ids: List[str] = Field(default_factory=list, description="推荐模型ID列表")
    suggested_tool_ids: List[str] = Field(default_factory=list, description="推荐工具ID列表")
    suggested_knowledge_base_ids: List[str] = Field(default_factory=list, description="推荐知识库ID列表")
    
    # 配置向导 - 增强的步骤定义
    config_wizard: List[Dict[str, Any]] = Field(default_factory=list, description="配置向导步骤，包含字段类型和选项来源")
    
    class Config:
        from_attributes = True


# 统一的Agent创建请求 - 只支持草稿模式
class AgentCreateRequest(BaseModel):
    """统一的Agent创建请求"""
    # 基础信息
    name: str = Field(..., description="Agent名称", max_length=64)
    description: Optional[str] = Field(None, description="Agent描述")
    
    # 创建方式选择 - 模板或自定义
    template_id: Optional[str] = Field(None, description="使用的模板ID，如果指定则为模板创建")
    agent_type: AgentTypeEnum = Field(..., description="Agent类型")
    
    # 配置信息 - 优先级：显式指定 > 模板默认值
    model_id: str = Field(..., description="LLM模型ID")
    prompt: str = Field(..., description="提示词")
    
    # 资源关联
    knowledge_base_ids: Optional[List[str]] = Field(None, description="知识库ID列表")
    tool_ids: Optional[List[str]] = Field(None, description="工具ID列表")
    mcp_server_ids: Optional[List[str]] = Field(None, description="MCP服务器ID列表")
    
    # 结构化配置 - 后端会自动合并到config JSON字段
    llm_model_config: Optional[AgentLLMModelConfig] = Field(None, description="LLM模型配置")
    knowledge_base_config: Optional[AgentKnowledgeBaseConfig] = Field(None, description="知识库配置")
    
    @field_validator('name')
    @classmethod
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('名称不能为空')
        return v
    
    @field_validator('agent_type')
    @classmethod
    def agent_type_required_for_custom(cls, v, values):
        # 如果没有使用模板，则agent_type必须指定
        template_id = values.data.get('template_id') if hasattr(values, 'data') else None
        if not template_id and not v:
            raise ValueError('自定义创建时必须指定agent_type')
        return v
    
    class Config:
        from_attributes = True

# Agent草稿配置
class AgentDraftConfig(BaseModel):
    """Agent草稿配置"""
    # 核心配置
    model_id: str = Field(..., description="LLM模型ID")
    prompt: str = Field(..., description="提示词")
    
    # 资源关联
    knowledge_base_ids: Optional[List[str]] = Field(None, description="知识库ID列表")
    tool_ids: Optional[List[str]] = Field(None, description="工具ID列表")
    mcp_server_ids: Optional[List[str]] = Field(None, description="MCP服务器ID列表")
    
    # 结构化配置 - 从数据库config JSON字段中提取
    llm_model_config: Optional[AgentLLMModelConfig] = Field(None, description="LLM模型配置")
    knowledge_base_config: Optional[AgentKnowledgeBaseConfig] = Field(None, description="知识库配置")
    
    # 状态信息
    has_changes: bool = Field(False, description="是否有未发布的更改")
    is_ready_to_publish: bool = Field(False, description="是否准备好发布")
    validation_errors: List[str] = Field(default_factory=list, description="配置验证错误")
    last_saved_at: Optional[datetime] = Field(None, description="最后保存时间")
    last_published_at: Optional[datetime] = Field(None, description="最后发布时间")
    
    class Config:
        from_attributes = True

# 发布请求
class AgentPublishRequest(BaseModel):
    """发布Agent配置"""
    release_notes: Optional[str] = Field(None, description="发布说明")

# 配置验证响应
class AgentConfigValidation(BaseModel):
    """Agent配置验证结果"""
    is_valid: bool = Field(..., description="配置是否有效")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    suggestions: List[str] = Field(default_factory=list, description="建议列表")

# Agent创建响应
class AgentCreateResponse(BaseModel):
    """Agent创建响应"""
    agent: Agent = Field(..., description="创建的Agent信息")
    draft_config: AgentDraftConfig = Field(..., description="初始草稿配置")
    next_steps: List[str] = Field(..., description="下一步操作提示")

# 版本历史
class AgentVersionHistory(BaseModel):
    """Agent版本历史"""
    version_id: str = Field(..., description="版本ID")
    version_number: str = Field(..., description="版本号")
    release_notes: Optional[str] = Field(None, description="发布说明")
    published_at: datetime = Field(..., description="发布时间")
    is_current: bool = Field(..., description="是否为当前运行版本")
    config_summary: Dict[str, Any] = Field(..., description="配置摘要")
    
    class Config:
        from_attributes = True

# 回滚请求
class AgentRollbackRequest(BaseModel):
    """回滚请求"""
    reason: Optional[str] = Field(None, description="回滚原因")

# 发布响应
class AgentPublishResponse(BaseModel):
    """发布响应"""
    success: bool = Field(..., description="是否发布成功")
    version: AgentVersionHistory = Field(..., description="发布的版本信息")
    agent: Agent = Field(..., description="Agent信息")

# 回滚响应
class AgentRollbackResponse(BaseModel):
    """回滚响应"""
    success: bool = Field(..., description="是否回滚成功")
    current_version: str = Field(..., description="当前版本号")
    rollback_reason: Optional[str] = Field(None, description="回滚原因")
    new_draft_created: bool = Field(..., description="是否创建了新草稿")

# 模板列表响应
# 可用资源Schema定义
class AvailableModel(BaseModel):
    """可用模型信息"""
    id: str = Field(..., description="模型ID")
    name: str = Field(..., description="模型名称")
    key: str = Field(..., description="模型标识符")
    description: Optional[str] = Field(None, description="模型描述")
    model_type: str = Field(..., description="模型类型")
    
    class Config:
        from_attributes = True

class AvailableTool(BaseModel):
    """可用工具信息"""
    id: str = Field(..., description="工具ID")
    tenant_tool_id: str = Field(..., description="租户工具ID")
    name: str = Field(..., description="工具名称")
    key: str = Field(..., description="工具标识符")
    description: Optional[str] = Field(None, description="工具描述")
    config_schema: Optional[Dict[str, Any]] = Field(None, description="配置Schema")
    tenant_config: Optional[Dict[str, Any]] = Field(None, description="租户配置")
    
    class Config:
        from_attributes = True

class AvailableKnowledgeBase(BaseModel):
    """可用知识库信息"""
    id: str = Field(..., description="知识库ID")
    name: str = Field(..., description="知识库名称")
    description: Optional[str] = Field(None, description="知识库描述")
    document_count: int = Field(0, description="文档数量")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True

class AvailableMCPServer(BaseModel):
    """可用MCP服务器信息"""
    id: str = Field(..., description="MCP服务器ID")
    name: str = Field(..., description="服务器名称")
    endpoint: str = Field(..., description="服务器端点")
    transport_type: str = Field(..., description="传输类型")
    status: str = Field(..., description="服务器状态")
    last_heartbeat: Optional[datetime] = Field(None, description="最后心跳时间")
    
    class Config:
        from_attributes = True

class AvailableResources(BaseModel):
    """可用资源集合"""
    models: List[AvailableModel] = Field(default_factory=list, description="可用模型列表")
    tools: List[AvailableTool] = Field(default_factory=list, description="可用工具列表")
    knowledge_bases: List[AvailableKnowledgeBase] = Field(default_factory=list, description="可用知识库列表")
    mcp_servers: List[AvailableMCPServer] = Field(default_factory=list, description="可用MCP服务器列表")
    
    class Config:
        from_attributes = True

class AgentTemplatesResponse(BaseModel):
    """模板列表响应"""
    templates: List[AgentTemplate] = Field(..., description="模板列表")
    available_resources: AvailableResources = Field(..., description="可用资源")

# 版本历史响应
class AgentHistoryResponse(BaseModel):
    """版本历史响应"""
    versions: List[AgentVersionHistory] = Field(..., description="版本历史列表")

# Agent草稿更新请求
class AgentDraftUpdateRequest(BaseModel):
    """Agent草稿更新请求"""
    model_id: Optional[str] = Field(None, description="LLM模型ID")
    prompt: Optional[str] = Field(None, description="提示词")
    knowledge_base_ids: Optional[List[str]] = Field(None, description="知识库ID列表")
    tool_ids: Optional[List[str]] = Field(None, description="工具ID列表")
    mcp_server_ids: Optional[List[str]] = Field(None, description="MCP服务器ID列表")
    llm_model_config: Optional[AgentLLMModelConfig] = Field(None, description="LLM模型配置")
    knowledge_base_config: Optional[AgentKnowledgeBaseConfig] = Field(None, description="知识库配置")

    class Config:
        from_attributes = True

# Agent草稿更新响应
class AgentDraftUpdateResponse(BaseModel):
    """Agent草稿更新响应"""
    success: bool = Field(..., description="是否更新成功")
    has_changes: bool = Field(..., description="是否有未发布的更改")
    is_ready_to_publish: bool = Field(..., description="是否准备好发布")
    validation_errors: List[str] = Field(default_factory=list, description="配置验证错误")
    last_saved_at: datetime = Field(..., description="最后保存时间")

# 配置向导响应
class AgentConfigWizardResponse(BaseModel):
    """配置向导响应"""
    wizard_steps: List[Dict[str, Any]] = Field(..., description="向导步骤")
    current_progress: Dict[str, Any] = Field(..., description="当前进度")
    template_info: Dict[str, Any] = Field(..., description="模板信息")

