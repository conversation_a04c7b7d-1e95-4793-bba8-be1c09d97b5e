from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, validator

# 注册请求
class UserRegister(BaseModel):
    username: str = Field(..., description="用户名", min_length=3, max_length=64)
    email: EmailStr = Field(..., description="邮箱")
    password: str = Field(..., description="密码，至少6位，必须包含大小写字母和数字或特殊字符", min_length=6)
    display_name: Optional[str] = Field(None, description="显示名称")
    invitation_token: Optional[str] = Field(None, description="邀请令牌（可选）")
    
    @validator('username')
    def username_must_be_valid(cls, v):
        if not v.strip() or ' ' in v:
            raise ValueError('用户名不能包含空格且不能为空')
        return v
    
    @validator('password')
    def password_must_be_strong(cls, v):
        if len(v) < 6:
            raise ValueError('密码长度至少为6位')
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(not c.isalnum() for c in v)
        
        if not (has_upper and has_lower):
            raise ValueError('密码必须包含大小写字母')
            
        if not (has_digit or has_special):
            raise ValueError('密码必须包含数字或特殊字符')
            
        return v

# 登录请求
class UserLogin(BaseModel):
    username: Optional[str] = Field(None, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    password: str = Field(..., description="密码")
    
    @validator('email')
    def check_email_or_username(cls, v, values):
        # 如果email为空，确保username已提供且不为空
        if not v and not values.get('username'):
            raise ValueError('必须提供用户名或邮箱')
        return v

# 令牌模型
class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_at: datetime  # 令牌过期时间
    user_id: str  # 用户ID
    username: str  # 用户名

# 令牌数据
class TokenData(BaseModel):
    user_id: Optional[str] = None
    tenant_id: Optional[str] = None  # 当前选择的租户
    username: Optional[str] = None
    scopes: List[str] = []

# 创建新组织
class TenantCreate(BaseModel):
    name: str = Field(..., description="组织名称", min_length=2, max_length=64)
    description: Optional[str] = Field(None, description="组织描述")
    
    @validator('name')
    def name_must_be_valid(cls, v):
        if not v.strip():
            raise ValueError('组织名称不能为空')
        return v.strip()

# 邀请用户请求
class InviteUserRequest(BaseModel):
    email: EmailStr = Field(..., description="被邀请者邮箱")
    role_key: str = Field(..., description="角色键值")
    
    @validator('email')
    def email_must_be_valid(cls, v):
        if not v.strip():
            raise ValueError('邮箱不能为空')
        return v.lower().strip()

# 接受邀请请求
class AcceptInvitationRequest(BaseModel):
    invitation_token: str = Field(..., description="邀请令牌")

# ===== 响应模型 =====

# 租户信息
class TenantInfo(BaseModel):
    id: str = Field(..., description="租户ID")
    name: str = Field(..., description="租户名称")
    
    class Config:
        from_attributes = True

# 注册响应中的用户信息
class UserRegisterInfo(BaseModel):
    id: str = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: EmailStr = Field(..., description="邮箱")
    is_verified: bool = Field(..., description="是否已验证")
    
    class Config:
        from_attributes = True

# 注册响应中的个人租户信息
class PersonalTenantInfo(BaseModel):
    id: str = Field(..., description="租户ID")
    name: str = Field(..., description="租户名称")
    
    class Config:
        from_attributes = True

# 用户注册响应
class UserRegisterResponse(BaseModel):
    message: str = Field(..., description="响应消息")
    user: UserRegisterInfo = Field(..., description="用户信息")
    personal_tenant: PersonalTenantInfo = Field(..., description="个人租户信息")
    invitation_accepted: Optional[bool] = Field(None, description="是否处理了邀请")
    joined_tenant: Optional[TenantInfo] = Field(None, description="通过邀请加入的租户")

# 邀请信息
class InvitationInfo(BaseModel):
    id: str = Field(..., description="邀请ID")
    token: str = Field(..., description="邀请令牌")
    email: EmailStr = Field(..., description="被邀请者邮箱")
    role_key: str = Field(..., description="角色键值")
    expires_at: datetime = Field(..., description="过期时间")
    invitation_link: str = Field(..., description="邀请链接")
    tenant_info: Optional[TenantInfo] = Field(None, description="租户信息")
    inviter_name: Optional[str] = Field(None, description="邀请人姓名")
    status: str = Field(..., description="邀请状态")
    
    class Config:
        from_attributes = True

# 邀请用户响应
class InvitationResponse(BaseModel):
    message: str = Field(..., description="响应消息")
    invitation: InvitationInfo = Field(..., description="邀请信息")

# 接受邀请响应
class AcceptInvitationResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    tenant_id: str = Field(..., description="租户ID")

# 创建租户响应中的租户信息
class TenantCreateInfo(BaseModel):
    id: str = Field(..., description="租户ID")
    name: str = Field(..., description="租户名称")
    description: Optional[str] = Field(None, description="租户描述")
    
    class Config:
        from_attributes = True

# 创建租户响应
class TenantCreateResponse(BaseModel):
    message: str = Field(..., description="响应消息")
    tenant: TenantCreateInfo = Field(..., description="租户信息")

# 获取邀请详情响应
class InvitationDetailResponse(BaseModel):
    invitation: InvitationInfo = Field(..., description="邀请详情")
    tenant_name: str = Field(..., description="租户名称")
    tenant_description: Optional[str] = Field(None, description="租户描述")
    inviter_name: str = Field(..., description="邀请人姓名")
    is_expired: bool = Field(..., description="是否已过期") 