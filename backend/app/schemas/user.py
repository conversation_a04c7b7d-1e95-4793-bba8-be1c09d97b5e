from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator, EmailStr

from .tenant import Tenant
from .role import Role

def validate_password_strength(password: str) -> str:
    """验证密码强度的通用函数"""
    if len(password) < 6:
        raise ValueError('密码长度至少为6位')
    
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(not c.isalnum() for c in password)
    
    if not (has_upper and has_lower):
        raise ValueError('密码必须包含大小写字母')
        
    if not (has_digit or has_special):
        raise ValueError('密码必须包含数字或特殊字符')
        
    return password

# 基础用户Schema
class UserBase(BaseModel):
    username: str = Field(..., description="用户名", max_length=64)
    email: EmailStr = Field(..., description="邮箱")
    display_name: Optional[str] = Field(None, description="显示名称", max_length=64)
    
    class Config:
        from_attributes = True

# 创建用户
class UserCreate(UserBase):
    password: str = Field(..., description="密码，至少6位，必须包含大小写字母和数字或特殊字符", min_length=6)
    is_active: Optional[bool] = Field(True, description="是否激活")
    extra: Optional[Dict[str, Any]] = Field(None, description="额外信息")
    
    @validator('username')
    def username_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('用户名不能为空')
        return v.strip()
    
    @validator('email')
    def email_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('邮箱不能为空')
        return v.strip()
    
    @validator('password')
    def password_must_be_strong(cls, v):
        return validate_password_strength(v)

# 更新用户
class UserUpdate(BaseModel):
    username: Optional[str] = Field(None, description="用户名", max_length=64)
    email: Optional[EmailStr] = Field(None, description="邮箱")
    display_name: Optional[str] = Field(None, description="显示名称", max_length=64)
    is_active: Optional[bool] = Field(None, description="是否激活")
    extra: Optional[Dict[str, Any]] = Field(None, description="额外信息")
    
    class Config:
        from_attributes = True

# 密码重置
class PasswordReset(BaseModel):
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., description="新密码，至少6位，必须包含大小写字母和数字或特殊字符", min_length=6)
    
    @validator('new_password')
    def new_password_must_be_strong(cls, v):
        return validate_password_strength(v)

# 响应模型
class User(UserBase):
    id: str
    is_active: bool
    last_login: Optional[datetime]
    created_at: datetime
    extra: Optional[Dict[str, Any]]
    
    class Config:
        from_attributes = True

# 带角色信息的用户（用于租户用户列表和详情）
class UserWithRole(User):
    role_key: str = Field(..., description="在当前租户中的角色标识")
    
    class Config:
        from_attributes = True

# 用户-租户-角色关联
class UserTenantRole(BaseModel):
    user_id: str
    tenant_id: str
    role_key: str
    tenant: Optional[Tenant]
    role: Optional[Role]
    
    class Config:
        from_attributes = True

# 用户详情
class UserDetail(User):
    tenants: List[UserTenantRole]
    
    class Config:
        from_attributes = True

# 用户租户关联创建
class UserTenantAssociationCreate(BaseModel):
    user_id: str = Field(..., description="用户ID")
    tenant_id: str = Field(..., description="租户ID")
    role_key: str = Field(..., description="角色标识")

# 用户租户关联更新
class UserTenantAssociationUpdate(BaseModel):
    role_key: str = Field(..., description="角色标识")

# 通用操作响应
class OperationResponse(BaseModel):
    success: bool
    message: str

# 用户租户关联响应
class UserTenantAssociationResponse(BaseModel):
    user_id: str
    tenant_id: str
    role_key: str
    success: bool
    message: str

# 分页列表响应
class PaginatedResponse(BaseModel):
    total: int
    items: List[Any]

# 用户列表响应
class UserListResponse(PaginatedResponse):
    items: List[User]

# 租户用户列表响应（包含角色信息）
class UserWithRoleListResponse(PaginatedResponse):
    items: List[UserWithRole] 