from datetime import datetime
from typing import List, Optional, Dict, Any, Union, Type
from pydantic import BaseModel, Field, field_validator
from enum import Enum

# 枚举定义
class ProviderType(str, Enum):
    """提供商类型"""
    BUILTIN = "BUILTIN"                 # 内置提供商（OpenAI、<PERSON>、Gemini等）
    CUSTOM_OPENAI = "CUSTOM_OPENAI"     # 自定义OpenAI兼容提供商
    
    @property
    def description(self) -> str:
        """返回枚举值的中文描述"""
        descriptions = {
            self.BUILTIN: "内置提供商",
            self.CUSTOM_OPENAI: "自定义OpenAI兼容"
        }
        return descriptions.get(self, "未知类型")

class AuthType(str, Enum):
    """认证类型"""
    API_KEY = "API_KEY"                 # API密钥认证
    BEARER_TOKEN = "BEARER_TOKEN"       # Bearer Token认证
    BASIC_AUTH = "BASIC_AUTH"          # 基础认证
    OAUTH = "OAUTH"                    # OAuth认证
    
    @property
    def description(self) -> str:
        """返回枚举值的中文描述"""
        descriptions = {
            self.API_KEY: "API密钥",
            self.BEARER_TOKEN: "Bearer令牌",
            self.BASIC_AUTH: "基础认证",
            self.OAUTH: "OAuth认证"
        }
        return descriptions.get(self, "未知认证")

class ProviderStatus(str, Enum):
    """提供商状态"""
    ENABLED = "enabled"                 # 已启用
    DISABLED = "disabled"               # 已禁用
    ERROR = "error"                     # 错误状态
    CONFIGURING = "configuring"         # 配置中
    
    @property
    def description(self) -> str:
        """返回枚举值的中文描述"""
        descriptions = {
            self.ENABLED: "已启用",
            self.DISABLED: "已禁用", 
            self.ERROR: "错误状态",
            self.CONFIGURING: "配置中"
        }
        return descriptions.get(self, "未知状态")

# Provider（提供商）相关Schema
class ProviderBase(BaseModel):
    name: str = Field(..., description="提供商名称", max_length=64)
    key: str = Field(..., description="提供商标识", max_length=64)
    provider_type: ProviderType = Field(ProviderType.BUILTIN, description="提供商类型")
    description: Optional[str] = Field(None, description="提供商描述")
    icon: Optional[str] = Field(None, description="图标URL", max_length=128)
    website: Optional[str] = Field(None, description="官网", max_length=256)
    api_base: Optional[str] = Field(None, description="API基础URL", max_length=256)
    api_compatible: Optional[str] = Field(None, description="API兼容类型", max_length=64)
    auth_type: AuthType = Field(AuthType.API_KEY, description="认证类型")
    config_schema: Optional[Dict[str, Any]] = Field(None, description="配置架构")
    is_active: Optional[bool] = Field(True, description="是否激活")
    
    model_config = {"from_attributes": True}

class ProviderCreate(ProviderBase):
    @field_validator('name')
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('提供商名称不能为空')
        return v.strip()
    
    @field_validator('key')
    def key_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('提供商标识不能为空')
        return v.strip()
    
    @field_validator('api_base')
    def validate_api_base_for_custom(cls, v, info):
        """自定义提供商必须提供API基础URL"""
        if hasattr(info, 'data') and info.data.get('provider_type') == ProviderType.CUSTOM_OPENAI:
            if not v or not v.strip():
                raise ValueError('自定义OpenAI兼容提供商必须提供API基础URL')
            if not v.startswith(('http://', 'https://')):
                raise ValueError('API基础URL必须以http://或https://开头')
            return v.strip().rstrip('/')
        return v.strip().rstrip('/') if v else v

class ProviderUpdate(BaseModel):
    name: Optional[str] = Field(None, description="提供商名称", max_length=64)
    provider_type: Optional[ProviderType] = Field(None, description="提供商类型")
    description: Optional[str] = Field(None, description="提供商描述")
    icon: Optional[str] = Field(None, description="图标URL", max_length=128)
    website: Optional[str] = Field(None, description="官网", max_length=256)
    api_base: Optional[str] = Field(None, description="API基础URL", max_length=256)
    api_compatible: Optional[str] = Field(None, description="API兼容类型", max_length=64)
    auth_type: Optional[AuthType] = Field(None, description="认证类型")
    config_schema: Optional[Dict[str, Any]] = Field(None, description="配置架构")
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    model_config = {"from_attributes": True}

class Provider(ProviderBase):
    id: str
    provider_type: ProviderType
    auth_type: AuthType
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    @property
    def is_builtin(self) -> bool:
        """判断是否为内置提供商"""
        return self.provider_type == ProviderType.BUILTIN
    
    @property
    def is_custom_openai(self) -> bool:
        """判断是否为自定义OpenAI兼容提供商"""
        return self.provider_type == ProviderType.CUSTOM_OPENAI
    
    model_config = {"from_attributes": True}

# 用于Provider引用的简化Schema
class ProviderReference(BaseModel):
    id: str
    name: str
    key: str


# AccessCredential（访问凭证）相关Schema
class AccessCredentialBase(BaseModel):
    tenant_id: str = Field(..., description="租户ID")
    provider_id: str = Field(..., description="提供商ID")
    name: str = Field(..., description="凭证名称", max_length=64)
    credentials: Dict[str, Any] = Field(..., description="凭证信息")
    
    model_config = {"from_attributes": True}

class AccessCredentialCreate(AccessCredentialBase):
    @field_validator('name')
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('凭证名称不能为空')
        return v.strip()
    
    @field_validator('credentials')
    def credentials_not_empty(cls, v):
        if not v or not isinstance(v, dict):
            raise ValueError('凭证信息不能为空且必须是字典格式')
        return v

class AccessCredentialUpdate(BaseModel):
    name: Optional[str] = Field(None, description="凭证名称", max_length=64)
    credentials: Optional[Dict[str, Any]] = Field(None, description="凭证信息")
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    model_config = {"from_attributes": True}

class AccessCredential(AccessCredentialBase):
    id: str
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}

# 列表响应Schema
class ProviderListResponse(BaseModel):
    total: int
    items: List[Provider]

class AccessCredentialListResponse(BaseModel):
    total: int
    items: List[AccessCredential]

class ProviderEnableRequest(BaseModel):
    """一键启用提供商请求"""
    provider_id: str = Field(..., description="提供商ID")
    name: str = Field(..., description="凭证名称", max_length=64)
    credentials: Dict[str, Any] = Field(..., description="凭证信息")
    auto_sync: bool = Field(True, description="是否自动同步模型")
    
    model_config = {"from_attributes": True}
    
    @field_validator('name')
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('凭证名称不能为空')
        return v.strip()
    
    @field_validator('credentials')
    def credentials_not_empty(cls, v):
        if not v or not isinstance(v, dict):
            raise ValueError('凭证信息不能为空且必须是字典格式')
        return v

class ProviderTestResult(BaseModel):
    """提供商测试结果"""
    success: bool
    message: str
    provider: ProviderReference
    credential_id: Optional[str] = None
    models_available: Optional[int] = None
    error_details: Optional[str] = None
    
    model_config = {"from_attributes": True}

class ProviderResponse(BaseModel):
    """提供商响应信息"""
    id: str
    name: str
    key: str
    provider_type: ProviderType
    auth_type: AuthType
    description: Optional[str] = None
    icon: Optional[str] = None
    website: Optional[str] = None
    api_base: Optional[str] = None
    status: ProviderStatus = ProviderStatus.DISABLED
    model_count: int = 0
    last_sync: Optional[datetime] = None
    config: Optional[Dict[str, Any]] = None
    is_active: bool = True
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}
    
    @property
    def is_builtin(self) -> bool:
        """判断是否为内置提供商"""
        return self.provider_type == ProviderType.BUILTIN
    
    @property
    def is_custom_openai(self) -> bool:
        """判断是否为自定义OpenAI兼容提供商"""
        return self.provider_type == ProviderType.CUSTOM_OPENAI

class AvailableProviderResponse(BaseModel):
    """可用提供商响应信息"""
    id: str
    name: str
    key: str
    provider_type: ProviderType
    auth_type: AuthType
    description: Optional[str] = None
    icon: Optional[str] = None
    website: Optional[str] = None
    is_enabled: bool = False
    credential_id: Optional[str] = None
    credential_name: Optional[str] = None 
    
    model_config = {"from_attributes": True}


# ============= API 返回对象定义 =============

class ProviderBasicInfo(BaseModel):
    """提供商基本信息"""
    id: str
    name: str
    key: str
    provider_type: str
    auth_type: str
    description: Optional[str] = None
    website: Optional[str] = None
    icon: Optional[str] = None
    api_base: Optional[str] = None
    config_schema: Optional[Dict[str, Any]] = None

class AvailableProviderItem(ProviderBasicInfo):
    """可用提供商项目"""
    pass

class AvailableProvidersResponse(BaseModel):
    """获取可用提供商列表响应"""
    total: int = 0
    items: List[AvailableProviderItem]
    

class EnabledProviderItem(BaseModel):
    """已启用提供商项目"""
    credential_id: str
    credential_name: str
    provider: ProviderBasicInfo
    model_count: int
    credentials: Dict[str, Any]
    created_at: datetime
    updated_at: Optional[datetime] = None

class EnabledProvidersResponse(BaseModel):
    """获取已启用提供商列表响应"""
    total: int = 0
    items: List[EnabledProviderItem]

    
class EnableProviderResponse(BaseModel):
    """启用提供商响应"""
    success: bool
    message: str
    credential_id: str
    credential_name: str
    provider: ProviderBasicInfo
    models_discovered: int

class DisableProviderResponse(BaseModel):
    """禁用提供商响应"""
    success: bool
    message: str
    disabled_models: int

class TestProviderConfigResponse(BaseModel):
    """测试提供商配置响应"""
    success: bool
    message: str
    provider: Optional[ProviderBasicInfo] = None
    models_available: Optional[int] = None
    models: Optional[List[Dict[str, Any]]] = None
    error_details: Optional[str] = None

class ModelInfo(BaseModel):
    """模型信息"""
    id: str
    key: str
    name: str
    description: Optional[str] = None
    model_type: Optional[str] = None

class ProviderModelsResponse(BaseModel):
    """提供商模型列表响应"""
    models: List[ModelInfo]
    total: int = 0
    

# ============= 提供商配置请求模型 =============

class ProviderConfigRequest(BaseModel):
    """提供商配置请求基类"""
    name: str = Field(..., description="配置名称", max_length=64, min_length=1)
    
    @field_validator('name')
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('配置名称不能为空')
        return v.strip()


# ============= 通用的API请求模型 =============

class GenericProviderConfigRequest(BaseModel):
    """通用的提供商配置请求（支持动态类型）"""
    name: str = Field(..., description="配置名称", max_length=64, min_length=1)
    credentials: Dict[str, Any] = Field(..., description="凭证信息")
    
    @field_validator('name')
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('配置名称不能为空')
        return v.strip()
    

class GenericProviderTestRequest(BaseModel):
    """通用的提供商测试请求（支持动态类型）"""
    provider_id: str = Field(..., description="提供商ID")
    credentials: Dict[str, Any] = Field(..., description="凭证信息")



# ============= 编辑提供商配置相关模型 =============

class EditProviderConfigRequest(BaseModel):
    """编辑提供商配置请求"""
    name: Optional[str] = Field(None, description="配置名称", max_length=64, min_length=1)
    credentials: Optional[Dict[str, Any]] = Field(None, description="凭证信息")
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    @field_validator('name')
    def name_not_empty_if_provided(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('配置名称不能为空')
        return v.strip() if v else v

class EditProviderConfigResponse(BaseModel):
    """编辑提供商配置响应"""
    success: bool
    message: str
    credential_id: str
    credential_name: str
    provider: ProviderBasicInfo
    updated_fields: List[str] = Field(default_factory=list, description="更新的字段列表")
    validation_performed: bool = Field(False, description="是否执行了配置验证")
    models_resynced: Optional[int] = Field(None, description="重新同步的模型数量") 