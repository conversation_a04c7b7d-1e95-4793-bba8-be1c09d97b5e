"""
任务队列的Pydantic模型
"""
from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from app.models.task import TaskStatus, TaskType


class TaskBase(BaseModel):
    """任务基础模型"""
    tenant_id: str
    task_type: TaskType
    status: TaskStatus
    params: Dict[str, Any] = {}
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    priority: int = 10


class TaskCreate(BaseModel):
    """创建任务请求模型"""
    task_type: TaskType
    params: Dict[str, Any] = Field(..., description="任务参数")
    priority: Optional[int] = Field(None, description="任务优先级 (数字越小优先级越高)")
    max_retries: Optional[int] = Field(None, description="最大重试次数")


class TaskStatusUpdate(BaseModel):
    """更新任务状态请求模型"""
    action: str = Field(..., description="操作类型: 'cancel' 或 'retry'")


class TaskReindexKB(BaseModel):
    """重新索引知识库请求模型"""
    chunk_size: int = Field(1000, description="文本块大小")
    chunk_overlap: int = Field(200, description="文本块重叠大小")
    priority: Optional[int] = Field(None, description="任务优先级 (数字越小优先级越高)")
    max_retries: Optional[int] = Field(None, description="最大重试次数")


class TaskResponse(BaseModel):
    """任务响应模型"""
    id: str
    tenant_id: str
    task_type: str
    status: str
    params: Dict[str, Any]
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    retry_count: int
    max_retries: int
    priority: int
    created_at: str
    updated_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None


class TaskListResponse(BaseModel):
    """任务列表响应模型"""
    items: List[TaskResponse]
    total: int
    skip: int
    limit: int 