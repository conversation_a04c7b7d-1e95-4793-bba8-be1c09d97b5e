from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator
# 导入Provider相关枚举和Schema
from app.schemas.provider import ProviderReference

# Model（模型）相关Schema
class ModelBase(BaseModel):
    name: str = Field(..., description="模型名称", max_length=64)
    key: str = Field(..., description="模型标识", max_length=64)
    model_type: str = Field(..., description="模型类型", max_length=32)
    provider_access_credential_id: str = Field(..., description="提供商访问凭证ID")
    extra_config: Optional[Dict[str, Any]] = Field(None, description="模型额外配置")
    
    model_config = {"from_attributes": True}

class ModelCreate(ModelBase):
    @field_validator('name')
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('模型名称不能为空')
        return v.strip()
    
    @field_validator('key')
    def key_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('模型标识不能为空')
        return v.strip()
    
    @field_validator('model_type')
    def model_type_valid(cls, v):
        valid_types = ["llm", "embedding", "image", "audio", "vision", "multimodal", "rerank"]
        if v not in valid_types:
            raise ValueError(f'模型类型必须是以下之一: {", ".join(valid_types)}')
        return v

class ModelUpdate(BaseModel):
    name: Optional[str] = Field(None, description="模型名称", max_length=64)
    key: Optional[str] = Field(None, description="模型标识", max_length=64)
    model_type: Optional[str] = Field(None, description="模型类型", max_length=32)
    provider_access_credential_id: Optional[str] = Field(None, description="提供商访问凭证ID")
    extra_config: Optional[Dict[str, Any]] = Field(None, description="模型额外配置")
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    model_config = {"from_attributes": True}

class Model(BaseModel):
    id: str
    tenant_id: str
    provider_id: str
    provider_access_credential_id: str
    name: str
    key: str
    model_type: str
    description: Optional[str] = None
    is_active: bool = True
    extra_config: Optional[Dict[str, Any]] = None  # 模型额外配置
    # provider: Optional[ProviderReference] = None
    
    model_config = {"from_attributes": True}


# 列表响应
class ModelListResponse(BaseModel):
    total: int
    items: List[Model]


# 模型类型
class ModelType(BaseModel):
    key: str = Field(..., description="模型类型标识")
    name: str = Field(..., description="模型类型名称")
    description: Optional[str] = Field(None, description="模型类型描述")

# 模型类型列表响应
class ModelTypeListResponse(BaseModel):
    items: List[ModelType]

