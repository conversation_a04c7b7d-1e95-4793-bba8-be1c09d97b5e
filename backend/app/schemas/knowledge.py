from typing import Optional, List, Dict, Any, Union, Literal
from datetime import datetime
from pydantic import BaseModel, Field, model_validator, field_validator
import logging

logger = logging.getLogger(__name__)

# 新增：Rerank模型配置
class RerankModelConfig(BaseModel):
    """Rerank模型配置"""
    enabled: bool = Field(False, description="是否启用Rerank模型")
    model_name: Optional[str] = Field(None, description="Rerank模型名称")
    model_id: Optional[str] = Field(None, description="Rerank模型ID")
    
    @field_validator('model_name', 'model_id')
    @classmethod
    def validate_model_info(cls, v, info):
        """验证模型信息：如果启用了Rerank，则模型名称和ID不能为空"""
        if info.data.get('enabled', False) and not v:
            raise ValueError("启用Rerank时，模型名称和ID不能为空")
        return v

# 新增：Embedding模型配置
class EmbeddingConfig(BaseModel):
    """Embedding模型配置"""
    model_name: str = Field(..., description="Embedding模型名称")
    model_id: str = Field(..., description="Embedding模型ID")

# 新增：向量检索配置
class VectorSearchConfig(BaseModel):
    """向量检索配置"""
    top_k: int = Field(5, description="返回结果数量", ge=1, le=100)
    score_threshold: float = Field(0.5, description="分数阈值", ge=0.0, le=1.0)
    rerank: Optional[RerankModelConfig] = Field(None, description="Rerank模型配置")

# 新增：关键词检索配置
class KeywordSearchConfig(BaseModel):
    """关键词检索配置"""
    top_k: int = Field(5, description="返回结果数量", ge=1, le=100)
    score_threshold: float = Field(0.5, description="分数阈值", ge=0.0, le=1.0)
    rerank: Optional[RerankModelConfig] = Field(None, description="Rerank模型配置")

# 新增：混合检索配置
class HybridSearchConfig(BaseModel):
    """混合检索配置"""
    top_k: int = Field(5, description="返回结果数量", ge=1, le=100)
    score_threshold: float = Field(0.5, description="分数阈值", ge=0.0, le=1.0)
    rerank: Optional[RerankModelConfig] = Field(None, description="Rerank模型配置")
    vector_weight: float = Field(0.7, description="向量检索权重", ge=0.0, le=1.0)
    keyword_weight: float = Field(0.3, description="关键词检索权重", ge=0.0, le=1.0)
    
    @field_validator('keyword_weight')
    @classmethod
    def validate_weights(cls, v, info):
        """验证权重总和为1"""
        vector_weight = info.data.get('vector_weight', 0.7)
        if abs(vector_weight + v - 1.0) > 0.01:  # 允许0.01的误差
            raise ValueError("向量检索权重和关键词检索权重之和必须为1")
        return v

# 新增：检索配置
class RetrievalConfig(BaseModel):
    """检索配置"""
    default_search_type: Literal["vector", "keyword", "hybrid"] = Field(
        "keyword", description="默认检索类型：向量检索、关键词检索、混合检索"
    )
    vector_search: Optional[VectorSearchConfig] = Field(None, description="向量检索配置")
    keyword_search: Optional[KeywordSearchConfig] = Field(None, description="关键词检索配置")
    hybrid_search: Optional[HybridSearchConfig] = Field(None, description="混合检索配置")
    
    @model_validator(mode='after')
    def validate_search_config(self):
        """验证检索配置：确保默认检索类型对应的配置存在"""
        search_type = self.default_search_type
        if search_type == "vector" and not self.vector_search:
            self.vector_search = VectorSearchConfig()
        elif search_type == "keyword" and not self.keyword_search:
            self.keyword_search = KeywordSearchConfig()
        elif search_type == "hybrid" and not self.hybrid_search:
            self.hybrid_search = HybridSearchConfig()
        return self

# 新增：知识库完整配置
class KnowledgeBaseFullConfig(BaseModel):
    """知识库完整配置"""
    embedding: EmbeddingConfig = Field(..., description="Embedding模型配置")
    retrieval: Optional[RetrievalConfig] = Field(None, description="检索配置")
    chunk_size: int = Field(1000, description="分块大小", ge=100, le=10000)
    chunk_overlap: int = Field(200, description="分块重叠大小", ge=0, le=5000)
    
    @field_validator('chunk_overlap')
    @classmethod
    def validate_chunk_overlap(cls, v, info):
        """验证分块重叠大小不能大于分块大小"""
        chunk_size = info.data.get('chunk_size', 1000)
        if v >= chunk_size:
            raise ValueError("分块重叠大小必须小于分块大小")
        return v


# KnowledgeBase基础模型
class KnowledgeBaseBase(BaseModel):
    name: str = Field(..., description="知识库名称", max_length=64)
    description: Optional[str] = Field(None, description="知识库描述")
    config: Optional[Dict[str, Any]] = Field(None, description="知识库配置")


# 创建知识库请求
class KnowledgeBaseCreate(BaseModel):
    tenant_id: str = Field(..., description="租户ID")
    name: str = Field(..., description="知识库名称", max_length=64)
    description: Optional[str] = Field(None, description="知识库描述")
    config: Optional[KnowledgeBaseFullConfig] = Field(None, description="知识库配置，包含embedding和retrieval等设置")


# 更新知识库请求
class KnowledgeBaseUpdate(BaseModel):
    name: Optional[str] = Field(None, description="知识库名称", max_length=64)
    description: Optional[str] = Field(None, description="知识库描述")
    is_active: Optional[bool] = Field(None, description="是否激活")
    config: Optional[KnowledgeBaseFullConfig] = Field(None, description="知识库配置，包含embedding和retrieval等设置")


# 知识库配置更新请求
class KnowledgeBaseConfigUpdate(BaseModel):
    """知识库配置更新请求"""
    embedding: Optional[EmbeddingConfig] = Field(None, description="Embedding模型配置")
    retrieval: Optional[RetrievalConfig] = Field(None, description="检索配置")
    chunk_size: Optional[int] = Field(None, description="分块大小", ge=100, le=10000)
    chunk_overlap: Optional[int] = Field(None, description="分块重叠大小", ge=0, le=5000)
    
    @field_validator('chunk_overlap')
    @classmethod
    def validate_chunk_overlap(cls, v, info):
        """验证分块重叠大小不能大于分块大小"""
        if v is not None and 'chunk_size' in info.data and info.data['chunk_size'] is not None:
            if v >= info.data['chunk_size']:
                raise ValueError("分块重叠大小必须小于分块大小")
        return v


# 知识库响应模型
class KnowledgeBaseResponse(KnowledgeBaseBase):
    id: str = Field(..., description="知识库ID")
    tenant_id: str = Field(..., description="租户ID")
    is_active: bool = Field(..., description="是否激活")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        orm_mode = True


# KnowledgeFile基础模型
class KnowledgeFileBase(BaseModel):
    file_name: str = Field(..., description="文件名称", max_length=256)
    file_type: Optional[str] = Field(None, description="文件类型")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")


# 创建知识文件请求
class KnowledgeFileCreate(KnowledgeFileBase):
    knowledge_base_id: str = Field(..., description="知识库ID")
    file_path: str = Field(..., description="文件路径", max_length=512)


# 文件上传响应
class FileUploadResponse(BaseModel):
    file_path: str = Field(..., description="文件S3路径")
    file_name: str = Field(..., description="文件名称")
    file_size: int = Field(..., description="文件大小")
    file_type: str = Field(..., description="文件类型")
    knowledge_base_id: str = Field(..., description="知识库ID")
    meta_data: Dict[str, Any] = Field(..., description="文件元数据")


# 更新知识文件请求
class KnowledgeFileUpdate(BaseModel):
    file_name: Optional[str] = Field(None, description="文件名称", max_length=256)
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")
    status: Optional[str] = Field(None, description="文件状态")


# 知识文件响应模型
class KnowledgeFileResponse(KnowledgeFileBase):
    id: str = Field(..., description="文件ID")
    knowledge_base_id: str = Field(..., description="知识库ID")
    file_path: str = Field(..., description="文件路径")
    status: str = Field(..., description="文件状态")
    chunk_count: int = Field(..., description="分块数量")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        orm_mode = True


# 知识文件URL响应
class KnowledgeFileUrlResponse(BaseModel):
    id: str = Field(..., description="文件ID")
    file_name: str = Field(..., description="文件名称")
    download_url: str = Field(..., description="文件下载URL")
    expires_at: datetime = Field(..., description="URL过期时间")


# 知识块基础模型
class KnowledgeChunkBase(BaseModel):
    content: str = Field(..., description="分块内容")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")
    embedding: Optional[List[float]] = Field(None, description="向量嵌入")


# 创建知识块请求
class KnowledgeChunkCreate(KnowledgeChunkBase):
    id: str = Field(..., description="分块ID")
    file_id: str = Field(..., description="文件ID")


# 批量创建知识块请求
class KnowledgeChunkBatchCreate(BaseModel):
    chunks: List[KnowledgeChunkCreate] = Field(..., description="知识块列表")


# 知识块响应模型
class KnowledgeChunkResponse(KnowledgeChunkBase):
    id: str = Field(..., description="分块ID")
    file_id: str = Field(..., description="文件ID")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        orm_mode = True


# 知识库版本发布请求
class KnowledgeBaseVersionCreate(BaseModel):
    knowledge_base_id: str = Field(..., description="知识库ID")
    version_name: str = Field(..., description="版本名称")
    description: Optional[str] = Field(None, description="版本描述")


# 知识库版本响应
class KnowledgeBaseVersionResponse(BaseModel):
    id: str = Field(..., description="版本ID")
    knowledge_base_id: str = Field(..., description="知识库ID")
    version_name: str = Field(..., description="版本名称")
    description: Optional[str] = Field(None, description="版本描述")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        orm_mode = True


# 知识库搜索请求
class KnowledgeSearchParams(BaseModel):
    top_k: int = Field(5, description="返回结果数量", ge=1, le=100)
    score_threshold: float = Field(0.5, description="分数阈值", ge=0.0, le=1.0)
    rerank: Optional[RerankModelConfig] = Field(None, description="Rerank模型配置")


class KnowledgeSearchCreate(BaseModel):
    knowledge_base_id: str = Field(..., description="知识库ID")
    query: str = Field(..., description="搜索查询")
    session_id: Optional[str] = Field(None, description="会话ID")
    search_params: Optional[Dict[str, Any]] = Field(None, description="搜索参数")



# 知识库搜索响应
class KnowledgeSearchResponse(BaseModel):
    id: str = Field(..., description="搜索ID")
    knowledge_base_id: str = Field(..., description="知识库ID")
    query: str = Field(..., description="搜索查询")
    session_id: Optional[str] = Field(None, description="会话ID")
    results: Optional[List[Dict[str, Any]]] = Field(None, description="搜索结果")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        orm_mode = True


# 分页响应包装
class PageResponse(BaseModel):
    items: List[Any] = Field(..., description="列表项")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")


# 知识库列表响应
class KnowledgeBaseListResponse(PageResponse):
    items: List[KnowledgeBaseResponse]


# 知识文件列表响应
class KnowledgeFileListResponse(PageResponse):
    items: List[KnowledgeFileResponse]


# 知识库版本列表响应
class KnowledgeBaseVersionListResponse(PageResponse):
    items: List[KnowledgeBaseVersionResponse]


# Agent使用信息
class AgentUsageInfo(BaseModel):
    agent_id: str = Field(..., description="Agent ID")
    agent_name: str = Field(..., description="Agent名称")
    agent_type: Optional[str] = Field(None, description="Agent类型")
    description: Optional[str] = Field(None, description="Agent描述")
    is_active: bool = Field(..., description="Agent是否激活")
    version_number: str = Field(..., description="使用该知识库的版本号")
    version_id: str = Field(..., description="版本ID")
    is_current_version: bool = Field(..., description="是否为当前版本")
    created_at: datetime = Field(..., description="Agent创建时间")
    updated_at: datetime = Field(..., description="Agent更新时间")


# 知识库使用情况响应
class KnowledgeBaseUsageResponse(BaseModel):
    knowledge_base_id: str = Field(..., description="知识库ID")
    knowledge_base_name: str = Field(..., description="知识库名称")
    total_agents: int = Field(..., description="使用该知识库的Agent总数")
    active_agents: int = Field(..., description="激活状态的Agent数量")
    agents: List[AgentUsageInfo] = Field(..., description="使用该知识库的Agent列表")
    
    class Config:
        orm_mode = True 