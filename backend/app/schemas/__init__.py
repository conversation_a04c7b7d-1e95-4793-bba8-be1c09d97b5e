"""
Schema层模块
"""

# Provider相关schema
from .provider import (
    ProviderType, AuthType, ProviderStatus,
    ProviderBase, ProviderCreate, ProviderUpdate, Provider, ProviderReference,
    AccessCredentialBase, AccessCredentialCreate, AccessCredentialUpdate, AccessCredential,
    ProviderListResponse, AccessCredentialListResponse,
    ProviderEnableRequest,
    ProviderTestResult, ProviderResponse, AvailableProviderResponse,
    # API 返回对象
    ProviderBasicInfo, AvailableProviderItem, AvailableProvidersResponse,
    EnabledProviderItem, EnabledProvidersResponse, EnableProviderResponse,
    DisableProviderResponse, TestProviderConfigResponse, ModelInfo, ProviderModelsResponse
)

# Model相关schema
from .model import (
    ModelBase, ModelCreate, ModelUpdate, Model,
    ModelListResponse
)

# 其他schema
from .user import *
from .tenant import *
from .auth import *
from .agent import *
from .tool import *
from .knowledge import *
from .task import *
from .role import *

__all__ = [
    # Provider相关
    "ProviderType", "AuthType", "ProviderStatus",
    "ProviderBase", "ProviderCreate", "ProviderUpdate", "Provider", "ProviderReference",
    "AccessCredentialBase", "AccessCredentialCreate", "AccessCredentialUpdate", "AccessCredential",
    "ProviderListResponse", "AccessCredentialListResponse",
    "ProviderEnableRequest",
    "ProviderTestResult", "ProviderResponse", "AvailableProviderResponse",
    
    # Model相关
    "ModelBase", "ModelCreate", "ModelUpdate", "Model",
    "ModelListResponse"
]
