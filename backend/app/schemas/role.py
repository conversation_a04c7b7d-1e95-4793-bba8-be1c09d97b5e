from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

from app.core.roles import TenantRole, get_role_name, get_role_description

# 角色信息（基于常量的角色）
class Role(BaseModel):
    key: str = Field(..., description="角色标识")
    name: str = Field(..., description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    
    class Config:
        from_attributes = True

# 角色详情（带权限信息）
class RoleWithPermissions(Role):
    permissions: Dict[str, bool] = Field({}, description="权限列表")
    
    class Config:
        from_attributes = True

# 角色详情（带用户数量）
class RoleWithUserCount(Role):
    user_count: int = Field(0, description="用户数量")
    
    class Config:
        from_attributes = True

# 通用操作响应
class RoleOperationResponse(BaseModel):
    success: bool
    message: str

# 角色列表响应
class RoleListResponse(BaseModel):
    total: int
    items: List[Role] 