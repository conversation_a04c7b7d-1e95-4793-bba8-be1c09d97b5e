from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator

# 基础租户Schema
class TenantBase(BaseModel):
    name: str = Field(..., description="租户名称", max_length=64)
    description: Optional[str] = Field(None, description="租户描述")
    
    class Config:
        from_attributes = True

# 创建租户
class TenantCreate(TenantBase):
    @validator('name')
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('租户名称不能为空')
        return v.strip()

# 更新租户
class TenantUpdate(BaseModel):
    name: Optional[str] = Field(None, description="租户名称", max_length=64)
    description: Optional[str] = Field(None, description="租户描述")
    
    class Config:
        from_attributes = True

# 响应模型
class Tenant(TenantBase):
    id: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 带统计信息的租户
class TenantWithStats(Tenant):
    user_count: int = Field(0, description="用户数量")
    agent_count: int = Field(0, description="Agent数量")
    
    class Config:
        from_attributes = True

# 列表响应
class TenantListResponse(BaseModel):
    total: int
    items: List[Tenant] 