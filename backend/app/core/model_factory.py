from typing import Dict, Any, Optional, List
import logging
import time
import threading
from datetime import datetime
from collections import OrderedDict, deque
from langchain_core.language_models import BaseChatModel
from langchain.embeddings.base import Embeddings
from sqlalchemy.orm import Session
from langchain_openai import ChatOpenAI
import sys
import statistics

from app.models.model import Model, Provider, ProviderAccessCredential

logger = logging.getLogger(__name__)

class CacheEntry:
    """模型缓存条目，包含模型实例和元数据"""
    
    def __init__(self, value: Any, ttl_seconds: int = 3600):
        self.value = value
        self.created_at = time.time()
        self.last_accessed = time.time()
        self.ttl_seconds = ttl_seconds
        self.access_count = 0
    
    def is_expired(self) -> bool:
        """检查缓存条目是否已过期"""
        return time.time() > self.created_at + self.ttl_seconds
    
    def access(self) -> Any:
        """访问缓存条目，更新访问时间和次数"""
        self.last_accessed = time.time()
        self.access_count += 1
        return self.value
    
    def get_age_seconds(self) -> float:
        """获取缓存条目的年龄（秒）"""
        return time.time() - self.created_at
    
    def get_idle_seconds(self) -> float:
        """获取缓存条目的空闲时间（秒）"""
        return time.time() - self.last_accessed


class ModelCache:
    """支持TTL和LRU策略的模型缓存"""
    
    def __init__(self, max_size: int = 100, default_ttl: int = 3600, cleanup_interval: int = 300, track_memory: bool = False):
        self._cache = OrderedDict()  # 使用OrderedDict支持LRU
        self._max_size = max_size
        self._default_ttl = default_ttl
        self._cleanup_interval = cleanup_interval
        self._lock = threading.RLock()  # 使用可重入锁
        self._last_cleanup = time.time()
        self._track_memory = track_memory
        self._memory_usage = 0
        self._stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "expirations": 0
        }
    
    def __enter__(self):
        """支持作为上下文管理器使用"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器时自动清理过期项"""
        self._cleanup_expired()
        return False  # 允许异常传播
    
    def get(self, key: str) -> Any:
        """
        获取缓存项，如果不存在或已过期则返回None
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的值或None
        """
        with self._lock:
            self._maybe_cleanup()
            
            if key not in self._cache:
                self._stats["misses"] += 1
                return None
            
            entry = self._cache[key]
            
            if entry.is_expired():
                self._stats["expirations"] += 1
                if self._track_memory:
                    self._update_memory_usage(key, remove=True)
                del self._cache[key]
                return None
            
            # 更新LRU顺序
            self._cache.move_to_end(key)
            self._stats["hits"] += 1
            
            return entry.access()
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        添加或更新缓存项
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒），如果不指定则使用默认值
        """
        if value is None:
            return
            
        ttl = ttl if ttl is not None else self._default_ttl
        
        with self._lock:
            self._maybe_cleanup()
            
            # 更新内存使用统计前先删除旧条目（如果存在）
            if self._track_memory and key in self._cache:
                self._update_memory_usage(key, remove=True)
            
            # 如果达到最大大小且键不存在，则移除最久未使用的项
            if len(self._cache) >= self._max_size and key not in self._cache:
                self._evict_one()
            
            # 添加或更新缓存
            self._cache[key] = CacheEntry(value, ttl)
            self._cache.move_to_end(key)  # 移到最近使用的位置
            
            # 更新内存使用统计
            if self._track_memory:
                self._update_memory_usage(key)
    
    def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        with self._lock:
            if key in self._cache:
                if self._track_memory:
                    self._update_memory_usage(key, remove=True)
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            if self._track_memory:
                self._memory_usage = 0
    
    def filter_delete(self, prefix: Optional[str] = None, suffix: Optional[str] = None) -> int:
        """
        根据前缀或后缀筛选并删除缓存项
        
        Args:
            prefix: 键前缀
            suffix: 键后缀
            
        Returns:
            删除的项数
        """
        count = 0
        with self._lock:
            keys_to_delete = []
            
            for key in list(self._cache.keys()):
                if (prefix and key.startswith(prefix)) or (suffix and key.endswith(suffix)):
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                if self._track_memory:
                    self._update_memory_usage(key, remove=True)
                del self._cache[key]
                count += 1
                
        return count
    
    def _maybe_cleanup(self) -> None:
        """根据清理间隔检查是否需要执行清理操作"""
        current_time = time.time()
        if current_time - self._last_cleanup > self._cleanup_interval:
            self._cleanup_expired()
            self._last_cleanup = current_time
    
    def _cleanup_expired(self) -> int:
        """
        清理所有过期的缓存项
        
        Returns:
            清理的项数
        """
        count = 0
        keys_to_delete = []
        
        for key, entry in self._cache.items():
            if entry.is_expired():
                keys_to_delete.append(key)
        
        for key in keys_to_delete:
            if self._track_memory:
                self._update_memory_usage(key, remove=True)
            del self._cache[key]
            count += 1
            self._stats["expirations"] += 1
        
        return count
    
    def _evict_one(self) -> bool:
        """
        根据LRU策略驱逐一个缓存项
        
        Returns:
            是否成功驱逐
        """
        if not self._cache:
            return False
        
        # 移除最老的项（OrderedDict的第一个项）
        key, _ = self._cache.popitem(last=False)
        if self._track_memory:
            self._update_memory_usage(key, remove=True)
        self._stats["evictions"] += 1
        return True
    
    def _update_memory_usage(self, key: str, remove: bool = False) -> None:
        """
        更新内存使用统计
        
        Args:
            key: 缓存键
            remove: 是否为删除操作
        """
        if not self._track_memory:
            return
            
        if remove:
            # 获取对象大小并从总使用量中减去
            if key in self._cache:
                obj = self._cache[key].value
                obj_size = sys.getsizeof(obj)
                self._memory_usage = max(0, self._memory_usage - obj_size)
        else:
            # 获取对象大小并添加到总使用量
            obj = self._cache[key].value
            obj_size = sys.getsizeof(obj)
            self._memory_usage += obj_size
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            包含缓存统计信息的字典
        """
        with self._lock:
            hit_rate = 0
            if self._stats["hits"] + self._stats["misses"] > 0:
                hit_rate = self._stats["hits"] / (self._stats["hits"] + self._stats["misses"])
            
            stats = {
                **self._stats,
                "size": len(self._cache),
                "max_size": self._max_size,
                "hit_rate": hit_rate
            }
            
            if self._track_memory:
                stats["memory_usage_bytes"] = self._memory_usage
                stats["memory_usage_mb"] = self._memory_usage / (1024 * 1024)
                
            return stats
    
    def get_entries_metadata(self) -> List[Dict[str, Any]]:
        """
        获取所有缓存条目的元数据
        
        Returns:
            包含缓存条目元数据的列表
        """
        result = []
        with self._lock:
            for key, entry in self._cache.items():
                metadata = {
                    "key": key,
                    "created_at": datetime.fromtimestamp(entry.created_at).isoformat(),
                    "last_accessed": datetime.fromtimestamp(entry.last_accessed).isoformat(),
                    "age_seconds": entry.get_age_seconds(),
                    "idle_seconds": entry.get_idle_seconds(),
                    "ttl_seconds": entry.ttl_seconds,
                    "access_count": entry.access_count,
                    "is_expired": entry.is_expired()
                }
                
                if self._track_memory:
                    metadata["size_bytes"] = sys.getsizeof(entry.value)
                    
                result.append(metadata)
        return result


class ModelFactory:
    """模型工厂，用于创建不同提供商的模型实例"""
    
    def __init__(self, 
                 llm_cache_size: int = 100, 
                 embedding_cache_size: int = 50,
                 default_ttl: int = 3600, 
                 cleanup_interval: int = 300,
                 track_memory: bool = False,
                 perf_history_size: int = 100):
        self.provider_handlers = {
            "openai": self._create_openai_model,
            "anthropic": self._create_anthropic_model,
            "zhipu": self._create_zhipu_model,
            "baidu": self._create_baidu_model,
            "aliyun": self._create_aliyun_model,
            "tencent": self._create_tencent_model,
            "huggingface": self._create_huggingface_model,
            "fastchat": self._create_fastchat_model,
            "ollama": self._create_ollama_model,
            "siliconflow": self._create_siliconflow_model,
            "openrouter": self._create_openrouter_model,
            "together": self._create_together_model,
            "groq": self._create_groq_model,
            "deepseek": self._create_deepseek_model,
        }
        self.embedding_provider_handlers = {
            "openai": self._create_openai_embedding,
            "huggingface": self._create_huggingface_embedding,
            "baidu": self._create_baidu_embedding,
            "aliyun": self._create_aliyun_embedding,
            "cohere": self._create_cohere_embedding,
            "zhipu": self._create_zhipu_embedding,
            "ollama": self._create_ollama_embedding,
            "siliconflow": self._create_openai_embedding,
        }
        # 替换简单字典缓存为高级缓存实现
        self.model_cache = ModelCache(
            max_size=llm_cache_size,
            default_ttl=default_ttl,
            cleanup_interval=cleanup_interval,
            track_memory=track_memory
        )
        self.embedding_model_cache = ModelCache(
            max_size=embedding_cache_size,
            default_ttl=default_ttl,
            cleanup_interval=cleanup_interval,
            track_memory=track_memory
        )
        
        # 性能监控
        self.perf_lock = threading.RLock()
        self.perf_history_size = perf_history_size
        self.creation_times = {
            "llm": {},  # provider -> [时间列表]
            "embedding": {}  # provider -> [时间列表]
        }
        self.cache_hit_times = deque(maxlen=perf_history_size)  # 缓存命中的访问时间
        self.cache_miss_times = deque(maxlen=perf_history_size)  # 缓存未命中的访问时间
    
    def create_model(
        self, 
        db: Session, 
        tenant_id: str, 
        model_key: str, 
        extra_config: Optional[Dict[str, Any]] = None
    ) -> Optional[BaseChatModel]:
        """
        创建模型实例
        
        Args:
            db: 数据库会话
            tenant_id: 租户ID
            model_key: 模型标识
            extra_config: 额外配置参数
            
        Returns:
            LangChain兼容的模型实例
        """
        # 记录开始时间
        start_time = time.time()
        
        # 创建缓存键
        cache_key = f"{tenant_id}:{model_key}"
        
        # 尝试从缓存获取
        cached_model = self.model_cache.get(cache_key)
        if cached_model:
            # 记录缓存命中时间
            with self.perf_lock:
                self.cache_hit_times.append(time.time() - start_time)
            logger.debug(f"从缓存获取模型实例: {model_key}")
            return cached_model
        
        try:
            # 查询模型信息
            model = db.query(Model).filter(Model.key == model_key, Model.is_active == True).first()
            if not model:
                logger.error(f"模型 {model_key} 不存在或未激活")
                return None
                
            # 查询访问凭证信息
            credential = db.query(ProviderAccessCredential).filter(
                ProviderAccessCredential.id == model.provider_access_credential_id,
                ProviderAccessCredential.is_active == True
            ).first()
            if not credential:
                logger.error(f"模型关联的访问凭证不存在或未激活")
                return None
            
            # 如果凭证不属于当前租户，查找租户下该提供商的任意可用凭证
            if credential.tenant_id != tenant_id:
                credential = db.query(ProviderAccessCredential).filter(
                    ProviderAccessCredential.tenant_id == tenant_id,
                    ProviderAccessCredential.provider_id == credential.provider_id,
                    ProviderAccessCredential.is_active == True
                ).first()
                
                if not credential:
                    logger.error(f"租户 {tenant_id} 未配置该提供商的有效凭证")
                    return None
                
            # 查询提供商信息
            provider = db.query(Provider).filter(
                Provider.id == credential.provider_id, 
                Provider.is_active == True
            ).first()
            if not provider:
                logger.error(f"提供商不存在或未激活")
                return None
                
            # 合并配置
            config = {}
            if model.extra_config:
                config.update(model.extra_config)
            if extra_config:
                config.update(extra_config)
                
            # 获取提供商处理函数
            handler = self.provider_handlers.get(provider.key.lower())
            if not handler:
                logger.error(f"不支持的提供商类型: {provider.key}")
                return None
            
            # 记录模型创建开始时间
            creation_start_time = time.time()
                
            # 创建模型实例
            model_instance = handler(model, credential.credentials, config)
            
            # 记录模型创建时间
            creation_time = time.time() - creation_start_time
            with self.perf_lock:
                provider_key = provider.key.lower()
                if provider_key not in self.creation_times["llm"]:
                    self.creation_times["llm"][provider_key] = deque(maxlen=self.perf_history_size)
                self.creation_times["llm"][provider_key].append(creation_time)
                self.cache_miss_times.append(time.time() - start_time)
            
            if model_instance:
                # 缓存模型实例
                # 根据模型类型设置不同的TTL
                ttl = config.get("cache_ttl", 3600)  # 默认1小时
                self.model_cache.put(cache_key, model_instance, ttl)
                return model_instance
                
            return None
            
        except Exception as e:
            logger.exception(f"创建模型实例失败: {str(e)}")
            return None
    
    def create_model_with_info(
        self,
        model: Model,
        provider: Provider,
        credential: ProviderAccessCredential,
        extra_config: Optional[Dict[str, Any]] = None
    ) -> Optional[BaseChatModel]:
        """
        使用已有的模型、提供商和凭证信息创建模型实例
        
        Args:
            model: 模型实例
            provider: 提供商实例
            credential: 访问凭证实例
            extra_config: 额外配置参数
            
        Returns:
            LangChain兼容的模型实例
        """
        try:
            # 合并配置
            config = {}
            if model.extra_config:
                config.update(model.extra_config)
            if extra_config:
                config.update(extra_config)
                
            # 获取提供商处理函数
            handler = self.provider_handlers.get(provider.key.lower())
            if not handler:
                logger.error(f"不支持的提供商类型: {provider.key}")
                return None
                
            # 创建模型实例
            model_instance = handler(model, credential.credentials, config)
            if model_instance:
                return model_instance
                
            return None
            
        except Exception as e:
            logger.exception(f"创建模型实例失败: {str(e)}")
            return None
    
    def create_embedding_model(
        self, 
        db: Session, 
        tenant_id: str, 
        model_id: str,
        extra_config: Optional[Dict[str, Any]] = None
    ) -> Optional[Embeddings]:
        """
        创建embedding模型实例
        
        Args:
            db: 数据库会话
            tenant_id: 租户ID
            model_key: 模型标识
            extra_config: 额外配置参数
            
        Returns:
            LangChain兼容的embedding模型实例
        """
        # 记录开始时间
        start_time = time.time()
        
        # 创建缓存键
        cache_key = f"{tenant_id}:{model_id}"
        
        # 尝试从缓存获取
        cached_model = self.embedding_model_cache.get(cache_key)
        if cached_model:
            # 记录缓存命中时间
            with self.perf_lock:
                self.cache_hit_times.append(time.time() - start_time)
            logger.debug(f"从缓存获取embedding模型实例: {model_id}")
            return cached_model
        
        try:
            # 查询模型信息
            model = db.query(Model).filter(Model.id == model_id, Model.is_active == True).first()
            if not model:
                logger.error(f"模型 {model_id} 不存在或未激活")
                return None
            
            # 检查模型类型是否为embedding
            if model.model_type != "embedding":
                logger.error(f"模型 {model_id} 不是embedding类型")
                return None
                
            # 查询访问凭证
            credential = db.query(ProviderAccessCredential).filter(
                ProviderAccessCredential.tenant_id == tenant_id,
                ProviderAccessCredential.id == model.provider_access_credential_id,
                ProviderAccessCredential.is_active == True
            ).first()
            if not credential:
                logger.error(f"租户 {tenant_id} 未配置 {model.name} 的有效凭证")
                return None

            # 查询提供商信息
            provider = db.query(Provider).filter(
                Provider.id == credential.provider_id, 
                Provider.is_active == True
            ).first()
            if not provider:
                logger.error(f"提供商 {credential.provider_id} 不存在或未激活")
                return None

            # 合并配置
            config = {}
            if model.extra_config:
                config.update(model.extra_config)
            if extra_config:
                config.update(extra_config)
                
            # 获取提供商处理函数
            handler = self.embedding_provider_handlers.get(provider.key.lower())
            # handler = self.embedding_provider_handlers.get("aliyun")
            if not handler:
                logger.error(f"不支持的embedding提供商类型: {provider.key}")
                return None
            
            # 记录模型创建开始时间
            creation_start_time = time.time()
                
            # 创建embedding模型实例
            embedding_model = handler(model, provider, credential.credentials, config)
            
            # 记录模型创建时间
            creation_time = time.time() - creation_start_time
            with self.perf_lock:
                provider_key = provider.key.lower()
                if provider_key not in self.creation_times["embedding"]:
                    self.creation_times["embedding"][provider_key] = deque(maxlen=self.perf_history_size)
                self.creation_times["embedding"][provider_key].append(creation_time)
                self.cache_miss_times.append(time.time() - start_time)
            
            if embedding_model:
                # 为embedding模型设置更长的TTL，因为它们变化较少
                ttl = config.get("cache_ttl", 7200)  # 默认2小时
                self.embedding_model_cache.put(cache_key, embedding_model, ttl)
                return embedding_model
                
            return None
            
        except Exception as e:
            logger.exception(f"创建embedding模型实例失败: {str(e)}")
            return None
        
    def _create_openai_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建OpenAI模型实例"""
        try:            
            # 准备参数
            model_kwargs = {}
            if "model_kwargs" in config:
                model_kwargs.update(config["model_kwargs"])
                
            # 创建模型实例
            return ChatOpenAI(
                model=model.key,
                api_key=credentials.get("api_key"),
                openai_api_base=credentials.get("api_base"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens"),
                model_kwargs=model_kwargs
            )
        except ImportError:
            logger.error("未安装langchain_openai包")
            return None
        except Exception as e:
            logger.exception(f"创建OpenAI模型实例失败: {str(e)}")
            return None
    
    def _create_anthropic_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建Anthropic模型实例"""
        try:
            from langchain_anthropic import ChatAnthropic
            
            # 创建模型实例
            return ChatAnthropic(
                model_name=model.key,
                anthropic_api_key=credentials.get("api_key"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens")
            )
        except ImportError:
            logger.error("未安装langchain_anthropic包")
            return None
        except Exception as e:
            logger.exception(f"创建Anthropic模型实例失败: {str(e)}")
            return None
    
    def _create_zhipu_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建智谱AI模型实例"""
        try:
            from langchain_community.chat_models import ChatZhipuAI
            
            # 创建模型实例
            return ChatZhipuAI(
                model_name=model.key,
                api_key=credentials.get("api_key"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens")
            )
        except ImportError:
            logger.error("未安装相关智谱AI包")
            return None
        except Exception as e:
            logger.exception(f"创建智谱AI模型实例失败: {str(e)}")
            return None
    
    def _create_baidu_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建百度文心模型实例"""
        try:
            from langchain_community.chat_models.baidu_qianfan_endpoint import QianfanChatEndpoint
            
            # 创建模型实例
            return QianfanChatEndpoint(
                model_name=model.key,
                qianfan_ak=credentials.get("api_key"),
                qianfan_sk=credentials.get("secret_key"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens")
            )
        except ImportError:
            logger.error("未安装百度文心相关包")
            return None
        except Exception as e:
            logger.exception(f"创建百度文心模型实例失败: {str(e)}")
            return None
    
    def _create_aliyun_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建阿里云通义模型实例"""
        try:
            from langchain_community.chat_models import ChatTongyi
            
            # 创建模型实例
            return ChatTongyi(
                model_name=model.key,
                dashscope_api_key=credentials.get("api_key"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens")
            )
        except ImportError:
            logger.error("未安装阿里云通义相关包")
            return None
        except Exception as e:
            logger.exception(f"创建阿里云通义模型实例失败: {str(e)}")
            return None
    
    def _create_tencent_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建腾讯混元模型实例"""
        try:
            from langchain_community.chat_models import ChatHunyuan
            
            # 创建模型实例
            return ChatHunyuan(
                model_name=model.key,
                hunyuan_app_id=credentials.get("app_id"),
                hunyuan_secret_id=credentials.get("secret_id"),
                hunyuan_secret_key=credentials.get("secret_key"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens")
            )
        except ImportError:
            logger.error("未安装腾讯混元相关包")
            return None
        except Exception as e:
            logger.exception(f"创建腾讯混元模型实例失败: {str(e)}")
            return None
    
    def _create_huggingface_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建HuggingFace模型实例"""
        try:
            from langchain_huggingface import HuggingFaceEndpoint, ChatHuggingFace
            
            # 创建模型实例
            llm = HuggingFaceEndpoint(
                repo_id=model.key,
                huggingfacehub_api_token=credentials.get("api_key"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens")
            )
            return ChatHuggingFace(
                llm=llm, verbose=True
            )
        except ImportError:
            logger.error("未安装HuggingFace相关包")
            return None
        except Exception as e:
            logger.exception(f"创建HuggingFace模型实例失败: {str(e)}")
            return None
    
    def _create_fastchat_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建FastChat模型实例"""
        try:            
            # 创建模型实例（FastChat兼容OpenAI API）
            return ChatOpenAI(
                model=model.key,
                api_key="dummy-key",  # FastChat通常不需要真实的API密钥
                openai_api_base=credentials.get("api_base", "http://localhost:8000/v1"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens")
            )
        except ImportError:
            logger.error("未安装FastChat相关包")
            return None
        except Exception as e:
            logger.exception(f"创建FastChat模型实例失败: {str(e)}")
            return None
    
    def _create_ollama_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建Ollama模型实例"""
        try:
            from langchain_ollama import ChatOllama
            
            # 创建模型实例
            return ChatOllama(                
                model=model.key,
                base_url=credentials.get("api_base", "http://localhost:11434"),
                temperature=config.get("temperature", 0.7)
            )
        except ImportError:
            logger.error("未安装Ollama相关包")
            return None
        except Exception as e:
            logger.exception(f"创建Ollama模型实例失败: {str(e)}")
            return None
    
    def _create_siliconflow_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建SiliconFlow模型实例"""
        try:
            # SiliconFlow使用OpenAI兼容API
            model_kwargs = {}
            if "model_kwargs" in config:
                model_kwargs.update(config["model_kwargs"])
            return ChatOpenAI(
                model=model.key,
                api_key=credentials.get("api_key"),
                openai_api_base=credentials.get("api_base", "https://api.siliconflow.cn/v1"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens"),
                model_kwargs=model_kwargs
            )
        except ImportError:
            logger.error("未安装所需的SiliconFlow兼容包")
            return None
        except Exception as e:
            logger.exception(f"创建SiliconFlow模型实例失败: {str(e)}")
            return None
    
    def _create_openrouter_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建OpenRouter模型实例"""
        try:
            
            
            model_kwargs = {
                "headers": {
                    "HTTP-Referer": credentials.get("referer", "https://yunzhiwen.ai"),  # 应用域名
                    "X-Title": credentials.get("app_name", "云知问AI平台"),  # 应用名称
                }
            }
            
            if "model_kwargs" in config:
                if "headers" in config["model_kwargs"]:
                    model_kwargs["headers"].update(config["model_kwargs"]["headers"])
                    del config["model_kwargs"]["headers"]
                model_kwargs.update(config["model_kwargs"])
            
            # 创建模型实例
            return ChatOpenAI(
                model=model.key,  # 例如: "anthropic/claude-3-opus"
                api_key=credentials.get("api_key"),
                openai_api_base="https://openrouter.ai/api/v1",
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens"),
                model_kwargs=model_kwargs
            )
        except ImportError:
            logger.error("未安装所需的OpenRouter兼容包")
            return None
        except Exception as e:
            logger.exception(f"创建OpenRouter模型实例失败: {str(e)}")
            return None
    
    def _create_together_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建Together.ai模型实例"""
        try:
            
            model_kwargs = {}
            if "model_kwargs" in config:
                model_kwargs.update(config["model_kwargs"])
                
            # 创建模型实例
            return ChatOpenAI(
                model=model.key,
                api_key=credentials.get("api_key"),
                openai_api_base="https://api.together.xyz/v1",
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens"),
                model_kwargs=model_kwargs
            )
        except ImportError:
            logger.error("未安装所需的Together.ai兼容包")
            return None
        except Exception as e:
            logger.exception(f"创建Together.ai模型实例失败: {str(e)}")
            return None
    
    def _create_groq_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建Groq模型实例"""
        try:
            from langchain_groq import ChatGroq
            
            # 创建模型实例
            return ChatGroq(
                model_name=model.key,
                groq_api_key=credentials.get("api_key"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens")
            )
        except ImportError:
            # 回退到OpenAI兼容API
            try:
                
                model_kwargs = {}
                if "model_kwargs" in config:
                    model_kwargs.update(config["model_kwargs"])
                    
                # 创建模型实例
                return ChatOpenAI(
                    model=model.key,
                    api_key=credentials.get("api_key"),
                    openai_api_base="https://api.groq.com/openai/v1",
                    temperature=config.get("temperature", 0.7),
                    max_tokens=config.get("max_tokens"),
                    model_kwargs=model_kwargs
                )
            except ImportError:
                logger.error("未安装所需的Groq兼容包")
                return None
            except Exception as e:
                logger.exception(f"通过OpenAI兼容API创建Groq模型实例失败: {str(e)}")
                return None
        except Exception as e:
            logger.exception(f"创建Groq模型实例失败: {str(e)}")
            return None
    
    def _create_deepseek_model(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[BaseChatModel]:
        """创建DeepSeek模型实例"""
        try:
            from langchain_deepseek import ChatDeepSeek
            
            # 创建模型实例
            return ChatDeepSeek(
                model_name=model.key,
                deepseek_api_key=credentials.get("api_key"),
                temperature=config.get("temperature", 0.7),
                max_tokens=config.get("max_tokens")
            )
        except ImportError:
            # 回退到OpenAI兼容API
            try:
                
                model_kwargs = {}
                if "model_kwargs" in config:
                    model_kwargs.update(config["model_kwargs"])
                    
                # 创建模型实例
                return ChatOpenAI(
                    model=model.key,
                    api_key=credentials.get("api_key"),
                    openai_api_base=credentials.get("api_base", "https://api.deepseek.com/v1"),
                    temperature=config.get("temperature", 0.7),
                    max_tokens=config.get("max_tokens"),
                    model_kwargs=model_kwargs
                )
            except ImportError:
                logger.error("未安装所需的DeepSeek兼容包")
                return None
            except Exception as e:
                logger.exception(f"通过OpenAI兼容API创建DeepSeek模型实例失败: {str(e)}")
                return None
        except Exception as e:
            logger.exception(f"创建DeepSeek模型实例失败: {str(e)}")
            return None
    
    def _create_openai_embedding(
        self, 
        model: Model,
        provider: Provider,
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[Embeddings]:
        """创建OpenAI embedding模型实例"""
        try:
            from langchain_openai import OpenAIEmbeddings
            
            # 准备参数
            model_kwargs = {}
            if "model_kwargs" in config:
                model_kwargs.update(config["model_kwargs"])
                
            # 创建模型实例
            return OpenAIEmbeddings(
                model=model.key,
                openai_api_key=credentials.get("api_key"),
                openai_api_base=provider.api_base or credentials.get("api_base"),
                model_kwargs=model_kwargs
            )
        except ImportError:
            logger.error("未安装langchain_openai包")
            return None
        except Exception as e:
            logger.exception(f"创建OpenAI embedding模型实例失败: {str(e)}")
            return None
    
    def _create_huggingface_embedding(
        self, 
        model: Model, 
        provider: Provider,
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[Embeddings]:
        """创建HuggingFace embedding模型实例"""
        try:
            from langchain_community.embeddings import HuggingFaceEmbeddings
            import os
            
            # 设置环境变量
            api_key = credentials.get("api_key", "")
            if api_key:
                os.environ["HUGGINGFACE_API_KEY"] = api_key
            
            # 准备参数
            model_kwargs = {}
            if "model_kwargs" in config:
                model_kwargs.update(config["model_kwargs"])
                
            # 创建模型实例
            return HuggingFaceEmbeddings(
                model_name=model.key,
                model_kwargs=model_kwargs,
                api_key=api_key,
                api_base=provider.api_base
            )
        except ImportError:
            logger.error("未安装langchain_community包或HuggingFace相关包")
            return None
        except Exception as e:
            logger.exception(f"创建HuggingFace embedding模型实例失败: {str(e)}")
            return None
    
    def _create_cohere_embedding(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[Embeddings]:
        """创建Cohere embedding模型实例"""
        try:
            from langchain_cohere import CohereEmbeddings
            
            # 创建模型实例
            return CohereEmbeddings(
                model=model.key,
                cohere_api_key=credentials.get("api_key")
            )
        except ImportError:
            logger.error("未安装langchain_cohere包")
            return None
        except Exception as e:
            logger.exception(f"创建Cohere embedding模型实例失败: {str(e)}")
            return None
    
    def _create_baidu_embedding(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[Embeddings]:
        """创建百度文心embedding模型实例"""
        try:
            from langchain_community.embeddings import QianfanEmbeddingsEndpoint
            
            # 创建模型实例
            return QianfanEmbeddingsEndpoint(
                model=model.key,
                qianfan_ak=credentials.get("api_key"),
                qianfan_sk=credentials.get("secret_key")
            )
        except ImportError:
            logger.error("未安装百度文心相关包")
            return None
        except Exception as e:
            logger.exception(f"创建百度文心embedding模型实例失败: {str(e)}")
            return None
    
    def _create_aliyun_embedding(
        self, 
        model: Model, 
        provider: Provider,
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[Embeddings]:
        """创建阿里云通义embedding模型实例"""
        try:
            from langchain_community.embeddings import DashScopeEmbeddings
            
            # 创建模型实例
            return DashScopeEmbeddings(
                model=model.key,
                dashscope_api_key=credentials.get("api_key")
            )
        except ImportError:
            logger.error("未安装阿里云通义相关包")
            return None
        except Exception as e:
            logger.exception(f"创建阿里云通义embedding模型实例失败: {str(e)}")
            return None
    
    def _create_zhipu_embedding(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[Embeddings]:
        """创建智谱AI embedding模型实例"""
        try:
            from langchain_community.embeddings import ZhipuAIEmbeddings
            
            # 创建模型实例
            return ZhipuAIEmbeddings(
                model=model.key,
                zhipuai_api_key=credentials.get("api_key")
            )
        except ImportError:
            logger.error("未安装智谱AI相关包")
            return None
        except Exception as e:
            logger.exception(f"创建智谱AI embedding模型实例失败: {str(e)}")
            return None
    
    def _create_ollama_embedding(
        self, 
        model: Model, 
        credentials: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> Optional[Embeddings]:
        """创建Ollama embedding模型实例"""
        try:
            from langchain_community.embeddings import OllamaEmbeddings
            
            # 创建模型实例
            return OllamaEmbeddings(
                model=model.key,
                base_url=credentials.get("api_base", "http://localhost:11434")
            )
        except ImportError:
            logger.error("未安装Ollama相关包")
            return None
        except Exception as e:
            logger.exception(f"创建Ollama embedding模型实例失败: {str(e)}")
            return None
    
    
    
    def clear_cache(self, tenant_id: Optional[str] = None, model_key: Optional[str] = None, model_type: Optional[str] = None):
        """
        清除模型缓存
        
        Args:
            tenant_id: 租户ID，如果指定，则只清除该租户的缓存
            model_key: 模型标识，如果指定，则只清除该模型的缓存
            model_type: 模型类型，"llm"、"embedding"或"rerank"，如果不指定则清除所有类型
        """
        # 确定要清除的缓存
        caches_to_clear = []
        if model_type is None or model_type == "llm":
            caches_to_clear.append(self.model_cache)
        if model_type is None or model_type == "embedding":
            caches_to_clear.append(self.embedding_model_cache)
            
        for cache in caches_to_clear:
            if tenant_id and model_key:
                # 清除特定租户的特定模型缓存
                cache_key = f"{tenant_id}:{model_key}"
                cache.delete(cache_key)
            elif tenant_id:
                # 清除特定租户的所有模型缓存
                cache.filter_delete(prefix=f"{tenant_id}:")
            elif model_key:
                # 清除所有租户的特定模型缓存
                cache.filter_delete(suffix=f":{model_key}")
            else:
                # 清除所有缓存
                cache.clear()

    def get_cache_stats(self, model_type: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        获取缓存统计信息
        
        Args:
            model_type: 模型类型，"llm"、"embedding"或"rerank"，如果不指定则返回所有类型
            
        Returns:
            包含缓存统计信息的字典
        """
        result = {}
        
        if model_type is None or model_type == "llm":
            result["llm"] = self.model_cache.get_stats()
            
        if model_type is None or model_type == "embedding":
            result["embedding"] = self.embedding_model_cache.get_stats()
            
        return result
    
    def get_cache_entries(self, model_type: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取缓存条目元数据
        
        Args:
            model_type: 模型类型，"llm"、"embedding"或"rerank"，如果不指定则返回所有类型
            
        Returns:
            包含缓存条目元数据的字典
        """
        result = {}
        
        if model_type is None or model_type == "llm":
            result["llm"] = self.model_cache.get_entries_metadata()
            
        if model_type is None or model_type == "embedding":
            result["embedding"] = self.embedding_model_cache.get_entries_metadata()
            
        return result

    def set_cache_ttl(self, ttl_seconds: int, model_type: Optional[str] = None):
        """
        设置缓存TTL
        
        Args:
            ttl_seconds: TTL秒数
            model_type: 模型类型，"llm"或"embedding"，如果不指定则设置所有类型
        """
        if model_type is None or model_type == "llm":
            self.model_cache._default_ttl = ttl_seconds
            
        if model_type is None or model_type == "embedding":
            self.embedding_model_cache._default_ttl = ttl_seconds 

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            包含性能统计信息的字典
        """
        with self.perf_lock:
            result = {
                "cache": {
                    "hit_count": len(self.cache_hit_times),
                    "miss_count": len(self.cache_miss_times),
                },
                "creation_times": {
                    "llm": {},
                    "embedding": {}
                }
            }
            
            # 计算缓存访问时间统计
            if self.cache_hit_times:
                hit_times = list(self.cache_hit_times)
                result["cache"]["hit_avg_ms"] = statistics.mean(hit_times) * 1000
                result["cache"]["hit_min_ms"] = min(hit_times) * 1000
                result["cache"]["hit_max_ms"] = max(hit_times) * 1000
                if len(hit_times) > 1:
                    result["cache"]["hit_stddev_ms"] = statistics.stdev(hit_times) * 1000
            
            if self.cache_miss_times:
                miss_times = list(self.cache_miss_times)
                result["cache"]["miss_avg_ms"] = statistics.mean(miss_times) * 1000
                result["cache"]["miss_min_ms"] = min(miss_times) * 1000
                result["cache"]["miss_max_ms"] = max(miss_times) * 1000
                if len(miss_times) > 1:
                    result["cache"]["miss_stddev_ms"] = statistics.stdev(miss_times) * 1000
            
            # 计算每个提供商的模型创建时间统计
            for model_type in ["llm", "embedding"]:
                for provider, times in self.creation_times[model_type].items():
                    if times:
                        time_list = list(times)
                        provider_stats = {
                            "count": len(time_list),
                            "avg_ms": statistics.mean(time_list) * 1000,
                            "min_ms": min(time_list) * 1000,
                            "max_ms": max(time_list) * 1000
                        }
                        if len(time_list) > 1:
                            provider_stats["stddev_ms"] = statistics.stdev(time_list) * 1000
                        result["creation_times"][model_type][provider] = provider_stats
            
            return result 