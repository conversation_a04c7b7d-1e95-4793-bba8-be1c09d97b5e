from enum import Enum
from typing import List, Dict, Any, Optional

# 平台角色
class PlatformRole(str, Enum):
    SUPER_ADMIN = "super_admin"  # 超级管理员

# 租户角色
class TenantRole(str, Enum):
    OWNER = "owner"      # 拥有者
    ADMIN = "admin"      # 管理员
    MEMBER = "member"    # 成员

# 角色权限配置
ROLE_PERMISSIONS: Dict[str, Dict[str, Any]] = {
    # 平台角色权限
    PlatformRole.SUPER_ADMIN: {
        "name": "超级管理员",
        "description": "拥有平台所有权限",
        "can_manage_all_tenants": True,
        "can_manage_all_users": True,
        "can_manage_system_config": True
    },
    
    # 租户角色权限
    TenantRole.OWNER: {
        "name": "拥有者",
        "description": "租户拥有者，拥有租户内所有权限",
        "can_manage_tenant": True,
        "can_manage_tenant_users": True,
        "can_manage_tenant_roles": True,
        "can_delete_tenant": True,
        "can_transfer_ownership": True,
        "can_manage_agents": True,
        "can_manage_knowledge_bases": True,
    },
    TenantRole.ADMIN: {
        "name": "管理员",
        "description": "租户管理员，拥有除删除租户外的大部分权限",
        "can_manage_tenant": True,
        "can_manage_tenant_users": True,
        "can_manage_agents": True,
        "can_manage_knowledge_bases": True,
    },
    TenantRole.MEMBER: {
        "name": "成员",
        "description": "租户普通成员，拥有基本使用权限",
        "can_use_agents": True,
        "can_use_knowledge_bases": True,
    }
}

def get_role_name(role_key: str) -> str:
    """获取角色名称"""
    return ROLE_PERMISSIONS.get(role_key, {}).get("name", role_key)

def get_role_description(role_key: str) -> str:
    """获取角色描述"""
    return ROLE_PERMISSIONS.get(role_key, {}).get("description", "")

def get_role_permission(role_key: str, permission: str) -> bool:
    """检查角色是否有特定权限"""
    return ROLE_PERMISSIONS.get(role_key, {}).get(permission, False)

def is_tenant_admin_role(role_key: str) -> bool:
    """检查是否为租户管理员角色（拥有者或管理员）"""
    return role_key in [TenantRole.OWNER, TenantRole.ADMIN]

def get_tenant_roles() -> List[Dict[str, Any]]:
    """获取所有租户角色列表"""
    roles = []
    for role_key in TenantRole:
        roles.append({
            "key": role_key,
            "name": get_role_name(role_key),
            "description": get_role_description(role_key)
        })
    return roles 