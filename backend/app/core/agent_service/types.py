from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from datetime import datetime

class AgentType(str, Enum):
    """Agent 类型枚举"""
    GENERAL = "general"      # 通用对话agent
    SUPERVISOR = "supervisor"  # 监督者agent
    
    @property
    def description(self) -> str:
        """返回枚举值的中文描述"""
        descriptions = {
            self.GENERAL: "通用助手",
            self.SUPERVISOR: "监督者"
        }
        return descriptions.get(self, "未知类型")

class SessionStatus(str, Enum):
    """会话状态枚举"""
    ACTIVE = "active"       # 活跃
    IDLE = "idle"          # 空闲
    EXPIRED = "expired"    # 已过期
    TERMINATED = "terminated"  # 已终止

@dataclass
class SessionInfo:
    """会话信息类"""
    session_id: str                 # 会话ID (格式: tenant_id_agent_id_thread_id)
    tenant_id: str                  # 租户ID
    agent_id: str                   # Agent ID
    thread_id: str                  # 线程ID
    user_id: Optional[str] = None   # 用户ID
    user_name: Optional[str] = None # 用户名
    status: SessionStatus = SessionStatus.ACTIVE  # 会话状态
    created_at: datetime = field(default_factory=datetime.now)    # 创建时间
    last_active: datetime = field(default_factory=datetime.now)   # 最后活跃时间
    message_count: int = 0          # 消息数量
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据 