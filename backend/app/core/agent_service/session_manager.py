import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from .interfaces import ISessionManager, IStorageManager
from .types import SessionInfo, SessionStatus
from .exceptions import AgentServiceError

logger = logging.getLogger(__name__)


class SessionManager(ISessionManager):
    """会话管理器 - 负责会话的CRUD操作和状态管理"""
    
    def __init__(self, db: Session, storage_manager: IStorageManager):
        self.db = db
        self.storage_manager = storage_manager
        
        logger.info("✅ 会话管理器初始化完成")
    
    async def create_session(
        self, 
        tenant_id: str, 
        agent_id: str, 
        thread_id: str,
        user_id: Optional[str] = None,
        user_name: Optional[str] = None
    ) -> str:
        """创建会话"""
        try:
            session_id = f"{tenant_id}_{agent_id}_{thread_id}"
            
            # 存储到MongoDB
            mongo_db = self.storage_manager.get_mongo_db()
            sessions_collection = mongo_db[f"sessions_{tenant_id}"]
            
            # 检查会话是否已存在
            existing_session = sessions_collection.find_one({"session_id": session_id})
            if existing_session:
                # 更新活跃时间
                await self.update_session_activity(tenant_id, session_id)
                return session_id
            
            # 创建新会话
            session_doc = {
                "session_id": session_id,
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id,
                "user_name": user_name,
                "status": SessionStatus.ACTIVE.value,
                "created_at": datetime.now(),
                "last_active": datetime.now(),
                "message_count": 0,
                "metadata": {}
            }
            
            sessions_collection.insert_one(session_doc)
            logger.info(f"✅ 创建会话成功: {session_id}")
            return session_id
                
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise AgentServiceError(f"创建会话失败: {e}")
    
    async def get_session(self, tenant_id: str, session_id: str) -> Optional[SessionInfo]:
        """获取会话信息"""
        try:
            mongo_db = self.storage_manager.get_mongo_db()
            sessions_collection = mongo_db[f"sessions_{tenant_id}"]
            
            session_doc = sessions_collection.find_one({"session_id": session_id})
            if not session_doc:
                return None
            
            return SessionInfo(
                session_id=session_doc["session_id"],
                tenant_id=session_doc["tenant_id"],
                agent_id=session_doc["agent_id"],
                thread_id=session_doc["thread_id"],
                user_id=session_doc.get("user_id"),
                user_name=session_doc.get("user_name"),
                status=SessionStatus(session_doc.get("status", "active")),
                created_at=session_doc["created_at"],
                last_active=session_doc["last_active"],
                message_count=session_doc.get("message_count", 0),
                metadata=session_doc.get("metadata", {})
            )
            
        except Exception as e:
            logger.error(f"获取会话信息失败: {e}")
            raise AgentServiceError(f"获取会话信息失败: {e}")
    
    async def list_sessions(
        self, 
        tenant_id: str, 
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        status: Optional[SessionStatus] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> List[SessionInfo]:
        """列出会话"""
        try:
            mongo_db = self.storage_manager.get_mongo_db()
            sessions_collection = mongo_db[f"sessions_{tenant_id}"]
            
            # 构建查询条件
            query = {}
            if agent_id:
                query["agent_id"] = agent_id
            if user_id:
                query["user_id"] = user_id
            if status:
                query["status"] = status.value
            
            # 执行查询
            cursor = sessions_collection.find(query).sort("last_active", -1).skip(skip).limit(limit)
            
            sessions = []
            for session_doc in cursor:
                sessions.append(SessionInfo(
                    session_id=session_doc["session_id"],
                    tenant_id=session_doc["tenant_id"],
                    agent_id=session_doc["agent_id"],
                    thread_id=session_doc["thread_id"],
                    user_id=session_doc.get("user_id"),
                    user_name=session_doc.get("user_name"),
                    status=SessionStatus(session_doc.get("status", "active")),
                    created_at=session_doc["created_at"],
                    last_active=session_doc["last_active"],
                    message_count=session_doc.get("message_count", 0),
                    metadata=session_doc.get("metadata", {})
                ))
            
            return sessions
            
        except Exception as e:
            logger.error(f"列出会话失败: {e}")
            raise AgentServiceError(f"列出会话失败: {e}")
    
    async def update_session_activity(self, tenant_id: str, session_id: str):
        """更新会话活跃时间"""
        try:
            mongo_db = self.storage_manager.get_mongo_db()
            sessions_collection = mongo_db[f"sessions_{tenant_id}"]
            
            sessions_collection.update_one(
                {"session_id": session_id},
                {
                    "$set": {"last_active": datetime.now()},
                    "$inc": {"message_count": 1}
                }
            )
            logger.debug(f"更新会话活跃时间: {session_id}")
            
        except Exception as e:
            logger.error(f"更新会话活跃时间失败: {e}")
            # 不抛出异常，避免影响主流程
    
    async def cleanup_expired_sessions(self, tenant_id: Optional[str] = None, expiry_hours: int = 24) -> int:
        """清理过期会话"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=expiry_hours)
            total_cleaned = 0
            
            mongo_db = self.storage_manager.get_mongo_db()
            
            if tenant_id:
                # 清理指定租户的过期会话
                sessions_collection = mongo_db[f"sessions_{tenant_id}"]
                result = sessions_collection.delete_many({
                    "last_active": {"$lt": cutoff_time}
                })
                total_cleaned = result.deleted_count
            else:
                # 清理所有租户的过期会话
                # 获取所有会话集合
                collection_names = mongo_db.list_collection_names()
                session_collections = [name for name in collection_names if name.startswith("sessions_")]
                
                for collection_name in session_collections:
                    sessions_collection = mongo_db[collection_name]
                    result = sessions_collection.delete_many({
                        "last_active": {"$lt": cutoff_time}
                    })
                    total_cleaned += result.deleted_count
            
            logger.info(f"✅ 清理了 {total_cleaned} 个过期会话")
            return total_cleaned
            
        except Exception as e:
            logger.error(f"清理过期会话失败: {e}")
            raise AgentServiceError(f"清理过期会话失败: {e}")
    
    async def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        try:
            mongo_db = self.storage_manager.get_mongo_db()
            collection_names = mongo_db.list_collection_names()
            session_collections = [name for name in collection_names if name.startswith("sessions_")]
            
            total_sessions = 0
            for collection_name in session_collections:
                sessions_collection = mongo_db[collection_name]
                count = sessions_collection.count_documents({})
                total_sessions += count
            
            return {
                "total_sessions": total_sessions,
                "session_collections": len(session_collections)
            }
            
        except Exception as e:
            logger.warning(f"获取会话统计失败: {e}")
            return {
                "total_sessions": 0,
                "session_collections": 0
            } 