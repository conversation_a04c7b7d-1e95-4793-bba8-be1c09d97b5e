import logging
import json
from typing import Dict, List, Optional, Any
from collections import defaultdict
from sqlalchemy.orm import Session
from langgraph.store.memory import InMemoryStore

from app.core.model_factory import ModelFactory
from app.services.tenant import TenantService
from app.services.agent import AgentService
from .interfaces import IAgentInstanceManager, IWorkflowEngine, IStorageManager
from .config import AgentServiceConfig
from .exceptions import AgentServiceError, AgentNotFoundError

logger = logging.getLogger(__name__)


class AgentInstanceManager(IAgentInstanceManager):
    """Agent实例管理器 - 负责Agent实例的创建、缓存和管理"""
    
    def __init__(
        self, 
        db: Session, 
        config: AgentServiceConfig,
        workflow_engine: IWorkflowEngine,
        storage_manager: IStorageManager
    ):
        self.db = db
        self.config = config
        self.workflow_engine = workflow_engine
        self.storage_manager = storage_manager
        
        # 使用现有的services获取数据
        self.tenant_service = TenantService(db=db)
        self.agent_service = AgentService(db=db)
        
        # Agent实例缓存 {tenant_id: {agent_id: agent_instance}}
        self._agent_instances: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        # 检查点存储缓存 {tenant_id: {agent_id: checkpointer}}
        self._checkpointers: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        logger.info("✅ Agent实例管理器初始化完成")
    
    async def get_agent_instance(self, tenant_id: str, agent_id: str):
        """获取或创建Agent实例"""
        # 检查缓存
        if tenant_id in self._agent_instances and agent_id in self._agent_instances[tenant_id]:
            return self._agent_instances[tenant_id][agent_id]
        
        # 从现有services获取Agent详情
        try:
            agent_detail = await self.agent_service.get_agent_detail(tenant_id, agent_id)
            
            if not agent_detail:
                raise AgentNotFoundError(agent_id, tenant_id)
            
            # 创建Agent实例
            await self._create_agent_instance(tenant_id, agent_id, agent_detail)
            return self._agent_instances[tenant_id][agent_id]
            
        except Exception as e:
            if isinstance(e, AgentServiceError):
                raise
            logger.error(f"获取Agent实例失败: {e}")
            raise AgentServiceError(f"获取Agent实例失败: {e}")
    
    async def _create_agent_instance(self, tenant_id: str, agent_id: str, agent_detail: Dict[str, Any]):
        """创建Agent实例 - 使用LangGraph Workflows"""
        try:
            # 从agent_detail获取模型信息
            model_id = None
            if 'versions' in agent_detail and agent_detail['versions']:
                # 获取当前版本的模型ID
                current_version = next((v for v in agent_detail['versions'] if v.is_current), None)
                if current_version and current_version.model_id:
                    model_id = current_version.model_id
            
            # 获取流式模型实例
            model_instance = await self._get_model_instance(tenant_id, model_id or "default", agent_detail)
            
            tools_info = agent_detail.get('tools', [])
            tool_ids = [tool['id'] for tool in tools_info]
            
            mcp_servers_info = agent_detail.get('mcp_servers', [])
            mcp_server_ids = [mcp_server['id'] for mcp_server in mcp_servers_info]
            
            from app.core.agent_service.agent_tool import AgentToolService
            tools = await AgentToolService.get_agent_tools(self.db, tenant_id, tool_ids, mcp_server_ids)
            
            # 创建检查点存储
            checkpointer = await self.storage_manager.create_checkpointer(tenant_id, agent_id)
            
            # 创建存储
            store = InMemoryStore()
            
            # 获取系统提示词
            system_prompt = agent_detail.get("prompt", "你是一个有用的AI助手。")
            
            # 获取知识库配置
            knowledge_base_ids = self._extract_knowledge_base_ids(agent_detail)
            
            # 创建LangGraph Workflows
            logger.info(f"创建LangGraph Workflows Agent...")
            agent = await self.workflow_engine.create_workflow(
                model_instance=model_instance,
                tools=tools,
                checkpointer=checkpointer,
                store=store,
                system_prompt=system_prompt,
                tenant_id=tenant_id,
                agent_id=agent_id,
                knowledge_base_ids=knowledge_base_ids
            )
            
            # 缓存实例
            self._agent_instances[tenant_id][agent_id] = agent
            self._checkpointers[tenant_id][agent_id] = checkpointer
            
            logger.info(f"✅ 创建LangGraph Workflows Agent实例成功: {tenant_id}/{agent_id}")
            
        except Exception as e:
            logger.error(f"创建Agent实例失败: {e}")
            raise AgentServiceError(f"创建Agent实例失败: {e}")
    
    def _get_model_config(self, agent_detail: Dict[str, Any], model: Any) -> Dict[str, Any]:
        """
        获取模型配置，按优先级顺序：
        1. agent配置中的llm_model_config
        2. model的extra_config
        3. 默认配置
        """
        # 默认配置
        default_config = {
            "temperature": 0.7,
            "max_tokens": 1000,
            "top_p": 1,
            "streaming": True,
        }
        
        # 从agent配置中获取llm_model_config
        agent_llm_config = {}
        if agent_detail and 'versions' in agent_detail and agent_detail['versions']:
            current_version = next((v for v in agent_detail['versions'] if v.is_current), None)
            if current_version and hasattr(current_version, 'llm_model_config') and current_version.llm_model_config:
                try:
                    # 如果llm_model_config是字符串，尝试解析为JSON
                    if isinstance(current_version.llm_model_config, str):
                        agent_llm_config = json.loads(current_version.llm_model_config)
                    elif isinstance(current_version.llm_model_config, dict):
                        agent_llm_config = current_version.llm_model_config
                    logger.info(f"获取到agent配置中的llm_model_config: {agent_llm_config}")
                except Exception as e:
                    logger.warning(f"解析agent llm_model_config失败: {e}")
                    agent_llm_config = {}
        
        # 从model的extra_config获取配置
        model_extra_config = {}
        if hasattr(model, 'extra_config') and model.extra_config:
            try:
                # 如果extra_config是字符串，尝试解析为JSON
                if isinstance(model.extra_config, str):
                    model_extra_config = json.loads(model.extra_config)
                elif isinstance(model.extra_config, dict):
                    model_extra_config = model.extra_config
                logger.info(f"获取到model的extra_config: {model_extra_config}")
            except Exception as e:
                logger.warning(f"解析model extra_config失败: {e}")
                model_extra_config = {}
        
        # 按优先级合并配置：默认配置 <- model配置 <- agent配置
        final_config = default_config.copy()
        final_config.update(model_extra_config)
        final_config.update(agent_llm_config)
        
        logger.info(f"最终模型配置: {final_config}") 
        return final_config

    async def _get_model_instance(self, tenant_id: str, model_id: str, agent_detail: Optional[Dict[str, Any]] = None):
        """获取模型实例"""
        logger.info(f"开始获取模型实例: tenant_id={tenant_id}, model_id={model_id}")
        
        # 使用ModelService获取模型信息
        from app.services.model import ModelService
        model_info = ModelService.get_model_info(self.db, model_id, tenant_id)
        
        if not model_info:
            error_msg = f"无法获取模型ID {model_id} 的信息，请检查模型配置"
            logger.error(error_msg)
            raise AgentServiceError(error_msg)
        
        model, provider, credential = model_info
        api_key = credential.credentials.get('api_key', None)
        masked_key = f"{api_key[:8]}...{api_key[-4:]}" if api_key and api_key != 'None' and len(api_key) > 12 else "Invalid/Short"
        logger.info(f"获取模型信息成功 - 模型: {model.key}, 提供商: {provider.key}, API密钥: {masked_key}")
        
        # 获取模型配置
        extra_config = self._get_model_config(agent_detail, model)
        
        # 使用模型工厂创建实例
        try:
            model_factory = ModelFactory()
            model_instance = model_factory.create_model_with_info(
                model=model,
                provider=provider,
                credential=credential,
                extra_config=extra_config
            )
            
            if not model_instance:
                error_msg = f"模型工厂返回None，无法为模型ID {model_id} (提供商: {provider.key}) 创建实例"
                logger.error(error_msg)
                raise AgentServiceError(error_msg)
            
            logger.info(f"✅ 成功创建流式模型实例: {model.key}")
            return model_instance
            
        except Exception as create_error:
            error_msg = f"模型工厂创建实例失败: {type(create_error).__name__}: {create_error}"
            logger.error(error_msg)
            raise AgentServiceError(error_msg)
    
    async def _get_tools(self, tool_names: List[str]) -> List[Any]:
        """获取工具列表"""
        # 这里可以根据工具名称获取具体的工具实例
        # 暂时返回空列表，后续可以扩展
        return []
    
    def _extract_knowledge_base_ids(self, agent_detail: Dict[str, Any]) -> Optional[List[str]]:
        """从Agent配置中提取知识库ID列表"""
        try:
            knowledge_base_ids = []
            
            # 方法1: 从当前版本的knowledge_base_ids字段获取
            if 'versions' in agent_detail and agent_detail['versions']:
                current_version = next((v for v in agent_detail['versions'] if v.is_current), None)
                if current_version and hasattr(current_version, 'knowledge_base_ids') and current_version.knowledge_base_ids:
                    if isinstance(current_version.knowledge_base_ids, list):
                        knowledge_base_ids = current_version.knowledge_base_ids
            
            # 方法2: 从agent的knowledge_bases关联中获取
            if not knowledge_base_ids and 'knowledge_bases' in agent_detail:
                knowledge_bases = agent_detail['knowledge_bases']
                if isinstance(knowledge_bases, list):
                    knowledge_base_ids = [kb.get('id') for kb in knowledge_bases if kb.get('id')]
            
            # 过滤和清理结果
            if knowledge_base_ids:
                knowledge_base_ids = [str(kb_id).strip() for kb_id in knowledge_base_ids if kb_id and str(kb_id).strip()]
                logger.info(f"从Agent配置中提取到知识库ID: {knowledge_base_ids}")
                return knowledge_base_ids
            else:
                logger.info("Agent未配置知识库")
                return None
                
        except Exception as e:
            logger.warning(f"提取知识库ID失败: {e}")
            return None
    
    async def clear_agent_cache(self, tenant_id: str, agent_id: Optional[str] = None):
        """清理Agent实例缓存"""
        try:
            if agent_id:
                # 清理指定Agent实例
                if tenant_id in self._agent_instances and agent_id in self._agent_instances[tenant_id]:
                    del self._agent_instances[tenant_id][agent_id]
                
                if tenant_id in self._checkpointers and agent_id in self._checkpointers[tenant_id]:
                    del self._checkpointers[tenant_id][agent_id]
                
                logger.info(f"✅ 清理Agent实例缓存: {tenant_id}/{agent_id}")
            else:
                # 清理租户的所有Agent实例
                if tenant_id in self._agent_instances:
                    del self._agent_instances[tenant_id]
                
                if tenant_id in self._checkpointers:
                    del self._checkpointers[tenant_id]
                
                logger.info(f"✅ 清理租户Agent实例缓存: {tenant_id}")
                
        except Exception as e:
            logger.error(f"清理Agent实例缓存失败: {e}")
            raise AgentServiceError(f"清理Agent实例缓存失败: {e}")
    
    async def get_cached_instances_count(self) -> Dict[str, int]:
        """获取缓存实例统计"""
        return {
            "cached_agent_instances": sum(len(agents) for agents in self._agent_instances.values()),
            "cached_checkpointers": sum(len(cps) for cps in self._checkpointers.values()),
            "active_tenants": len(self._agent_instances)
        } 