import logging
from typing import Dict, Any
from pymongo import MongoClient
from langgraph.checkpoint.mongodb.aio import AsyncMongoDBSaver
from collections import defaultdict

from .interfaces import IStorageManager
from .config import AgentServiceConfig
from .exceptions import AgentServiceError

logger = logging.getLogger(__name__)


class StorageManager(IStorageManager):
    """存储管理器 - 负责MongoDB连接和检查点存储"""
    
    def __init__(self, config: AgentServiceConfig):
        self.config = config
        
        # MongoDB连接
        self._mongo_client = None
        self._mongo_db = None
        
        # MongoDB上下文管理器缓存 {tenant_id: {agent_id: context}}
        self._mongodb_contexts: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        logger.info("✅ 存储管理器初始化完成")
    
    def get_mongo_db(self):
        """获取MongoDB数据库连接（用于会话存储和检查点）"""
        if self._mongo_client is None:
            try:
                self._mongo_client = MongoClient(self.config.mongodb_url)
                self._mongo_db = self._mongo_client[self.config.mongodb_database]
                logger.info(f"✅ MongoDB连接成功: {self.config.mongodb_database}")
            except Exception as e:
                logger.error(f"MongoDB连接失败: {e}")
                raise AgentServiceError(f"MongoDB连接失败: {e}")
        return self._mongo_db
    
    def test_mongodb_connection(self) -> bool:
        """测试MongoDB连接是否可用"""
        try:
            # 使用临时客户端测试连接
            test_client = MongoClient(self.config.mongodb_url, serverSelectionTimeoutMS=5000)
            # 尝试访问服务器信息
            test_client.server_info()
            test_client.close()
            logger.info("✅ MongoDB连接测试成功")
            return True
        except Exception as e:
            logger.warning(f"MongoDB连接测试失败: {e}")
            return False
    
    async def create_checkpointer(self, tenant_id: str, agent_id: str):
        """创建异步MongoDB检查点存储"""
        logger.info(f"创建异步MongoDB检查点存储器 for {tenant_id}/{agent_id}")
        
        try:
            # 首先测试MongoDB连接
            if not self.test_mongodb_connection():
                raise AgentServiceError("MongoDB连接不可用，无法创建检查点存储器")
            
            # 创建异步MongoDB检查点存储器
            logger.info(f"创建AsyncMongoDBSaver...")
            mongodb_context = AsyncMongoDBSaver.from_conn_string(
                self.config.mongodb_url,
                db_name=f"checkpoints_{tenant_id}"
            )
            
            # 进入异步上下文管理器
            checkpointer = await mongodb_context.__aenter__()
            
            # 设置索引（如果需要）
            if hasattr(checkpointer, 'setup'):
                await checkpointer.setup()
                logger.info(f"AsyncMongoDBSaver索引设置完成")
            
            # 保存上下文管理器以便后续清理
            if tenant_id not in self._mongodb_contexts:
                self._mongodb_contexts[tenant_id] = {}
            self._mongodb_contexts[tenant_id][agent_id] = mongodb_context
            
            logger.info(f"✅ AsyncMongoDBSaver创建成功: {tenant_id}/{agent_id}")
            return checkpointer
            
        except Exception as e:
            logger.error(f"创建AsyncMongoDBSaver失败: {e}")
            raise AgentServiceError(f"无法创建AsyncMongoDBSaver检查点存储器: {e}")
    
    def get_checkpointer_stats(self) -> Dict[str, int]:
        """获取检查点存储统计信息"""
        return {
            "total_contexts": sum(len(contexts) for contexts in self._mongodb_contexts.values()),
            "active_tenants": len(self._mongodb_contexts)
        }
    
    def cleanup_connections(self):
        """清理连接资源"""
        logger.info("开始清理存储管理器资源...")
        
        # 清理AsyncMongoDBSaver上下文
        for tenant_id, tenant_contexts in self._mongodb_contexts.items():
            for agent_id, mongodb_context in tenant_contexts.items():
                try:
                    # 所有的都是异步上下文管理器（AsyncMongoDBSaver）
                    import asyncio
                    asyncio.run(mongodb_context.__aexit__(None, None, None))
                    logger.info(f"✅ 清理AsyncMongoDBSaver上下文: {tenant_id}/{agent_id}")
                except Exception as e:
                    logger.warning(f"清理AsyncMongoDBSaver上下文失败: {tenant_id}/{agent_id}, 错误: {e}")
        
        self._mongodb_contexts.clear()
        
        # 清理MongoDB会话存储连接
        if self._mongo_client:
            self._mongo_client.close()
            self._mongo_client = None
            self._mongo_db = None
        
        logger.info("✅ 存储管理器资源清理完成") 