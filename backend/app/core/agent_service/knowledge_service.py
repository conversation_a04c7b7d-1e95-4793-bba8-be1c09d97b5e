import logging
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from app.services.knowledge import KnowledgeSearchService

from .interfaces import IKnowledgeService
from .config import AgentServiceConfig

logger = logging.getLogger(__name__)


class KnowledgeService(IKnowledgeService):
    """知识库服务 - 负责知识库检索和上下文格式化"""
    
    def __init__(self, db: Session, config: AgentServiceConfig):
        self.db = db
        self.config = config
        
        logger.info("✅ 知识库服务初始化完成")
    
    async def retrieve_knowledge(
        self, 
        tenant_id: str, 
        agent_id: str, 
        query: str, 
        knowledge_base_ids: Optional[List[str]] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """从知识库检索相关内容"""
        try:
            # 检查是否配置了知识库
            if not knowledge_base_ids or len(knowledge_base_ids) == 0:
                logger.info(f"[{agent_id}] 未配置知识库，跳过知识库检索")
                return []
            
            logger.info(f"[{agent_id}] 开始知识库检索: {query[:50]}..., 知识库数量: {len(knowledge_base_ids)}")
            
            all_results = []
            
            # 遍历每个知识库进行检索
            for kb_id in knowledge_base_ids:
                try:
                    search_result = KnowledgeSearchService.search(
                        db=self.db, 
                        query=query, 
                        tenant_id=tenant_id, 
                        kb_id=kb_id, 
                        save_search=False
                    )
                    
                    # 处理检索结果
                    if search_result and hasattr(search_result, 'results') and search_result.results:
                        for result in search_result.results[:limit]:  # 限制每个知识库的结果数量
                            formatted_result = {
                                "content": result.get("content", ""),
                                "source": result.get("source", f"knowledge_base_{kb_id}"),
                                "score": result.get("score", 0.0),
                                "metadata": result.get("metadata", {}),
                                "file": result.get("file", {})
                            }
                            all_results.append(formatted_result)
                            
                except Exception as kb_error:
                    logger.warning(f"知识库 {kb_id} 检索失败: {kb_error}")
                    continue
            
            # 按相关度排序并限制总结果数量
            all_results.sort(key=lambda x: x.get("score", 0.0), reverse=True)
            final_results = all_results[:limit]
            
            logger.info(f"[{agent_id}] 知识库检索完成，总共找到 {len(final_results)} 个结果")
            return final_results
            
        except Exception as e:
            logger.error(f"知识库检索失败: {e}")
            return []
    
    def format_knowledge_context(self, retrieval_results: List[Dict[str, Any]]) -> str:
        """格式化知识库检索结果为上下文"""
        if not retrieval_results:
            return ""
        
        context_parts = ["以下是相关的知识库内容，请参考这些信息来回答用户的问题：\n"]
        
        for i, result in enumerate(retrieval_results, 1):
            content = result.get("content", "")
            source = result.get("source", "未知来源")
            score = result.get("score", 0.0)
            
            context_parts.append(f"[知识片段 {i}] (来源: {source}, 相关度: {score:.2f})")
            context_parts.append(content)
            context_parts.append("")  # 空行分隔
        
        context_parts.append("请基于以上知识库内容回答用户问题，如果知识库中没有相关信息，请如实说明。\n")
        
        return "\n".join(context_parts)
    
    async def get_knowledge_stats(self, tenant_id: str, agent_id: Optional[str] = None) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            # TODO: 实现真实的知识库统计
            # 可以通过KnowledgeSearchService获取知识库统计信息
            return {
                "total_documents": 0,
                "indexed_documents": 0,
                "last_updated": None,
                "retrieval_enabled": True,  # 现在支持检索了
                "service_type": "KnowledgeSearchService"
            }
            
        except Exception as e:
            logger.error(f"获取知识库统计失败: {e}")
            return {
                "total_documents": 0,
                "indexed_documents": 0,
                "last_updated": None,
                "retrieval_enabled": False,
                "error": str(e)
            }
    
    async def update_knowledge_base(
        self, 
        tenant_id: str, 
        agent_id: str, 
        documents: List[Dict[str, Any]]
    ) -> bool:
        """更新知识库"""
        try:
            logger.info(f"[{agent_id}] 开始更新知识库，文档数量: {len(documents)}")
            
            # TODO: 实现真实的知识库更新逻辑
            # 这里只是模拟
            logger.info(f"[{agent_id}] 知识库更新完成")
            return True
            
        except Exception as e:
            logger.error(f"更新知识库失败: {e}")
            return False 