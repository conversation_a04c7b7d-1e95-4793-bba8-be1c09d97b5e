from typing import Dict, List, Optional, Any, AsyncIterator
from datetime import datetime, timedelta
import logging
from sqlalchemy.orm import Session

from .config import AgentServiceConfig
from .types import SessionStatus, SessionInfo
from .exceptions import AgentServiceError

# 导入重构后的组件
from .storage_manager import StorageManager
from .session_manager import SessionManager
from .knowledge_service import KnowledgeService
from .workflow_engine import WorkflowEngine
from .agent_instance_manager import AgentInstanceManager
from .chat_handler import ChatHandler

logger = logging.getLogger(__name__)


class MultiTenantAgentService:
    """多租户Agent执行服务 - 重构后的主协调器"""
    
    def __init__(self, db: Session, config: Optional[AgentServiceConfig] = None):
        self.db = db
        self.config = config or AgentServiceConfig()
        
        # 初始化各个组件
        self.storage_manager = StorageManager(self.config)
        self.session_manager = SessionManager(db, self.storage_manager)
        self.knowledge_service = KnowledgeService(db, self.config)
        self.workflow_engine = WorkflowEngine(self.config, self.knowledge_service)
        self.agent_manager = AgentInstanceManager(
            db, self.config, self.workflow_engine, self.storage_manager
        )
        self.chat_handler = ChatHandler(
            self.agent_manager,
            self.session_manager,
            self.knowledge_service,
            self.config
        )
        
        logger.info("✅ 多租户Agent执行服务初始化完成（重构版本）")
    
    # ==================== Agent实例管理 ====================
    
    async def get_agent_instance(self, tenant_id: str, agent_id: str):
        """获取或创建Agent实例"""
        return await self.agent_manager.get_agent_instance(tenant_id, agent_id)
    
    async def clear_agent_instance_cache(self, tenant_id: str, agent_id: Optional[str] = None):
        """清理Agent实例缓存"""
        return await self.agent_manager.clear_agent_cache(tenant_id, agent_id)
    
    # ==================== 会话管理 ====================
    
    async def create_session(
        self, 
        tenant_id: str, 
        agent_id: str, 
        thread_id: str,
        user_id: Optional[str] = None,
        user_name: Optional[str] = None
    ) -> str:
        """创建会话"""
        return await self.session_manager.create_session(
            tenant_id, agent_id, thread_id, user_id, user_name
        )
    
    async def get_session(self, tenant_id: str, session_id: str) -> Optional[SessionInfo]:
        """获取会话信息"""
        return await self.session_manager.get_session(tenant_id, session_id)
    
    async def list_sessions(
        self, 
        tenant_id: str, 
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        status: Optional[SessionStatus] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> List[SessionInfo]:
        """列出会话"""
        return await self.session_manager.list_sessions(
            tenant_id, agent_id, user_id, status, skip, limit
        )
    
    # ==================== 对话功能 ====================
    
    async def chat_with_agent(
        self,
        tenant_id: str,
        agent_id: str,
        message: str,
        thread_id: str = "default",
        user_id: Optional[str] = None,
        user_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """与Agent进行对话"""
        return await self.chat_handler.handle_chat(
            tenant_id, agent_id, message, thread_id, user_id, user_name
        )
    
    async def chat_with_agent_stream(
        self,
        tenant_id: str,
        agent_id: str,
        message: str,
        thread_id: str = "default",
        user_id: Optional[str] = None,
        user_name: Optional[str] = None
    ) -> AsyncIterator[Dict[str, Any]]:
        """与Agent进行流式对话"""
        async for chunk in self.chat_handler.handle_stream_chat(
            tenant_id, agent_id, message, thread_id, user_id, user_name
        ):
            yield chunk
    
    # ==================== 维护功能 ====================
    
    async def cleanup_expired_sessions(self, tenant_id: Optional[str] = None, expiry_hours: int = 24) -> int:
        """清理过期会话"""
        return await self.session_manager.cleanup_expired_sessions(tenant_id, expiry_hours)
    
    async def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        try:
            # 获取各组件的统计信息
            agent_stats = await self.agent_manager.get_cached_instances_count()
            session_stats = await self.session_manager.get_session_stats()
            storage_stats = self.storage_manager.get_checkpointer_stats()
            knowledge_stats = await self.knowledge_service.get_knowledge_stats("", None)
            
            return {
                **agent_stats,
                **session_stats,
                **storage_stats,
                **knowledge_stats,
                "checkpoint_type": "AsyncMongoDBSaver"
            }
            
        except Exception as e:
            logger.error(f"获取执行统计信息失败: {e}")
            raise AgentServiceError(f"获取执行统计信息失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        logger.info("开始清理多租户Agent执行服务资源...")
        
        try:
            # 清理存储管理器
            self.storage_manager.cleanup_connections()
            logger.info("✅ 多租户Agent执行服务资源清理完成（重构版本）")
        except Exception as e:
            logger.error(f"资源清理失败: {e}") 