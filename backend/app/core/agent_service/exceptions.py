class AgentServiceError(Exception):
    """Agent服务基础异常类"""
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class TenantNotFoundError(AgentServiceError):
    """租户不存在异常"""
    def __init__(self, tenant_id: str):
        super().__init__(f"租户 {tenant_id} 不存在", "TENANT_NOT_FOUND")
        self.tenant_id = tenant_id

class AgentNotFoundError(AgentServiceError):
    """Agent不存在异常"""
    def __init__(self, agent_id: str, tenant_id: str = None):
        message = f"Agent {agent_id} 不存在"
        if tenant_id:
            message += f" (租户: {tenant_id})"
        super().__init__(message, "AGENT_NOT_FOUND")
        self.agent_id = agent_id
        self.tenant_id = tenant_id

class SessionNotFoundError(AgentServiceError):
    """会话不存在异常"""
    def __init__(self, session_id: str):
        super().__init__(f"会话 {session_id} 不存在", "SESSION_NOT_FOUND")
        self.session_id = session_id

class UserNotFoundError(AgentServiceError):
    """用户不存在异常"""
    def __init__(self, user_id: str):
        super().__init__(f"用户 {user_id} 不存在", "USER_NOT_FOUND")
        self.user_id = user_id

class TenantLimitExceededError(AgentServiceError):
    """租户限制超出异常"""
    def __init__(self, tenant_id: str, limit_type: str, current: int, max_limit: int):
        message = f"租户 {tenant_id} 的 {limit_type} 已达到限制: {current}/{max_limit}"
        super().__init__(message, "TENANT_LIMIT_EXCEEDED")
        self.tenant_id = tenant_id
        self.limit_type = limit_type
        self.current = current
        self.max_limit = max_limit

class AgentTypeNotAllowedError(AgentServiceError):
    """Agent类型不允许异常"""
    def __init__(self, tenant_id: str, agent_type: str):
        super().__init__(f"租户 {tenant_id} 不允许创建 {agent_type} 类型的Agent", "AGENT_TYPE_NOT_ALLOWED")
        self.tenant_id = tenant_id
        self.agent_type = agent_type

class DatabaseOperationError(AgentServiceError):
    """数据库操作异常"""
    def __init__(self, operation: str, details: str = None):
        message = f"数据库操作失败: {operation}"
        if details:
            message += f" - {details}"
        super().__init__(message, "DATABASE_OPERATION_ERROR")
        self.operation = operation
        self.details = details 