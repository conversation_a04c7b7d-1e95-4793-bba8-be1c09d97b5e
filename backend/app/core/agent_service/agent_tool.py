from typing import List
import logging
from sqlalchemy.orm import Session
from app.models.tool import Too<PERSON>, TenantTool, MCPServer
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_core.tools import BaseTool
from langchain_core.tools import tool
from app.core.platform_tool import PlatformToolService
from app.services.tool import MCPServerService
logger = logging.getLogger(__name__)

class AgentToolService:
    """Agent工具服务"""


    @staticmethod
    async def get_agent_tools(db: Session, tenant_id: str, tool_ids:List[str], mcp_server_ids: List[str]) -> List[BaseTool]:
        """获取Agent工具配置列表"""
    
        ## 从数据库中获取工具配置
        tenant_tools = db.query(TenantTool).filter(
            TenantTool.tenant_id == tenant_id,
            TenantTool.tool_id.in_(tool_ids)
        ).all()
        mcp_servers = db.query(MCPServer).filter(
            MCPServer.tenant_id == tenant_id,
            MCPServer.id.in_(mcp_server_ids),
            MCPServer.status == "active"
        ).all()
        
        # 构建工具列表
        tools = []
        for tenant_tool in tenant_tools:
            tool_func = PlatformToolService.get_tool(tenant_tool.tool_id, db, tenant_id)
        
            if not tool_func:
                continue
          
            tools.extend(tool_func)
        # 处理MCP服务器配置（）
        server_configs = {}
        for agent_mcp_server in mcp_servers:
            # 根据服务器类型和认证方式拼接配置
            server_config =MCPServerService.get_mcp_server_config(agent_mcp_server.endpoint, agent_mcp_server.transport_type, agent_mcp_server.meta_data)
            if not server_config:
                continue
            # 以服务器名称作为配置键
            server_configs[agent_mcp_server.name +'_'+ agent_mcp_server.id] = server_config
        
        # 如果有MCP服务器配置，创建客户端并获取工具（假设 MultiServerMCPClient 能返回 BaseTool 列表）
        logger.info(server_configs)
        if server_configs:
            try:
                client = MultiServerMCPClient(server_configs)
                mcp_tools = await client.get_tools()  # 异步调用
                tools.extend(mcp_tools)
                return tools
            except Exception as e:
                logger.error(e)
                return tools  
        print(tools.__len__())
        return tools
    

    
    @staticmethod
    def check_tenant_tool_is_available(db: Session, tenant_id: str, tool_ids: List[str]) -> bool:
        """检查租户工具是否可用"""
        
        tenant_tools = db.query(TenantTool).filter(
            TenantTool.tenant_id == tenant_id,
            TenantTool.tool_id.in_(tool_ids),
            TenantTool.is_active == True
        ).all()
        return len(tenant_tools) == len(tool_ids)

    @staticmethod
    def get_agent_tool(db: Session, tenant_id: str, tool_ids: List[str]) -> List[TenantTool]:
        """获取Agent工具配置列表"""
       
        # 从数据库中获取工具配置
        return db.query(TenantTool).filter(
            TenantTool.tenant_id == tenant_id,
            TenantTool.tool_id.in_(tool_ids)
        ).all()
        
    @staticmethod
    def get_agent_mcp_server(db: Session, tenant_id: str, mcp_server_ids: List[str]) -> List[MCPServer]:
        """获取Agent MCP服务器配置列表"""
        
        # 从数据库中获取MCP服务器配置
        return db.query(MCPServer).filter(
            MCPServer.tenant_id == tenant_id,
            MCPServer.id.in_(mcp_server_ids)
        ).all()       
    @staticmethod
    def check_mcp_server_is_available(db: Session, tenant_id: str, mcp_server_ids: List[str]) -> bool:
        """检查MCP服务器是否可用"""
        
        mcp_servers = db.query(MCPServer).filter(
            MCPServer.id.in_(mcp_server_ids),
            MCPServer.tenant_id == tenant_id,
            MCPServer.status == "active"
        ).all()
        return len(mcp_servers) == len(mcp_server_ids)
