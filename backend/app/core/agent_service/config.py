from dataclasses import dataclass, field
import os

@dataclass
class AgentServiceConfig:
    """Agent执行服务配置"""
    # MongoDB配置
    mongodb_url: str = field(default_factory=lambda: os.getenv("MONGODB_URL", "*****************************************************"))
    mongodb_database: str = field(default_factory=lambda: os.getenv("MONGODB_DATABASE", "agent_service"))
    
    # 默认限制
    default_max_sessions_per_agent: int = 100
    default_session_timeout: int = 3600  # 秒
    
    # 清理配置
    session_cleanup_interval: int = 3600  # 秒，会话清理间隔
    session_expiry_hours: int = 24  # 小时，会话过期时间
    
    # 性能配置
    enable_caching: bool = True
    cache_ttl: int = 300  # 缓存TTL，秒
    max_concurrent_sessions: int = 1000
    
    # 安全配置
    enable_tenant_isolation: bool = True
    enable_rate_limiting: bool = True
    max_requests_per_minute: int = 60 