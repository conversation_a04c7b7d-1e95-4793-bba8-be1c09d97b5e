import logging
from typing import Dict, List, Optional, Any, Annotated
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.store.memory import InMemoryStore
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, BaseMessage, ToolMessage
from langchain_core.runnables import RunnableConfig

from .interfaces import IWorkflowEngine
from .config import AgentServiceConfig

logger = logging.getLogger(__name__)

# 定义Agent状态结构
class AgentState(dict):
    """Agent执行状态"""
    messages: Annotated[List[BaseMessage], add_messages]
    next_action: Optional[str]
    intermediate_steps: List[Dict[str, Any]]
    agent_id: str  
    tenant_id: str
    thread_id: str
    user_id: Optional[str]
    knowledge_context: Optional[str]  # 知识库检索的上下文
    retrieval_query: Optional[str]    # 检索查询
    retrieval_results: List[Dict[str, Any]]  # 检索结果


class WorkflowEngine(IWorkflowEngine):
    """工作流引擎 - 负责LangGraph工作流的创建和编译"""
    
    def __init__(self, config: AgentServiceConfig, knowledge_service):
        self.config = config
        self.knowledge_service = knowledge_service
        
        logger.info("✅ 工作流引擎初始化完成")
    
    async def create_workflow(
        self,
        model_instance: Any,
        tools: List[Any],
        checkpointer: Any,
        store: Any,
        system_prompt: str,
        tenant_id: str,
        agent_id: str,
        knowledge_base_ids: Optional[List[str]] = None
    ):
        """创建LangGraph工作流"""
        
        # 节点函数定义
        async def input_processor_node(state: AgentState, config: RunnableConfig) -> AgentState:
            """输入处理节点"""
            logger.info(f"[{agent_id}] 输入处理节点执行")
            
            # 如果没有系统消息，添加系统消息
            messages = state["messages"]
            if not any(isinstance(msg, SystemMessage) for msg in messages):
                system_message = SystemMessage(content=system_prompt)
                messages = [system_message] + messages
            
            # 提取用户查询用于知识库检索
            user_messages = [msg for msg in messages if isinstance(msg, HumanMessage)]
            retrieval_query = user_messages[-1].content if user_messages else ""
            
            # 根据是否配置知识库决定下一步行动
            has_knowledge_base = knowledge_base_ids and len(knowledge_base_ids) > 0
            should_retrieve = retrieval_query and has_knowledge_base
                
            return {
                **state,
                "messages": messages,
                "retrieval_query": retrieval_query,
                "next_action": "knowledge_retrieval" if should_retrieve else ("model_call" if not tools else "model_call_with_tools")
            }
        
        async def knowledge_retrieval_node(state: AgentState, config: RunnableConfig) -> AgentState:
            """知识库检索节点"""
            logger.info(f"[{agent_id}] 知识库检索节点执行")
            
            query = state.get("retrieval_query", "")
            if not query:
                # 如果没有查询，跳过知识库检索
                return {
                    **state,
                    "next_action": "model_call" if not tools else "model_call_with_tools"
                }
            
            try:
                # 执行知识库检索
                retrieval_results = await self.knowledge_service.retrieve_knowledge(
                    tenant_id=state["tenant_id"],
                    agent_id=state["agent_id"], 
                    query=query,
                    knowledge_base_ids=knowledge_base_ids,
                    limit=5
                )
                
                # 格式化知识库上下文
                knowledge_context = self.knowledge_service.format_knowledge_context(retrieval_results)
                
                # 如果有知识库内容，将其添加到消息中
                updated_messages = state["messages"]
                if knowledge_context:
                    # 将知识库上下文合并到现有的系统消息中，而不是创建新的系统消息
                    system_msgs = [msg for msg in updated_messages if isinstance(msg, SystemMessage)]
                    other_msgs = [msg for msg in updated_messages if not isinstance(msg, SystemMessage)]
                    
                    # 如果已有系统消息，将知识库上下文添加到第一个系统消息中
                    if system_msgs:
                        # 合并知识库上下文到现有系统消息
                        first_system_msg = system_msgs[0]
                        combined_content = first_system_msg.content + "\n\n=== 知识库上下文 ===\n" + knowledge_context
                        updated_system_msg = SystemMessage(content=combined_content)
                        
                        # 保持系统消息在最前面，只保留一个系统消息
                        updated_messages = [updated_system_msg] + other_msgs
                    else:
                        # 如果没有系统消息，创建一个包含知识库上下文的系统消息
                        knowledge_msg = SystemMessage(content="=== 知识库上下文 ===\n" + knowledge_context)
                        updated_messages = [knowledge_msg] + other_msgs
                
                return {
                    **state,
                    "messages": updated_messages,
                    "knowledge_context": knowledge_context,
                    "retrieval_results": retrieval_results,
                    "next_action": "model_call" if not tools else "model_call_with_tools"
                }
                
            except Exception as e:
                logger.error(f"知识库检索节点失败: {e}")
                # 检索失败时继续执行，不中断流程
                return {
                    **state,
                    "retrieval_results": [],
                    "knowledge_context": "",
                    "next_action": "model_call" if not tools else "model_call_with_tools"
                }
        
        async def model_call_node(state: AgentState, config: RunnableConfig) -> AgentState:
            """模型调用节点（无工具）"""
            logger.info(f"[{agent_id}] 模型调用节点执行")
            
            try:
                # 调用模型
                response = await model_instance.ainvoke(state["messages"])
                
                # 添加响应消息
                updated_messages = state["messages"] + [response]
                
                return {
                    **state,
                    "messages": updated_messages,
                    "next_action": None  # 结束
                }
            except Exception as e:
                logger.error(f"模型调用失败: {e}")
                error_message = AIMessage(content=f"抱歉，我遇到了一个错误：{str(e)}")
                return {
                    **state,
                    "messages": state["messages"] + [error_message],
                    "next_action": None
                }
        
        async def model_call_with_tools_node(state: AgentState, config: RunnableConfig) -> AgentState:
            """带工具的模型调用节点"""
            logger.info(f"[{agent_id}] 带工具的模型调用节点执行")
            
            try:
                # 绑定工具到模型
                model_with_tools = model_instance.bind_tools(tools) if tools else model_instance
                
                # 调用模型
                response = await model_with_tools.ainvoke(state["messages"])
                
                # 添加响应消息
                updated_messages = state["messages"] + [response]
                
                # 检查是否需要调用工具
                if hasattr(response, 'tool_calls') and response.tool_calls:
                    return {
                        **state,
                        "messages": updated_messages,
                        "next_action": "tool_call"
                    }
                else:
                    return {
                        **state,
                        "messages": updated_messages,
                        "next_action": None  # 结束
                    }
            except Exception as e:
                print(e)
                logger.error(f"模型调用失败: {e}")
                error_message = AIMessage(content=f"抱歉，我遇到了一个错误：{str(e)}")
                return {
                    **state,
                    "messages": state["messages"] + [error_message],
                    "next_action": None
                }
        
        async def tool_call_node(state: AgentState, config: RunnableConfig) -> AgentState:
            """工具调用节点"""
            logger.info(f"[{agent_id}] 工具调用节点执行")
            
            try:
                last_message = state["messages"][-1]
                
                if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
                    return {
                        **state,
                        "next_action": None
                    }
                
                # 创建工具映射
                tool_map = {tool.name: tool for tool in tools}
                
                tool_messages = []
                for tool_call in last_message.tool_calls:
                    tool_name = tool_call["name"]
                    tool_args = tool_call["args"]
                    tool_id = tool_call["id"]
                    
                    if tool_name in tool_map:
                        try:
                            tool_result = await tool_map[tool_name].ainvoke(tool_args)
                            tool_message = ToolMessage(
                                content=str(tool_result),
                                tool_call_id=tool_id
                            )
                            tool_messages.append(tool_message)
                            
                            # 记录工具调用步骤
                            state["intermediate_steps"].append({
                                "tool": tool_name,
                                "input": tool_args,
                                "output": tool_result
                            })
                        except Exception as tool_error:
                            logger.error(f"工具调用失败 {tool_name}: {tool_error}")
                            error_message = ToolMessage(
                                content=f"工具调用失败: {str(tool_error)}",
                                tool_call_id=tool_id
                            )
                            tool_messages.append(error_message)
                    else:
                        logger.warning(f"未找到工具: {tool_name}")
                        error_message = ToolMessage(
                            content=f"未找到工具: {tool_name}",
                            tool_call_id=tool_id
                        )
                        tool_messages.append(error_message)
                
                # 添加工具消息
                updated_messages = state["messages"] + tool_messages
                
                return {
                    **state,
                    "messages": updated_messages,
                    "next_action": "model_call_with_tools"  # 回到模型调用
                }
            except Exception as e:
                logger.error(f"工具调用节点失败: {e}")
                error_message = AIMessage(content=f"工具调用过程中发生错误：{str(e)}")
                return {
                    **state,
                    "messages": state["messages"] + [error_message],
                    "next_action": None
                }
        
        # 路由函数
        def router(state: AgentState) -> str:
            """路由函数，决定下一个节点"""
            next_action = state.get("next_action")
            
            if next_action == "knowledge_retrieval":
                return "knowledge_retrieval"
            elif next_action == "model_call":
                return "model_call"
            elif next_action == "model_call_with_tools":
                return "model_call_with_tools"
            elif next_action == "tool_call":
                return "tool_call"
            else:
                return END
        
        # 构建工作流图
        workflow = StateGraph(AgentState)
        
        # 添加节点
        workflow.add_node("input_processor", input_processor_node)
        workflow.add_node("knowledge_retrieval", knowledge_retrieval_node)
        workflow.add_node("model_call", model_call_node)
        workflow.add_node("model_call_with_tools", model_call_with_tools_node)
        workflow.add_node("tool_call", tool_call_node)
        
        # 设置入口点
        workflow.set_entry_point("input_processor")
        
        # 添加边
        workflow.add_conditional_edges("input_processor", router)
        workflow.add_conditional_edges("knowledge_retrieval", router)
        workflow.add_conditional_edges("model_call", router)
        workflow.add_conditional_edges("model_call_with_tools", router)
        workflow.add_conditional_edges("tool_call", router)
        
        # 编译工作流
        compiled_workflow = workflow.compile(
            checkpointer=checkpointer,
            store=store
        )
        
        logger.info(f"✅ LangGraph工作流编译完成: {tenant_id}/{agent_id}")
        return compiled_workflow 