from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, AsyncIterator
from sqlalchemy.orm import Session

from .types import SessionInfo, SessionStatus
from .config import AgentServiceConfig


class IAgentInstanceManager(ABC):
    """Agent实例管理器接口"""
    
    @abstractmethod
    async def get_agent_instance(self, tenant_id: str, agent_id: str):
        """获取或创建Agent实例"""
        pass
    
    @abstractmethod
    async def clear_agent_cache(self, tenant_id: str, agent_id: Optional[str] = None):
        """清理Agent实例缓存"""
        pass
    
    @abstractmethod
    async def get_cached_instances_count(self) -> Dict[str, int]:
        """获取缓存实例统计"""
        pass


class ISessionManager(ABC):
    """会话管理器接口"""
    
    @abstractmethod
    async def create_session(
        self, 
        tenant_id: str, 
        agent_id: str, 
        thread_id: str,
        user_id: Optional[str] = None,
        user_name: Optional[str] = None
    ) -> str:
        """创建会话"""
        pass
    
    @abstractmethod
    async def get_session(self, tenant_id: str, session_id: str) -> Optional[SessionInfo]:
        """获取会话信息"""
        pass
    
    @abstractmethod
    async def list_sessions(
        self, 
        tenant_id: str, 
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        status: Optional[SessionStatus] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> List[SessionInfo]:
        """列出会话"""
        pass
    
    @abstractmethod
    async def update_session_activity(self, tenant_id: str, session_id: str):
        """更新会话活跃时间"""
        pass
    
    @abstractmethod
    async def cleanup_expired_sessions(self, tenant_id: Optional[str] = None, expiry_hours: int = 24) -> int:
        """清理过期会话"""
        pass


class IKnowledgeService(ABC):
    """知识库服务接口"""
    
    @abstractmethod
    async def retrieve_knowledge(
        self, 
        tenant_id: str, 
        agent_id: str, 
        query: str, 
        knowledge_base_ids: Optional[List[str]] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """从知识库检索相关内容"""
        pass
    
    @abstractmethod
    def format_knowledge_context(self, retrieval_results: List[Dict[str, Any]]) -> str:
        """格式化知识库检索结果为上下文"""
        pass


class IWorkflowEngine(ABC):
    """工作流引擎接口"""
    
    @abstractmethod
    async def create_workflow(
        self,
        model_instance: Any,
        tools: List[Any],
        checkpointer: Any,
        store: Any,
        system_prompt: str,
        tenant_id: str,
        agent_id: str,
        knowledge_base_ids: Optional[List[str]] = None
    ):
        """创建LangGraph工作流"""
        pass


class IStorageManager(ABC):
    """存储管理器接口"""
    
    @abstractmethod
    def get_mongo_db(self):
        """获取MongoDB数据库连接"""
        pass
    
    @abstractmethod
    async def create_checkpointer(self, tenant_id: str, agent_id: str):
        """创建检查点存储"""
        pass
    
    @abstractmethod
    def test_mongodb_connection(self) -> bool:
        """测试MongoDB连接"""
        pass
    
    @abstractmethod
    def cleanup_connections(self):
        """清理连接资源"""
        pass


class IChatHandler(ABC):
    """对话处理器接口"""
    
    @abstractmethod
    async def handle_chat(
        self,
        tenant_id: str,
        agent_id: str,
        message: str,
        thread_id: str = "default",
        user_id: Optional[str] = None,
        user_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """处理普通对话"""
        pass
    
    @abstractmethod
    async def handle_stream_chat(
        self,
        tenant_id: str,
        agent_id: str,
        message: str,
        thread_id: str = "default",
        user_id: Optional[str] = None,
        user_name: Optional[str] = None
    ) -> AsyncIterator[Dict[str, Any]]:
        """处理流式对话"""
        pass 