from typing import Optional
import logging
from sqlalchemy.orm import Session

from .service import MultiTenantAgentService
from .config import AgentServiceConfig

logger = logging.getLogger(__name__)

class AgentServiceFactory:
    """Agent执行服务工厂类"""
    
    _instance: Optional[MultiTenantAgentService] = None
    _config: Optional[AgentServiceConfig] = None
    
    @classmethod
    def create_service(cls, db: Session, config: Optional[AgentServiceConfig] = None) -> MultiTenantAgentService:
        """创建或获取Agent执行服务实例"""
        if cls._instance is None:
            cls._config = config or AgentServiceConfig()
            cls._instance = MultiTenantAgentService(db=db, config=cls._config)
            logger.info("✅ 创建新的多租户Agent执行服务实例")
        return cls._instance
    
    @classmethod
    def get_service(cls) -> Optional[MultiTenantAgentService]:
        """获取当前的Agent执行服务实例"""
        return cls._instance
    
    @classmethod
    def cleanup_service(cls):
        """清理Agent执行服务实例"""
        if cls._instance:
            cls._instance.cleanup()
            cls._instance = None
            cls._config = None
            logger.info("✅ 清理多租户Agent执行服务实例")

# 全局服务获取函数
def get_agent_service() -> Optional[MultiTenantAgentService]:
    """获取全局Agent执行服务实例"""
    return AgentServiceFactory.get_service()

async def ensure_agent_service_initialized(db: Session, config: Optional[AgentServiceConfig] = None) -> MultiTenantAgentService:
    """确保Agent执行服务已初始化"""
    service = AgentServiceFactory.create_service(db, config)
    logger.info("✅ Agent执行服务初始化完成")
    return service

# 保留这个函数以便向后兼容，但实际上不再创建默认数据
async def initialize_default_tenants_and_agents(service: MultiTenantAgentService):
    """
    初始化默认租户和Agent（保留以便向后兼容）
    
    注意：由于当前服务专注于执行逻辑，不再负责租户和Agent的创建。
    这些操作应该通过现有的TenantService和AgentService完成。
    """
    logger.info("ℹ️  当前服务专注于Agent执行，租户和Agent的创建请使用对应的Service") 