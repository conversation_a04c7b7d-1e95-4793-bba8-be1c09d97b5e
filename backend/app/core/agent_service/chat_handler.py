import logging
from typing import Dict, List, Optional, Any, AsyncIterator
from langchain_core.messages import HumanMessage
from datetime import datetime

from .interfaces import (
    IChatHandler, IAgentInstanceManager, ISessionManager, IKnowledgeService
)
from .config import AgentServiceConfig
from .exceptions import AgentServiceError
from .workflow_engine import AgentState

logger = logging.getLogger(__name__)


class ChatHandler(IChatHandler):
    """对话处理器 - 负责普通对话和流式对话的处理"""
    
    def __init__(
        self,
        agent_manager: IAgentInstanceManager,
        session_manager: ISessionManager,
        knowledge_service: IKnowledgeService,
        config: AgentServiceConfig
    ):
        self.agent_manager = agent_manager
        self.session_manager = session_manager
        self.knowledge_service = knowledge_service
        self.config = config
        
        logger.info("✅ 对话处理器初始化完成")
    
    async def handle_chat(
        self,
        tenant_id: str,
        agent_id: str,
        message: str,
        thread_id: str = "default",
        user_id: Optional[str] = None,
        user_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """处理普通对话"""
        try:
            # 获取Agent实例
            agent = await self.agent_manager.get_agent_instance(tenant_id, agent_id)
            
            # 创建或更新会话
            session_id = await self.session_manager.create_session(
                tenant_id, agent_id, thread_id, user_id, user_name
            )
            
            # 配置
            config = {"configurable": {"thread_id": session_id}}
            
            # 准备输入状态（LangGraph Workflows格式）
            input_state = {
                "messages": [HumanMessage(content=message)],
                "next_action": None,
                "intermediate_steps": [],
                "agent_id": agent_id,
                "tenant_id": tenant_id,
                "thread_id": thread_id,
                "user_id": user_id,
                "knowledge_context": None,
                "retrieval_query": None,
                "retrieval_results": []
            }
            
            # 调用Agent
            response = agent.invoke(input_state, config)
            
            # 更新会话活跃时间
            await self.session_manager.update_session_activity(tenant_id, session_id)
            
            # 提取响应内容
            last_message = response["messages"][-1]
            
            return {
                "success": True,
                "response": last_message.content,
                "session_id": session_id,
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id
            }
            
        except Exception as e:
            if isinstance(e, AgentServiceError):
                raise
            logger.error(f"与Agent对话失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id
            }
    
    async def handle_stream_chat(
        self,
        tenant_id: str,
        agent_id: str,
        message: str,
        thread_id: str = "default",
        user_id: Optional[str] = None,
        user_name: Optional[str] = None
    ) -> AsyncIterator[Dict[str, Any]]:
        """处理流式对话"""
        try:
            logger.info(f"开始真正的流式对话: {tenant_id}/{agent_id}, 消息: {message[:50]}...")
            
            # 获取Agent实例
            agent = await self.agent_manager.get_agent_instance(tenant_id, agent_id)
            logger.info(f"获取到流式Agent实例: {type(agent)}")
            
            # 创建或更新会话
            session_id = await self.session_manager.create_session(
                tenant_id, agent_id, thread_id, user_id, user_name
            )
            logger.info(f"创建会话成功: {session_id}")
            
            # 配置流式返回
            config = {
                "configurable": {
                    "thread_id": session_id
                }
            }
            
            # 准备输入状态（LangGraph Workflows格式）
            input_state = {
                "messages": [HumanMessage(content=message)],
                "next_action": None,
                "intermediate_steps": [],
                "agent_id": agent_id,
                "tenant_id": tenant_id,
                "thread_id": thread_id,
                "user_id": user_id,
                "knowledge_context": None,
                "retrieval_query": None,
                "retrieval_results": []
            }            
            
            # 真正的流式调用Agent
            chunk_count = 0
            content_buffer = ""
            
            try:
                async for chunk in agent.astream(input_state, config, stream_mode="messages"):
                    chunk_count += 1
                    # 处理stream_mode="messages"返回的元组格式: (message, metadata)
                    if isinstance(chunk, tuple) and len(chunk) == 2:
                        message_obj, metadata = chunk
                        
                        # 简化：如果有内容就直接发送
                        if hasattr(message_obj, 'content'):
                            content = message_obj.content or ""
                            if content == "":
                                continue
                            # 直接发送内容，不做复杂的增量计算
                            yield {
                                "type": "content",
                                "data": content,
                                "session_id": session_id,
                                "tenant_id": tenant_id,
                                "agent_id": agent_id,
                                "thread_id": thread_id,
                                "user_id": user_id,
                                "message_type": message_obj.__class__.__name__,
                                "metadata": {
                                    "step": metadata.get('langgraph_step'),
                                    "node": metadata.get('langgraph_node'),
                                    "model": metadata.get('ls_model_name')
                                }
                            }
                            
                            # 更新内容缓冲区
                            if content:
                                content_buffer = content
                    
                    # 处理字典格式
                    elif isinstance(chunk, dict):
                        # 处理工具调用
                        if "tools" in chunk:
                            yield {
                                "type": "tool_call",
                                "data": str(chunk["tools"]),
                                "session_id": session_id,
                                "tenant_id": tenant_id,
                                "agent_id": agent_id,
                                "thread_id": thread_id,
                                "user_id": user_id
                            }
                        
                        # 处理知识库检索
                        elif "knowledge_retrieval" in chunk:
                            kb_data = chunk["knowledge_retrieval"]
                            if isinstance(kb_data, dict) and "retrieval_results" in kb_data:
                                results_count = len(kb_data.get("retrieval_results", []))
                                yield {
                                    "type": "knowledge_retrieval",
                                    "data": f"正在检索知识库... 找到 {results_count} 个相关文档",
                                    "retrieval_count": results_count,
                                    "session_id": session_id,
                                    "tenant_id": tenant_id,
                                    "agent_id": agent_id,
                                    "thread_id": thread_id,
                                    "user_id": user_id
                                }
                        
                        # 处理agent步骤
                        elif "agent" in chunk:
                            agent_data = chunk["agent"]
                            if isinstance(agent_data, dict) and "messages" in agent_data:
                                for message_obj in agent_data["messages"]:
                                    if hasattr(message_obj, 'content') and message_obj.content:
                                        yield {
                                            "type": "content",
                                            "data": message_obj.content,
                                            "session_id": session_id,
                                            "tenant_id": tenant_id,
                                            "agent_id": agent_id,
                                            "thread_id": thread_id,
                                            "user_id": user_id,
                                            "message_type": message_obj.__class__.__name__
                                        }
                                        content_buffer = message_obj.content
                        
                        # 处理其他步骤
                        else:
                            step_info = list(chunk.keys())[0] if chunk.keys() else "unknown"
                            yield {
                                "type": "step",
                                "data": f"执行步骤: {step_info}",
                                "session_id": session_id,
                                "tenant_id": tenant_id,
                                "agent_id": agent_id,
                                "thread_id": thread_id,
                                "user_id": user_id
                            }
                    
                    # 处理其他未知格式
                    else:
                        yield {
                            "type": "debug",
                            "data": f"未知chunk格式: {type(chunk)}",
                            "session_id": session_id,
                            "tenant_id": tenant_id,
                            "agent_id": agent_id,
                            "thread_id": thread_id,
                            "user_id": user_id
                        }
                            
            except Exception as stream_error:
                logger.error(f"流式Agent调用失败: {type(stream_error).__name__}: {stream_error}")
                raise stream_error
            
            logger.info(f"真正的流式调用完成，总共处理 {chunk_count} 个chunk")
            
            # 更新会话活跃时间
            try:
                await self.session_manager.update_session_activity(tenant_id, session_id)
            except Exception as mongo_error:
                logger.warning(f"更新会话失败，但继续执行: {mongo_error}")
            
            # 发送完成信号
            yield {
                "type": "done",
                "data": "流式对话完成",
                "final_content": content_buffer,
                "total_chunks": chunk_count,
                "session_id": session_id,
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"流式对话失败: {type(e).__name__}: {e}", exc_info=True)
            
            if isinstance(e, AgentServiceError):
                error_message = e.message
            else:
                error_message = str(e)
            
            yield {
                "type": "error",
                "data": error_message,
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id
            } 