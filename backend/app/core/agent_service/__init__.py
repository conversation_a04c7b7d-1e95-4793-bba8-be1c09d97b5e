"""
多租户Agent执行服务

基于LangGraph和FastAPI构建的企业级AI Agent执行平台
专注于Agent实例管理、对话执行和会话持久化

核心特性:
- Agent实例管理和缓存
- 对话执行（普通和流式）
- 会话状态管理（MongoDB存储）
- 检查点管理（MongoDBSaver）
- 资源清理和监控
- 异步API设计

快速开始:
    from app.core.agent_service import (
        ensure_agent_service_initialized,
        ChatAPI, SessionAPI
    )
    
    # 初始化服务
    service = await ensure_agent_service_initialized(db)
    
    # 与Agent对话
    response = await service.chat_with_agent("tenant_id", "agent_id", "你好")
"""

from .service import MultiTenantAgentService
from .factory import (
    AgentServiceFactory, 
    ensure_agent_service_initialized, 
    initialize_default_tenants_and_agents,
    get_agent_service
)
from .config import (
    AgentServiceConfig
)
from .types import (
    AgentType, 
    SessionStatus, 
    SessionInfo
)
from .exceptions import (
    AgentServiceError, 
    TenantNotFoundError, 
    AgentNotFoundError,
    SessionNotFoundError, 
    DatabaseOperationError
)

# 导出重构后的组件（可选，用于单独使用）
from .storage_manager import StorageManager
from .session_manager import SessionManager
from .knowledge_service import KnowledgeService
from .workflow_engine import WorkflowEngine
from .agent_instance_manager import AgentInstanceManager
from .chat_handler import ChatHandler

# 版本信息
__version__ = "1.0.0"
__author__ = "YunZhiWen Team"
__description__ = "多租户Agent执行服务 - 专注于核心执行逻辑"

# 主要导出
__all__ = [
    # 核心服务
    "MultiTenantAgentService",
    
    # 工厂和初始化
    "AgentServiceFactory",
    "ensure_agent_service_initialized", 
    "initialize_default_tenants_and_agents",
    "get_agent_service",
    
    # 配置
    "AgentServiceConfig", 
    
    # 数据类型
    "AgentType", 
    "SessionStatus", 
    "SessionInfo",
    
    # 异常
    "AgentServiceError", 
    "TenantNotFoundError", 
    "AgentNotFoundError",
    "SessionNotFoundError", 
    "DatabaseOperationError",
    
    # API集成
    "get_multi_tenant_agent_service",
    "handle_agent_service_exception",
    "SessionAPI",
    "ChatAPI",
    "AgentInstanceAPI",
    "ServiceAPI",
    
    # 元信息
    "__version__",
    "__author__", 
    "__description__",
    # 重构后的组件
    "StorageManager",
    "SessionManager", 
    "KnowledgeService",
    "WorkflowEngine",
    "AgentInstanceManager",
    "ChatHandler"
]

# 便捷的导入别名
MTAgentService = MultiTenantAgentService
ServiceConfig = AgentServiceConfig
get_service = get_agent_service
init_service = ensure_agent_service_initialized 