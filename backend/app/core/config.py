"""
配置管理模块
"""
import os
import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import PostgresDsn, MySQLDsn, field_validator

from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your_secret_key")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # 前端配置
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:3000")
    
    # 数据库类型: mysql 或 postgresql
    DB_TYPE: str = os.getenv("DB_TYPE", "postgresql")
    
    # PostgreSQL 配置
    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_USERNAME: str = os.getenv("POSTGRES_USERNAME", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "password")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "yunzhiwen")
    POSTGRES_PORT: int = os.getenv("POSTGRES_PORT", 5432)
    
    # MySQL 配置
    MYSQL_SERVER: str = os.getenv("MYSQL_SERVER", "localhost")
    MYSQL_USERNAME: str = os.getenv("MYSQL_USERNAME", "root")
    MYSQL_PASSWORD: str = os.getenv("MYSQL_PASSWORD", "password")
    MYSQL_DB: str = os.getenv("MYSQL_DB", "yunzhiwen")
    MYSQL_PORT: int = os.getenv("MYSQL_PORT", 3306)
    
    # S3 配置
    S3_ACCESS_KEY: str = os.getenv("S3_ACCESS_KEY", "your_access_key")
    S3_SECRET_KEY: str = os.getenv("S3_SECRET_KEY", "your_secret_key")
    S3_BUCKET_NAME: str = os.getenv("S3_BUCKET_NAME", "your_bucket_name")
    S3_REGION: str = os.getenv("S3_REGION", "your_region")
    S3_ENDPOINT_URL: str = os.getenv("S3_ENDPOINT_URL", "your_endpoint_url")
    S3_FILE_PREFIX: str = os.getenv("S3_FILE_PREFIX", "yunzhiwen")
    S3_URL_EXPIRE_SECONDS: int = os.getenv("S3_URL_EXPIRE_SECONDS", 3600)
    
    # 图标专用S3配置
    S3_ICON_BUCKET_NAME: str = os.getenv("S3_ICON_BUCKET_NAME", "agent-icon")
    S3_ICON_CDN_URL: str = os.getenv("S3_ICON_CDN_URL", "")  # 图标CDN地址（可选）
    S3_ICON_URL_EXPIRE_SECONDS: int = os.getenv("S3_ICON_URL_EXPIRE_SECONDS", 86400)  # 图标URL过期时间，默认24小时
    
    # 向量数据库配置
    VECTOR_STORE_TYPE: str = os.getenv("VECTOR_STORE_TYPE", "faiss")  # 默认使用FAISS
    VECTOR_STORE_DATA_DIR: str = os.getenv("VECTOR_STORE_DATA_DIR", "data/vector_stores")

    # MongoDB 配置
    MONGODB_URL: str = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
    MONGODB_DATABASE: str = os.getenv("MONGODB_DATABASE", "yunzhiwen")

    # Elasticsearch 配置
    ES_URL: str = os.getenv("ES_URL", "http://localhost:9200")
    ES_USERNAME: str = os.getenv("ES_USERNAME", "elastic")
    ES_PASSWORD: str = os.getenv("ES_PASSWORD", "elastic")
    
    # 数据库连接字符串
    SQLALCHEMY_DATABASE_URI: Optional[str] = None

    @field_validator("SQLALCHEMY_DATABASE_URI", mode="before")
    def assemble_db_connection(cls, v: Optional[str], info) -> Any:
        values = info.data
        if isinstance(v, str):
            return v
            
        db_type = values.get("DB_TYPE", "postgresql")
        
        if db_type == "postgresql":
            return PostgresDsn.build(
                scheme="postgresql",
                username=values.get("POSTGRES_USERNAME"),
                password=values.get("POSTGRES_PASSWORD"),
                host=values.get("POSTGRES_SERVER"),
                port=values.get("POSTGRES_PORT"),
                path=f"{values.get('POSTGRES_DB') or ''}",
            )
        elif db_type == "mysql":
            url = MySQLDsn.build(
                scheme="mysql+pymysql",
                username=values.get("MYSQL_USERNAME"),
                password=values.get("MYSQL_PASSWORD"),
                host=values.get("MYSQL_SERVER"),
                port=values.get("MYSQL_PORT"),
                path=f"{values.get('MYSQL_DB') or ''}",
            )
            print(url, type(url))
            return str(url)
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
    
    # 数据库连接池配置
    SQLALCHEMY_POOL_SIZE: int = 20  # 合理的连接池大小
    SQLALCHEMY_MAX_OVERFLOW: int = 10  # 增加溢出连接数
    SQLALCHEMY_POOL_TIMEOUT: int = 30  # 秒
    SQLALCHEMY_POOL_RECYCLE: int = 1800  # 30分钟，防止连接超时
    SQLALCHEMY_POOL_PRE_PING: bool = True  # 启用连接预检查，自动处理过期连接
    SQLALCHEMY_POOL_RESET_ON_RETURN: str = "commit"  # 连接返回时重置策略

    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost", "http://localhost:3000", "http://localhost:8000"]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    model_config = {
        "case_sensitive": True,
        "env_file": ".env",
        "extra": "allow"  # 允许额外的字段
    }


settings = Settings() 