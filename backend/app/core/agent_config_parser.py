from typing import Dict, Any, List, Optional, Tuple
import json
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import StateGraph

class AgentConfigParser:
    """Agent配置解析器，将存储的配置转换为LangGraph可用的格式"""
    
    def __init__(self):
        pass
    
    def parse_agent_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析Agent配置
        
        Args:
            config: 从数据库读取的Agent配置字典
            
        Returns:
            处理后可直接用于构建LangGraph的配置
        """
        if not config:
            return {}
        
        # 解析节点定义
        nodes = self._parse_nodes(config.get("nodes", []))
        
        # 解析边和路由规则
        edges, conditional_edges = self._parse_edges(config.get("edges", []))
        
        # 解析工具定义
        tools = self._parse_tools(config.get("tools", []))
        
        # 解析提示词模板
        prompts = self._parse_prompts(config.get("prompts", {}))
        
        return {
            "nodes": nodes,
            "edges": edges,
            "conditional_edges": conditional_edges,
            "tools": tools,
            "prompts": prompts,
            "entry_point": config.get("entry_point", "llm"),
            "metadata": config.get("metadata", {})
        }
    
    def _parse_nodes(self, nodes_config: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """解析节点配置"""
        nodes = {}
        
        for node in nodes_config:
            node_id = node.get("id")
            if not node_id:
                continue
                
            nodes[node_id] = {
                "type": node.get("type", "function"),
                "config": node.get("config", {}),
                "metadata": node.get("metadata", {})
            }
            
        return nodes
    
    def _parse_edges(self, edges_config: List[Dict[str, Any]]) -> Tuple[List[Tuple[str, str]], Dict[str, Dict[str, str]]]:
        """解析边和条件路由配置"""
        edges = []
        conditional_edges = {}
        
        for edge in edges_config:
            source = edge.get("source")
            target = edge.get("target")
            condition = edge.get("condition")
            
            if not source or not target:
                continue
                
            if condition:
                if source not in conditional_edges:
                    conditional_edges[source] = {}
                conditional_edges[source][condition] = target
            else:
                edges.append((source, target))
                
        return edges, conditional_edges
    
    def _parse_tools(self, tools_config: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析工具配置"""
        tools = []
        
        for tool in tools_config:
            tools.append({
                "name": tool.get("name"),
                "description": tool.get("description"),
                "parameters": tool.get("parameters", {}),
                "function": tool.get("function_name"),
                "metadata": tool.get("metadata", {})
            })
            
        return tools
    
    def _parse_prompts(self, prompts_config: Dict[str, Any]) -> Dict[str, Any]:
        """解析提示词配置"""
        prompts = {}
        
        for key, prompt in prompts_config.items():
            if isinstance(prompt, str):
                prompts[key] = prompt
            elif isinstance(prompt, dict):
                if "template" in prompt:
                    prompts[key] = {
                        "template": prompt.get("template"),
                        "variables": prompt.get("variables", {}),
                        "type": prompt.get("type", "text")
                    }
                    
        return prompts
    
    def create_system_message(self, prompt_template: str, variables: Dict[str, Any] = None) -> SystemMessage:
        """创建系统消息"""
        if variables:
            try:
                prompt = prompt_template.format(**variables)
            except KeyError:
                prompt = prompt_template
        else:
            prompt = prompt_template
            
        return SystemMessage(content=prompt)
    
    def create_tool_schema(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """创建工具模式定义"""
        tool_schemas = []
        
        for tool in tools:
            schema = {
                "type": "function",
                "function": {
                    "name": tool.get("name", ""),
                    "description": tool.get("description", ""),
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
            
            # 添加参数定义
            for param_name, param in tool.get("parameters", {}).items():
                schema["function"]["parameters"]["properties"][param_name] = {
                    "type": param.get("type", "string"),
                    "description": param.get("description", "")
                }
                
                if param.get("required", False):
                    schema["function"]["parameters"]["required"].append(param_name)
                    
            tool_schemas.append(schema)
            
        return tool_schemas 