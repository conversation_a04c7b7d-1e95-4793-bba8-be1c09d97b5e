import json
from typing import Dict, List, Any, Optional, TypedDict, Annotated, Sequence, Literal, Union, cast, Callable, Tuple
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from pydantic import BaseModel
from sqlalchemy.orm import Session
from app.models.agent import Agent, AgentVersion
from app.models.model import Model, Provider, ProviderAccessCredential
from app.core.agent_config_parser import AgentConfigParser
from app.core.model_factory import ModelFactory

class AgentState(TypedDict):
    """LangGraph的状态定义"""
    messages: Annotated[Sequence[BaseMessage], "对话历史消息"]
    sender: Annotated[str, "当前发送者"]
    tools_output: Annotated[Dict[str, Any], "工具调用结果"]
    tool_calls: Annotated[List[Dict[str, Any]], "工具调用请求列表"]
    current_tool_call: Annotated[Optional[Dict[str, Any]], "当前正在处理的工具调用"]
    metadata: Annotated[Dict[str, Any], "元数据"]

class LangGraphEngine:
    """LangGraph引擎实现"""
    
    def __init__(self, db: Optional[Session] = None):
        self.config_parser = AgentConfigParser()
        self.llm_cache = {}  # 缓存已初始化的LLM实例
        self.db = db  # 数据库会话
        self.model_service = None
        self.model_factory = ModelFactory()

    
    def set_db(self, db: Session):
        """设置数据库会话"""
        self.db = db

    
    def build_agent_graph(self, agent: Agent, version: AgentVersion, metadata: Optional[Dict[str, Any]] = None, model_info: Optional[Tuple[Model, Provider, ProviderAccessCredential]] = None) -> StateGraph:
        """
        根据Agent模型和版本构建LangGraph图
        
        Args:
            agent: Agent数据模型
            version: Agent版本模型
            
        Returns:
            构建好的StateGraph实例
        """
        # 从版本配置中提取图结构定义
        config = version.config or {}
        
        # 使用配置解析器解析配置
        parsed_config = self.config_parser.parse_agent_config(config)
        
        # 创建状态图
        workflow = StateGraph(AgentState)
        
        # 构建基本提示系统消息
        system_message = self._build_system_message(agent, version, parsed_config)
        
        # 模型信息已从外部传入，无需在此处获取
        
        # 创建LLM节点
        llm_node = self._build_llm_node(agent, version, parsed_config, system_message, model_info)
        
        # 创建工具执行节点
        tool_node = self._build_tool_node(agent, version, parsed_config)
        
        # 创建路由函数
        router_function = self._build_router(agent, version, parsed_config)
        
        # 添加节点
        workflow.add_node("llm", llm_node)
        workflow.add_node("tool_executor", tool_node)
        
        # 设置入口点
        entry_point = parsed_config.get("entry_point", "llm")
        workflow.set_entry_point(entry_point)
        
        # 添加条件边 - 从LLM节点根据状态决定下一步
        workflow.add_conditional_edges("llm", router_function)
        
        # 工具执行完成后回到LLM
        workflow.add_edge("tool_executor", "llm")
        
        # 从解析的配置添加自定义边
        for source, target in parsed_config.get("edges", []):
            # 只添加存在的节点之间的边
            if source in ["llm", "tool_executor"] and target in ["llm", "tool_executor"]:
                workflow.add_edge(source, target)
        
        # 添加来自配置的条件边
        for source, edges in parsed_config.get("conditional_edges", {}).items():
            # 只处理存在的节点
            if source in ["llm", "tool_executor"]:
                def custom_router(state: AgentState, edge_map=edges):
                    # 简单实现，实际应根据配置实现更复杂的路由逻辑
                    for condition, target in edge_map.items():
                        # 可以根据condition实现自定义条件
                        if condition == "has_tool_calls" and state.get("tool_calls"):
                            return target
                        elif condition == "is_user" and state.get("sender") == "user":
                            return target
                    return END
                
                # 避免与主路由冲突
                if source != "llm":
                    workflow.add_conditional_edges(source, custom_router)
        
        return workflow
    
    def _build_system_message(self, agent: Agent, version: AgentVersion, parsed_config: Dict[str, Any]) -> SystemMessage:
        """构建系统提示消息"""
        # 从prompt字段获取系统提示
        prompt_text = version.prompt or ""
        
        # 如果配置中有prompts字段，使用其中的system_prompt
        if "prompts" in parsed_config and "system_prompt" in parsed_config["prompts"]:
            prompt_config = parsed_config["prompts"]["system_prompt"]
            if isinstance(prompt_config, str):
                prompt_text = prompt_config
            elif isinstance(prompt_config, dict) and "template" in prompt_config:
                prompt_text = prompt_config["template"]
                # 如果有变量，替换变量
                if "variables" in prompt_config:
                    try:
                        prompt_text = prompt_text.format(**prompt_config["variables"])
                    except (KeyError, ValueError):
                        pass
        
        return SystemMessage(content=prompt_text)
    
    def _build_llm_node(self, agent: Agent, version: AgentVersion, parsed_config: Dict[str, Any], system_message: SystemMessage, model_info: Optional[Tuple[Model, Provider, ProviderAccessCredential]] = None) -> Callable[[AgentState], AgentState]:
        """构建LLM处理节点"""
        
        # 从配置中提取模型配置
        model_config = self._get_model_config(parsed_config)
        
        # 如果有模型信息，更新模型配置
        if model_info:
            model, provider, credential = model_info
            model_config.update({
                "model_key": model.key,
                "model_name": model.name,
                "provider_key": provider.key,
                "provider_name": provider.name,
                "provider_api_base": provider.api_base,
                "credentials": credential.credentials
            })
        
        async def llm_node(state: AgentState) -> AgentState:
            """LLM处理节点"""
            # 获取消息历史
            messages = list(state["messages"])
            
            # 添加系统提示
            if messages and not isinstance(messages[0], SystemMessage):
                messages.insert(0, system_message)
            
            # 获取元数据
            metadata = state.get("metadata", {})
            tenant_id = metadata.get("tenant_id")
            user_id = metadata.get("user_info", {}).get("user_id")
            session_id = metadata.get("session_id")
            
            # 确保数据库会话可用
            if not self.db:
                raise ValueError("数据库会话未初始化，无法调用LLM")
            
            try:
                # 方法1: 使用ModelFactory创建LangChain模型实例直接调用
                # 如果有模型信息，优先使用传入的模型信息
                if model_info:
                    model, provider, credential = model_info
                    print(f"🔧 使用传入的模型信息: {model.key} (提供商: {provider.key})")
                    llm = self.model_factory.create_model_with_info(
                        model=model,
                        provider=provider,
                        credential=credential,
                        extra_config={
                            "temperature": model_config.get("temperature", 0.7),
                            "max_tokens": model_config.get("max_tokens", 2000),
                        }
                    )
                else:
                    print(f"🔧 使用默认模型配置: {model_config.get('model_key', 'gpt-3.5-turbo')}")
                    llm = self.model_factory.create_model(
                        db=self.db,
                        tenant_id=tenant_id,
                        model_key=model_config.get("model_key", "gpt-3.5-turbo"),
                        extra_config={
                            "temperature": model_config.get("temperature", 0.7),
                            "max_tokens": model_config.get("max_tokens", 2000),
                        }
                    )
                
                print(f"🤖 LLM实例创建结果: {llm is not None} (类型: {type(llm) if llm else 'None'})")
                
                # 如果成功创建了LLM实例，直接使用LangChain调用
                if llm:
                    print(f"📤 发送消息到LLM: {len(messages)} 条消息")
                    response = await llm.ainvoke(messages)
                    print(f"📥 LLM响应: {response.content[:100]}..." if len(response.content) > 100 else f"📥 LLM响应: {response.content}")
                    tool_calls = []
                    
                    # 从LLM响应中提取工具调用（如果有）
                    if hasattr(response, "additional_kwargs") and "tool_calls" in response.additional_kwargs:
                        for tool_call in response.additional_kwargs["tool_calls"]:
                            try:
                                tool_args = json.loads(tool_call.get("function", {}).get("arguments", "{}"))
                                tool_calls.append({
                                    "name": tool_call.get("function", {}).get("name", ""),
                                    "args": tool_args
                                })
                            except:
                                pass
                else:
                    # 如果ModelFactory创建失败，创建一个简单的响应
                    response = AIMessage(content="抱歉，我现在无法处理您的请求。模型服务暂时不可用。")
                    tool_calls = []
                
            except Exception as e:
                # 错误处理 - 创建一个表示错误的响应
                response = AIMessage(content=f"模型调用错误: {str(e)}")
                tool_calls = []
            
            # 更新状态
            new_messages = messages + [response]
            return {
                **state,
                "messages": new_messages,
                "tool_calls": tool_calls,
                "sender": "assistant"
            }
            
        return llm_node
    
    def _get_model_config(self, parsed_config: Dict[str, Any]) -> Dict[str, Any]:
        """从解析后的配置中提取模型配置"""
        # 默认模型配置
        default_config = {
            "model_key": "gpt-3.5-turbo",
            "max_tokens": 2000,
            "temperature": 0.7,
            "top_p": 1.0,
            "stop_sequences": [],
            "additional_params": {}
        }
        
        # 优先使用配置中的model_config
        if "model" in parsed_config:
            model_config = parsed_config.get("model", {})
            return {**default_config, **model_config}
        
        return default_config
    
    def _prepare_prompt_from_messages(self, messages: List[BaseMessage]) -> str:
        """将消息列表转换为单一提示词字符串"""
        # 创建提示词
        prompt_parts = []
        
        for msg in messages:
            role = "system"
            if isinstance(msg, HumanMessage):
                role = "user"
            elif isinstance(msg, AIMessage):
                role = "assistant"
                
            prompt_parts.append(f"{role}: {msg.content}")
            
        return "\n".join(prompt_parts)
    
    def _extract_tool_calls(self, response_text: str) -> List[Dict[str, Any]]:
        """从响应文本中提取工具调用请求"""
        tool_calls = []
        
        # 简单实现：查找类似 "工具调用: tool_name(arg1, arg2)" 的模式
        # 实际实现中应使用更健壮的方法，如解析JSON或使用正则表达式
        
        # 简单匹配 "工具调用:" 或 "function_call:" 后面的内容
        markers = ["工具调用:", "function_call:", "调用工具:", "tool:"]
        for marker in markers:
            if marker in response_text:
                parts = response_text.split(marker)
                for i in range(1, len(parts)):
                    tool_text = parts[i].strip().split("\n")[0]
                    
                    # 尝试提取工具名称和参数
                    if "(" in tool_text and ")" in tool_text:
                        tool_name = tool_text.split("(")[0].strip()
                        args_text = tool_text.split("(")[1].split(")")[0]
                        
                        # 简单解析参数
                        args = {}
                        if "," in args_text:
                            arg_pairs = args_text.split(",")
                            for pair in arg_pairs:
                                if "=" in pair:
                                    k, v = pair.split("=", 1)
                                    args[k.strip()] = v.strip().strip('"\'')
                        
                        tool_calls.append({
                            "name": tool_name,
                            "args": args
                        })
        
        # 尝试查找JSON格式的工具调用
        try:
            import re
            # 匹配JSON对象的正则表达式
            json_pattern = r'\{[^{}]*\}'
            matches = re.findall(json_pattern, response_text)
            
            for match in matches:
                try:
                    obj = json.loads(match)
                    if "name" in obj and ("args" in obj or "arguments" in obj):
                        tool_calls.append({
                            "name": obj["name"],
                            "args": obj.get("args", obj.get("arguments", {}))
                        })
                except:
                    pass
        except:
            pass
            
        return tool_calls
    
    def _build_tool_node(self, agent: Agent, version: AgentVersion, parsed_config: Dict[str, Any]) -> Callable[[AgentState], AgentState]:
        """构建工具调用节点"""
        
        def tool_executor(state: AgentState) -> AgentState:
            """工具调用执行节点"""
            # 复制当前状态
            new_state = dict(state)
            tools_output = dict(state.get("tools_output", {}))
            
            # 处理工具调用
            if state.get("tool_calls"):
                for tool_call in state.get("tool_calls", []):
                    tool_name = tool_call.get("name", "")
                    tool_args = tool_call.get("args", {})
                    
                    # 实际实现中，根据工具名称查找并执行对应工具
                    # tool_result = self._execute_tool(tool_name, tool_args, agent, version)
                    
                    # 模拟工具执行结果
                    tool_result = {
                        "result": f"执行工具 {tool_name} 的结果，参数：{json.dumps(tool_args)}"
                    }
                    
                    # 更新工具输出
                    tools_output[tool_name] = tool_result
            
            # 更新状态
            new_state["tools_output"] = tools_output
            new_state["tool_calls"] = []  # 清空工具调用列表，表示已处理
            
            return new_state
            
        return tool_executor
    
    def _build_router(self, agent: Agent, version: AgentVersion, parsed_config: Dict[str, Any]) -> Callable[[AgentState], str]:
        """构建路由决策函数"""
        
        def router(state: AgentState):
            """路由决策函数 - 从LLM节点决定下一步"""
            # 如果有工具调用请求，执行工具
            if state.get("tool_calls") and len(state.get("tool_calls", [])) > 0:
                return "tool_executor"
            
            # 否则结束对话
            return END
            
        return router
    
    async def execute_agent(
        self, 
        agent: Agent, 
        version: AgentVersion, 
        prompt: str, 
        history: Optional[List[Dict[str, str]]] = None,
        stream: bool = False,
        metadata: Optional[Dict[str, Any]] = None,
        model_info: Optional[Tuple[Model, Provider, ProviderAccessCredential]] = None
    ) -> Union[Dict[str, Any], Any]:
        """
        执行Agent推理
        
        Args:
            agent: Agent数据模型
            version: Agent版本模型
            prompt: 用户输入提示
            history: 对话历史
            stream: 是否使用流式响应
            metadata: 额外元数据
            
        Returns:
            执行结果或流式响应
        """
        # 构建图
        graph = self.build_agent_graph(agent, version, metadata, model_info)
        
        # 准备输入状态
        messages = []
        if history:
            for msg in history:
                if msg.get("role") == "user":
                    messages.append(HumanMessage(content=msg.get("content", "")))
                elif msg.get("role") == "assistant":
                    messages.append(AIMessage(content=msg.get("content", "")))
        
        # 添加当前提示
        messages.append(HumanMessage(content=prompt))
        
        input_state = {
            "messages": messages,
            "sender": "user",
            "metadata": metadata or {},
            "tools_output": {},
            "tool_calls": [],
            "current_tool_call": None
        }
        
        # 创建可运行对象
        runnable = graph.compile()
        
        # 运行配置
        config: RunnableConfig = {
            "configurable": {"thread_id": metadata.get("session_id") if metadata else None}
        }
        
        # 执行
        if stream:
            return runnable.astream(input_state, config=config)
        else:
            result = await runnable.ainvoke(input_state, config=config)
            return result 