
from typing import List, Optional, Any
from sqlalchemy.orm import Session

from typing import Callable, Optional

from langchain_tavily import TavilySearch
from langchain_community.tools import BraveSearch,BearlyInterpreterTool
from app.models.tool import TenantTool, Tool as DbTool
from langchain_openai import ChatOpenAI
from langchain_community.agent_toolkits import SQLDatabaseToolkit
from tempfile import TemporaryDirectory
from langchain_community.agent_toolkits import FileManagementToolkit
from langchain_community.tools import WolframAlphaQueryRun
from langchain_community.utilities.wolfram_alpha import WolframAlphaAPIWrapper

from langchain_community.tools import BaseTool
from langchain_community.tools import SceneXplainTool
from langchain_community.utilities.scenexplain import SceneXplainAPIWrapper
from langchain_dappier import DappierRealTimeSearchTool


import os
class PlatformToolService:
    """平台工具服务类，提供平台工具配置相关的业务逻辑"""

    @staticmethod
    def get_execution_func(tool_key: str) -> Optional[Callable]:
        """
        根据工具key获取对应的执行函数
        :param tool_key: 工具标识（如"add"对应加法工具）
        :return: 执行函数或None（若不存在）
        """
        # 通过反射获取类中定义的工具方法（需确保工具方法名与key一致）
        # 例如，对于"add"工具，方法名为"add"
        return getattr(PlatformToolService, tool_key, None)

    @classmethod  
    def get_all_tools(cls) -> List[Any]:
        """获取所有平台工具（返回BaseTool实例）"""
        tools = []
        #从tool_list中获取所有工具
        for tool in cls.list_tools:
            tool_func = cls.get_execution_func(tool["name"])
            if tool_func:
                tool_name = tool.get("name")
                tool_tag = tool.get("tag")
                tool_param = tool_func.args_schema.model_json_schema()
                tool_description = tool.get("description")
                tool_config = tool.get("config_schema")
                tool_active = tool.get("is_active")
                tools.append({
                "name": tool_name,
                "description": tool_description,
                "param_schema":tool_param,
                "config_schema": tool_config,
                "tag": tool_tag,
                "is_active": tool_active,
                })
            else:
                if tool.get("name") == "tavily_search":
                    tavily_search = TavilySearch(tavily_api_key="tavily_api_key")  # 占位符密钥
                    if tavily_search.args_schema is not None:
                        tool_param = tavily_search.args_schema.model_json_schema()
                    else:
                        tool_param = {}  # 默认空模式
                    
                elif tool.get("name") == "brave_search":
                    brave_search = BraveSearch.from_api_key("brave_api_key")  # 占位符密钥
                    if brave_search.args_schema is not None:
                        tool_param = brave_search.args_schema.model_json_schema()
                    else:
                        tool_param = {} 
                    
                elif tool.get("name") == "bearly_interpreter":
                    bearly_interpreter = BearlyInterpreterTool("bearly_api_key")  # 占位符密钥
                    if bearly_interpreter.args_schema is not None:
                        tool_param = bearly_interpreter.args_schema.model_json_schema()
                    else:
                        tool_param = {}
                    
                elif tool.get("name") == "sql_database":
                
                    tool_param = {}
                elif tool.get("name") == "file_management":
                    
                    tool_param = {}
                elif tool.get("name") == "wolfram_alpha":
                    # wolfram_alpha = cls.get_wolfram_alpha_tool("wolfram_alpha_app_id")
                    # wolfram_alpha = WolframAlphaQueryRun(api_wrapper = WolframAlphaAPIWrapper(wolfram_alpha_appid = "wolfram_alpha_app_id"))
                    tool_param = {}
                elif tool.get("name") == "scene_xplain":
                    scenexplain = SceneXplainTool(api_wrapper = SceneXplainAPIWrapper(scenex_api_key = "scenex_api_key"))
                    if scenexplain.args_schema is not None:
                        tool_param = scenexplain.args_schema.model_json_schema()
                    else:
                        tool_param = {}
                elif tool.get("name") == "dappier_real_time_search":
                    os.environ["DAPPIER_API_KEY"] = "dappier_api_key"
                    realtime_search = DappierRealTimeSearchTool()
                    if realtime_search.args_schema is not None:
                        tool_param = realtime_search.args_schema.model_json_schema()    
                    else:
                        tool_param = {}
                else:
                    continue
                tool_config = tool.get("config_schema")
                tool_description = tool.get("description")
                tool_tag = tool.get("tag")
                tool_name = tool.get("name")
                tool_active = tool.get("is_active")
                tools.append({
                    "name": tool_name,
                    "description": tool_description,
                    "param_schema": tool_param,
                    "config_schema": tool_config,
                    "tag": tool_tag,
                    "is_active": tool_active,
                })
        return tools



    @classmethod
    def get_tool(cls, tool_id: str,  db: Session,tenant_id: str) -> List[BaseTool]:
        """
        根据工具ID获取工具配置及实例
        """
    
        db_tool = db.query(DbTool).filter(
            DbTool.id == tool_id,
        ).first()
        if not db_tool:
            return []
        # 查询租户工具配置
        tenant_tool = db.query(TenantTool).filter(
            TenantTool.tenant_id == tenant_id,
            TenantTool.tool_id == tool_id
        ).first()
        if not tenant_tool:
            return []
        tool_config = tenant_tool.config or {}
        
        if db_tool.key == "tavily_search":
            tavily_api_key = tool_config.get("tavily_api_key")
            if not tavily_api_key:
                return []
            tavily_search = TavilySearch(tavily_api_key = tavily_api_key)
            return [tavily_search]
        elif db_tool.key == "brave_search":
            brave_api_key = tool_config.get("brave_api_key")
            if not brave_api_key:
                return []
            brave_search = BraveSearch.from_api_key(brave_api_key)
            return [brave_search]
        elif db_tool.key == "bearly_interpreter":
            bearly_api_key = tool_config.get("bearly_api_key")
            if not bearly_api_key:
                return []
            bearly_interpreter = BearlyInterpreterTool(bearly_api_key)
            return [bearly_interpreter.as_tool()]
        elif db_tool.key == "sql_database":
            port = tool_config.get("port")
            host = tool_config.get("host")
            dbname = tool_config.get("dbname")
            username = tool_config.get("username")
            password = tool_config.get("password")
            database_type = tool_config.get("database_type")
            #构建数据库连接字符串
            if database_type == "mysql":
                database_type = "mysql+pymysql"
            elif database_type == "postgresql":
                database_type = "postgresql"
            else:
                return []
            if not port or not host or not dbname or not username or not password:
                return []
            database_url = f"{database_type}://{username}:{password}@{host}:{port}/{dbname}"
            from langchain_community.utilities import SQLDatabase
            
            class ReadonlySQLDatabase(SQLDatabase):
                def run(self, query: str, *args, **kwargs) -> str:
                    # 检查是否为写操作（忽略大小写）
                    write_keywords = {'insert', 'update', 'delete', 'drop', 'create', 'alter'}
                    if any(keyword in query.lower() for keyword in write_keywords):
                        raise PermissionError("仅允许执行查询语句，禁止写操作")
            
                    return super().run(query, *args, **kwargs)
            #使用自定义类替代原SQLDatabase
            database = ReadonlySQLDatabase.from_uri(database_url)
            llm = ChatOpenAI(api_key="api_key",base_url="base_url")
            toolkit = SQLDatabaseToolkit(db=database, llm=llm)
            
            return toolkit.get_tools()
        elif db_tool.key == "file_management":
            working_directory = TemporaryDirectory()
            # 获取并验证root_dir路径
            root_dir = tool_config.get("root_dir", working_directory.name)
            # 初始化工具包
            toolkit = FileManagementToolkit(root_dir=root_dir)
            return toolkit.get_tools()
        elif db_tool.key == "wolfram_alpha":
            # 获取Wolfram Alpha应用ID
            wolfram_alpha_app_id = tool_config.get("wolfram_alpha_app_id")
            if not wolfram_alpha_app_id:
                return []
            api_wrapper = WolframAlphaAPIWrapper(wolfram_alpha_appid = wolfram_alpha_app_id)
            api_wrapper.wolfram_client.aquery = cls.relaxed_wolfram_alpha_content_type_check(api_wrapper.wolfram_client.aquery,api_wrapper.wolfram_client)
            # 创建Wolfram Alpha查询工具
            wolfram_tool = WolframAlphaQueryRun(api_wrapper = api_wrapper)
            return [wolfram_tool]
        elif db_tool.key == "scene_xplain":
            # 获取Scene Xplain API密钥
            scenex_api_key = tool_config.get("scenex_api_key")
            print("scenex_api_key",scenex_api_key)
            if not scenex_api_key:
                return []
            # 创建Scene Xplain API包装器
            api_wrapper = SceneXplainAPIWrapper(scenex_api_key = scenex_api_key)
            # 创建Scene Xplain工具
            scenexplain_tool = SceneXplainTool(api_wrapper = api_wrapper)
            return [scenexplain_tool]
        elif db_tool.key == "dappier_real_time_search":
            # 获取Dappier API密钥
            dappier_api_key = tool_config.get("dappier_api_key")
            if not dappier_api_key:
                return []
            # 创建Dappier实时搜索工具
            os.environ["DAPPIER_API_KEY"] = dappier_api_key
            dappier_tool = DappierRealTimeSearchTool()
            return [dappier_tool]
        # 构建工具实例
        tool_func = cls.get_execution_func(db_tool.key)
        
        return [tool_func]

    def relaxed_wolfram_alpha_content_type_check(func,wolfram_client):
        """装饰器：宽松处理Content-Type检查"""
        import functools
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            print("开始执行")
            try:
                return await func(self, *args, **kwargs)
            except AssertionError as e:
                    from wolframalpha import  Document
                    import multidict
                    import httpx
                    import xmltodict
                    async with httpx.AsyncClient() as client:
                        response = await client.get(
                        wolfram_client.url,
                        params=multidict.MultiDict(
                        (), appid=wolfram_client.app_id, input=self, **kwargs
                        ),
                        )
                    content_type = response.headers.get('Content-Type', '').lower()
                    
                    # 宽松检查
                    if 'xml' in content_type:
                        try:
                            # 手动解析XML并返回
                            doc = xmltodict.parse(response.content, postprocessor=Document.make)
                            return doc['queryresult']
                            
                        except Exception as parse_error:
                            print("XML解析错误:", parse_error)
                            # 解析失败，重新抛出原异常
                            raise
                    else:
                        # 非XML内容，抛出原异常
                        raise

        return wrapper

    #创建一个列表，存储所有的工具工具名称和工具所属分类和工具所需配置参数
    list_tools = [

        {
            "name": "tavily_search",
            "tag": "搜索类",
            "description": "使用Tavily搜索引擎进行搜索。",
            "is_active": True,
            #需要生成json_schema的格式
            "config_schema": {
                "type": "object",
                "properties": {
                    "tavily_api_key": {
                        "type": "string",
                        "description": "The API key for the Tavily search engine.",
                        "secret": True
                    }
                },
                "required": ["tavily_api_key"]
            }
            
        },
        {
            "name": "brave_search",
            "tag": "搜索类",
            "description": "使用Brave搜索引擎进行搜索。",
            "is_active": True,
            #需要生成json_schema的格式
            "config_schema": {
                "type": "object",
                "properties": {
                    "brave_api_key": {
                        "type": "string",
                        "description": "The API key for the Brave search engine.",
                        "secret": True
                    }
                },
                "required": ["brave_api_key"]
            }  
        },
        {
            "name": "bearly_interpreter",
            "tag": "代码解释器类",
            "description": "使用Bearly解释器进行代码解释。",
            "is_active": True,
            #需要生成json_schema的格式
            "config_schema": {
                "type": "object",
                "properties": {
                    "bearly_api_key": {
                        "type": "string",
                        "description": "The API key for the Bearly interpreter.",
                        "secret": True
                    }
                },
                "required": ["bearly_api_key"]
            }
                 
        },
        {
            "name": "sql_database",
            "tag": "数据库类",
            "description": "使用SQL数据库进行查询。",
            "is_active": True,
            #需要生成json_schema的格式
            "config_schema":{
                "type": "object",
                "properties": {
                    "port": {
                        "type": "integer",
                        "description": "The port number of the database server."
                    },
                    "host": {
                        "type": "string",
                        "description": "The host address of the database server."
                    },
                    "dbname": {
                        "type": "string",
                        "description": "The name of the database."
                    },
                    "username": {
                        "type": "string",
                        "description": "The username for the database connection."
                    },
                    
                    "password": {
                        "type": "string",
                        "description": "The password for the database connection.",
                        "secret": True
                    },
                    "database_type": {
                        "type": "string",
                        "description": "The type of the database (e.g., mysql, postgresql).",
                        "enum": ["mysql", "postgresql"]
                    }
                },
                "required": ["port", "host", "dbname", "username", "password", "database_type"]
            }
        },
        {
            "name": "file_management",
            "tag": "文件管理类",
            "description": "使用文件管理工具进行文件操作。",
            "is_active": True,
            #需要生成json_schema的格式
            "config_schema":{
                "type": "object",
                "properties": {
                    "root_dir": {
                        "type": "string",
                        "description": "The root directory for file operations. Must be an absolute path. Defaults to a temporary directory."
                    }
                }
            }
        },
        {
            "name": "wolfram_alpha",
            "tag": "数学类",
            "description": "使用Wolfram Alpha进行数学计算。",
            "is_active": True,
            #需要生成json_schema的格式
            "config_schema":{
                "type": "object",
                "properties": {
                    "wolfram_alpha_app_id": {
                        "type": "string",
                        "description": "The App ID for the Wolfram Alpha API.",
                        "secret": True
                    }
                },
                "required": ["wolfram_alpha_app_id"]
            }
        },
        {
            "name": "scene_xplain",
            "tag": "图像类",
            "description": "使用SceneXplain进行图像分析。",
            "is_active": True,
            #需要生成json_schema的格式
            "config_schema":{
                "type": "object",
                "properties": {
                    "scenex_api_key": {
                        "type": "string",
                        "description": "The API key for the SceneXplain API.",
                        "secret": True
                    }
                },
                "required": ["scenex_api_key"]
            }
        },
        {
            "name": "dappier_real_time_search",
            "tag": "搜索类",
            "description": "使用Dappier进行实时搜索。",
            "is_active": True,
            #需要生成json_schema的格式
            "config_schema":{
                "type": "object",
                "properties": {
                    "dappier_api_key": {
                        "type": "string",
                        "description": "The API key for the Dappier API.",
                        "secret": True
                    }
                },
                "required": ["dappier_api_key"]
            }
        }

    ]


