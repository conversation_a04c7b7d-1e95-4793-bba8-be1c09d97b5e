from datetime import datetime, timedelta, UTC
from typing import Any, Dict, Optional, Union
import uuid

from jose import jwt
from passlib.context import CryptContext
from pydantic import ValidationError

from app.core.config import settings
from app.schemas.auth import TokenData

# 配置密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT相关函数
def create_access_token(
    subject: Union[str, Any], 
    expires_delta: Optional[timedelta] = None,
    tenant_id: Optional[str] = None,
    scopes: list = []
) -> str:
    """
    创建访问令牌
    """
    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
    else:
        expire = datetime.now(UTC) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {
        "exp": expire, 
        "sub": str(subject),
        "iat": datetime.now(UTC),
        "jti": str(uuid.uuid4()),  # 令牌唯一标识符
    }
    
    if tenant_id:
        to_encode["tenant_id"] = tenant_id
        
    if scopes:
        to_encode["scopes"] = scopes
    
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm="HS256")
    return encoded_jwt, expire

def decode_token(token: str) -> Optional[TokenData]:
    """
    解码并验证令牌
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=["HS256"]
        )
        
        token_data = TokenData(
            user_id=payload.get("sub"),
            tenant_id=payload.get("tenant_id"),
            username=payload.get("username"),
            scopes=payload.get("scopes", [])
        )
        
        # 检查令牌是否过期 - 使用两个具有相同时区信息(UTC)的datetime对象进行比较
        if datetime.fromtimestamp(payload["exp"], tz=UTC) < datetime.now(UTC):
            return None
            
        return token_data
    except (jwt.JWTError, ValidationError):
        return None

# 密码相关函数
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """
    获取密码哈希
    """
    return pwd_context.hash(password)

# 生成随机令牌
def generate_verification_token() -> str:
    """
    生成邮箱验证令牌
    """
    return uuid.uuid4().hex

def generate_reset_token() -> str:
    """
    生成密码重置令牌
    """
    return uuid.uuid4().hex 