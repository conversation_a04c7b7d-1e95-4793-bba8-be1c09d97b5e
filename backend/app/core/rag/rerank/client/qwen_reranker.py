"""
Rerank model implementation that uses specialized reranking models.
"""

from typing import List, <PERSON><PERSON>

from sqlalchemy.orm import Session

from langchain_core.documents import Document

from app.core.rag.rerank.base import BaseReranker
from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)


class QwenReranker(BaseReranker):
    """Reranker that uses specialized reranking models from different providers."""
    
    def __init__(
        self, 
        db: Session,
        model_key: str,
        tenant_id: str,
        api_key: str    
    ):
        """
        Initialize the Qwen reranker.
        
        Args:
            db: Database session
            model_key: Model key
            tenant_id: Tenant ID
            api_key: API key
        """
        super().__init__(model_key, tenant_id)
        self.db = db
        self.tenant_id = tenant_id
        self.model_key = model_key
        self.api_key = api_key   
        logger.info(f"Initialized {model_key} reranker with model: {model_key}")

    
    async def rerank(
        self,
        query: str,
        documents: List[Document],
        top_k: int = 5,
    ) -> List[Document]:
        """
        Rerank documents based on query relevance.
        
        Args:
            query: Query text
            documents: List of documents to rerank
            top_k: Number of top documents to return
            instruction: Optional instruction for reranking
            
        Returns:
            Reranked list of documents
        """
        reranked_docs_with_scores = await self.rerank_with_scores(query, documents, top_k)
        return [doc for doc, _ in reranked_docs_with_scores]
    
    async def rerank_with_scores(
        self,
        query: str,
        documents: List[Document],
        top_k: int = 5,
    ) -> List[Tuple[Document, float]]:
        """
        Rerank documents based on query relevance and return scores.
        
        Args:
            query: Query text
            documents: List of documents to rerank
            top_k: Number of top documents to return
            instruction: Optional instruction for reranking
            
        Returns:
            List of (document, score) tuples
        """
        if not documents:
            return []
        
        try:
            # Get rerank client configuration
            import dashscope

            logger.info(f"Reranking with {self.model_key} model")


            document_texts = [doc.page_content for doc in documents]

            response = dashscope.TextReRank.call(
                model=self.model_key,
                api_key=self.api_key,
                query=query,
                documents=document_texts,
                top_n=top_k,
                return_documents=False
            )

            if response.status_code != 200:
                logger.error(f"Error in dashscope reranking: {response.message}")
                return []
            
            reranked_docs_with_scores = []
            for item in response.output.results:
                if "index" in item and "relevance_score" in item:
                    idx = int(item["index"])
                    score = float(item["relevance_score"])
                    if "score" in documents[idx].metadata:
                        documents[idx].metadata["original_score"] = documents[idx].metadata["score"]
                    documents[idx].metadata["score"] = score
                    reranked_docs_with_scores.append((documents[idx], score))
            
            return reranked_docs_with_scores

        except Exception as e:
            logger.error(f"Error in {self.model_key} reranking: {str(e)}")
            return []
    
    
    