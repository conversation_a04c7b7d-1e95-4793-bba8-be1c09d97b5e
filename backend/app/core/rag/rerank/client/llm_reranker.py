"""
LLM-based reranker implementation.
"""

from typing import List, Optional, Tuple
import json

from langchain_core.language_models import BaseChatModel
from langchain_core.documents import Document
from sqlalchemy.orm import Session

from app.core.rag.rerank.base import BaseReranker
from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)


class LLMReranker(BaseReranker):
    """LLM-based reranker implementation."""
    
    def __init__(
        self, 
        db: Session,
        model: BaseChatModel,
        model_key: str, 
        tenant_id: str,
        default_instruction: str = "Rank documents based on their relevance to the query, prioritizing documents with content directly related to the query.",
        temperature: float = 0.0,
        max_tokens: int = 5000
    ):
        """
        Initialize the LLM reranker.
        
        Args:
            db: Database session
            model: Model instance
            model_key: Model key
            tenant_id: Tenant ID
            default_instruction: Default instruction for reranking
            temperature: Temperature for LLM (lower means more deterministic)
            max_tokens: Maximum tokens for LLM response
        """
        super().__init__(model_key, tenant_id)
        self.db = db
        self.model = model
        self.tenant_id = tenant_id
        self.default_instruction = default_instruction
        self.temperature = temperature
        self.max_tokens = max_tokens
        logger.info(f"Initialized LLM reranker with model_key: {model_key} and tenant_id: {tenant_id}")
    
    
    async def rerank(
        self,
        query: str,
        documents: List[Document],
        top_k: int = 5,
        instruction: Optional[str] = None,
    ) -> List[Document]:
        """
        Rerank documents based on query relevance.
        
        Args:
            query: Query text
            documents: List of documents to rerank
            top_k: Number of top documents to return
            instruction: Optional instruction for reranking
            
        Returns:
            Reranked list of documents
        """
        reranked_docs_with_scores = await self.rerank_with_scores(query, documents, top_k, instruction)
        return [doc for doc, _ in reranked_docs_with_scores]
    
    async def rerank_with_scores(
        self,
        query: str,
        documents: List[Document],
        top_k: int = 5,
        instruction: Optional[str] = None,
    ) -> List[Tuple[Document, float]]:
        """
        Rerank documents based on query relevance and return scores.
        
        Args:
            query: Query text
            documents: List of documents to rerank
            top_k: Number of top documents to return
            instruction: Optional instruction for reranking
            
        Returns:
            List of (document, score) tuples
        """
        if not documents:
            return []
        
        # Use provided instruction or fall back to default
        rerank_instruction = instruction or self.default_instruction
        
        try:
            # Prepare the prompt for reranking
            prompt = self._create_rerank_prompt(query, documents, rerank_instruction)

            
            # Call the model
            response = self.model.invoke(
                input=prompt,
                enable_thinking = False
            )
            
            # Parse the response to get reranked indices and scores
            response_content = response.content if hasattr(response, 'content') else str(response)
            reranked_docs_with_scores = self._parse_rerank_response(response_content, documents)
            
            # Return top_k results
            return reranked_docs_with_scores[:top_k]
        
        except Exception as e:
            logger.error(f"Error in LLM reranking: {str(e)}")
            # Return original documents if reranking fails
            return []
    
    def _create_rerank_prompt(self, query: str, documents: List[Document], instruction: str) -> str:
        """
        Create a prompt for LLM to rerank documents.
        
        Args:
            query: Query text
            documents: List of documents to rerank
            instruction: Instruction for reranking
            
        Returns:
            Prompt for LLM
        """
        doc_texts = []
        for i, doc in enumerate(documents):
            doc_texts.append(f"文档[{i}]: {doc.page_content}")
        
        documents_text = "\n\n".join(doc_texts)
        
        prompt = f"""你是一个专业的文档重排序系统。你的任务是根据用户的查询，对一系列文档按照相关性从高到低进行排序。

需要排序的文档如下：

{documents_text}

用户查询:
{query}

排序指南:
{instruction}

请按照以下JSON格式返回排序后的文档索引及其相关性分数:

```json
{{
  "rankings": [
    {{"index": 文档索引, "score": 相关性分数(0-1之间)}},
    ...
  ]
}}
```

仅返回上述JSON格式，不要包含任何其他解释文本。按相关性分数从高到低进行排序。"""

        return prompt
    
    def _parse_rerank_response(self, response_text: str, documents: List[Document]) -> List[Tuple[Document, float]]:
        """
        Parse the LLM response to get reranked documents with scores.
        
        Args:
            response_text: LLM response text
            documents: Original documents
            
        Returns:
            List of (document, score) tuples
        """
        try:
            # Extract JSON from response (in case there is extra text)
            json_text = ""
            in_json = False
            for line in response_text.split('\n'):
                if line.strip() == '```json':
                    in_json = True
                    continue
                elif line.strip() == '```' and in_json:
                    break
                elif in_json:
                    json_text += line + '\n'
            
            # If no JSON block found, try to parse the whole response
            if not json_text:
                json_text = response_text
            
            # Parse JSON
            result = json.loads(json_text)
            
            if "rankings" not in result:
                logger.warning(f"Unexpected response format: {response_text}")
                return []
            
            # Extract reranked indices and scores
            reranked_docs_with_scores = []
            for ranking in result["rankings"]:
                if "index" in ranking and "score" in ranking:
                    idx = ranking["index"]
                    score = float(ranking["score"])
                    
                    if 0 <= idx < len(documents):
                        doc = documents[idx]
                        
                        # Store original score if it exists
                        if "score" in doc.metadata:
                            doc.metadata["original_score"] = doc.metadata["score"]
                            
                        # Update with new score
                        doc.metadata["score"] = score
                        reranked_docs_with_scores.append((doc, score))
            
            # Sort by score descending
            reranked_docs_with_scores.sort(key=lambda x: x[1], reverse=True)
            
            # If we couldn't extract any valid rankings, return original documents
            if not reranked_docs_with_scores:
                return []
            
            return reranked_docs_with_scores
            
        except Exception as e:
            logger.error(f"Error parsing LLM rerank response: {str(e)}")
            return []