# 文档重排序（Reranking）模块

文档重排序模块是RAG（检索增强生成）系统的重要组件，用于优化检索结果的排序，将最相关的文档排在前面，提高后续生成步骤的质量。

## 功能概述

- 支持多种重排序方法：
  - 基于LLM文本模型的重排序
  - 专用重排序模型（如Qwen、BGE等）
- 支持多种模型提供商：
  - 阿里云（Qwen）
  - 百度（文心一言）
  - 火山引擎（字节）
  - 本地模型
  - 自定义API
- 灵活的重排序指令
- 可配置的分数阈值和结果数量

## 使用方法

### 1. 直接使用重排序器

```python
from app.core.rag.rerank import LLMReranker, RerankModelReranker, RerankModelType, RerankProviderType

# 创建LLM重排序器
llm_reranker = LLMReranker(
    model_id="qwen-turbo",
    model_name="阿里Qwen-Turbo",
    default_instruction="请根据查询与文档的相关性对文档进行排序，优先考虑内容与查询直接相关的文档。",
    temperature=0.0
)

# 创建专用重排序模型
rerank_model = RerankModelReranker(
    model_id="qwen-reranker",
    model_name="阿里Qwen-Reranker",
    api_key="your_api_key",
    provider_type=RerankProviderType.ALIYUN,
    model_version="qwen-reranker-7b-v1"
)

# 执行重排序
query = "如何使用Python处理JSON数据?"
documents = [...] # 文档列表
reranked_docs = await llm_reranker.rerank(query, documents, top_k=5)
```

### 2. 使用重排序管理器

```python
from app.core.rag.rerank import RerankerManager

# 创建重排序管理器
reranker_manager = RerankerManager()

# 配置LLM重排序器
llm_config = {
    "model_id": "qwen-turbo",
    "model_name": "阿里Qwen-Turbo",
    "model_type": "llm",
    "temperature": 0.0,
    "default_instruction": "根据查询相关性排序"
}

# 配置专用重排序模型
rerank_model_config = {
    "model_id": "qwen-reranker",
    "model_name": "阿里Qwen-Reranker",
    "model_type": "rerank",
    "provider_type": "aliyun",
    "api_key": "your_api_key", 
    # 或使用环境变量: "api_key_env": "ALIYUN_API_KEY",
    "model_version": "qwen-reranker-7b-v1"
}

# 执行重排序
query = "如何使用Python处理JSON数据?"
documents = [...] # 文档列表

# 使用LLM重排序
reranked_docs_llm = await reranker_manager.rerank_documents(
    llm_config, query, documents, top_k=5
)

# 使用专用重排序模型
reranked_docs_model = await reranker_manager.rerank_documents(
    rerank_model_config, query, documents, top_k=5
)
```

### 3. 在检索流程中使用

```python
from app.core.rag.retrieval import Retriever

# 创建检索器
retriever = Retriever(db_session)

# 配置知识库的重排序设置
kb_config = {
    "retrieval": {
        "default_search_type": "hybrid",
        "hybrid_search": {
            "vector_weight": 0.7,
            "keyword_weight": 0.3,
            "rerank": {
                "enabled": True,
                "model_id": "qwen-turbo",
                "model_type": "llm",
                "instruction": "请对文档按照与查询的相关性进行排序"
            }
        }
    }
}

# 执行检索并自动重排序
results = retriever.retrieve_documents(
    kb_id="kb_id",
    tenant_id="tenant_id",
    query="如何实现Python异步编程?",
    use_rerank=True
)
```

## 配置示例

### LLM重排序器配置

```python
llm_config = {
    "model_id": "qwen-turbo",  # 模型ID，需在模型工厂中注册
    "model_name": "阿里Qwen-Turbo",  # 模型名称（可选）
    "model_type": "llm",  # 指定为LLM重排序器
    "temperature": 0.0,  # LLM温度参数，越低越稳定
    "max_tokens": 500,  # 最大输出Token数
    "default_instruction": "请根据查询与文档的相关性对文档进行排序，详细分析查询意图，优先考虑内容相关、信息丰富的文档。"
}
```

### 阿里云专用重排序模型配置

```python
aliyun_config = {
    "model_id": "qwen-reranker",  # 模型ID（自定义）
    "model_name": "阿里云QwenReranker",  # 模型名称（可选）
    "model_type": "rerank",  # 指定为专用重排序模型
    "provider_type": "aliyun",  # 提供商类型
    "api_key": "your_api_key_here",  # API密钥
    # 或使用环境变量: "api_key_env": "ALIYUN_API_KEY",
    "api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-embedding/rerank",  # API端点（可选）
    "model_version": "qwen-reranker-7b-v1",  # 模型版本
    "default_instruction": "请根据查询相关性排序文档"
}
```

### 百度专用重排序模型配置

```python
baidu_config = {
    "model_id": "baidu-reranker",
    "model_name": "百度文心重排序",
    "model_type": "rerank",
    "provider_type": "baidu",
    "api_key": "your_api_key_here",
    "api_secret": "your_api_secret_here",
    # 或使用环境变量:
    # "api_key_env": "BAIDU_API_KEY",
    # "api_secret_env": "BAIDU_SECRET_KEY",
    "api_url": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/rerank/ranking"
}
```

### 火山引擎专用重排序模型配置

```python
huoshan_config = {
    "model_id": "huoshan-reranker",
    "model_name": "火山引擎重排序",
    "model_type": "rerank",
    "provider_type": "huoshan",
    "api_key": "your_api_key_here",
    "api_url": "https://open.volcengineapi.com/api/v1/rerank",
    # 可添加其他参数
    "additional_params": {
        "custom_param1": "value1",
        "custom_param2": "value2"
    }
}
```

### 本地重排序模型配置

```python
local_config = {
    "model_id": "local-reranker",
    "model_name": "本地BGE重排序器",
    "model_type": "rerank",
    "provider_type": "local",
    "model_version": "BAAI/bge-reranker-base",  # HuggingFace模型标识或本地路径
    "api_key": "not_used_but_required"  # 接口需要但不实际使用
}
```

## 重排序模型比较

| 模型类型 | 优点 | 缺点 | 适用场景 |
|---------|------|------|---------|
| LLM重排序 | 理解语义更深入，可处理复杂指令 | 速度较慢，成本较高 | 需要深度语义理解的场景 |
| 专用重排序模型 | 速度快，成本低 | 语义理解能力有限，不支持复杂指令 | 高吞吐量的简单重排序场景 |

## 注意事项

1. LLM重排序器需确保对应的LLM模型已在模型工厂中注册
2. 使用专用重排序模型时需提供有效的API密钥
3. 如果重排序失败，系统将回退使用原始检索结果
4. 对于大量文档（>20篇）的重排序，LLM重排序可能受限于上下文窗口大小
5. 重排序配置可根据需求灵活调整 