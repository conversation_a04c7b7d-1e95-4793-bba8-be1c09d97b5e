"""
Reranker manager for document reranking.
"""

from typing import Dict, List, Any

from langchain_core.documents import Document
from sqlalchemy.orm import Session

from app.core.rag.rerank.base import BaseReranker
from app.core.rag.rerank.reranker_factory import RerankerFactory
from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)


class RerankerManager:
    """Manager for reranker instances."""

    def __init__(self, db: Session):
        """Initialize the reranker manager."""
        self.db = db
        self._reranker_factory = RerankerFactory(db=db)
        logger.info("Initialized RerankerManager")

    async def get_reranker(
        self,
        config: Dict[str, Any],
    ) -> BaseReranker:
        """
        Get or create a reranker instance.

        Args:
            config: Reranker configuration

        Returns:
            Reranker instance
        """
        # Check if required fields are missing
        if "model_id" not in config:
            raise ValueError("model_id is required in reranker config")
        
        model_id = config["model_id"]  
        tenant_id = config.get("tenant_id", "")
        
        # Create reranker using reranker factory
        reranker = await self._reranker_factory.create_reranker(
            model_id=model_id,
            tenant_id=tenant_id,
            extra_config=config
        )
        
        if not reranker:
            raise ValueError(f"Failed to create reranker for model ID: {model_id}")
        
        return reranker
    
    async def rerank_documents(
        self,
        reranker_config: Dict[str, Any],
        query: str,
        documents: List[Document],
        top_k: int = 5
    ) -> List[Document]:
        """
        Rerank documents using the configured reranker.
        
        Args:
            reranker_config: Reranker configuration
            query: Query text
            documents: List of documents to rerank
            top_k: Number of top documents to return
            
        Returns:
            Reranked list of documents
        """
        if not documents:
            return []
            
        try:
            # Get reranker
            reranker = await self.get_reranker(reranker_config)
            
            # Perform reranking
            reranked_docs = await reranker.rerank(
                query=query,
                documents=documents,
                top_k=top_k
            )
            
            return reranked_docs
            
        except Exception as e:
            logger.error(f"Error in reranking documents: {str(e)}")
            # Return original documents if reranking fails
            return documents[:top_k]
    
    async def rerank_documents_with_scores(
        self,
        reranker_config: Dict[str, Any],
        query: str,
        documents: List[Document],
        top_k: int = 5
    ) -> List[tuple[Document, float]]:
        """
        Rerank documents and return scores.
        
        Args:
            reranker_config: Reranker configuration
            query: Query text
            documents: List of documents to rerank
            top_k: Number of top documents to return
            instruction: Optional instruction for reranking
            
        Returns:
            List of (document, score) tuples
        """
        if not documents:
            return []
            
        try:
            # Get reranker
            reranker = await self.get_reranker(reranker_config)
            
            # Perform reranking
            reranked_docs_with_scores = await reranker.rerank_with_scores(
                query=query,
                documents=documents,
                top_k=top_k
            )
            
            return reranked_docs_with_scores
            
        except Exception as e:
            logger.error(f"Error in reranking documents with scores: {str(e)}")
            # Return original documents with their scores if reranking fails
            return [(doc, doc.metadata.get("score", 0.0)) for doc in documents[:top_k]] 