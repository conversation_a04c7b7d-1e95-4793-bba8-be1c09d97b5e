"""
Base reranker interface.
"""

from typing import List, Optional, Tuple
from abc import ABC, abstractmethod

from langchain_core.documents import Document


class BaseReranker(ABC):
    """Base reranker interface."""
    
    def __init__(self, model_key: str, tenant_id: str):
        """
        Initialize the base reranker.
        
        Args:
            model_key: Model key
            tenant_id: Tenant ID
        """
        self.model_key = model_key
        self.tenant_id = tenant_id
        
    
    @abstractmethod
    async def rerank(
        self,
        query: str,
        documents: List[Document],
        top_k: int = 5,
    ) -> List[Document]:
        """
        Rerank documents based on query relevance.
        
        Args:
            query: Query text
            documents: List of documents to rerank
            top_k: Number of top documents to return
            
        Returns:
            Reranked list of documents
        """
        pass
    
    @abstractmethod
    async def rerank_with_scores(
        self,
        query: str,
        documents: List[Document],
        top_k: int = 5,
    ) -> List[Tuple[Document, float]]:
        """
        Rerank documents based on query relevance and return scores.
        
        Args:
            query: Query text
            documents: List of documents to rerank
            top_k: Number of top documents to return
            
        Returns:
            List of (document, score) tuples sorted by relevance score (descending)
        """
        pass 