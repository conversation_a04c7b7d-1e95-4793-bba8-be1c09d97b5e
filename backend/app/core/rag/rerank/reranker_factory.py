"""
Factory for creating reranker instances.
"""

from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.rag.rerank.base import BaseReranker
from app.core.rag.rerank.client import <PERSON><PERSON><PERSON><PERSON>, QwenReranker
from app.core.model_factory import ModelFactory
from app.models.model import Model, Provider, ProviderAccessCredential
from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)

class RerankerFactory:
    """Factory for creating reranker instances."""
    
    def __init__(self, db: Session):
        """Initialize the reranker factory."""
        self.db = db
        # 不直接导入ModelFactory，避免循环依赖
        logger.info("Initialized RerankerFactory")
    
    async def create_reranker(
        self, 
        model_id: str,
        tenant_id: str,
        extra_config: Optional[Dict[str, Any]] = None
    ) -> Optional[BaseReranker]:
        """
        Create a reranker instance.
        
        Args:
            model_id: Model ID
            tenant_id: Tenant ID
            extra_config: Extra configuration
            
        Returns:
            Reranker instance
        """
        
        
        extra_config = extra_config or {}
        
        try:
            # 查询模型信息
            model = self.db.query(Model).filter(Model.id == model_id, Model.is_active == True).first()
            if not model:
                logger.error(f"Model {model_id} does not exist or is not active")
                return None
                
            # 查询访问凭证信息
            credential = self.db.query(ProviderAccessCredential).filter(
                ProviderAccessCredential.tenant_id == tenant_id,
                ProviderAccessCredential.provider_id == model.provider_id,
                ProviderAccessCredential.is_active == True
            ).first()
            if not credential:
                logger.error(f"No active credential found for tenant {tenant_id} and provider {model.provider_id}")
                return None
            
            # 查询提供商信息
            provider = self.db.query(Provider).filter(
                Provider.id == credential.provider_id, 
                Provider.is_active == True
            ).first()
            if not provider:
                logger.error(f"Provider {credential.provider_id} does not exist or is not active")
                return None

            # 合并配置
            config = {}
            if model.extra_config:
                config.update(model.extra_config)
            if extra_config:
                config.update(extra_config)
            
            # 创建重排序模型实例
            model_type = model.model_type
            if model_type == "rerank":
                if provider.key.lower() == "aliyun":
                    return QwenReranker(
                        db=self.db, 
                        model_key=model.key, 
                        tenant_id=tenant_id,
                        api_key=credential.credentials.get("api_key")
                    )
            elif model_type == "llm":
                # llm reranker - 需要使用ModelFactory创建LLM模型
                # 延迟导入ModelFactory，避免循环依赖
                model_factory = ModelFactory()
                llm_model =  model_factory.create_model(
                    db=self.db, 
                    tenant_id=tenant_id, 
                    model_key=model.key, 
                    extra_config={
                        "temperature": config.get("temperature", 0.0), 
                        "max_tokens": config.get("max_tokens", 5000)
                    }
                )
                
                if llm_model is None:
                    logger.error(f"Failed to load LLM model: {model.key}")
                    return None
                    
                
                return LLMReranker(
                    db=self.db, 
                    model=llm_model,
                    model_key=model.key,
                    tenant_id=tenant_id,
                    default_instruction=config.get("default_instruction", 
                        "Rank documents based on their relevance to the query, prioritizing documents with content directly related to the query."),
                    temperature=config.get("temperature", 0.0),
                    max_tokens=config.get("max_tokens", 5000)
                )
                
            return None
            
        except ImportError as e:
            logger.error(f"Import error when creating reranker: {str(e)}")
            return None
        except Exception as e:
            logger.exception(f"Failed to create reranker: {str(e)}")
            return None 