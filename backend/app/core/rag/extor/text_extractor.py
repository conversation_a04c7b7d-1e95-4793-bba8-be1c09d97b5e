"""
文本文件解析器。
"""

import re
import string
from pathlib import Path
from typing import List, Optional

from langchain.schema import Document

from app.core.rag.extor.extractor_base import BaseExtractor
from app.core.rag.extor.helpers import detect_file_encodings

from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)


class TextExtractor(BaseExtractor):
    """
    加载文本文件。
    
    Args:
        file_path: 要加载的文件路径。
        encoding: 文件编码（可选）。
        autodetect_encoding: 是否自动检测文件编码。
        binary_fallback: 如果文本解析失败，是否尝试二进制解析。
    """

    def __init__(
        self, 
        file_path: str, 
        encoding: Optional[str] = None, 
        autodetect_encoding: bool = False,
        binary_fallback: bool = True
    ):
        """使用文件路径初始化。"""
        self._file_path = file_path
        self._encoding = encoding
        self._autodetect_encoding = autodetect_encoding
        self._binary_fallback = binary_fallback

    def extract(self) -> List[Document]:
        """从文件路径加载文档。"""
        logger.debug(f"从文本文件加载: {self._file_path}")
        text = ""
        try:
            text = Path(self._file_path).read_text(encoding=self._encoding)
            logger.debug(f"成功读取文本文件: {self._file_path}")
        except UnicodeDecodeError as e:
            logger.warning(f"使用指定编码读取文件时出错: {e}")
            if self._autodetect_encoding:
                logger.info(f"尝试自动检测文件编码: {self._file_path}")
                detected_encodings = detect_file_encodings(self._file_path)
                for encoding in detected_encodings:
                    try:
                        logger.debug(f"尝试使用编码 {encoding.encoding} 读取文件")
                        text = Path(self._file_path).read_text(encoding=encoding.encoding)
                        logger.info(f"成功使用编码 {encoding.encoding} 读取文件")
                        break
                    except UnicodeDecodeError:
                        logger.warning(f"使用编码 {encoding.encoding} 读取文件失败")
                        continue
                
                # 如果所有编码都失败但启用了二进制回退
                if not text and self._binary_fallback:
                    logger.info("所有编码都失败，尝试二进制解析")
                    return self._extract_as_binary()
            elif self._binary_fallback:
                logger.info("尝试二进制解析")
                return self._extract_as_binary()
            else:
                logger.error(f"加载文件 {self._file_path} 时出错: {e}")
                raise RuntimeError(f"加载文件 {self._file_path} 时出错") from e
        except Exception as e:
            logger.error(f"加载文件 {self._file_path} 时出错: {e}")
            if self._binary_fallback:
                logger.info("尝试二进制解析")
                return self._extract_as_binary()
            else:
                raise RuntimeError(f"加载文件 {self._file_path} 时出错") from e

        metadata = {"source": self._file_path}
        return [Document(page_content=text, metadata=metadata)]
    
    def _extract_as_binary(self) -> List[Document]:
        """从二进制内容中提取文本。"""
        logger.info(f"尝试从二进制内容中提取文本: {self._file_path}")
        
        try:
            # 读取二进制内容
            with open(self._file_path, 'rb') as f:
                binary_content = f.read()
            
            # 尝试提取文本，保留制表符和换行符
            text = binary_content.decode('latin-1', errors='replace')
            
            # 保留可能的表格结构和可打印字符
            printable_chars = set(string.printable)
            chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
            
            # 构建结果字符串，保留可打印字符、空白和中文
            result = []
            for c in text:
                if c in printable_chars or chinese_pattern.match(c):
                    result.append(c)
            
            result_text = ''.join(result)
            
            # 移除无意义的控制字符，但保留表格结构
            result_text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', result_text)
            
            # 提取可能的有意义内容
            # 查找连续的中文或英文单词
            meaningful_parts = []
            
            # 提取可能的标题和内容（连续的中文或英文单词）
            word_pattern = re.compile(r'[A-Za-z\u4e00-\u9fff]{3,}')
            words = word_pattern.findall(result_text)
            
            if words:
                meaningful_text = ' '.join(words)
                meaningful_parts.append(meaningful_text)
            
            # 如果找到有意义的内容，使用它
            if meaningful_parts:
                final_text = '\n'.join(meaningful_parts)
                logger.info(f"成功从二进制内容中提取有意义的文本: {self._file_path}")
                return [Document(page_content=final_text, metadata={"source": self._file_path})]
            
            # 如果没有找到有意义的内容，返回原始处理结果
            logger.info(f"成功从二进制内容中提取文本: {self._file_path}")
            return [Document(page_content=result_text, metadata={"source": self._file_path})]
            
        except Exception as e:
            logger.error(f"从二进制内容中提取文本时出错: {e}")
            # 返回包含错误信息的文档，而不是抛出异常
            return [Document(
                page_content=f"[无法提取文件内容: {str(e)}]",
                metadata={"source": self._file_path, "error": str(e)}
            )] 