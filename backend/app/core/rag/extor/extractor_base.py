"""
抽象接口，用于文档解析器实现。
"""

from abc import ABC, abstractmethod
from typing import List

from langchain.schema import Document


class BaseExtractor(ABC):
    """
    文档解析器接口。
    所有具体的文档解析器都应该继承这个类，并实现extract方法。
    """

    @abstractmethod
    def extract(self) -> List[Document]:
        """
        从文件中提取文本内容，并返回Document对象列表。
        
        Returns:
            List[Document]: 提取的文档列表
        """
        raise NotImplementedError 