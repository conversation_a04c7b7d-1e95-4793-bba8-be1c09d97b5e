"""
CSV文件解析器。
"""

import csv
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any

from langchain.schema import Document

from app.core.rag.extor.extractor_base import BaseExtractor
from app.core.rag.extor.helpers import detect_file_encodings

from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)

class CSVExtractor(BaseExtractor):
    """
    加载CSV文件。
    
    Args:
        file_path: 要加载的文件路径。
        encoding: 文件编码（可选）。
        autodetect_encoding: 是否自动检测文件编码。
        source_column: 用作源信息的列名（可选）。
        csv_args: 传递给CSV解析器的参数（可选）。
    """

    def __init__(
        self,
        file_path: str,
        encoding: Optional[str] = None,
        autodetect_encoding: bool = False,
        source_column: Optional[str] = None,
        csv_args: Optional[Dict[str, Any]] = None,
    ):
        """使用文件路径初始化。"""
        self._file_path = file_path
        self._encoding = encoding
        self._autodetect_encoding = autodetect_encoding
        self.source_column = source_column
        self.csv_args = csv_args or {}

    def extract(self) -> List[Document]:
        """将数据加载到文档对象中。"""
        logger.debug(f"从CSV文件加载: {self._file_path}")
        docs = []
        
        try:
            with open(self._file_path, newline="", encoding=self._encoding) as csvfile:
                docs = self._read_from_file(csvfile)
                logger.info(f"成功读取CSV文件: {self._file_path}")
        except UnicodeDecodeError as e:
            logger.warning(f"使用指定编码读取CSV文件时出错: {e}")
            if self._autodetect_encoding:
                logger.info(f"尝试自动检测文件编码: {self._file_path}")
                detected_encodings = detect_file_encodings(self._file_path)
                for encoding in detected_encodings:
                    try:
                        logger.debug(f"尝试使用编码 {encoding.encoding} 读取CSV文件")
                        with open(self._file_path, newline="", encoding=encoding.encoding) as csvfile:
                            docs = self._read_from_file(csvfile)
                            logger.info(f"成功使用编码 {encoding.encoding} 读取CSV文件")
                        break
                    except UnicodeDecodeError:
                        logger.warning(f"使用编码 {encoding.encoding} 读取CSV文件失败")
                        continue
            else:
                logger.error(f"加载CSV文件 {self._file_path} 时出错: {e}")
                raise RuntimeError(f"加载CSV文件 {self._file_path} 时出错") from e
        except Exception as e:
            logger.error(f"加载CSV文件时出错: {e}")
            raise RuntimeError(f"加载CSV文件 {self._file_path} 时出错") from e

        return docs

    def _read_from_file(self, csvfile) -> List[Document]:
        """从文件中读取CSV数据。"""
        docs = []
        
        try:
            # 尝试使用pandas
            try:
                import pandas as pd
                return self._read_with_pandas(csvfile)
            except ImportError:
                logger.warning("未安装pandas库，使用内置csv模块")
                
                # 使用内置csv模块
                return self._read_with_csv_module(csvfile)
                
        except csv.Error as e:
            logger.error(f"读取CSV文件时出错: {e}")
            raise RuntimeError(f"读取CSV文件时出错: {e}")
        except Exception as e:
            logger.error(f"处理CSV文件时出错: {e}")
            raise RuntimeError(f"处理CSV文件时出错: {e}")
    
    def _read_with_pandas(self, csvfile) -> List[Document]:
        """使用pandas读取CSV文件。"""
        import pandas as pd
        
        # 加载CSV文件到pandas DataFrame
        df = pd.read_csv(csvfile, on_bad_lines="skip", **self.csv_args)
        
        # 检查源列是否存在
        if self.source_column and self.source_column not in df.columns:
            logger.warning(f"源列 '{self.source_column}' 在CSV文件中不存在")
            raise ValueError(f"源列 '{self.source_column}' 在CSV文件中不存在")
        
        # 创建文档对象
        docs = []
        for i, row in df.iterrows():
            # 将行数据格式化为文本
            content = ";".join(f"{col.strip()}: {str(row[col]).strip()}" for col in df.columns)
            source = str(row[self.source_column]) if self.source_column else ""
            metadata = {"source": source or self._file_path, "row": i}
            doc = Document(page_content=content, metadata=metadata)
            docs.append(doc)
        
        logger.info(f"使用pandas成功提取了 {len(docs)} 行数据")
        return docs
    
    def _read_with_csv_module(self, csvfile) -> List[Document]:
        """使用内置csv模块读取CSV文件。"""
        # 创建CSV读取器
        csv_reader = csv.reader(csvfile, **self.csv_args)
        
        # 读取标题行
        try:
            headers = next(csv_reader)
        except StopIteration:
            logger.warning("CSV文件为空或格式不正确")
            return [Document(
                page_content="[CSV文件为空或格式不正确]",
                metadata={"source": self._file_path}
            )]
        
        # 确定源列索引
        source_idx = -1
        if self.source_column:
            try:
                source_idx = headers.index(self.source_column)
            except ValueError:
                logger.warning(f"源列 '{self.source_column}' 在CSV文件中不存在")
                raise ValueError(f"源列 '{self.source_column}' 在CSV文件中不存在")
        
        # 创建文档对象
        docs = []
        for i, row in enumerate(csv_reader):
            if not row:  # 跳过空行
                continue
                
            # 确保行长度与标题行相同
            if len(row) < len(headers):
                row.extend([""] * (len(headers) - len(row)))
            elif len(row) > len(headers):
                row = row[:len(headers)]
            
            # 将行数据格式化为文本
            content = ";".join(f"{headers[j].strip()}: {row[j].strip()}" for j in range(len(headers)))
            source = row[source_idx] if source_idx >= 0 else ""
            metadata = {"source": source or self._file_path, "row": i}
            doc = Document(page_content=content, metadata=metadata)
            docs.append(doc)
        
        logger.info(f"使用csv模块成功提取了 {len(docs)} 行数据")
        return docs 