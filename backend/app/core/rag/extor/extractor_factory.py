"""
文档解析器工厂。
"""

import logging
import os
from pathlib import Path
from typing import Dict, Optional, Type

from app.core.rag.extor.csv_extractor import CSVExtractor
from app.core.rag.extor.excel_extractor import ExcelExtractor
from app.core.rag.extor.extractor_base import BaseExtractor
from app.core.rag.extor.html_extractor import HtmlExtractor
from app.core.rag.extor.markdown_extractor import MarkdownExtractor
from app.core.rag.extor.pdf_extractor import PdfExtractor
from app.core.rag.extor.powerpoint_extractor import PowerPointExtractor
from app.core.rag.extor.text_extractor import TextExtractor
from app.core.rag.extor.word_extractor import WordExtractor

from app.core.logging_config import logging_config
logger = logging_config.get_logger(__name__)


class ExtractorFactory:
    """
    文档解析器工厂类。
    根据文件类型创建合适的解析器。
    """
    
    # 文件扩展名到解析器类的映射
    _EXTRACTORS: Dict[str, Type[BaseExtractor]] = {
        # 文本文件
        ".txt": TextExtractor,
        ".log": TextExtractor,
        ".json": TextExtractor,
        ".xml": TextExtractor,
        ".yaml": TextExtractor,
        ".yml": TextExtractor,
        
        # PDF文件
        ".pdf": PdfExtractor,
        
        # Word文档
        ".docx": WordExtractor,
        ".doc": WordExtractor,
        
        # Excel文件
        ".xlsx": ExcelExtractor,
        ".xls": ExcelExtractor,
        
        # CSV文件
        ".csv": CSVExtractor,
        
        # HTML文件
        ".html": HtmlExtractor,
        ".htm": HtmlExtractor,
        
        # Markdown文件
        ".md": MarkdownExtractor,
        ".markdown": MarkdownExtractor,
        ".mdx": MarkdownExtractor,
        
        # PowerPoint文件
        ".pptx": PowerPointExtractor,
        ".ppt": PowerPointExtractor,
    }
    
    @classmethod
    def get_extractor(cls, file_path: str, **kwargs) -> BaseExtractor:
        """
        根据文件路径获取合适的解析器。
        
        Args:
            file_path: 文件路径
            **kwargs: 传递给解析器的额外参数
            
        Returns:
            BaseExtractor: 相应的文档解析器实例
            
        Raises:
            ValueError: 如果文件类型不受支持
        """
        # 获取文件扩展名
        file_extension = Path(file_path).suffix.lower()
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 获取解析器类
        extractor_class = cls._EXTRACTORS.get(file_extension)
        
        if extractor_class:
            logger.info(f"为文件 {file_path} 选择 {extractor_class.__name__} 解析器")
            return extractor_class(file_path=file_path, **kwargs)
        else:
            # 对于未知文件类型，尝试使用文本解析器并启用二进制回退
            logger.warning(f"不支持的文件类型: {file_extension}，尝试使用文本解析器")
            
            # 检查文件是否可能是二进制文件
            is_likely_binary = cls._is_likely_binary(file_path)
            
            if is_likely_binary:
                logger.info(f"文件 {file_path} 可能是二进制文件，使用文本解析器的二进制模式")
            
            # 确保二进制回退被启用
            kwargs['binary_fallback'] = True
            
            return TextExtractor(file_path=file_path, **kwargs)
    
    @classmethod
    def is_supported(cls, file_path: str) -> bool:
        """
        检查文件类型是否受支持。
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 如果文件类型受支持，则为True
        """
        file_extension = Path(file_path).suffix.lower()
        return file_extension in cls._EXTRACTORS
    
    @classmethod
    def get_supported_extensions(cls) -> list[str]:
        """
        获取所有支持的文件扩展名。
        
        Returns:
            list[str]: 支持的文件扩展名列表
        """
        return list(cls._EXTRACTORS.keys())
    
    @classmethod
    def register_extractor(cls, extension: str, extractor_class: Type[BaseExtractor]) -> None:
        """
        注册新的解析器类型。
        
        Args:
            extension: 文件扩展名（包括点，如".pdf"）
            extractor_class: 解析器类
        """
        cls._EXTRACTORS[extension.lower()] = extractor_class
        logger.info(f"已注册 {extractor_class.__name__} 用于处理 {extension} 文件")
    
    @staticmethod
    def _is_likely_binary(file_path: str) -> bool:
        """
        检查文件是否可能是二进制文件。
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 如果文件可能是二进制文件，则为True
        """
        # 读取文件的前4KB
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(4096)
                
            # 检查是否包含空字节（通常表示二进制文件）
            if b'\x00' in chunk:
                return True
                
            # 检查非ASCII字符的比例
            non_ascii = sum(1 for b in chunk if b < 32 and b not in (9, 10, 13))  # 非空白ASCII控制字符
            if non_ascii / len(chunk) > 0.1:  # 超过10%是非ASCII字符
                return True
                
            return False
        except Exception:
            # 如果无法读取，假设是二进制
            return True 