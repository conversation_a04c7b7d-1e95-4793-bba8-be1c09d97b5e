"""
PDF文件解析器。
"""

from typing import Iterator, List, Optional

from langchain.schema import Document

from app.core.rag.extor.extractor_base import BaseExtractor

from app.core.logging_config import logging_config
logger = logging_config.get_logger(__name__)


class PdfExtractor(BaseExtractor):
    """
    加载PDF文件。
    
    Args:
        file_path: 要加载的文件路径。
    """

    def __init__(self, file_path: str):
        """使用文件路径初始化。"""
        self._file_path = file_path

    def extract(self) -> List[Document]:
        """从文件路径加载文档。"""
        logger.debug(f"从PDF文件加载: {self._file_path}")
        try:
            import pypdf
        except ImportError:
            logger.error("未安装pypdf库，无法解析PDF文件")
            raise ImportError(
                "解析PDF文件需要pypdf库。请安装: pip install pypdf"
            )
        
        documents = []
        
        try:
            with open(self._file_path, "rb") as file:
                pdf = pypdf.PdfReader(file)
                
                for i, page in enumerate(pdf.pages):
                    text = page.extract_text()
                    if text:
                        metadata = {
                            "source": self._file_path,
                            "page": i + 1,
                            "total_pages": len(pdf.pages)
                        }
                        documents.append(Document(page_content=text, metadata=metadata))
                        logger.debug(f"成功提取PDF页面 {i+1}/{len(pdf.pages)}")
                    else:
                        logger.warning(f"PDF页面 {i+1} 没有可提取的文本")
                
                logger.info(f"成功从PDF文件提取 {len(documents)} 页内容: {self._file_path}")
                return documents
        
        except Exception as e:
            logger.error(f"解析PDF文件时出错: {e}")
            raise RuntimeError(f"解析PDF文件 {self._file_path} 时出错") from e 