"""
Markdown文件解析器。
"""

import re
from pathlib import Path
from typing import List, Optional, Tuple

from langchain.schema import Document

from app.core.rag.extor.extractor_base import BaseExtractor
from app.core.rag.extor.helpers import detect_file_encodings

from app.core.logging_config import logging_config
logger = logging_config.get_logger(__name__)


class MarkdownExtractor(BaseExtractor):
    """
    加载Markdown文件。
    
    Args:
        file_path: 要加载的文件路径。
        remove_hyperlinks: 是否移除超链接。
        remove_images: 是否移除图片。
        encoding: 文件编码（可选）。
        autodetect_encoding: 是否自动检测文件编码。
    """

    def __init__(
        self,
        file_path: str,
        remove_hyperlinks: bool = False,
        remove_images: bool = False,
        encoding: Optional[str] = None,
        autodetect_encoding: bool = True,
    ):
        """使用文件路径初始化。"""
        self._file_path = file_path
        self._remove_hyperlinks = remove_hyperlinks
        self._remove_images = remove_images
        self._encoding = encoding
        self._autodetect_encoding = autodetect_encoding

    def extract(self) -> List[Document]:
        """从文件路径加载文档。"""
        logger.debug(f"从Markdown文件加载: {self._file_path}")
        
        try:
            # 解析Markdown文件
            tups = self.parse_tups(self._file_path)
            documents = []
            
            for header, value in tups:
                value = value.strip()
                if header is None:
                    documents.append(Document(page_content=value, metadata={"source": self._file_path}))
                else:
                    documents.append(Document(
                        page_content=f"\n\n{header}\n{value}",
                        metadata={"source": self._file_path, "header": header}
                    ))
            
            logger.info(f"成功解析Markdown文件: {self._file_path}")
            return documents
            
        except Exception as e:
            logger.error(f"解析Markdown文件时出错: {e}")
            return [Document(
                page_content=f"[无法提取Markdown内容: {str(e)}]", 
                metadata={"source": self._file_path, "error": str(e)}
            )]

    def markdown_to_tups(self, markdown_text: str) -> List[Tuple[Optional[str], str]]:
        """
        将Markdown文本转换为元组列表。
        
        元组的第一个元素是标题，第二个元素是标题下的文本。
        如果没有标题，第一个元素为None。
        """
        logger.debug("将Markdown文本转换为元组列表")
        markdown_tups: List[Tuple[Optional[str], str]] = []
        lines = markdown_text.split("\n")

        current_header = None
        current_text = ""
        code_block_flag = False

        for line in lines:
            # 处理代码块
            if line.startswith("```"):
                code_block_flag = not code_block_flag
                current_text += line + "\n"
                continue
                
            if code_block_flag:
                current_text += line + "\n"
                continue
                
            # 检测标题行
            header_match = re.match(r"^#+\s", line)
            if header_match:
                # 如果已经有标题，保存当前内容
                if current_header is not None:
                    markdown_tups.append((current_header, current_text))

                current_header = line
                current_text = ""
            else:
                current_text += line + "\n"
                
        # 添加最后一个部分
        markdown_tups.append((current_header, current_text))

        # 处理标题格式
        if current_header is not None:
            # 移除标题中的#号并清理HTML标签
            markdown_tups = [
                (re.sub(r"#", "", header).strip() if header else None, re.sub(r"<.*?>", "", value))
                for header, value in markdown_tups
            ]
        else:
            # 如果没有标题，简化换行符
            markdown_tups = [(header, re.sub("\n", " ", value)) for header, value in markdown_tups]

        return markdown_tups

    def remove_images(self, content: str) -> str:
        """移除Markdown中的图片。"""
        logger.debug("移除Markdown中的图片")
        # 移除 ![[image.png]] 格式的图片
        pattern = r"!{1}\[\[(.*)\]\]"
        content = re.sub(pattern, "", content)
        
        # 移除 ![alt](url) 格式的图片
        pattern = r"!\[(.*?)\]\((.*?)\)"
        content = re.sub(pattern, "", content)
        
        return content

    def remove_hyperlinks(self, content: str) -> str:
        """移除Markdown中的超链接，保留链接文本。"""
        logger.debug("移除Markdown中的超链接")
        # 将 [text](url) 替换为 text
        pattern = r"\[(.*?)\]\((.*?)\)"
        content = re.sub(pattern, r"\1", content)
        
        return content

    def parse_tups(self, filepath: str) -> List[Tuple[Optional[str], str]]:
        """解析文件内容为元组。"""
        logger.debug(f"解析文件: {filepath}")
        content = ""
        
        try:
            content = Path(filepath).read_text(encoding=self._encoding)
            logger.debug(f"成功读取文件: {filepath}")
        except UnicodeDecodeError as e:
            logger.warning(f"使用指定编码读取文件时出错: {e}")
            if self._autodetect_encoding:
                logger.info(f"尝试自动检测文件编码: {filepath}")
                detected_encodings = detect_file_encodings(filepath)
                for encoding in detected_encodings:
                    try:
                        logger.debug(f"尝试使用编码 {encoding.encoding} 读取文件")
                        content = Path(filepath).read_text(encoding=encoding.encoding)
                        logger.info(f"成功使用编码 {encoding.encoding} 读取文件")
                        break
                    except UnicodeDecodeError:
                        logger.warning(f"使用编码 {encoding.encoding} 读取文件失败")
                        continue
            else:
                logger.error(f"加载文件 {filepath} 时出错: {e}")
                raise RuntimeError(f"加载文件 {filepath} 时出错") from e
        except Exception as e:
            logger.error(f"加载文件 {filepath} 时出错: {e}")
            raise RuntimeError(f"加载文件 {filepath} 时出错") from e

        # 应用内容处理
        if self._remove_hyperlinks:
            logger.debug("移除超链接")
            content = self.remove_hyperlinks(content)

        if self._remove_images:
            logger.debug("移除图片")
            content = self.remove_images(content)

        # 解析为元组
        return self.markdown_to_tups(content) 