"""
文档加载器辅助工具。
"""

import concurrent.futures
from pathlib import Path
from typing import NamedTuple, Optional, cast


class FileEncoding(NamedTuple):
    """文件编码命名元组。"""

    encoding: Optional[str]
    """文件的编码。"""
    confidence: float
    """编码检测的置信度。"""
    language: Optional[str]
    """文件的语言。"""


def detect_file_encodings(file_path: str, timeout: int = 5) -> list[FileEncoding]:
    """
    尝试检测文件编码。
    
    返回按置信度排序的`FileEncoding`元组列表。
    
    Args:
        file_path: 要检测编码的文件路径。
        timeout: 编码检测的超时时间（秒）。
        
    Returns:
        list[FileEncoding]: 检测到的编码列表，按置信度排序。
    """
    import chardet

    def read_and_detect(file_path: str) -> list[dict]:
        rawdata = Path(file_path).read_bytes()
        return cast(list[dict], chardet.detect_all(rawdata))

    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(read_and_detect, file_path)
        try:
            encodings = future.result(timeout=timeout)
        except concurrent.futures.TimeoutError:
            raise TimeoutError(f"检测{file_path}的编码时超时")

    if all(encoding["encoding"] is None for encoding in encodings):
        raise RuntimeError(f"无法检测{file_path}的编码")
    return [FileEncoding(**enc) for enc in encodings if enc["encoding"] is not None] 