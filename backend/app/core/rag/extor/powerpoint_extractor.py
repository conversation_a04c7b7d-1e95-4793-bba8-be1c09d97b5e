"""
PowerPoint文件解析器。
"""

import os
import re
from typing import List, Optional

from langchain.schema import Document

from app.core.rag.extor.extractor_base import BaseExtractor
from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)

class PowerPointExtractor(BaseExtractor):
    """
    加载PowerPoint文件。
    
    Args:
        file_path: 要加载的文件路径。
    """

    def __init__(self, file_path: str):
        """使用文件路径初始化。"""
        self._file_path = file_path

    def extract(self) -> List[Document]:
        """从文件路径加载文档。"""
        logger.debug(f"从PowerPoint文件加载: {self._file_path}")
        
        file_extension = os.path.splitext(self._file_path)[-1].lower()
        
        if file_extension not in [".pptx", ".ppt"]:
            logger.warning(f"不支持的PowerPoint文件格式: {file_extension}")
            raise ValueError(f"不支持的PowerPoint文件格式: {file_extension}")
        
        try:
            # 尝试使用python-pptx (仅支持.pptx)
            if file_extension == ".pptx":
                try:
                    from pptx import Presentation
                    return self._extract_with_python_pptx()
                except ImportError:
                    logger.warning("未安装python-pptx库，尝试使用二进制提取")
            
            # 如果是.ppt或者python-pptx不可用，尝试使用二进制提取
            return self._extract_as_binary()
            
        except Exception as e:
            logger.error(f"解析PowerPoint文件时出错: {e}")
            return [Document(
                page_content=f"[无法提取PowerPoint内容: {str(e)}]", 
                metadata={"source": self._file_path, "error": str(e)}
            )]
    
    def _extract_with_python_pptx(self) -> List[Document]:
        """使用python-pptx提取PowerPoint内容。"""
        from pptx import Presentation
        
        try:
            prs = Presentation(self._file_path)
            
            # 提取所有幻灯片的文本
            all_text = []
            
            for i, slide in enumerate(prs.slides):
                slide_text = []
                slide_text.append(f"--- 幻灯片 {i+1} ---")
                
                # 提取标题
                if slide.shapes.title:
                    title_text = slide.shapes.title.text
                    if title_text:
                        slide_text.append(f"标题: {title_text}")
                
                # 提取所有形状中的文本
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text:
                        # 避免重复标题
                        if shape != slide.shapes.title:
                            slide_text.append(shape.text)
                
                # 将幻灯片文本添加到总文本
                if len(slide_text) > 1:  # 确保有内容
                    all_text.append("\n".join(slide_text))
            
            # 创建文档
            if all_text:
                text_content = "\n\n".join(all_text)
                logger.info(f"使用python-pptx成功提取PowerPoint内容: {self._file_path}")
                return [Document(page_content=text_content, metadata={"source": self._file_path})]
            else:
                logger.warning(f"PowerPoint文件没有可提取的文本: {self._file_path}")
                return [Document(
                    page_content="[PowerPoint文件没有可提取的文本]",
                    metadata={"source": self._file_path}
                )]
                
        except Exception as e:
            logger.error(f"使用python-pptx提取PowerPoint内容时出错: {e}")
            raise
    
    def _extract_as_binary(self) -> List[Document]:
        """当其他方法失败时，尝试从二进制内容中提取文本。"""
        logger.info(f"尝试从二进制内容中提取文本: {self._file_path}")
        
        try:
            # 尝试使用Apache Tika（如果已安装）
            try:
                from tika import parser
                parsed = parser.from_file(self._file_path)
                if parsed and "content" in parsed and parsed["content"]:
                    text = parsed["content"]
                    logger.info(f"使用Apache Tika成功提取PowerPoint内容: {self._file_path}")
                    return [Document(page_content=text, metadata={"source": self._file_path})]
            except ImportError:
                logger.warning("未安装tika库，尝试其他方法")
            except Exception as e:
                logger.warning(f"使用Apache Tika提取失败: {e}")
            
            # 尝试使用系统命令（如果可用）
            try:
                import subprocess
                import tempfile
                
                # 对于.ppt文件，尝试使用LibreOffice转换
                if self._file_path.lower().endswith('.ppt'):
                    with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
                        temp_txt_path = temp_file.name
                    
                    # 尝试使用LibreOffice转换
                    try:
                        cmd = [
                            'soffice', '--headless', '--convert-to', 'txt:Text', 
                            '--outdir', os.path.dirname(temp_txt_path), self._file_path
                        ]
                        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                        
                        if result.returncode == 0:
                            # 获取转换后的文件名
                            base_name = os.path.basename(self._file_path)
                            txt_name = os.path.splitext(base_name)[0] + '.txt'
                            txt_path = os.path.join(os.path.dirname(temp_txt_path), txt_name)
                            
                            # 读取转换后的文本
                            if os.path.exists(txt_path):
                                with open(txt_path, 'r', encoding='utf-8', errors='replace') as f:
                                    text = f.read()
                                # 清理临时文件
                                try:
                                    os.unlink(txt_path)
                                except:
                                    pass
                                
                                logger.info(f"使用LibreOffice成功提取PowerPoint内容: {self._file_path}")
                                return [Document(page_content=text, metadata={"source": self._file_path})]
                    except (subprocess.SubprocessError, FileNotFoundError) as e:
                        logger.warning(f"使用LibreOffice提取失败: {e}")
                    finally:
                        # 清理临时文件
                        try:
                            os.unlink(temp_txt_path)
                        except:
                            pass
            except Exception as e:
                logger.warning(f"使用系统命令提取失败: {e}")
            
            # 如果上述方法都失败，尝试最基本的二进制提取
            with open(self._file_path, 'rb') as f:
                binary_content = f.read()
            
            # 尝试提取文本
            text = binary_content.decode('latin-1', errors='replace')
            
            # 保留可打印字符
            import string
            printable_chars = set(string.printable)
            chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
            
            # 构建结果字符串，保留可打印字符、空白和中文
            result = []
            for c in text:
                if c in printable_chars or chinese_pattern.match(c):
                    result.append(c)
            
            result_text = ''.join(result)
            
            # 清理结果
            # 移除无意义的控制字符，但保留基本格式
            result_text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', result_text)
            # 规范化空白
            result_text = re.sub(r'\s+', ' ', result_text).strip()
            
            # 提取可能的有意义内容
            # 查找连续的中文或英文单词
            meaningful_parts = []
            
            # 提取可能的标题和内容（连续的中文或英文单词）
            word_pattern = re.compile(r'[A-Za-z\u4e00-\u9fff]{3,}')
            words = word_pattern.findall(result_text)
            
            if words:
                meaningful_text = ' '.join(words)
                meaningful_parts.append(meaningful_text)
            
            # 如果找到有意义的内容，使用它
            if meaningful_parts:
                final_text = '\n'.join(meaningful_parts)
                logger.info(f"成功从二进制内容中提取有意义的文本: {self._file_path}")
                return [Document(page_content=final_text, metadata={"source": self._file_path})]
            
            # 如果没有找到有意义的内容，返回原始处理结果
            logger.info(f"成功从二进制内容中提取文本: {self._file_path}")
            return [Document(page_content=result_text, metadata={"source": self._file_path})]
            
        except Exception as e:
            logger.error(f"从二进制内容中提取文本时出错: {e}")
            # 返回空文档，而不是引发异常
            return [Document(
                page_content=f"[无法提取PowerPoint内容: {str(e)}]", 
                metadata={"source": self._file_path, "error": str(e)}
            )] 