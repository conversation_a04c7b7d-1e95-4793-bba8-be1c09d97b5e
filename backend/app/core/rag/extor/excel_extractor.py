"""
Excel文件解析器。
"""

import logging
import os
from typing import List, Optional

from langchain.schema import Document

from app.core.rag.extor.extractor_base import BaseExtractor

from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)


class ExcelExtractor(BaseExtractor):
    """
    加载Excel文件。
    
    Args:
        file_path: 要加载的文件路径。
    """

    def __init__(self, file_path: str):
        """使用文件路径初始化。"""
        self._file_path = file_path

    def extract(self) -> List[Document]:
        """从文件路径加载文档。"""
        logger.debug(f"从Excel文件加载: {self._file_path}")
        
        file_extension = os.path.splitext(self._file_path)[-1].lower()
        
        if file_extension not in [".xlsx", ".xls"]:
            logger.warning(f"不支持的Excel文件格式: {file_extension}")
            raise ValueError(f"不支持的Excel文件格式: {file_extension}")
        
        try:
            # 尝试使用pandas
            try:
                import pandas as pd
                return self._extract_with_pandas()
            except ImportError:
                logger.warning("未安装pandas库，尝试使用openpyxl")
                
                # 尝试使用openpyxl (.xlsx文件)
                if file_extension == ".xlsx":
                    try:
                        import openpyxl
                        return self._extract_with_openpyxl()
                    except ImportError:
                        logger.warning("未安装openpyxl库，尝试使用xlrd")
                
                # 尝试使用xlrd (.xls文件)
                try:
                    import xlrd
                    return self._extract_with_xlrd()
                except ImportError:
                    logger.error("未安装xlrd库，无法解析Excel文件")
                    raise ImportError(
                        "解析Excel文件需要pandas、openpyxl或xlrd库。请安装其中之一: pip install pandas openpyxl xlrd"
                    )
        
        except Exception as e:
            logger.error(f"解析Excel文件时出错: {e}")
            return [Document(
                page_content=f"[无法提取Excel内容: {str(e)}]", 
                metadata={"source": self._file_path, "error": str(e)}
            )]
    
    def _extract_with_pandas(self) -> List[Document]:
        """使用pandas提取Excel内容。"""
        import pandas as pd
        
        try:
            # 读取所有sheet
            excel_file = pd.ExcelFile(self._file_path)
            documents = []
            
            for sheet_name in excel_file.sheet_names:
                logger.debug(f"处理sheet: {sheet_name}")
                
                try:
                    df = excel_file.parse(sheet_name=sheet_name)
                    
                    # 删除全部为空的行
                    df.dropna(how="all", inplace=True)
                    
                    # 将DataFrame转换为文本
                    if not df.empty:
                        # 创建表头
                        text_parts = [f"--- Sheet: {sheet_name} ---"]
                        
                        # 添加列名作为表头
                        header = " | ".join(str(col) for col in df.columns)
                        text_parts.append(header)
                        text_parts.append("-" * len(header))
                        
                        # 添加数据行
                        for _, row in df.iterrows():
                            row_content = []
                            for k, v in row.items():
                                if pd.notna(v):  # 跳过NaN值
                                    row_content.append(f"{v}")
                            text_parts.append(" | ".join(row_content))
                        
                        # 创建文档
                        sheet_text = "\n".join(text_parts)
                        documents.append(Document(
                            page_content=sheet_text,
                            metadata={"source": self._file_path, "sheet": sheet_name}
                        ))
                        logger.debug(f"成功提取sheet {sheet_name} 内容")
                    else:
                        logger.warning(f"Sheet {sheet_name} 为空，跳过")
                
                except Exception as e:
                    logger.warning(f"处理sheet {sheet_name} 时出错: {e}")
                    documents.append(Document(
                        page_content=f"[无法提取Sheet {sheet_name} 内容: {str(e)}]",
                        metadata={"source": self._file_path, "sheet": sheet_name, "error": str(e)}
                    ))
            
            if documents:
                logger.info(f"使用pandas成功提取Excel内容: {self._file_path}")
                return documents
            else:
                logger.warning(f"Excel文件没有可提取的内容: {self._file_path}")
                return [Document(
                    page_content="[Excel文件没有可提取的内容]",
                    metadata={"source": self._file_path}
                )]
        
        except Exception as e:
            logger.error(f"使用pandas提取Excel内容时出错: {e}")
            raise
    
    def _extract_with_openpyxl(self) -> List[Document]:
        """使用openpyxl提取Excel内容（.xlsx文件）。"""
        import openpyxl
        
        try:
            wb = openpyxl.load_workbook(self._file_path, data_only=True)
            documents = []
            
            for sheet_name in wb.sheetnames:
                logger.debug(f"处理sheet: {sheet_name}")
                
                try:
                    sheet = wb[sheet_name]
                    
                    # 提取表格内容
                    text_parts = [f"--- Sheet: {sheet_name} ---"]
                    
                    # 遍历行和列
                    for row in sheet.iter_rows():
                        row_values = []
                        for cell in row:
                            if cell.value is not None:
                                row_values.append(str(cell.value))
                        if row_values:  # 只添加非空行
                            text_parts.append(" | ".join(row_values))
                    
                    # 创建文档
                    if len(text_parts) > 1:  # 确保有内容
                        sheet_text = "\n".join(text_parts)
                        documents.append(Document(
                            page_content=sheet_text,
                            metadata={"source": self._file_path, "sheet": sheet_name}
                        ))
                        logger.debug(f"成功提取sheet {sheet_name} 内容")
                    else:
                        logger.warning(f"Sheet {sheet_name} 为空，跳过")
                
                except Exception as e:
                    logger.warning(f"处理sheet {sheet_name} 时出错: {e}")
                    documents.append(Document(
                        page_content=f"[无法提取Sheet {sheet_name} 内容: {str(e)}]",
                        metadata={"source": self._file_path, "sheet": sheet_name, "error": str(e)}
                    ))
            
            if documents:
                logger.info(f"使用openpyxl成功提取Excel内容: {self._file_path}")
                return documents
            else:
                logger.warning(f"Excel文件没有可提取的内容: {self._file_path}")
                return [Document(
                    page_content="[Excel文件没有可提取的内容]",
                    metadata={"source": self._file_path}
                )]
        
        except Exception as e:
            logger.error(f"使用openpyxl提取Excel内容时出错: {e}")
            raise
    
    def _extract_with_xlrd(self) -> List[Document]:
        """使用xlrd提取Excel内容（.xls文件）。"""
        import xlrd
        
        try:
            wb = xlrd.open_workbook(self._file_path)
            documents = []
            
            for sheet_index in range(wb.nsheets):
                sheet = wb.sheet_by_index(sheet_index)
                sheet_name = sheet.name
                logger.debug(f"处理sheet: {sheet_name}")
                
                try:
                    # 提取表格内容
                    text_parts = [f"--- Sheet: {sheet_name} ---"]
                    
                    # 遍历行和列
                    for row_idx in range(sheet.nrows):
                        row_values = []
                        for col_idx in range(sheet.ncols):
                            cell_value = sheet.cell_value(row_idx, col_idx)
                            if cell_value:  # 只添加非空单元格
                                row_values.append(str(cell_value))
                        if row_values:  # 只添加非空行
                            text_parts.append(" | ".join(row_values))
                    
                    # 创建文档
                    if len(text_parts) > 1:  # 确保有内容
                        sheet_text = "\n".join(text_parts)
                        documents.append(Document(
                            page_content=sheet_text,
                            metadata={"source": self._file_path, "sheet": sheet_name}
                        ))
                        logger.debug(f"成功提取sheet {sheet_name} 内容")
                    else:
                        logger.warning(f"Sheet {sheet_name} 为空，跳过")
                
                except Exception as e:
                    logger.warning(f"处理sheet {sheet_name} 时出错: {e}")
                    documents.append(Document(
                        page_content=f"[无法提取Sheet {sheet_name} 内容: {str(e)}]",
                        metadata={"source": self._file_path, "sheet": sheet_name, "error": str(e)}
                    ))
            
            if documents:
                logger.info(f"使用xlrd成功提取Excel内容: {self._file_path}")
                return documents
            else:
                logger.warning(f"Excel文件没有可提取的内容: {self._file_path}")
                return [Document(
                    page_content="[Excel文件没有可提取的内容]",
                    metadata={"source": self._file_path}
                )]
        
        except Exception as e:
            logger.error(f"使用xlrd提取Excel内容时出错: {e}")
            raise 