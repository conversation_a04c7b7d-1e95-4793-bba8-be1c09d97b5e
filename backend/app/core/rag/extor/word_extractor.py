"""
Word文档解析器。
"""

import logging
import os
import re
import tempfile
import zipfile
from pathlib import Path
from typing import List, Optional

from langchain.schema import Document

from app.core.rag.extor.extractor_base import BaseExtractor

from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)


class WordExtractor(BaseExtractor):
    """
    加载Word文档。
    
    Args:
        file_path: 要加载的文件路径。
    """

    def __init__(self, file_path: str):
        """使用文件路径初始化。"""
        self._file_path = file_path

    def extract(self) -> List[Document]:
        """从文件路径加载文档。"""
        logger.debug(f"从Word文档加载: {self._file_path}")
        
        # 检查文件扩展名
        file_extension = Path(self._file_path).suffix.lower()
        
        if file_extension == ".docx":
            return self._extract_docx()
        elif file_extension == ".doc":
            return self._extract_doc()
        else:
            logger.warning(f"不支持的Word文档格式: {file_extension}")
            raise ValueError(f"不支持的Word文档格式: {file_extension}")
    
    def _extract_docx(self) -> List[Document]:
        """解析.docx文件。"""
        try:
            # 尝试使用python-docx
            try:
                import docx
                return self._extract_with_python_docx()
            except ImportError:
                logger.warning("未安装python-docx库，尝试使用docx2txt")
                
                # 尝试使用docx2txt
                try:
                    import docx2txt
                    text = docx2txt.process(self._file_path)
                    logger.info(f"使用docx2txt成功提取文本: {self._file_path}")
                    return [Document(page_content=text, metadata={"source": self._file_path})]
                except ImportError:
                    logger.warning("未安装docx2txt库，尝试使用ZIP解析")
                    
                    # 尝试使用ZIP解析
                    return self._extract_docx_with_zip()
        except Exception as e:
            logger.error(f"解析DOCX文档时出错: {e}")
            # 最后尝试使用文本提取
            logger.info("尝试使用二进制方式提取文本")
            return self._extract_as_binary()
    
    def _extract_with_python_docx(self) -> List[Document]:
        """使用python-docx库提取文本。"""
        import docx
        
        try:
            doc = docx.Document(self._file_path)
            
            # 提取段落文本
            paragraphs_text = []
            for para in doc.paragraphs:
                if para.text.strip():
                    paragraphs_text.append(para.text)
            
            # 提取表格文本
            tables_text = []
            for table in doc.tables:
                table_rows = []
                for row in table.rows:
                    row_cells = [cell.text.strip() for cell in row.cells]
                    table_rows.append(" | ".join(row_cells))
                tables_text.append("\n".join(table_rows))
            
            # 合并所有文本
            all_text = "\n\n".join(paragraphs_text)
            if tables_text:
                all_text += "\n\n表格内容:\n" + "\n\n".join(tables_text)
            
            logger.info(f"使用python-docx成功提取文本: {self._file_path}")
            return [Document(page_content=all_text, metadata={"source": self._file_path})]
        except Exception as e:
            logger.error(f"使用python-docx提取文本时出错: {e}")
            raise
    
    def _extract_docx_with_zip(self) -> List[Document]:
        """使用ZIP解析DOCX文件。"""
        try:
            text_content = ""
            with zipfile.ZipFile(self._file_path) as docx_zip:
                # 提取document.xml
                if 'word/document.xml' in docx_zip.namelist():
                    xml_content = docx_zip.read('word/document.xml').decode('utf-8')
                    
                    # 提取表格
                    table_pattern = r'<w:tbl.*?>(.*?)</w:tbl>'
                    tables = re.findall(table_pattern, xml_content, re.DOTALL)
                    
                    if tables:
                        logger.info(f"在DOCX文件中发现 {len(tables)} 个表格")
                        for i, table in enumerate(tables):
                            # 提取表格行
                            row_pattern = r'<w:tr.*?>(.*?)</w:tr>'
                            rows = re.findall(row_pattern, table, re.DOTALL)
                            
                            table_text = f"\n--- 表格 {i+1} ---\n"
                            
                            for row in rows:
                                # 提取单元格
                                cell_pattern = r'<w:tc.*?>(.*?)</w:tc>'
                                cells = re.findall(cell_pattern, row, re.DOTALL)
                                
                                # 提取单元格文本
                                cell_texts = []
                                for cell in cells:
                                    # 提取段落文本
                                    text_pattern = r'<w:t.*?>(.*?)</w:t>'
                                    texts = re.findall(text_pattern, cell, re.DOTALL)
                                    cell_text = ''.join(texts).strip()
                                    cell_texts.append(cell_text)
                                
                                # 使用制表符连接单元格文本
                                row_text = '\t'.join(cell_texts)
                                table_text += f"{row_text}\n"
                            
                            text_content += table_text + "\n"
                    
                    # 提取段落文本
                    text_pattern = r'<w:t.*?>(.*?)</w:t>'
                    texts = re.findall(text_pattern, xml_content, re.DOTALL)
                    normal_text = ' '.join(texts).strip()
                    
                    # 如果没有从表格中提取到文本，则使用常规文本
                    if not text_content:
                        text_content = normal_text
                    else:
                        # 添加非表格文本
                        text_content += "\n\n--- 文档正文 ---\n" + normal_text
            
            logger.info(f"使用ZIP解析成功提取DOCX文本: {self._file_path}")
            return [Document(page_content=text_content, metadata={"source": self._file_path})]
        
        except zipfile.BadZipFile:
            logger.warning(f"无法作为ZIP文件打开: {self._file_path}")
            return self._extract_as_binary()
        except Exception as e:
            logger.error(f"使用ZIP解析DOCX时出错: {e}")
            return self._extract_as_binary()
    
    def _extract_doc(self) -> List[Document]:
        """解析.doc文件。"""
        # 尝试使用antiword
        try:
            import subprocess
            
            # 检查antiword是否安装
            try:
                result = subprocess.run(
                    ["antiword", "-v"], 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    check=False
                )
                if result.returncode != 0:
                    raise ImportError("Antiword未正确安装")
                
                # 使用antiword提取文本
                result = subprocess.run(
                    ["antiword", self._file_path], 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    check=True
                )
                text = result.stdout.decode("utf-8", errors="replace")
                logger.info(f"使用antiword成功提取文本: {self._file_path}")
                return [Document(page_content=text, metadata={"source": self._file_path})]
            
            except (ImportError, subprocess.SubprocessError) as e:
                logger.warning(f"使用antiword提取失败: {e}")
                # 回退到二进制提取
                return self._extract_as_binary()
                
        except Exception as e:
            logger.error(f"解析DOC文档时出错: {e}")
            # 回退到二进制提取
            return self._extract_as_binary()
    
    def _extract_as_binary(self) -> List[Document]:
        """当其他方法失败时，尝试从二进制内容中提取文本。"""
        logger.info(f"尝试从二进制内容中提取文本: {self._file_path}")
        
        try:
            # 读取二进制内容
            with open(self._file_path, 'rb') as f:
                binary_content = f.read()
            
            # 尝试提取文本，保留制表符和换行符
            text = binary_content.decode('latin-1', errors='replace')
            
            # 保留可能的表格结构和可打印字符
            import string
            printable_chars = set(string.printable)
            chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
            
            # 构建结果字符串，保留可打印字符、空白和中文
            result = []
            for c in text:
                if c in printable_chars or chinese_pattern.match(c):
                    result.append(c)
            
            table_text = ''.join(result)
            
            # 尝试识别和格式化表格数据
            lines = table_text.split('\n')
            formatted_lines = []
            
            for line in lines:
                # 如果行中包含多个制表符或空格序列，可能是表格数据
                if line.count('\t') > 1 or re.search(r'\s{2,}', line):
                    # 规范化空白字符，将连续空格替换为制表符
                    normalized_line = re.sub(r'\s{2,}', '\t', line)
                    formatted_lines.append(normalized_line)
                else:
                    formatted_lines.append(line)
            
            result_text = '\n'.join(formatted_lines)
            
            # 移除无意义的控制字符，但保留表格结构
            result_text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', result_text)
            
            # 特殊处理类似"申请单位名称 xxx 申请时间 20241108"这样的表格结构
            form_pattern = r'([^\s]+)\s+([^\s]+)\s+([^\s]+)\s+([^\s]+)'
            form_matches = re.findall(form_pattern, result_text)
            
            if form_matches:
                logger.info(f"检测到可能的表格结构，找到 {len(form_matches)} 个匹配项")
                table_content = "\n--- 检测到的表格内容 ---\n"
                
                for match in form_matches:
                    if len(match) >= 4:
                        # 检查是否符合"字段1 值1 字段2 值2"模式
                        # 字段通常是中文或英文词组
                        if chinese_pattern.search(match[0]) or len(match[0]) > 1:
                            table_content += f"{match[0]}\t{match[1]}\n{match[2]}\t{match[3]}\n"
                
                # 如果找到了表格内容，添加到结果中
                if table_content != "\n--- 检测到的表格内容 ---\n":
                    result_text = table_content + "\n\n" + result_text
            
            logger.info(f"成功从二进制内容中提取文本: {self._file_path}")
            return [Document(page_content=result_text, metadata={"source": self._file_path})]
        
        except Exception as e:
            logger.error(f"从二进制内容中提取文本时出错: {e}")
            # 返回空文档，而不是引发异常
            return [Document(
                page_content=f"[无法提取文档内容: {str(e)}]", 
                metadata={"source": self._file_path, "error": str(e)}
            )] 