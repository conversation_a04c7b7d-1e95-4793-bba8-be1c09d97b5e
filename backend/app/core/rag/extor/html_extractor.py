"""
HTML文件解析器。
"""

from typing import List, Optional

from langchain.schema import Document

from app.core.rag.extor.extractor_base import BaseExtractor
from app.core.rag.extor.helpers import detect_file_encodings

from app.core.logging_config import logging_config
logger = logging_config.get_logger(__name__)


class HtmlExtractor(BaseExtractor):
    """
    加载HTML文件。
    
    Args:
        file_path: 要加载的文件路径。
        encoding: 文件编码（可选）。
        autodetect_encoding: 是否自动检测文件编码。
    """

    def __init__(
        self,
        file_path: str,
        encoding: Optional[str] = None,
        autodetect_encoding: bool = False,
    ):
        """使用文件路径初始化。"""
        self._file_path = file_path
        self._encoding = encoding
        self._autodetect_encoding = autodetect_encoding

    def extract(self) -> List[Document]:
        """从文件路径加载文档。"""
        logger.debug(f"从HTML文件加载: {self._file_path}")
        
        try:
            # 尝试使用BeautifulSoup
            try:
                from bs4 import BeautifulSoup
                return self._extract_with_bs4()
            except ImportError:
                logger.warning("未安装BeautifulSoup库，尝试使用正则表达式提取")
                return self._extract_with_regex()
        except Exception as e:
            logger.error(f"解析HTML文件时出错: {e}")
            return [Document(
                page_content=f"[无法提取HTML内容: {str(e)}]", 
                metadata={"source": self._file_path, "error": str(e)}
            )]
    
    def _extract_with_bs4(self) -> List[Document]:
        """使用BeautifulSoup提取HTML内容。"""
        from bs4 import BeautifulSoup
        
        # 读取HTML文件
        html_content = self._read_file()
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, "html.parser")
        
        # 提取文本内容
        text = soup.get_text(separator="\n", strip=True)
        
        # 处理标题
        title = ""
        title_tag = soup.find("title")
        if title_tag:
            title = title_tag.get_text(strip=True)
        
        # 提取元数据
        metadata = {"source": self._file_path}
        if title:
            metadata["title"] = title
        
        # 处理链接
        links = []
        for link in soup.find_all("a", href=True):
            link_text = link.get_text(strip=True)
            href = link["href"]
            if link_text and href:
                links.append(f"{link_text}: {href}")
        
        # 添加链接到文本
        if links:
            text += "\n\n链接:\n" + "\n".join(links)
        
        logger.info(f"使用BeautifulSoup成功提取HTML内容: {self._file_path}")
        return [Document(page_content=text, metadata=metadata)]
    
    def _extract_with_regex(self) -> List[Document]:
        """使用正则表达式提取HTML内容。"""
        import re
        
        # 读取HTML文件
        html_content = self._read_file()
        
        # 提取标题
        title_match = re.search(r"<title>(.*?)</title>", html_content, re.IGNORECASE | re.DOTALL)
        title = title_match.group(1).strip() if title_match else ""
        
        # 移除脚本和样式
        html_content = re.sub(r"<script.*?>.*?</script>", "", html_content, flags=re.IGNORECASE | re.DOTALL)
        html_content = re.sub(r"<style.*?>.*?</style>", "", html_content, flags=re.IGNORECASE | re.DOTALL)
        
        # 提取文本内容
        text = re.sub(r"<[^>]*>", " ", html_content)
        text = re.sub(r"\s+", " ", text).strip()
        
        # 提取链接
        links = []
        link_pattern = re.compile(r'<a\s+[^>]*href="([^"]*)"[^>]*>(.*?)</a>', re.IGNORECASE | re.DOTALL)
        for match in link_pattern.finditer(html_content):
            href = match.group(1)
            link_text = re.sub(r"<[^>]*>", "", match.group(2)).strip()
            if link_text and href:
                links.append(f"{link_text}: {href}")
        
        # 添加链接到文本
        if links:
            text += "\n\n链接:\n" + "\n".join(links)
        
        # 提取元数据
        metadata = {"source": self._file_path}
        if title:
            metadata["title"] = title
        
        logger.info(f"使用正则表达式成功提取HTML内容: {self._file_path}")
        return [Document(page_content=text, metadata=metadata)]
    
    def _read_file(self) -> str:
        """读取HTML文件内容。"""
        try:
            with open(self._file_path, "r", encoding=self._encoding) as file:
                return file.read()
        except UnicodeDecodeError as e:
            if self._autodetect_encoding:
                logger.info(f"尝试自动检测文件编码: {self._file_path}")
                detected_encodings = detect_file_encodings(self._file_path)
                for encoding in detected_encodings:
                    try:
                        logger.debug(f"尝试使用编码 {encoding.encoding} 读取HTML文件")
                        with open(self._file_path, "r", encoding=encoding.encoding) as file:
                            content = file.read()
                            logger.info(f"成功使用编码 {encoding.encoding} 读取HTML文件")
                            return content
                    except UnicodeDecodeError:
                        logger.warning(f"使用编码 {encoding.encoding} 读取HTML文件失败")
                        continue
            
            logger.error(f"读取HTML文件 {self._file_path} 时出错: {e}")
            raise RuntimeError(f"读取HTML文件 {self._file_path} 时出错") from e
        except Exception as e:
            logger.error(f"读取HTML文件 {self._file_path} 时出错: {e}")
            raise RuntimeError(f"读取HTML文件 {self._file_path} 时出错") from e 