"""
文档加载器主入口。
提供统一的接口加载各种类型的文档。
"""

import logging
import os
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Union

from langchain.schema import Document

from app.core.rag.extor.extractor_factory import ExtractorFactory
from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)


class DocumentLoader:
    """
    文档加载器主类。
    提供统一的接口加载各种类型的文档。
    """
    
    @staticmethod
    def load_document(file_path: str, **kwargs) -> List[Document]:
        """
        加载文档。
        
        Args:
            file_path: 文件路径
            **kwargs: 传递给解析器的额外参数
            
        Returns:
            List[Document]: 文档列表
        """
        logger.info(f"加载文档: {file_path}")
        
        try:
            # 使用工厂类获取合适的解析器
            extractor = ExtractorFactory.get_extractor(file_path, **kwargs)
            
            # 提取文档
            documents = extractor.extract()
            
            logger.info(f"成功从 {file_path} 加载了 {len(documents)} 个文档")
            return documents
            
        except Exception as e:
            logger.error(f"加载文档 {file_path} 时出错: {e}")
            # 返回包含错误信息的文档，而不是抛出异常
            return [Document(
                page_content=f"[无法加载文档: {str(e)}]",
                metadata={"source": file_path, "error": str(e)}
            )]
    
    @staticmethod
    def load_documents(file_paths: List[str], **kwargs) -> List[Document]:
        """
        批量加载多个文档。
        
        Args:
            file_paths: 文件路径列表
            **kwargs: 传递给解析器的额外参数
            
        Returns:
            List[Document]: 文档列表
        """
        logger.info(f"批量加载 {len(file_paths)} 个文档")
        
        all_documents = []
        
        for file_path in file_paths:
            try:
                documents = DocumentLoader.load_document(file_path, **kwargs)
                all_documents.extend(documents)
            except Exception as e:
                logger.error(f"加载文档 {file_path} 时出错: {e}")
                # 添加错误文档并继续处理其他文件
                all_documents.append(Document(
                    page_content=f"[无法加载文档: {str(e)}]",
                    metadata={"source": file_path, "error": str(e)}
                ))
        
        logger.info(f"成功加载了 {len(all_documents)} 个文档")
        return all_documents
    
    @staticmethod
    def load_directory(directory_path: str, recursive: bool = True, file_extensions: Optional[List[str]] = None, **kwargs) -> List[Document]:
        """
        加载目录中的所有文档。
        
        Args:
            directory_path: 目录路径
            recursive: 是否递归加载子目录
            file_extensions: 要加载的文件扩展名列表（如 [".pdf", ".docx"]）
            **kwargs: 传递给解析器的额外参数
            
        Returns:
            List[Document]: 文档列表
        """
        logger.info(f"加载目录: {directory_path}")
        
        if not os.path.isdir(directory_path):
            logger.error(f"目录不存在: {directory_path}")
            raise ValueError(f"目录不存在: {directory_path}")
        
        # 如果未指定文件扩展名，则使用所有支持的扩展名
        if file_extensions is None:
            file_extensions = ExtractorFactory.get_supported_extensions()
        else:
            # 确保所有扩展名都以点开头
            file_extensions = [ext if ext.startswith(".") else f".{ext}" for ext in file_extensions]
        
        # 查找所有匹配的文件
        all_files = []
        
        if recursive:
            for root, _, files in os.walk(directory_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext in file_extensions:
                        all_files.append(file_path)
        else:
            for file in os.listdir(directory_path):
                file_path = os.path.join(directory_path, file)
                if os.path.isfile(file_path):
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext in file_extensions:
                        all_files.append(file_path)
        
        logger.info(f"在目录 {directory_path} 中找到 {len(all_files)} 个匹配的文件")
        
        # 加载所有文件
        return DocumentLoader.load_documents(all_files, **kwargs)
    
    @staticmethod
    def load_text(text: str, metadata: Optional[Dict] = None) -> List[Document]:
        """
        从文本字符串加载文档。
        
        Args:
            text: 文本内容
            metadata: 元数据字典
            
        Returns:
            List[Document]: 文档列表
        """
        logger.debug("从文本字符串加载文档")
        
        if metadata is None:
            metadata = {}
        
        return [Document(page_content=text, metadata=metadata)]
    
    @staticmethod
    def load_from_binary(binary_data: bytes, file_extension: str, **kwargs) -> List[Document]:
        """
        从二进制数据加载文档。
        
        Args:
            binary_data: 二进制数据
            file_extension: 文件扩展名（如 ".pdf"）
            **kwargs: 传递给解析器的额外参数
            
        Returns:
            List[Document]: 文档列表
        """
        logger.debug(f"从二进制数据加载 {file_extension} 文档")
        
        # 确保扩展名以点开头
        if not file_extension.startswith("."):
            file_extension = f".{file_extension}"
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=file_extension, delete=False) as temp_file:
            temp_path = temp_file.name
            temp_file.write(binary_data)
        
        try:
            # 加载文档
            documents = DocumentLoader.load_document(temp_path, **kwargs)
            return documents
        finally:
            # 删除临时文件
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.warning(f"删除临时文件时出错: {e}")
    
    @staticmethod
    def get_supported_extensions() -> List[str]:
        """
        获取所有支持的文件扩展名。
        
        Returns:
            List[str]: 支持的文件扩展名列表
        """
        return ExtractorFactory.get_supported_extensions() 