# LangChain RAG Module

This module provides a complete Retrieval Augmented Generation (RAG) system built with LangChain. It supports multiple document types, various vector stores, and different retrieval methods.

## Features

- **Document Processing**: Support for PDF, Word, CSV, Excel, PowerPoint, HTML, and text files
- **Embedding Models**: Integration with OpenAI and HuggingFace embedding models
- **Vector Stores**: Support for local (FAISS, Chroma), MongoDB, and Elasticsearch
- **Retrieval Methods**: 
  - Similarity search using vector embeddings
  - Keyword search with SQL or Elasticsearch BM25
  - Hybrid search combining both methods
- **RAG Pipeline**: Complete pipeline for combining retrieval with generation
- **Multi-tenant**: Support for tenant isolation and access control
- **Database Integration**: Storage of chunks and search history in the database
- **Advanced Search Features**: Fuzzy matching, field weighting, and result highlighting with Elasticsearch

## Components

- `document_loaders.py`: Loads and chunks documents from various file formats
- `embeddings.py`: Manages embedding models and integration with providers
- `vector_stores.py`: Handles different vector store backends
- `indexing.py`: Indexes documents and manages the vector store
- `retrieval.py`: Retrieves documents using different search methods
- `pipeline.py`: Combines retrieval with generation for RAG
- `examples.py`: Example usage of the RAG module

## Usage

### Document Indexing

```python
from app.services.rag import index_document

# Index a document
result = await index_document(
    db=db,
    file_id="file_123",
    tenant_id="tenant_123",
    embedding_model_key="text-embedding-ada-002"
)
```

### Document Retrieval

```python
from app.services.rag import retrieve_documents
from app.services.rag.retrieval import SearchMethod

# Retrieve documents using hybrid search
result = retrieve_documents(
    db=db,
    kb_id="kb_123",
    tenant_id="tenant_123",
    query="What is the capital of France?",
    search_method=SearchMethod.HYBRID,
    filter_criteria={"file_id": "file_123"},
    top_k=5
)

# Use keyword search with Elasticsearch BM25 (when VECTOR_STORE_TYPE is set to "elasticsearch")
result = retrieve_documents(
    db=db,
    kb_id="kb_123",
    tenant_id="tenant_123",
    query="What is the capital of France?",
    search_method=SearchMethod.KEYWORD,
    top_k=5
)
```

### RAG Chain

```python
from app.services.rag import create_rag_chain
from app.services.rag.pipeline import query_rag_chain

# Create a RAG chain
chain_data = create_rag_chain(
    db=db,
    kb_id="kb_123",
    tenant_id="tenant_123",
    embedding_model_key="text-embedding-ada-002",
    llm_model_key="gpt-3.5-turbo",
    return_source_documents=True
)

# Query the RAG chain
result = query_rag_chain(chain_data, "What is the capital of France?")
```

## Installation

Install the required dependencies:

```bash
pip install -r requirements_rag.txt
```

## Configuration

The following environment variables are used:

- `S3_ENDPOINT_URL`: S3 endpoint URL for document storage
- `S3_ACCESS_KEY`: S3 access key
- `S3_SECRET_KEY`: S3 secret key
- `S3_BUCKET_NAME`: S3 bucket name (default: "knowledge-base")
- `MONGODB_URI`: MongoDB connection string (for MongoDB vector store)
- `MONGODB_DB_NAME`: MongoDB database name (default: "knowledge_base")
- `ELASTICSEARCH_URL`: Elasticsearch URL (default: "http://localhost:9200")
- `ES_URL`: Elasticsearch URL for vector store
- `ES_USERNAME`: Elasticsearch username
- `ES_PASSWORD`: Elasticsearch password
- `VECTOR_STORE_TYPE`: Type of vector store to use (faiss, chroma, mongodb, elasticsearch)

## Vector Store Selection

Vector store type can be specified in the knowledge base configuration or through the `VECTOR_STORE_TYPE` environment variable:

```json
{
  "vector_store_type": "elasticsearch",  // or "faiss", "chroma", "mongodb"
  "embedding_model_key": "text-embedding-ada-002",
  "llm_model_key": "gpt-3.5-turbo"
}
```

## Elasticsearch BM25 Search

When using Elasticsearch as the vector store, the keyword search method automatically uses Elasticsearch's BM25 algorithm for improved relevance ranking. This provides several advantages:

- Better relevance scoring compared to simple keyword matching
- Support for fuzzy matching to handle typos and spelling variations
- Field weighting to prioritize matches in specific fields
- Result highlighting to emphasize matching text
- Improved performance for large document collections

The implementation automatically detects when Elasticsearch is configured as the vector store and routes keyword searches to use the native Elasticsearch query capabilities.

## Reranking

### Initialization

```python
from app.db.session import SessionLocal
from app.core.rag import DocumentLoader, Indexer, Retriever, RerankerManager

# Get database session
db = SessionLocal()

# Initialize components
document_loader = DocumentLoader()
indexer = Indexer(db)
retriever = Retriever(db)
reranker_manager = RerankerManager(db)
```

### Document Indexing

```python
# Load document
document_path = "/path/to/document.pdf"
documents = document_loader.load_document(document_path)

# Index documents
indexer.index_documents(
    tenant_id="tenant-123",
    kb_id="kb-123",
    documents=documents,
    embedding_model_id="model-123",
    vector_store_type="milvus"
)
```

### Document Retrieval

```python
# Retrieve documents
results = retriever.retrieve_documents(
    kb_id="kb-123",
    tenant_id="tenant-123",
    query="查询问题",
    search_method="hybrid",
    top_k=5
)
```

### Document Reranking

```python
# Get reranker
reranker = await reranker_manager.get_reranker("rerank-model-123")

# Rerank documents
reranked_docs = await reranker.rerank(
    query="查询问题",
    documents=documents,
    top_k=5
)

# Or use manager directly to rerank
reranked_docs = await reranker_manager.rerank(
    model_id="rerank-model-123",
    query="查询问题",
    documents=documents,
    top_k=5
)
```

## Reranking Feature Introduction

Reranking feature supports the following features:

1. **Multiple Reranking Models**:
   - `CrossEncoderReranker`: Reranking based on local Transformer model
   - `CohereReranker`: Reranking based on Cohere API
   - `OpenAIReranker`: Reranking based on OpenAI API
   - `ThirdPartyReranker`: General third-party reranking API, supports multiple service providers (Aliyun Qwen, Baidu Wenxin, Zhipu GLM, etc.)

2. **Optional Reranking Instructions**:
   - Specific instructions can be provided through the `rerank_instruction` parameter to guide the reranking model more accurately evaluate document relevance
   - If no instructions are provided, the default instructions from model configuration will be used

3. **Flexible Knowledge Base Configuration**:
   - Reranking feature can be enabled or disabled in knowledge base configuration
   - Reranking model, return result number, and reranking instructions can be specified

4. **Error Handling**:
   - When reranking fails, the system will gracefully degrade and return original retrieval results

Example configuration can be found in `rerank/examples/qwen_reranker_example.py` file. 