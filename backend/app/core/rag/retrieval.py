"""
Retrieval services for RAG pipelines.
Includes similarity search, keyword search, and hybrid search.
"""

from typing import List, Dict, Any, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor
from enum import Enum
import asyncio
from sqlalchemy.orm import Session
from sqlalchemy import or_
from sqlalchemy.sql import func
import re
from elasticsearch import Elasticsearch

from langchain.embeddings.base import Embeddings
from langchain_core.documents import Document

from elasticsearch import Elasticsearch

from app.core.config import settings
from app.core.rag.vector_stores import VectorStoreType

from app.core.logging_config import logging_config
from app.models.knowledge import KnowledgeBase, KnowledgeFile, KnowledgeChunk
from app.models.model import Model
from app.core.rag.embeddings import EmbeddingManager
from app.core.rag.vector_stores import VectorStoreManager
from app.core.rag.rerank.reranker_manager import RerankerManager


# Configure logger
logger = logging_config.get_logger(__name__)

class SearchMethod(str, Enum):
    """Search methods for retrieving documents."""
    SIMILARITY = "similarity"
    KEYWORD = "keyword"
    HYBRID = "hybrid"


# 默认检索配置
DEFAULT_RETRIEVAL_CONFIG = {
    "default_search_type": "hybrid",  # 默认使用混合搜索
    "vector_search": {
        "top_k": 5,
        "score_threshold": 0.3,
        "rerank": {
            "enabled": False,
            "model_name": "",
            "model_id": ""
        }
    },
    "keyword_search": {
        "top_k": 5,
        "score_threshold": 0.3,
        "rerank": {
            "enabled": False,
            "model_name": "",
            "model_id": ""
        }
    },
    "hybrid_search": {
        "top_k": 5,
        "score_threshold": 0.3,
        "vector_weight": 0.7,
        "keyword_weight": 0.3,
        "rerank": {
            "enabled": False,
            "model_name": "",
            "model_id": ""
        }
    }
}


class Retriever:
    """
    Class for retrieving documents from knowledge bases using various search methods.
    """
    
    def __init__(self, db: Session):
        # 创建线程池用于并行搜索任务
        self.executor = ThreadPoolExecutor(max_workers=2)
        """
        Initialize the retriever.
        
        Args:
            db: Database session
        """
        self.db = db
        self.embedding_manager = EmbeddingManager(db)
        self.vector_store_manager = VectorStoreManager()
        self.reranker_manager = RerankerManager(db)
    
    def retrieve_documents(
        self,
        kb_id: str,
        tenant_id: str,
        query: str
    ) -> Dict[str, Any]:
        """
        统一检索入口 - 从知识库配置获取所有参数
        
        参数:
            kb_id: 知识库ID
            tenant_id: 租户ID
            query: 搜索查询 
            
        返回:
            标准化检索结果字典
        """
        # 1. 获取知识库和配置
        knowledge_base = self._get_knowledge_base(kb_id, tenant_id)
        kb_config = knowledge_base.config or {}
        
        # 2. 解析检索配置
        retrieval_config = self._parse_retrieval_config(kb_config)
        search_method = retrieval_config["search_method"]
        top_k = retrieval_config["top_k"]
        score_threshold = retrieval_config["score_threshold"]
        use_rerank = retrieval_config["use_rerank"]
        vector_weight = retrieval_config["vector_weight"]
        keyword_weight = retrieval_config["keyword_weight"]
        rerank_config = retrieval_config["rerank_config"]
        
        # 3. 动态调整top_k
        adjusted_top_k = self._adjust_top_k(top_k, len(query))
        
        # 4. 获取embedding模型
        embedding_model = self._get_embedding_model(kb_config, tenant_id)
        
        # 5. 准备元数据过滤器
        metadata_filter = {}
        metadata_filter["file_ids"] = [file.id for file in knowledge_base.files if file.status == "indexed"]
        
        # 6. 执行搜索
        results = self._execute_search(
            knowledge_base=knowledge_base,
            embedding_model=embedding_model,
            tenant_id=tenant_id,
            query=query,
            metadata_filter=metadata_filter,
            top_k=adjusted_top_k,
            config=retrieval_config
        )
        
        # 7. 应用重排序
        reranked = False
        if use_rerank and results and rerank_config.get("model_id"):
            try:
                # 构建重排序配置
                rerank_model_config = {
                    "model_id": rerank_config.get("model_id"),
                    "tenant_id": tenant_id
                }
                
                # 使用异步方式重排序文档
                reranked_results, reranked = self._apply_reranking(
                    results=results,
                    query=query,
                    rerank_config=rerank_model_config,
                    top_k=adjusted_top_k
                )
                
                if reranked:
                    results = reranked_results
            except Exception as e:
                logger.error(f"Rerank失败: {str(e)}")
        
        # 8. 应用分数阈值过滤
        if score_threshold > 0:
            results = [doc for doc in results if doc.metadata.get("score", 0) >= score_threshold]
            
        # 9. 格式化结果
        formatted_results = []
        for doc in results:
            # Extract chunk_id from metadata
            chunk_id = doc.metadata.get("chunk_id")
            
            # Get the chunk from the database if chunk_id is available
            if chunk_id:
                chunk = self.db.query(KnowledgeChunk).filter(KnowledgeChunk.id == chunk_id).first()
                if chunk:
                    # Get file information
                    file = self.db.query(KnowledgeFile).filter(KnowledgeFile.id == chunk.file_id).first()
                    file_info = {
                        "id": file.id,
                        "name": file.file_name,
                        "type": file.file_type
                    } if file else None
                    
                    formatted_results.append({
                        "chunk_id": chunk_id,
                        "content": doc.page_content,
                        "metadata": doc.metadata,
                        "file": file_info,
                        "score": doc.metadata.get("score", 0)
                    })
            else:
                # If no chunk_id, just use the document as is
                formatted_results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "score": doc.metadata.get("score", 0)
                })
        
        # 10. 返回标准格式结果
        return {
            "query": query,
            "knowledge_base_id": kb_id,
            "method": search_method,
            "count": len(formatted_results),
            "results": formatted_results,
            "has_results": len(formatted_results) > 0,
            "documents": results,  # 原始文档对象
            "search_params": {
                "top_k": adjusted_top_k,
                "score_threshold": score_threshold,
                "use_rerank": use_rerank,
                "reranked": reranked,
                "vector_weight": vector_weight if search_method == SearchMethod.HYBRID else None,
                "keyword_weight": keyword_weight if search_method == SearchMethod.HYBRID else None
            }
        }

    def _parse_retrieval_config(self, kb_config: Dict) -> Dict:
        """
        从知识库配置解析检索参数
        
        Args:
            kb_config: 知识库配置
            
        Returns:
            解析后的检索配置
        """
        # 获取默认检索配置
        retrieval_config = kb_config.get("retrieval", DEFAULT_RETRIEVAL_CONFIG)
        
        # 确定搜索方法
        default_type = retrieval_config.get("default_search_type", "hybrid")
        search_method = {
            "vector": SearchMethod.SIMILARITY,
            "keyword": SearchMethod.KEYWORD
        }.get(default_type, SearchMethod.HYBRID)
        
        # 根据搜索方法获取配置块
        config_map = {
            SearchMethod.SIMILARITY: retrieval_config.get("vector_search", {}),
            SearchMethod.KEYWORD: retrieval_config.get("keyword_search", {}),
            SearchMethod.HYBRID: retrieval_config.get("hybrid_search", {})
        }
        method_config = config_map[search_method]
        
        # 返回解析后的配置
        return {
            "search_method": search_method,
            "top_k": method_config.get("top_k", 5),
            "score_threshold": method_config.get("score_threshold", 0.3),
            "use_rerank": method_config.get("rerank", {}).get("enabled", False),
            "vector_weight": method_config.get("vector_weight", 0.7),
            "keyword_weight": method_config.get("keyword_weight", 0.3),
            "rerank_config": method_config.get("rerank", {})
        }
    
    def _get_knowledge_base(self, kb_id: str, tenant_id: str) -> KnowledgeBase:
        """
        获取并验证知识库
        
        Args:
            kb_id: 知识库ID
            tenant_id: 租户ID
            
        Returns:
            知识库对象
            
        Raises:
            ValueError: 知识库不存在时抛出
        """
        knowledge_base = self.db.query(KnowledgeBase).filter(
            KnowledgeBase.id == kb_id,
            KnowledgeBase.tenant_id == tenant_id
        ).first()
        
        if not knowledge_base:
            raise ValueError(f"知识库不存在: {kb_id}")
            
        return knowledge_base
    
    def _get_embedding_model(self, kb_config: Dict, tenant_id: str) -> Any:
        """
        获取并验证embedding模型
        
        Args:
            kb_config: 知识库配置
            tenant_id: 租户ID
            
        Returns:
            Embedding模型
            
        Raises:
            ValueError: 模型不存在或无法加载时抛出
        """
        # 检查是否有embedding配置
        embedding_config = kb_config.get("embedding", {})
        model_id = embedding_config.get("model_id") if embedding_config else kb_config.get("embedding_model_id")
        
        if not model_id:
            raise ValueError(f"知识库未配置Embedding模型")

        model = self.db.query(Model).filter(Model.id == model_id, Model.is_active == True).first()
        if not model:
            raise ValueError(f"Embedding模型不存在或未激活: {model_id}")

        embedding_model = self.embedding_manager.get_model(model_id=model_id, tenant_id=tenant_id)

        if not embedding_model:
            raise ValueError(f"无法加载Embedding模型: {model_id}")
            
        return embedding_model
    
    def _adjust_top_k(self, base_top_k: int, query_length: int) -> int:
        """
        根据查询长度智能调整top_k
        
        Args:
            base_top_k: 基础top_k值
            query_length: 查询长度
            
        Returns:
            调整后的top_k值
        """
        MAX_TOP_K = 100
        if query_length < 10:  # 短查询
            return min(base_top_k * 3, MAX_TOP_K)
        elif query_length < 30:  # 中等查询
            return min(base_top_k * 2, MAX_TOP_K)
        elif query_length > 100:  # 长查询
            return max(int(base_top_k * 0.7), 5)
        return base_top_k  # 默认
    
    def _execute_search(self, knowledge_base, embedding_model, tenant_id, query, metadata_filter, top_k, config) -> List[Document]:
        """
        根据配置执行具体搜索
        
        Args:
            knowledge_base: 知识库对象
            embedding_model: Embedding模型
            tenant_id: 租户ID
            query: 搜索查询
            metadata_filter: 元数据过滤条件
            top_k: 返回结果数量
            config: 检索配置
            
        Returns:
            文档列表
        """
        search_method = config["search_method"]
        vector_weight = config["vector_weight"]
        keyword_weight = config["keyword_weight"]
        
        # 根据搜索方法执行搜索
        if search_method == SearchMethod.SIMILARITY:
            return self._similarity_search(
                knowledge_base, 
                embedding_model, 
                tenant_id, 
                query, 
                metadata_filter, 
                top_k
            )
        elif search_method == SearchMethod.KEYWORD:
            return self._keyword_search(
                knowledge_base.id, 
                tenant_id, 
                query, 
                metadata_filter, 
                top_k
            )
        else:  # HYBRID
            return self._hybrid_search(
                knowledge_base, 
                embedding_model, 
                tenant_id, 
                query, 
                metadata_filter, 
                top_k,
                vector_weight,
                keyword_weight
            )
    
    def _apply_reranking(self, results, query, rerank_config, top_k) -> Tuple[List[Document], bool]:
        """
        应用重排序并返回结果和状态
        
        Args:
            results: 原始搜索结果
            query: 搜索查询
            rerank_config: 重排序配置
            top_k: 返回结果数量
            
        Returns:
            重排序后的结果和重排序状态
        """
        try:
            # 使用异步方式重排序文档
            reranked_results = asyncio.run(
                self.reranker_manager.rerank_documents(
                    reranker_config=rerank_config,
                    query=query,
                    documents=results,
                    top_k=top_k
                )
            )
            
            if reranked_results:
                logger.info(f"成功对 {len(reranked_results)} 个文档进行重排序")
                return reranked_results, True
            return results, False
        except Exception as e:
            logger.error(f"Rerank失败: {str(e)}")
            return results, False
    
    def _similarity_search(
        self,
        knowledge_base: KnowledgeBase,
        embedding_model: Embeddings,
        tenant_id: str,
        query: str,
        metadata_filter: Dict[str, Any],
        top_k: int
    ) -> List[Document]:
        """
        Perform similarity search using vector store.
        
        Args:
            knowledge_base: Knowledge base object
            embedding_model: Embedding model
            tenant_id: Tenant ID
            query: Search query
            metadata_filter: Metadata filter criteria
            top_k: Number of results to return
            
        Returns:
            List of Document objects
        """
        # Get vector store
        try:
            vector_store = self.vector_store_manager.get_vector_store(knowledge_base, embedding_model, tenant_id)
            
            filter_query = []
            if metadata_filter:
                for key, value in metadata_filter.items():
                    if key == "file_ids":
                        file_ids = value
                        if isinstance(file_ids, list) and len(file_ids) > 0:
                            # 使用terms查询多个文件ID
                            filter_query.append({
                                "terms": {
                                    "metadata.file_id.keyword": file_ids
                                }
                            })
                        elif isinstance(file_ids, str):
                            # 单个文件ID使用term查询
                            filter_query.append({
                                "term": {
                                    "metadata.file_id.keyword": file_ids
                                }
                            })
            
            # Perform similarity search
            results = vector_store.similarity_search_with_score(
                query=query,
                k=top_k,
                filter=filter_query
            )
            
            # Convert results to Documents with scores
            documents = []
            for doc, score in results:
                # Add score to metadata
                doc.metadata["score"] = float(score)
                documents.append(doc)
            
            # Return sorted documents
            return sorted(documents, key=lambda x: x.metadata["score"], reverse=True)
        except Exception as e:
            # Log the error but return empty results instead of failing
            logger.warning(f"Similarity search error: {str(e)}")
            return []

    def _keyword_search(
        self,
        kb_id: str,
        tenant_id: str,
        query: str,
        metadata_filter: Dict[str, Any],
        top_k: int
    ) -> List[Document]:
        """
        Perform keyword search using database or Elasticsearch BM25.
        
        Args:
            kb_id: Knowledge base ID
            tenant_id: Tenant ID
            query: Search query
            metadata_filter: Metadata filter criteria
            top_k: Number of results to return
            
        Returns:
            List of Document objects
        """
        try:
            # 获取知识库信息
            knowledge_base = self.db.query(KnowledgeBase).filter(KnowledgeBase.id == kb_id).first()
            if not knowledge_base:
                logger.warning(f"Knowledge base not found: {kb_id}")
                return []
            
            # 检查是否使用Elasticsearch
            store_type = VectorStoreType(settings.VECTOR_STORE_TYPE)
            
            # 如果是Elasticsearch，使用BM25检索
            if store_type == VectorStoreType.ELASTICSEARCH:
                return self._elasticsearch_bm25_search(
                    knowledge_base=knowledge_base,
                    tenant_id=tenant_id,
                    query=query,
                    metadata_filter=metadata_filter,
                    top_k=top_k
                )
            
            # 否则使用原有的数据库关键字搜索
            # Parse query into keywords
            keywords = re.findall(r'\w+', query.lower())
            
            # Get file IDs if file_id is in metadata_filter
            file_ids = []
            if "file_ids" in metadata_filter:
                if isinstance(metadata_filter["file_ids"], list):
                    file_ids = metadata_filter["file_ids"]
                else:
                    file_ids = [metadata_filter["file_ids"]]
            
            # Build query
            chunk_query = self.db.query(KnowledgeChunk)
            
            # Join with KnowledgeFile to get knowledge_base_id
            chunk_query = chunk_query.join(
                KnowledgeFile,
                KnowledgeChunk.file_id == KnowledgeFile.id
            )
            
            # Filter by knowledge base
            chunk_query = chunk_query.filter(KnowledgeFile.knowledge_base_id == kb_id)
            
            # Filter by file IDs if provided
            if file_ids:
                chunk_query = chunk_query.filter(KnowledgeChunk.file_id.in_(file_ids))
            
            # Add full-text search condition for each keyword
            search_conditions = []
            for keyword in keywords:
                if len(keyword) >= 3:  # Skip very short keywords
                    search_conditions.append(KnowledgeChunk.content.ilike(f"%{keyword}%"))
            
            if search_conditions:
                chunk_query = chunk_query.filter(or_(*search_conditions))
            
            # Order by relevance (number of keyword matches)
            relevance_score = func.length(KnowledgeChunk.content)
            for keyword in keywords:
                if len(keyword) >= 3:
                    # Count occurrences of each keyword
                    relevance_score = relevance_score + func.length(
                        func.replace(
                            func.lower(KnowledgeChunk.content),
                            keyword.lower(),
                            ""
                        )
                    ) * -1
            
            chunk_query = chunk_query.order_by(relevance_score.desc())
            
            # Limit results
            chunks = chunk_query.limit(top_k).all()
            
            # Convert to Documents
            documents = []
            for i, chunk in enumerate(chunks):
                # Calculate score based on position
                score = 1.0 - (i / max(len(chunks), 1))
                
                # Get file
                file = self.db.query(KnowledgeFile).filter(KnowledgeFile.id == chunk.file_id).first()
                
                # Create metadata
                metadata = {
                    "chunk_id": chunk.id,
                    "file_id": chunk.file_id,
                    "knowledge_base_id": kb_id,
                    "tenant_id": tenant_id,
                    "score": score
                }
                
                if file:
                    metadata["file_name"] = file.file_name
                    metadata["file_type"] = file.file_type
                
                if chunk.meta_data:
                    metadata.update(chunk.meta_data)
                
                # Create Document
                doc = Document(
                    page_content=chunk.content,
                    metadata=metadata
                )
                
                documents.append(doc)
            
            return documents
        except Exception as e:
            logger.warning(f"Keyword search error: {str(e)}")
            return []

    def _elasticsearch_bm25_search(
        self,
        knowledge_base: KnowledgeBase,
        tenant_id: str,
        query: str,
        metadata_filter: Dict[str, Any],
        top_k: int
    ) -> List[Document]:
        """
        Perform BM25 keyword search using Elasticsearch.
        
        Args:
            knowledge_base: Knowledge base object
            tenant_id: Tenant ID
            query: Search query
            metadata_filter: Metadata filter criteria
            top_k: Number of results to return
            
        Returns:
            List of Document objects
        """
        try:
            # 获取ES连接信息
            es_url = settings.ES_URL
            es_username = settings.ES_USERNAME
            es_password = settings.ES_PASSWORD
            
            # 构建索引名称
            store_key = f"{tenant_id}:{knowledge_base.id}"
            index_name = f"kb_{store_key.replace(':', '_').lower()}"
            
            # 创建ES客户端
            es_client = Elasticsearch(
                es_url,
                basic_auth=(es_username, es_password)
            )
            
            # 检查索引是否存在
            if not es_client.indices.exists(index=index_name):
                logger.warning(f"Elasticsearch index does not exist: {index_name}")
                return []
            
            # 构建查询
            search_query = {
                "size": top_k,
                "query": {
                    "bool": {
                        "must": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["text^3", "metadata.file_name^2"],
                                    "type": "best_fields",
                                    "fuzziness": "AUTO",
                                    "operator": "or"
                                }
                            }
                        ]
                    }
                },
                "highlight": {
                    "fields": {
                        "text": {}
                    },
                    "pre_tags": ["<em>"],
                    "post_tags": ["</em>"]
                }
            }
            
            # 添加文件过滤条件
            if "file_ids" in metadata_filter:
                file_condition = None
                if isinstance(metadata_filter["file_ids"], list):
                    file_condition = {"terms": {"metadata.file_id.keyword": metadata_filter["file_ids"]}}
                else:
                    file_condition = {"term": {"metadata.file_id.keyword": metadata_filter["file_ids"]}}
                
                search_query["query"]["bool"]["filter"] = [file_condition]
            
            # 执行搜索
            response = es_client.search(
                index=index_name,
                body=search_query
            )
            
            # 处理结果
            documents = []
            for hit in response["hits"]["hits"]:
                score = hit["_score"]
                source = hit["_source"]

                # 提取内容和元数据
                content = source.get("text", "")
                metadata = source.get("metadata", {})
                
                # 添加分数
                metadata["score"] = score
                
                # 如果有高亮内容，替换原始内容以显示匹配部分
                if "highlight" in hit and "text" in hit["highlight"]:
                    # 保留原始内容，但在元数据中添加高亮内容
                    metadata["highlighted_content"] = " ... ".join(hit["highlight"]["text"])
                
                # 创建文档
                doc = Document(
                    page_content=content,
                    metadata=metadata
                )
                
                documents.append(doc)
            
            return documents
        except Exception as e:
            logger.warning(f"Elasticsearch BM25 search error: {str(e)}")
            return []

    def _hybrid_search(
        self,
        knowledge_base: KnowledgeBase,
        embedding_model: Embeddings,
        tenant_id: str,
        query: str,
        metadata_filter: Dict[str, Any],
        top_k: int,
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3
    ) -> List[Document]:
        """
        Perform hybrid search combining vector and keyword search.
        
        Args:
            knowledge_base: Knowledge base object
            embedding_model: Embedding model
            tenant_id: Tenant ID
            query: Search query
            metadata_filter: Metadata filter criteria
            top_k: Number of results to return
            vector_weight: Weight for vector search results (default: 0.7)
            keyword_weight: Weight for keyword search results (default: 0.3)
            
        Returns:
            List of Document objects
        """
        try:
            # 权重归一化
            vector_weight, keyword_weight = self._normalize_weights(vector_weight, keyword_weight)
            
            # 并行获取搜索结果
            vector_results, keyword_results = self._parallel_search(
                knowledge_base, embedding_model, tenant_id, query, metadata_filter, top_k
            )
            
            # 融合结果
            combined_results = self._linear_interpolation(vector_results, keyword_results, vector_weight, keyword_weight)
            
            # 处理并返回结果
            return self._process_results(combined_results, top_k)
        except Exception as e:
            logger.warning(f"Hybrid search error: {str(e)}", exc_info=True)
            return []

    def _normalize_weights(self, vector_weight: float, keyword_weight: float) -> Tuple[float, float]:
        """
        归一化向量和关键词搜索权重
        时间复杂度：O(1) 空间复杂度：O(1)
        """
        total_weight = vector_weight + keyword_weight
        if total_weight != 1.0:
            return (vector_weight / total_weight, keyword_weight / total_weight)
        return (vector_weight, keyword_weight)

    def _parallel_search(self, knowledge_base: KnowledgeBase, embedding_model: Embeddings, tenant_id: str, query: str, metadata_filter: Dict[str, Any], top_k: int) -> Tuple[List[Document], List[Document]]:
        """
        并行执行向量和关键词搜索
        时间复杂度：O(max(V, K)) 空间复杂度：O(V + K)
        其中V为向量搜索时间，K为关键词搜索时间
        """
        # 提交并行任务
        vector_future = self.executor.submit(
            self._similarity_search, knowledge_base, embedding_model, tenant_id, query, metadata_filter, top_k
        )
        keyword_future = self.executor.submit(
            self._keyword_search, knowledge_base.id, tenant_id, query, metadata_filter, top_k
        )
        
        # 获取结果
        return vector_future.result(), keyword_future.result()

    def _linear_interpolation(self, vector_results: List[Document], keyword_results: List[Document], 
                             vector_weight: float = 0.7, keyword_weight: float = 0.3) -> List[Dict[str, Any]]:
        """
        使用线性插值融合搜索结果
        时间复杂度：O(V + K) 空间复杂度：O(V + K)
        """
        fused_scores = {}
        
        # 归一化分数
        max_vector_score = max([doc.metadata.get("score", 0) for doc in vector_results]) if vector_results else 1.0
        max_keyword_score = max([doc.metadata.get("score", 0) for doc in keyword_results]) if keyword_results else 1.0
        
        # 处理向量搜索结果
        for doc in vector_results:
            chunk_id = doc.metadata.get("chunk_id")
            if chunk_id:
                norm_score = (doc.metadata.get("score", 0) / max_vector_score) * vector_weight
                if chunk_id in fused_scores:
                    fused_scores[chunk_id]["score"] += norm_score
                    fused_scores[chunk_id]["in_vector"] = True
                else:
                    fused_scores[chunk_id] = {
                        "doc": doc, "score": norm_score,
                        "in_vector": True, "in_keyword": False
                    }
        
        # 处理关键词搜索结果
        for doc in keyword_results:
            chunk_id = doc.metadata.get("chunk_id")
            if chunk_id:
                norm_score = (doc.metadata.get("score", 0) / max_keyword_score) * keyword_weight
                if chunk_id in fused_scores:
                    fused_scores[chunk_id]["score"] += norm_score
                    fused_scores[chunk_id]["in_keyword"] = True
                else:
                    fused_scores[chunk_id] = {
                        "doc": doc, "score": norm_score,
                        "in_vector": False, "in_keyword": True
                    }
        
        return list(fused_scores.values())

    def _process_results(self, combined_results: List[Dict[str, Any]], top_k: int) -> List[Document]:
        """
        处理融合结果并返回排序后的文档
        时间复杂度：O(n log n) 空间复杂度：O(n)
        其中n为融合后的结果数量
        """
        # 更新文档元数据
        for result in combined_results:
            # RRF分数不需要归一化
            result["doc"].metadata["score"] = result["score"]
            result["doc"].metadata["vector_match"] = result["in_vector"]
            result["doc"].metadata["keyword_match"] = result["in_keyword"]
            result["doc"].metadata["fusion_method"] = "rrf"
        
        # 按分数排序并返回前top_k结果
        sorted_results = sorted(
            combined_results,
            key=lambda x: x["score"],
            reverse=True
        )
        
        return [item["doc"] for item in sorted_results[:top_k]]
