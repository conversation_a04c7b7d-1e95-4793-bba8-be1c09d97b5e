"""
Indexing functionality for RAG.
"""

from typing import List, Dict, Any, Optional, Tu<PERSON>
from datetime import datetime, timezone
from sqlalchemy.orm import Session

from app.models.knowledge import KnowledgeBase, KnowledgeFile, KnowledgeChunk
from app.schemas.knowledge import KnowledgeChunkCreate
from app.core.rag.document_loaders import DocumentLoader
from app.core.rag.embeddings import EmbeddingManager
from app.core.rag.vector_stores import VectorStoreManager
from app.utils.id_generator import generate_knowledge_chunk_id

# 设置UTC时区
UTC = timezone.utc
from app.core.logging_config import logging_config
logger = logging_config.get_logger(__name__)


class Indexer:
    """
    Class for indexing and managing document chunks in vector stores.
    """
    
    def __init__(self, db: Session):
        """Initialize the indexer.
        
        Args:
            db: Database session
        """
        self.db = db
        self.document_loader = DocumentLoader()
        self.embedding_manager = EmbeddingManager(db)
        self.vector_store_manager = VectorStoreManager()
        """
        Initialize the indexer.
        
        Args:
            db: Database session
        """
        self.db = db
    
    async def index_document(
        self,
        file_id: str,
        tenant_id: str
    ) -> Tuple[int, List[str]]:
        """
        Index a document in the vector store and create chunk records.
        
        Args:
            file_id: ID of the file to index
            tenant_id: Tenant ID
            
        Returns:
            Tuple of (number of chunks indexed, list of chunk IDs)
        """
        # Get the file
        file = self.db.query(KnowledgeFile).filter(KnowledgeFile.id == file_id).first()
        if not file:
            raise ValueError(f"File not found: {file_id}")
        
        # Get the knowledge base
        knowledge_base = self.db.query(KnowledgeBase).filter(
            KnowledgeBase.id == file.knowledge_base_id,
            KnowledgeBase.tenant_id == tenant_id
        ).first()
        
        if not knowledge_base:
            raise ValueError(f"Knowledge base not found for file: {file_id}")
        
        chunk_size = knowledge_base.config.get("chunk_size", 512)
        chunk_overlap = knowledge_base.config.get("chunk_overlap", 128)

        # Load the document and split into chunks
        chunks = self.document_loader.load_from_s3(file, chunk_size, chunk_overlap)
        
        # If no chunks were created, update file status and return
        if not chunks:
            file.status = "error"
            self.db.commit()
            return 0, []
        
        # Get embedding model
        embedding_config = knowledge_base.config.get("embedding")

        if not embedding_config:
            logger.error(f"No embedding config specified for file: {file_id}")
            file.status = "error"
            self.db.commit()
            raise ValueError("No embedding config specified")
        
        model_id = embedding_config.get("model_id")
        if not model_id:
            logger.error(f"No model_id specified for file: {file_id}")
            file.status = "error"
            self.db.commit()
            raise ValueError("No model_id specified")
        
        embedding_model = self.embedding_manager.get_model(model_id=model_id, tenant_id=tenant_id)

        if not embedding_model:
            logger.error(f"No embedding model specified for file: {file_id}")
            file.status = "error"
            self.db.commit()
            raise ValueError("No embedding model specified")
        
        chunk_texts = [chunk.page_content for chunk in chunks if chunk.page_content]

        # Process embeddings in batches of 10 to avoid DashScope API limit
        logger.info(f"Embedding {len(chunk_texts)} chunks in batches")
        batch_size = 10  # DashScope API limit
        all_embeddings = []
        
        for i in range(0, len(chunk_texts), batch_size):
            batch = chunk_texts[i:i+batch_size]
            logger.info(f"Processing batch {i//batch_size + 1} with {len(batch)} chunks")
            batch_embeddings = embedding_model.embed_documents(batch)
            all_embeddings.extend(batch_embeddings)
        
        # Get vector store
        vector_store = self.vector_store_manager.get_vector_store(knowledge_base, embedding_model, tenant_id)

        logger.info(f"Vector store created: {vector_store}")
        
        # Create chunk records and collect texts for embedding
        chunk_ids = []
        texts = []
        metadatas = []
        
        for i, chunk in enumerate(chunks):
            # Create chunk record
            chunk_data = KnowledgeChunkCreate(
                id=generate_knowledge_chunk_id(),
                file_id=file.id,
                content=chunk.page_content,
                embedding=all_embeddings[i],
                meta_data={
                    **chunk.metadata,
                    "tenant_id": tenant_id,
                    "knowledge_base_id": knowledge_base.id
                }
            )
            
            db_chunk = KnowledgeChunk(
                id=chunk_data.id,
                file_id=chunk_data.file_id,
                content=chunk_data.content,
                embedding=chunk_data.embedding,
                meta_data=chunk_data.meta_data
            )
            
            self.db.add(db_chunk)
            self.db.flush()  # Flush to get the ID
            
            chunk_ids.append(db_chunk.id)
            texts.append(chunk.page_content)
            metadatas.append({
                **chunk.metadata,
                "chunk_id": chunk_data.id,
                "file_id": file.id,
                "knowledge_base_id": knowledge_base.id,
                "tenant_id": tenant_id
            })
        
        # Add embeddings to vector store
        try:
            logger.info(f"Adding {len(texts)} texts to vector store for knowledge base {knowledge_base.id}")
            
            # 检查是否为FAISS向量存储
            from app.core.config import settings
            if settings.VECTOR_STORE_TYPE == "faiss":
                # 使用专门的FAISS添加函数
                success = self.vector_store_manager.add_texts_to_faiss_store(
                    tenant_id=tenant_id,
                    knowledge_base_id=knowledge_base.id,
                    texts=texts,
                    metadatas=metadatas,
                    embedding_model=embedding_model,
                    embeddings=all_embeddings
                )
                if not success:
                    raise ValueError("Failed to add texts to FAISS vector store")
            else:
                # Process vector store additions in batches as well
                for i in range(0, len(texts), batch_size):
                    batch_texts = texts[i:i+batch_size]
                    batch_embeddings = all_embeddings[i:i+batch_size]
                    batch_metadatas = metadatas[i:i+batch_size]
                    
                    logger.info(f"Adding batch {i//batch_size + 1} with {len(batch_texts)} texts to vector store")
                    vector_store.add_texts(
                        texts=batch_texts, 
                        embeddings=batch_embeddings, 
                        metadatas=batch_metadatas
                    )
                
                # 确保向量存储被保存
                if hasattr(vector_store, "persist"):
                    logger.info(f"Persisting vector store for knowledge base {knowledge_base.id}")
                    vector_store.persist()
            
            logger.info(f"Successfully added {len(texts)} texts to vector store")
        except Exception as e:
            logger.error(f"Error adding texts to vector store: {str(e)}")
            file.status = "error"
            self.db.commit()
            raise ValueError(f"Failed to add texts to vector store: {str(e)}")
        
        # Update file status
        file.status = "indexed"
        file.chunk_count = len(chunks)
        file.updated_at = datetime.now(UTC)
        
        # Commit all changes
        self.db.commit()
        
        return len(chunks), chunk_ids
    
    async def reindex_knowledge_base(
        self,
        kb_id: str,
        tenant_id: str
    ) -> Dict[str, Any]:
        """
        Reindex all files in a knowledge base.
        
        Args:
            kb_id: Knowledge base ID
            tenant_id: Tenant ID
            
        Returns:
            Dictionary with indexing results
        """
        # Get the knowledge base
        knowledge_base = self.db.query(KnowledgeBase).filter(
            KnowledgeBase.id == kb_id,
            KnowledgeBase.tenant_id == tenant_id
        ).first()
        
        if not knowledge_base:
            raise ValueError(f"Knowledge base not found: {kb_id}")
        
        # Get all files in the knowledge base
        files = self.db.query(KnowledgeFile).filter(
            KnowledgeFile.knowledge_base_id == kb_id
        ).all()
        
        # Delete existing chunks and vector store
        self.delete_index(kb_id, tenant_id)
        
        # Index each file
        results = {}
        for file in files:
            try:
                num_chunks, chunk_ids = await self.index_document(
                    file_id=file.id,
                    tenant_id=tenant_id
                )
                
                results[file.id] = {
                    "status": "success",
                    "num_chunks": num_chunks,
                    "chunk_ids": chunk_ids
                }
                
            except Exception as e:
                # Update file status to error
                file.status = "error"
                file.meta_data = {**(file.meta_data or {}), "error": str(e)}
                self.db.commit()
                
                results[file.id] = {
                    "status": "error",
                    "error": str(e)
                }
        
        return {
            "knowledge_base_id": kb_id,
            "total_files": len(files),
            "file_results": results
        }
    
    def delete_index(
        self,
        kb_id: str,
        tenant_id: str
    ) -> Dict[str, Any]:
        """
        Delete all chunks and vector store for a knowledge base.
        
        Args:
            kb_id: Knowledge base ID
            tenant_id: Tenant ID
            
        Returns:
            Dictionary with deletion results
        """
        # Get the knowledge base
        knowledge_base = self.db.query(KnowledgeBase).filter(
            KnowledgeBase.id == kb_id,
            KnowledgeBase.tenant_id == tenant_id
        ).first()
        
        if not knowledge_base:
            raise ValueError(f"Knowledge base not found: {kb_id}")
        
        # Delete all chunks for files in the knowledge base
        from sqlalchemy import delete
        
        # Get all file IDs in the knowledge base
        file_ids = [file.id for file in knowledge_base.files]
        
        # Delete chunks for these files
        if file_ids:
            stmt = delete(KnowledgeChunk).where(KnowledgeChunk.file_id.in_(file_ids))
            result = self.db.execute(stmt)
            deleted_chunks = result.rowcount
        else:
            deleted_chunks = 0
        
        # Delete vector store
        self.vector_store_manager.delete_vector_store(tenant_id, knowledge_base.id)
        
        # Update file statuses
        for file in knowledge_base.files:
            file.status = "processing"
            file.chunk_count = 0
        
        self.db.commit()
        
        return {
            "knowledge_base_id": kb_id,
            "deleted_chunks": deleted_chunks,
            "vector_store_deleted": True
        }
    
    def delete_file_index(
        self,
        file_id: str,
        tenant_id: str
    ) -> Dict[str, Any]:
        """
        Delete chunks and vector store entries for a single file.
        
        Args:
            file_id: File ID to delete
            tenant_id: Tenant ID
            
        Returns:
            Dictionary with deletion results
        """
        # Get the file
        file = self.db.query(KnowledgeFile).filter(KnowledgeFile.id == file_id).first()
        if not file:
            raise ValueError(f"File not found: {file_id}")
        
        # Get the knowledge base with tenant validation
        knowledge_base = self.db.query(KnowledgeBase).filter(
            KnowledgeBase.id == file.knowledge_base_id,
            KnowledgeBase.tenant_id == tenant_id
        ).first()
        
        if not knowledge_base:
            raise ValueError(f"Knowledge base not found or access denied. KB ID: {file.knowledge_base_id}, Tenant ID: {tenant_id}")
        
        # Additional tenant verification on file level (if file has tenant metadata)
        if file.meta_data and "tenant_id" in file.meta_data and file.meta_data["tenant_id"] != tenant_id:
            raise ValueError(f"File belongs to a different tenant. Access denied.")
        
        # Get all chunks for the file
        chunks = self.db.query(KnowledgeChunk).filter(KnowledgeChunk.file_id == file_id).all()
        
        # Additional tenant verification for chunks
        for chunk in chunks:
            if chunk.meta_data and "tenant_id" in chunk.meta_data and chunk.meta_data["tenant_id"] != tenant_id:
                raise ValueError(f"Chunk {chunk.id} belongs to a different tenant. Access denied.")
        
        chunk_ids = [chunk.id for chunk in chunks]
        
        # Get chunk IDs and metadata for vector store deletion
        chunk_metadatas = []
        for chunk in chunks:
            chunk_metadatas.append({
                "chunk_id": chunk.id,
                "file_id": file_id,
                "knowledge_base_id": knowledge_base.id,
                "tenant_id": tenant_id
            })
        
        # Delete chunks from the database
        from sqlalchemy import delete
        stmt = delete(KnowledgeChunk).where(
            KnowledgeChunk.file_id == file_id
        )
        
        result = self.db.execute(stmt)
        deleted_chunks = result.rowcount
        
        # Delete vector store entries if there are chunks
        if chunks:
            # Get embedding model for vector store
            kb_config = knowledge_base.config or {}
            model_id = kb_config.get("embedding_model_id")
            if model_id:
                try:
                    embedding_model = self.embedding_manager.get_model(model_id=model_id, tenant_id=tenant_id)
                    vector_store = self.vector_store_manager.get_vector_store(knowledge_base, embedding_model, tenant_id)
                    
                    # Delete vectors by metadata filter with tenant verification
                    if hasattr(vector_store, "delete"):
                        vector_store.delete(filter={"file_id": file_id, "tenant_id": tenant_id})
                    
                    # Persist changes if supported
                    if hasattr(vector_store, "persist"):
                        vector_store.persist()
                except Exception as e:
                    # Log error but continue with database updates
                    print(f"Error deleting vectors: {str(e)}")
        
        self.db.commit()
        
        return {
            "file_id": file_id,
            "knowledge_base_id": knowledge_base.id,
            "tenant_id": tenant_id,
            "deleted_chunks": deleted_chunks,
            "chunk_ids": chunk_ids,
            "vector_store_updated": len(chunks) > 0
        }