"""
Vector stores for RAG with support for different storage backends.
"""

from typing import Dict, Any, Optional, List, Type
from enum import Enum
import os
import json

from langchain.vectorstores.base import VectorStore
from langchain_community.vectorstores import FAISS, Chroma
from langchain_community.vectorstores.mongodb_atlas import MongoDBAtlasVectorSearch
from langchain_elasticsearch import ElasticsearchStore
from langchain.embeddings.base import Embeddings
from langchain.schema import Document
from sqlalchemy.orm import Session

from app.utils.singleton import Singleton
from app.models.knowledge import KnowledgeBase
from app.core.config import settings

__all__ = [
    "VectorStoreManager"
]
from app.core.logging_config import logging_config
logger = logging_config.get_logger(__name__)

class VectorStoreType(str, Enum):
    """Supported vector store types."""
    LOCAL_FAISS = "faiss"
    LOCAL_CHROMA = "chroma"
    MONGODB = "mongodb"
    ELASTICSEARCH = "elasticsearch"


class VectorStoreManager:
    """
    Manager class for interacting with vector stores.
    """
    
    def __init__(self):
        """Initialize the vector store manager."""
        self._factory = VectorStoreFactory()
    
    def get_vector_store(
        self,
        knowledge_base: KnowledgeBase,
        embedding_model: Embeddings,
        tenant_id: str,
        **kwargs
    ) -> VectorStore:
        """
        Get or create a vector store for a knowledge base.
        
        Args:
            knowledge_base: Knowledge base object
            embedding_model: Embedding model to use
            tenant_id: Tenant ID
            **kwargs: Additional parameters for the vector store
            
        Returns:
            Vector store instance
        """
        return self._factory.get_vector_store(
            knowledge_base=knowledge_base,
            embedding_model=embedding_model,
            tenant_id=tenant_id,
            **kwargs
        )
    
    def delete_vector_store(
        self,
        tenant_id: str,
        knowledge_base_id: str
    ) -> bool:
        """
        Delete a vector store.
        
        Args:
            tenant_id: Tenant ID
            knowledge_base_id: Knowledge base ID
            
        Returns:
            True if successful, False otherwise
        """
        return self._factory.delete_vector_store(tenant_id, knowledge_base_id)
    
    def add_texts(
        self,
        vector_store: VectorStore,
        texts: List[str],
        metadatas: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Add texts to a vector store.
        
        Args:
            vector_store: Vector store instance
            texts: List of text strings
            metadatas: List of metadata dictionaries
            ids: List of document IDs (optional)
            
        Returns:
            List of document IDs
        """
        if not texts or not metadatas:
            return []
        
        if len(texts) != len(metadatas):
            raise ValueError("Number of texts and metadatas must match")
        
        return vector_store.add_texts(texts=texts, metadatas=metadatas, ids=ids)
    
    def add_texts_to_faiss_store(
        self,
        tenant_id: str,
        knowledge_base_id: str,
        texts: List[str],
        embeddings: List[List[float]],
        metadatas: List[Dict[str, Any]],
        embedding_model: Embeddings
    ) -> bool:
        """
        Add texts with pre-computed embeddings to a FAISS vector store.
        
        Args:
            tenant_id: Tenant ID
            knowledge_base_id: Knowledge base ID
            texts: List of text strings
            embeddings: List of embedding vectors
            metadatas: List of metadata dictionaries
            embedding_model: Embedding model
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Generate a unique key for this vector store
            store_key = f"{tenant_id}:{knowledge_base_id}"
            
            # Define the path for the FAISS index
            data_dir = settings.VECTOR_STORE_DATA_DIR
            index_path = os.path.join(data_dir, f"{store_key.replace(':', '_')}_faiss")
            
            # Check if the index already exists
            if os.path.exists(f"{index_path}.faiss"):
                # Load existing index
                store = FAISS.load_local(
                    folder_path=data_dir,
                    index_name=f"{store_key.replace(':', '_')}_faiss",
                    embeddings=embedding_model,
                    allow_dangerous_deserialization=True
                )
                
                # Add embeddings to the index
                store.add_embeddings(
                    text_embeddings=list(zip(texts, embeddings)),
                    metadatas=metadatas
                )
            else:
                # Create a new index with the embeddings
                store = FAISS.from_embeddings(
                    text_embeddings=list(zip(texts, embeddings)),
                    embedding=embedding_model,
                    metadatas=metadatas
                )
            
            # Save the index
            store.save_local(folder_path=data_dir, index_name=f"{store_key.replace(':', '_')}_faiss")
            
            return True
        except Exception as e:
            logger.error(f"Error adding texts to FAISS store: {str(e)}")
            return False
    
    def search(
        self,
        vector_store: VectorStore,
        query: str,
        k: int = 5,
        filter: Optional[Dict[str, Any]] = None
    ) -> List[Document]:
        """
        Search documents in a vector store.
        
        Args:
            vector_store: Vector store instance
            query: Search query
            k: Number of results to return
            filter: Filter criteria
            
        Returns:
            List of Document objects
        """
        return vector_store.similarity_search(query=query, k=k, filter=filter)
    
    def search_with_score(
        self,
        vector_store: VectorStore,
        query: str,
        k: int = 5,
        filter: Optional[Dict[str, Any]] = None
    ) -> List[tuple[Document, float]]:
        """
        Search documents in a vector store and return scores.
        
        Args:
            vector_store: Vector store instance
            query: Search query
            k: Number of results to return
            filter: Filter criteria
            
        Returns:
            List of tuples containing Document objects and relevance scores
        """
        return vector_store.similarity_search_with_score(query=query, k=k, filter=filter)


class VectorStoreFactory(metaclass=Singleton):
    """Factory for creating vector stores."""
    
    def __init__(self):
        """Initialize the vector store factory."""
        self._vector_stores = {}
        self._data_dir = settings.VECTOR_STORE_DATA_DIR
        os.makedirs(self._data_dir, exist_ok=True)
    
    def get_vector_store(
        self,
        knowledge_base: KnowledgeBase,
        embedding_model: Embeddings,
        tenant_id: str,
        **kwargs
    ) -> VectorStore:
        """
        Get or create a vector store for a knowledge base.
        
        Args:
            knowledge_base: Knowledge base object
            embedding_model: Embedding model to use
            tenant_id: Tenant ID
            **kwargs: Additional parameters for the vector store
            
        Returns:
            Vector store instance
        """
        # Generate a unique key for this vector store
        store_key = f"{tenant_id}:{knowledge_base.id}"
        
        # Return cached store if it exists
        if store_key in self._vector_stores:
            return self._vector_stores[store_key]
        
        store_type = VectorStoreType(settings.VECTOR_STORE_TYPE)
        
        # Create the vector store
        if store_type == VectorStoreType.LOCAL_FAISS:
            store = self._create_faiss_store(store_key, embedding_model, **kwargs)
        elif store_type == VectorStoreType.LOCAL_CHROMA:
            store = self._create_chroma_store(store_key, embedding_model, **kwargs)
        elif store_type == VectorStoreType.MONGODB:
            store = self._create_mongodb_store(store_key, embedding_model, **kwargs)
        elif store_type == VectorStoreType.ELASTICSEARCH:
            store = self._create_elasticsearch_store(store_key, embedding_model, **kwargs)
        else:
            raise ValueError(f"Unsupported vector store type: {store_type}")
        
        # Cache the store
        self._vector_stores[store_key] = store

        logger.info(f"Vector store created: {store_key}")
        
        return store
    
    def _create_faiss_store(
        self,
        store_key: str,
        embedding_model: Embeddings,
        **kwargs
    ) -> FAISS:
        """Create a FAISS vector store."""
        logger.info(f"Creating FAISS vector store for {store_key}")
        # Define the path for the FAISS index
        index_path = os.path.join(self._data_dir, f"{store_key.replace(':', '_')}_faiss")
        
        # Check if the index already exists
        if os.path.exists(f"{index_path}.faiss"):
            # Load existing index
            logger.info(f"Loading existing FAISS index from {index_path}")
            try:
                return FAISS.load_local(
                    folder_path=self._data_dir,
                    index_name=f"{store_key.replace(':', '_')}_faiss",
                    embeddings=embedding_model,
                    allow_dangerous_deserialization=True
                )
            except Exception as e:
                logger.error(f"Error loading FAISS index: {str(e)}, creating new one")
                # 如果加载失败，删除旧索引并创建新的
                if os.path.exists(f"{index_path}.faiss"):
                    os.remove(f"{index_path}.faiss")
                if os.path.exists(f"{index_path}.pkl"):
                    os.remove(f"{index_path}.pkl")
        
        # Create a new empty index
        logger.info(f"Creating new FAISS index for {store_key}")
        
        # 创建一个样本嵌入以确定向量维度
        placeholder_text = "Sample text to determine embedding dimension"
        placeholder_id = "placeholder_id"

        try:
            # 创建带有占位符的索引
            store = FAISS.from_texts(
                texts=[placeholder_text],
                embedding=embedding_model,
                metadatas=[{"is_placeholder": True}],
                ids=[placeholder_id],
                **kwargs
            )
            
            # 删除占位符
            store.delete(ids=[placeholder_id])
            
            # 确保索引被保存
            store.save_local(folder_path=self._data_dir, index_name=f"{store_key.replace(':', '_')}_faiss")
            logger.info(f"Successfully created and saved new FAISS index for {store_key}")
            
            # 验证索引是否可以加载
            test_load = FAISS.load_local(
                folder_path=self._data_dir,
                index_name=f"{store_key.replace(':', '_')}_faiss",
                embeddings=embedding_model,
                allow_dangerous_deserialization=True
            )
            logger.info(f"Successfully verified FAISS index can be loaded")
            
            return store
        except Exception as e:
            logger.error(f"Error creating FAISS index: {str(e)}")
            raise ValueError(f"Failed to create FAISS vector store: {str(e)}")
    
    def _create_chroma_store(
        self,
        store_key: str,
        embedding_model: Embeddings,
        **kwargs
    ) -> Chroma:
        """Create a Chroma vector store."""
        # Define the path for the Chroma database
        persist_directory = os.path.join(self._data_dir, f"{store_key.replace(':', '_')}_chroma")
        
        # Create or load the Chroma database
        return Chroma(
            persist_directory=persist_directory,
            embedding_function=embedding_model,
            **kwargs
        )
    
    def _create_mongodb_store(
        self,
        store_key: str,
        embedding_model: Embeddings,
        **kwargs
    ) -> MongoDBAtlasVectorSearch:
        """Create a MongoDB Atlas vector store."""
        # Get MongoDB connection details from environment or config
        connection_string = settings.MONGODB_URL
        db_name = settings.MONGODB_DATABASE
        collection_name = f"kb_{store_key.replace(':', '_')}"
        
        if not connection_string:
            raise ValueError("MongoDB connection string not configured")
        
        logger.info(f"Creating MongoDB Atlas vector store for {store_key}")
        # Create MongoDB vector store
        return MongoDBAtlasVectorSearch.from_connection_string(
            connection_string=connection_string,
            namespace=f"{db_name}.{collection_name}",
            embedding=embedding_model,
            **kwargs
        )
    
    def _create_elasticsearch_store(
        self,
        store_key: str,
        embedding_model: Embeddings,
        **kwargs
    ) -> ElasticsearchStore:
        """Create an Elasticsearch vector store."""
        # Get Elasticsearch connection details from environment or config
        es_url = settings.ES_URL
        es_username = settings.ES_USERNAME
        es_password = settings.ES_PASSWORD
        index_name = f"kb_{store_key.replace(':', '_').lower()}"
        
        # Create Elasticsearch vector store
        return ElasticsearchStore(
            es_url=es_url,
            index_name=index_name,
            embedding=embedding_model,
            es_user=es_username,
            es_password=es_password,
            **kwargs
        )
    
    def delete_vector_store(
        self,
        tenant_id: str,
        knowledge_base_id: str
    ) -> bool:
        """
        Delete a vector store.
        
        Args:
            tenant_id: Tenant ID
            knowledge_base_id: Knowledge base ID
            
        Returns:
            True if successful, False otherwise
        """
        store_key = f"{tenant_id}:{knowledge_base_id}"
        
        # Remove from cache
        if store_key in self._vector_stores:
            del self._vector_stores[store_key]
        
        # Determine store type from knowledge base config, system config, or default
        store_type = VectorStoreType(settings.VECTOR_STORE_TYPE)
        
        try:
            if store_type == VectorStoreType.LOCAL_FAISS:
                # Delete FAISS index files
                index_path = os.path.join(self._data_dir, f"{store_key.replace(':', '_')}_faiss")
                if os.path.exists(f"{index_path}.faiss"):
                    os.remove(f"{index_path}.faiss")
                if os.path.exists(f"{index_path}.pkl"):
                    os.remove(f"{index_path}.pkl")
            
            elif store_type == VectorStoreType.LOCAL_CHROMA:
                # Delete Chroma directory
                persist_directory = os.path.join(self._data_dir, f"{store_key.replace(':', '_')}_chroma")
                if os.path.exists(persist_directory):
                    import shutil
                    shutil.rmtree(persist_directory)
            
            elif store_type == VectorStoreType.MONGODB:
                # Delete MongoDB collection
                from pymongo import MongoClient
                client = MongoClient(settings.MONGODB_URL)
                db = client[settings.MONGODB_DATABASE]
                collection_name = f"kb_{store_key.replace(':', '_')}"
                if collection_name in db.list_collection_names():
                    db.drop_collection(collection_name)
            
            elif store_type == VectorStoreType.ELASTICSEARCH:
                # Delete Elasticsearch index
                from elasticsearch import Elasticsearch
                es_client = Elasticsearch(
                    settings.ES_URL,
                    basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD)
                )
                index_name = f"kb_{store_key.replace(':', '_').lower()}"
                if es_client.indices.exists(index=index_name):
                    es_client.indices.delete(index=index_name)
            
            logger.info(f"Vector store deleted: {store_key}")
            return True
        
        except Exception as e:
            logger.error(f"Error deleting vector store: {str(e)}")
            return False


# Create a singleton instance
vector_store_factory = VectorStoreFactory()
vector_store_manager = VectorStoreManager()

# 为了向后兼容，保留原始函数接口
