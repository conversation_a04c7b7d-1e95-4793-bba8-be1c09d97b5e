"""
RAG (Retrieval-Augmented Generation) functionality for knowledge bases.
"""


from app.core.rag.indexing import Indexer
from app.core.rag.retrieval import SearchMethod

# 导出新的类接口
from app.core.rag.document_loaders import DocumentLoader
from app.core.rag.embeddings import EmbeddingManager
from app.core.rag.vector_stores import VectorStoreManager, VectorStoreType
from app.core.rag.retrieval import Retriever
from app.core.rag.rerank import RerankerManager

__all__ = [
    "DocumentLoader",
    "EmbeddingManager",
    "VectorStoreManager",
    "VectorStoreType",
    "Indexer",
    "Retriever",
    "SearchMethod",
    "RerankerManager"
] 