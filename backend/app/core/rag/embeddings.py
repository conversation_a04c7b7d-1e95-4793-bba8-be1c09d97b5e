"""
Embedding models for RAG using provider models from the database.
"""

from typing import Dict, Any, Optional, List

from langchain.embeddings.base import Embeddings
from sqlalchemy.orm import Session

from app.models.model import Model, ProviderAccessCredential
from app.utils.singleton import Singleton
from app.core.model_factory import ModelFactory

from app.core.logging_config import logging_config
logger = logging_config.get_logger(__name__)


class EmbeddingManager:
    """
    Class for managing and accessing embedding models.
    """
    
    def __init__(self, db: Session):
        """
        Initialize the embedding manager.
        
        Args:
            db: Database session
        """
        self.db = db
        self._model_factory = ModelFactory()
    
    def get_model(self, model_id: str, tenant_id: str) -> Embeddings:
        """
        Get an embedding model by ID.
        
        Args:
            model_id: Model ID
            tenant_id: Tenant ID
            
        Returns:
            Embeddings instance
        """
        return self._model_factory.create_embedding_model(self.db, tenant_id, model_id)
    
    def get_model_info(self, model_id: str) -> Dict[str, Any]:
        """
        Get information about an embedding model.
        
        Args:
            model_id: Model ID
            
        Returns:
            Dictionary with model information
        """
        model = self.db.query(Model).filter(Model.id == model_id, Model.is_active == True).first()
        if not model:
            raise ValueError(f"Model not found: {model_id}")
        
        return {
            "id": model.id,
            "name": model.name,
            "key": model.key,
            "provider": model.provider,
            "description": model.description,
            "dimension": model.extra_config.get("dimension") if model.extra_config else None
        }
    
    def list_available_models(self) -> List[Dict[str, Any]]:
        """
        List all available embedding models.
        
        Returns:
            List of dictionaries with model information
        """
        models = self.db.query(Model).filter(
            Model.is_active == True,
            Model.model_type == "embedding"
        ).all()
        
        return [
            {
                "id": model.id,
                "name": model.name,
                "key": model.key,
                "provider": model.provider,
                "description": model.description,
                "dimension": model.extra_config.get("dimension") if model.extra_config else None
            }
            for model in models
        ]
