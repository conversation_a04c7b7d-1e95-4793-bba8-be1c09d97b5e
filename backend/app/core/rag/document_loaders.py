"""
Document loaders for various file types.
"""

from typing import List, Dict, Any, Optional, Tuple, Callable
import os
import tempfile
import logging
import zipfile
import io
import chardet
import re
import string
from pathlib import Path
import traceback

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from app.services.storage import s3_storage_service
from app.models.knowledge import KnowledgeFile

from app.core.rag.extor.document_loader import DocumentLoader as ExtorDocumentLoader

from app.core.logging_config import logging_config

logger = logging_config.get_logger(__name__)


class RobustDocumentLoader:
    """
    鲁棒的文档加载器。
    提供与原有接口兼容的方法。
    """
    
    @staticmethod
    def load_document(file_path: str, **kwargs) -> List[Document]:
        """
        加载文档。
        
        Args:
            file_path: 文件路径
            **kwargs: 传递给解析器的额外参数
            
        Returns:
            List[Document]: 文档列表
        """
        logger.info(f"通过适配器加载文档: {file_path}")
        return ExtorDocumentLoader.load_document(file_path, **kwargs)
    
    @staticmethod
    def load_documents(file_paths: List[str], **kwargs) -> List[Document]:
        """
        批量加载多个文档。
        
        Args:
            file_paths: 文件路径列表
            **kwargs: 传递给解析器的额外参数
            
        Returns:
            List[Document]: 文档列表
        """
        logger.info(f"通过适配器批量加载 {len(file_paths)} 个文档")
        return ExtorDocumentLoader.load_documents(file_paths, **kwargs)
    
    @staticmethod
    def load_directory(directory_path: str, recursive: bool = True, file_extensions: Optional[List[str]] = None, **kwargs) -> List[Document]:
        """
        加载目录中的所有文档。
        
        Args:
            directory_path: 目录路径
            recursive: 是否递归加载子目录
            file_extensions: 要加载的文件扩展名列表（如 [".pdf", ".docx"]）
            **kwargs: 传递给解析器的额外参数
            
        Returns:
            List[Document]: 文档列表
        """
        logger.info(f"通过适配器加载目录: {directory_path}")
        return ExtorDocumentLoader.load_directory(directory_path, recursive, file_extensions, **kwargs)
    
    @staticmethod
    def load_text(text: str, metadata: Optional[dict] = None) -> List[Document]:
        """
        从文本字符串加载文档。
        
        Args:
            text: 文本内容
            metadata: 元数据字典
            
        Returns:
            List[Document]: 文档列表
        """
        logger.debug("通过适配器从文本字符串加载文档")
        return ExtorDocumentLoader.load_text(text, metadata)
    
    @staticmethod
    def load_from_binary(binary_data: bytes, file_extension: str, **kwargs) -> List[Document]:
        """
        从二进制数据加载文档。
        
        Args:
            binary_data: 二进制数据
            file_extension: 文件扩展名（如 ".pdf"）
            **kwargs: 传递给解析器的额外参数
            
        Returns:
            List[Document]: 文档列表
        """
        logger.debug(f"通过适配器从二进制数据加载 {file_extension} 文档")
        return ExtorDocumentLoader.load_from_binary(binary_data, file_extension, **kwargs)
    
    @staticmethod
    def get_supported_extensions() -> List[str]:
        """
        获取所有支持的文件扩展名。
        
        Returns:
            List[str]: 支持的文件扩展名列表
        """
        return ExtorDocumentLoader.get_supported_extensions()


class DocumentLoader:
    """
    Class for loading and processing documents from various sources.
    """
    
    @staticmethod
    def get_supported_file_types() -> Dict[str, List[str]]:
        """
        Get a dictionary of supported file types and their extensions.
        
        Returns:
            Dictionary mapping file types to lists of extensions
        """
        return {
            "PDF": [".pdf"],
            "Word": [".doc", ".docx"],
            "CSV": [".csv"],
            "Excel": [".xls", ".xlsx"],
            "PowerPoint": [".ppt", ".pptx"],
            "HTML": [".html", ".htm"],
            "Text": [".txt", ".md", ".json", ".py", ".js", ".java", ".c", ".cpp", ".cs", ".rb"]
        }
    
    @classmethod
    def get_loader_for_file(cls, file_path: str, file_name: str = None) -> Any:
        """
        Get the appropriate document loader for a file based on its type or extension.
        
        Args:
            file_path: Path to the file
            file_name: Name of the file (optional)
            
        Returns:
            Document loader instance
        """
        logger.info(f"获取文件加载器: {file_path}, 文件名: {file_name}")
        
        # 使用RobustDocumentLoader创建一个加载器包装器
        class RobustLoaderWrapper:
            def __init__(self, file_path):
                self.file_path = file_path
                self.file_name = file_name
            
            def load(self) -> List[Document]:
                logger.info(f"使用RobustDocumentLoader加载文档: {self.file_path}")
                return RobustDocumentLoader.load_document(self.file_path)
        
        return RobustLoaderWrapper(file_path)
    
    @classmethod
    def create_text_splitter(cls, chunk_size: int = 1000, chunk_overlap: int = 200) -> RecursiveCharacterTextSplitter:
        """
        Create a text splitter for splitting documents into chunks.
        
        Args:
            chunk_size: Size of text chunks
            chunk_overlap: Overlap between chunks
            
        Returns:
            Text splitter instance
        """
        return RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            is_separator_regex=False
        )
    
    @classmethod
    def load_from_s3(cls, file: KnowledgeFile, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[Document]:
        """
        Load a document from S3 storage and split it into chunks.
        
        Args:
            file: KnowledgeFile object containing file metadata
            chunk_size: Size of text chunks
            chunk_overlap: Overlap between chunks
            
        Returns:
            List of Document objects
        """
        logger.info(f"从S3加载文档: {file.file_path}, 文件ID: {file.id}, 文件名: {file.file_name}")
        
        # Extract the object key from the S3 path
        object_key = s3_storage_service.extract_object_key_from_path(file.file_path)
        if not object_key:
            logger.error(f"无效的S3路径: {file.file_path}")
            raise ValueError(f"Invalid S3 path: {file.file_path}")
        
        # Download the file to a temporary location
        try:
            file_content = s3_storage_service.download_file(object_key)
            logger.info(f"成功从S3下载文件: {object_key}, 大小: {len(file_content)} 字节")
        except Exception as e:
            logger.error(f"从S3下载文件失败: {object_key}, 错误: {str(e)}")
            raise
        
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.file_name).suffix) as temp_file:
            temp_file.write(file_content)
            temp_path = temp_file.name
            logger.debug(f"创建临时文件: {temp_path}")
        
        try:
            # Get appropriate loader and load the document
            loader = cls.get_loader_for_file(temp_path, file.file_name)
            logger.info(f"开始加载文档: {file.file_name}")
            documents = loader.load()
            logger.info(f"成功加载文档: {file.file_name}, 获取 {len(documents)} 个文档片段")
            
            # Create text splitter and split the document into chunks
            text_splitter = cls.create_text_splitter(chunk_size, chunk_overlap)
            logger.debug(f"使用文本分割器: 块大小={chunk_size}, 重叠={chunk_overlap}")
            chunks = text_splitter.split_documents(documents)
            logger.info(f"文档分割完成: {file.file_name}, 生成 {len(chunks)} 个块")
            
            # Add file metadata to each chunk
            for chunk in chunks:
                # Preserve existing metadata and add file information
                chunk.metadata.update({
                    "file_id": file.id,
                    "file_name": file.file_name,
                    "file_type": file.file_type,
                    "knowledge_base_id": file.knowledge_base_id
                })
            
            return chunks
        
        except Exception as e:
            logger.error(f"处理文档时出错: {file.file_name}, 错误: {str(e)}")
            logger.error(f"异常详情: {traceback.format_exc()}")
            raise
        
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_path):
                logger.debug(f"清理临时文件: {temp_path}")
                os.unlink(temp_path)
