import logging
from typing import Dict, Any, List
from sqlalchemy.orm import Session
from datetime import datetime, timezone
import requests
import json

from app.models.model import Provider, Model
from app.schemas.provider import ProviderCreate, ProviderType
from app.schemas.model import ModelCreate

logger = logging.getLogger(__name__)

class ModelProviderInitializer:
    """
    模型提供商初始化器，用于将支持的服务商及其模型初始化到数据库中
    """
    
    def __init__(self):
        
        self.api_key_properties = {
            "api_key": {
                "type": "string",
                "title": "API密钥",
                "description": "用于身份验证的API密钥",
                "format": "password",
                "minLength": 1
            }
        }
        
        self.api_base_properties = {
            "api_base": {
                "type": "string",
                "title": "API基础URL",
                "description": "API服务的基础URL，可覆盖默认设置",
                "format": "uri"
            }
        }
        
        self.timeout_properties = {
            "timeout": {
                "type": "integer",
                "title": "请求超时时间",
                "description": "API请求超时时间（秒）",
                "default": 30,
                "minimum": 1,
                "maximum": 300
            }
        }
        
        self.custom_headers_properties = {
            "custom_headers": {
                "type": "object",
                "title": "自定义请求头",
                "description": "额外的HTTP请求头",
                "additionalProperties": {"type": "string"}
            }
        }
        
       
        # JSON Schema 配置定义，用于动态生成配置表单
        self.config_schemas = {
            "openai_compatible": {
                "$schema": "http://json-schema.org/draft-07/schema#",
                "type": "object",
                "title": "OpenAI配置",
                "description": "OpenAI兼容API提供商的配置参数",
                "properties": {
                    **self.api_base_properties,
                    **self.api_key_properties,
                    **self.timeout_properties,
                    **self.custom_headers_properties
                },
                "required": ["api_key", "timeout"],
                "additionalProperties": False
            },
            "custom_openai_compatible": {
                "$schema": "http://json-schema.org/draft-07/schema#",
                "type": "object",
                "title": "自定义OpenAI兼容配置",
                "description": "自定义OpenAI兼容API提供商的配置参数",
                "properties": {
                    **self.api_key_properties,
                    **self.api_base_properties,
                    **self.timeout_properties,
                    **self.custom_headers_properties,
                },
                "required": ["api_key", "api_base", "timeout"],
                "additionalProperties": False
            }
        }
        
        # 支持的提供商及其基本信息
        self.supported_providers = {
            "openai": {
                "name": "OpenAI",
                "description": "OpenAI API提供商，支持GPT系列模型",
                "website": "https://openai.com",
                "icon": "https://openai.com/favicon.svg",
                "api_base": "https://api.openai.com/v1",
                "api_compatible": "openai",
                "provider_type": ProviderType.BUILTIN,
                "config_schema": self.config_schemas["openai_compatible"]
            },
            "aliyun": {
                "name": "阿里云通义",
                "description": "阿里云通义大模型API提供商",
                "website": "https://www.tongyi.com",
                "icon": "https://img.alicdn.com/imgextra/i4/O1CN01EfJVFQ1uZPd7W4W6i_!!6000000006051-2-tps-112-112.png",
                "api_compatible": "openai",
                "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                "provider_type": ProviderType.BUILTIN,
                "config_schema": self.config_schemas["openai_compatible"]
            },
            "tencent": {
                "name": "腾讯混元",
                "description": "腾讯混元大模型API提供商",
                "website": "https://hunyuan.tencent.com",
                "icon": "https://cdn-portal.hunyuan.tencent.com/public/static/logo/favicon.png",
                "api_compatible": "openai",
                "api_base": "https://hunyuan.tencentcloudapi.com/v1",
                "provider_type": ProviderType.BUILTIN,
                "config_schema": self.config_schemas["openai_compatible"]
            },
            "deepseek": {
                "name": "DeepSeek",
                "description": "DeepSeek AI模型API提供商",
                "website": "https://www.deepseek.com/",
                "icon": "https://cdn.deepseek.com/logo.png",
                "api_compatible": "openai",
                "api_base": "https://api.deepseek.com/v1",
                "provider_type": ProviderType.BUILTIN,
                "config_schema": self.config_schemas["openai_compatible"]
            },
            "siliconflow": {
                "name": "SiliconFlow",
                "description": "SiliconFlow提供高性能模型推理服务",
                "website": "https://siliconflow.cn",
                "icon": "https://www.siliconflow.cn/favicon.ico",
                "api_base": "https://api.siliconflow.cn/v1",
                "provider_type": ProviderType.BUILTIN,
                "api_compatible": "openai",
                "config_schema": self.config_schemas["openai_compatible"]
            },
            # 自定义OpenAI兼容提供商模板
            "custom_openai": {
                "name": "自定义OpenAI兼容",
                "description": "自定义的OpenAI兼容API提供商",
                "website": "",
                "icon": "https://openai.com/favicon.svg",
                "api_base": "https://api.example.com/v1",  # 提供一个示例URL
                "api_compatible": "openai",
                "provider_type": ProviderType.CUSTOM_OPENAI,
                "config_schema": self.config_schemas["custom_openai_compatible"],
            },
            "ollama": {
                "name": "Ollama",
                "description": "本地运行的开源模型服务",
                "website": "https://ollama.ai",
                "icon": "https://ollama.com/public/ollama.png",
                "api_base": "http://localhost:11434/v1",
                "api_compatible": "openai",
                "provider_type": ProviderType.BUILTIN,
                "config_schema": self.config_schemas["openai_compatible"]
            }
        }
    
    def initialize_providers(self, db: Session) -> Dict[str, Any]:
        """
        初始化所有支持的提供商及其模型到数据库
        
        Args:
            db: 数据库会话
            
        Returns:
            包含初始化结果的字典
        """
        results = {
            "total": len(self.supported_providers),
            "success": 0,
            "failures": 0,
            "details": {}
        }
        
        for provider_key, provider_info in self.supported_providers.items():
            try:
                # 检查提供商是否已存在
                existing_provider = db.query(Provider).filter(Provider.key == provider_key).first()
                
                if existing_provider:
                    logger.info(f"提供商 {provider_key} 已存在，将更新基本信息")
                    # 更新提供商信息
                    existing_provider.name = provider_info["name"]
                    existing_provider.description = provider_info["description"]
                    existing_provider.website = provider_info["website"]
                    existing_provider.icon = provider_info["icon"]
                    existing_provider.api_base = provider_info.get("api_base")
                    existing_provider.api_compatible = provider_info.get("api_compatible")
                    existing_provider.config_schema = provider_info.get("config_schema")
                    existing_provider.provider_type = provider_info.get("provider_type")
                    existing_provider.updated_at = datetime.now(timezone.utc)
                    
                    provider_id = existing_provider.id
                    print(f"更新提供商 {existing_provider.name} - {existing_provider.provider_type} 成功")
                else:
                    # 创建新提供商
                    provider_create = ProviderCreate(
                        key=provider_key,
                        name=provider_info["name"],
                        description=provider_info["description"],
                        website=provider_info["website"],
                        icon=provider_info["icon"],
                        api_base=provider_info.get("api_base"),
                        api_compatible=provider_info.get("api_compatible"),
                        config_schema=provider_info.get("config_schema"),
                        provider_type=provider_info.get("provider_type"),
                        is_active=True
                    )
                    
                    new_provider = Provider(
                        key=provider_create.key,
                        name=provider_create.name,
                        description=provider_create.description,
                        website=provider_create.website,
                        icon=provider_create.icon,
                        api_base=provider_create.api_base,
                        api_compatible=provider_create.api_compatible,
                        config_schema=provider_create.config_schema,
                        provider_type=provider_create.provider_type,
                        is_active=provider_create.is_active,
                        created_at=datetime.now(timezone.utc)
                    )
                    
                    db.add(new_provider)
                    db.flush()
                    provider_id = new_provider.id
                    logger.info(f"创建新提供商 {provider_key} 成功")
                results["success"] += 1
                
            except Exception as e:
                logger.exception(f"初始化提供商 {provider_key} 失败")
                results["details"][provider_key] = {
                    "success": False,
                    "error": str(e)
                }
                results["failures"] += 1
        
        # 提交事务
        db.commit()
        return results
    
    def fetch_models_from_custom_provider(
        self, 
        api_base: str, 
        config_schema: Dict[str, Any], 
        credentials: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        从自定义OpenAI兼容提供商获取模型列表
        
        Args:
            api_base: API基础URL
            config_schema: 配置架构
            credentials: 认证凭证
            
        Returns:
            模型列表
        """
        try:
            # 构建模型列表API URL，优先使用配置中的路径
            model_path = credentials.get("model_path", config_schema.get("properties", {}).get("model_path", {}).get("default", "/models"))
            url = f"{api_base.rstrip('/')}{model_path}"
            
            # 构建请求头
            headers = {
                "Content-Type": "application/json"
            }
            
            # 根据认证信息添加授权头
            if "api_key" in credentials and credentials["api_key"]:
                headers["Authorization"] = f"Bearer {credentials['api_key']}"
            elif "bearer_token" in credentials and credentials["bearer_token"]:
                headers["Authorization"] = f"Bearer {credentials['bearer_token']}"
            
            # 添加自定义请求头
            if "custom_headers" in credentials and isinstance(credentials["custom_headers"], dict):
                headers.update(credentials["custom_headers"])
            
            # 发送请求，使用配置的超时时间
            timeout = credentials.get("timeout", 30)
            response = requests.get(url, headers=headers, timeout=timeout)
            response.raise_for_status()
            
            data = response.json()
            
            # 解析模型列表
            models = []
            if "data" in data and isinstance(data["data"], list):
                for model_data in data["data"]:
                    if isinstance(model_data, dict) and "id" in model_data:
                        model_info = {
                            "key": model_data["id"],
                            "name": model_data.get("name", model_data["id"]),
                            "model_type": "llm",  # 默认为语言模型
                            "description": f"自定义提供商模型: {model_data['id']}"
                        }
                        
                        # 添加额外信息
                        if "object" in model_data:
                            model_info["object"] = model_data["object"]
                        if "owned_by" in model_data:
                            model_info["owned_by"] = model_data["owned_by"]
                        if "created" in model_data:
                            model_info["created"] = model_data["created"]
                            
                        models.append(model_info)
            
            logger.info(f"从自定义提供商获取到 {len(models)} 个模型")
            return models
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求自定义提供商API失败: {str(e)}")
            raise Exception(f"无法连接到自定义提供商: {str(e)}")
        except Exception as e:
            logger.error(f"解析自定义提供商响应失败: {str(e)}")
            raise Exception(f"解析自定义提供商响应失败: {str(e)}")
    
    def _initialize_models(
        self, 
        db: Session, 
        provider_id: int, 
        models: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        为指定提供商初始化模型
        
        Args:
            db: 数据库会话
            provider_id: 提供商ID
            models: 模型信息列表
            
        Returns:
            包含初始化结果的字典
        """
        results = {
            "total": len(models),
            "added": 0,
            "updated": 0,
            "failed": 0,
            "models": []
        }
        
        for model_info in models:
            try:
                model_key = model_info["key"]
                
                # 检查模型是否已存在
                existing_model = db.query(Model).filter(
                    Model.key == model_key,
                    Model.provider_id == provider_id
                ).first()
                
                if existing_model:
                    # 更新模型信息
                    existing_model.name = model_info["name"]
                    existing_model.description = model_info.get("description", "")
                    existing_model.model_type = model_info.get("model_type", "chat")
                    existing_model.token_limit = model_info.get("token_limit", 0)
                    existing_model.updated_at = datetime.now(timezone.utc)
                    
                    results["updated"] += 1
                    results["models"].append({
                        "key": model_key,
                        "status": "updated"
                    })
                else:
                    # 创建新模型
                    model_create = ModelCreate(
                        key=model_key,
                        name=model_info["name"],
                        provider_id=provider_id,
                        model_type=model_info.get("model_type", "chat"),
                        description=model_info.get("description", ""),
                        token_limit=model_info.get("token_limit", 0),
                        is_active=True
                    )
                    
                    new_model = Model(
                        key=model_create.key,
                        name=model_create.name,
                        provider_id=model_create.provider_id,
                        model_type=model_create.model_type,
                        description=model_create.description,
                        token_limit=model_create.token_limit,
                        is_active=model_create.is_active,
                        created_at=datetime.now(timezone.utc)
                    )
                    
                    db.add(new_model)
                    results["added"] += 1
                    results["models"].append({
                        "key": model_key,
                        "status": "added"
                    })
            except Exception as e:
                logger.exception(f"初始化模型 {model_info.get('key', '未知')} 失败")
                results["failed"] += 1
                results["models"].append({
                    "key": model_info.get("key", "unknown"),
                    "status": "failed",
                    "error": str(e)
                })
        
        return results
    
    def fetch_models_from_provider(self, provider_key: str, credentials: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从模型提供商API获取支持的模型列表（如果API支持此功能）
        
        Args:
            provider_key: 提供商标识
            credentials: 访问凭证
            
        Returns:
            模型信息列表
        """
        provider_info = self.supported_providers.get(provider_key.lower(), {})
        api_compatible = provider_info.get("api_compatible", "")
        
        try:
            # 根据API兼容性选择获取方法
            if api_compatible == "openai":
                # 使用通用的OpenAI兼容API方法
                api_base = credentials.get("api_base") or provider_info.get("api_base", "https://api.openai.com/v1")
                return self._fetch_openai_compatible_models(credentials, api_base)
            elif api_compatible == "anthropic":
                return self._fetch_anthropic_models(credentials)
            elif api_compatible == "zhipu":
                return self._fetch_zhipu_models(credentials)
            elif api_compatible == "baidu":
                return self._fetch_baidu_models(credentials)
            elif api_compatible == "ollama":
                return self._fetch_ollama_models(credentials)
            elif api_compatible == "custom_openai":
                return self._fetch_openai_compatible_models(credentials)
            else:
                # 不支持API获取的提供商，返回预定义列表
                logger.info(f"提供商 {provider_key} 不支持从API获取模型列表，使用预定义列表")
                return provider_info.get("models", [])
        except Exception as e:
            logger.exception(f"从提供商 {provider_key} 获取模型列表失败: {str(e)}")
            # 发生错误时，返回预定义的模型列表
            return provider_info.get("models", [])
    
    def _fetch_openai_compatible_models(self, credentials: Dict[str, Any], api_base: str) -> List[Dict[str, Any]]:
        """
        从使用OpenAI兼容API的提供商获取模型列表
        
        Args:
            credentials: 访问凭证
            api_base: API基础URL
            
        Returns:
            模型信息列表
        """
        api_key = credentials.get("api_key")
        
        if not api_key:
            raise ValueError("API密钥未提供")
        
        # 去除末尾的斜杠
        if api_base.endswith("/"):
            api_base = api_base[:-1]
            
        # 发送请求获取模型列表
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{api_base}/models", headers=headers)
        response.raise_for_status()
        
        # 解析响应
        model_data = response.json().get("data", [])
        models = []
        print(json.dumps(model_data))
        
        for model in model_data:
            model_id = model.get("id", "")
            object_type = model.get("object", "")
            created = model.get("created", "")
            owned_by = model.get("owned_by", "")
            

            model_info = {
                "key": model_id,
                "name": model_id if not owned_by else f"{owned_by} {model_id}",
                "model_type": object_type,
                "description": owned_by or "",
                "created": created,
            }
            
            models.append(model_info)        
        return models
    
    def _fetch_anthropic_models(self, credentials: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从Anthropic API获取模型列表"""
        api_key = credentials.get("api_key")
        
        if not api_key:
            raise ValueError("Anthropic API密钥未提供")
        
        # Anthropic目前没有直接获取模型列表的API
        # 返回固定的Claude模型列表
        claude_models = [
            {
                "key": "claude-3-opus-20240229",
                "name": "Claude 3 Opus",
                "model_type": "chat",
                "description": "Anthropic的Claude 3 Opus模型，功能最强大",
                "token_limit": 200000
            },
            {
                "key": "claude-3-sonnet-20240229",
                "name": "Claude 3 Sonnet",
                "model_type": "chat",
                "description": "Anthropic的Claude 3 Sonnet模型，平衡性能和成本",
                "token_limit": 200000
            },
            {
                "key": "claude-3-haiku-20240307",
                "name": "Claude 3 Haiku",
                "model_type": "chat",
                "description": "Anthropic的Claude 3 Haiku模型，最快速的Claude",
                "token_limit": 200000
            }
        ]
        
        return claude_models
    
    def _fetch_zhipu_models(self, credentials: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从智谱API获取模型列表"""
        api_key = credentials.get("api_key")
        
        if not api_key:
            raise ValueError("智谱API密钥未提供")
        
        # 智谱目前没有直接获取模型列表的API
        # 返回固定的GLM模型列表
        models = [
            {
                "key": "glm-4",
                "name": "GLM-4",
                "model_type": "chat",
                "description": "智谱AI的GLM-4模型",
                "token_limit": 128000
            },
            {
                "key": "glm-3-turbo",
                "name": "GLM-3-Turbo",
                "model_type": "chat",
                "description": "智谱AI的GLM-3-Turbo模型",
                "token_limit": 128000
            },
            {
                "key": "embedding-2",
                "name": "Embedding-2",
                "model_type": "embedding",
                "description": "智谱AI的文本嵌入模型",
                "token_limit": 8192
            }
        ]
        
        return models
    
    def _fetch_baidu_models(self, credentials: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从百度文心获取模型列表"""
        api_key = credentials.get("api_key")
        secret_key = credentials.get("secret_key")
        
        if not api_key or not secret_key:
            raise ValueError("百度文心API密钥未提供")
        
        # 百度目前没有直接获取模型列表的API
        # 返回固定的文心一言模型列表
        models = [
            {
                "key": "ernie-bot-4",
                "name": "文心一言4.0",
                "model_type": "chat",
                "description": "百度文心一言4.0大模型",
                "token_limit": 4096
            },
            {
                "key": "ernie-bot",
                "name": "文心一言",
                "model_type": "chat",
                "description": "百度文心一言大模型",
                "token_limit": 3072
            },
            {
                "key": "ernie-bot-turbo",
                "name": "文心一言Turbo",
                "model_type": "chat",
                "description": "百度文心一言轻量版大模型",
                "token_limit": 2048
            }
        ]
        
        return models
        
    def _fetch_ollama_models(self, credentials: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从Ollama获取模型列表"""
        api_base = credentials.get("api_base", "http://localhost:11434/api")
        
        # 去除末尾的斜杠
        if api_base.endswith("/"):
            api_base = api_base[:-1]
            
        try:
            # 发送请求获取模型列表
            response = requests.get(f"{api_base}/tags")
            response.raise_for_status()
            
            # 解析响应
            model_data = response.json().get("models", [])
            models = []
            
            for model in model_data:
                model_name = model.get("name", "")
                
                model_info = {
                    "key": model_name,
                    "name": f"{model_name} (Ollama)",
                    "model_type": "chat",
                    "description": f"本地运行的{model_name}模型",
                    "token_limit": 4096,  # 默认值
                }
                
                models.append(model_info)
                
            return models
        except Exception as e:
            logger.exception(f"从Ollama获取模型列表失败: {str(e)}")
            # 发生错误时，返回预定义的模型列表
            return self.supported_providers.get("ollama", {}).get("models", [])
    
    def _fetch_huggingface_models(self, credentials: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从Hugging Face获取模型列表"""
        api_key = credentials.get("api_key")
        
        if not api_key:
            raise ValueError("Hugging Face API密钥未提供")
        
        # Hugging Face没有标准的模型列表API，返回预定义列表
        return self.supported_providers.get("huggingface", {}).get("models", [])

# 创建全局单例实例
model_provider_initializer = ModelProviderInitializer() 
