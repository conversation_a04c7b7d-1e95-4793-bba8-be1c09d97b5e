"""
日志配置模块
"""

import logging
import os
from typing import Dict, Any, Optional

# 默认日志格式
DEFAULT_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 日志级别映射
LOG_LEVELS = {
    "debug": logging.DEBUG,
    "info": logging.INFO,
    "warning": logging.WARNING,
    "error": logging.ERROR,
    "critical": logging.CRITICAL
}

class LoggingConfig:
    """全局日志配置管理类"""
    
    def __init__(self):
        self.default_level = logging.INFO
        self.format = DEFAULT_FORMAT
        self.module_levels: Dict[str, int] = {}
    
    def configure_logging(self, 
                         default_level: Optional[str] = None, 
                         format_str: Optional[str] = None,
                         module_levels: Optional[Dict[str, str]] = None) -> None:
        """
        配置全局日志设置
        
        Args:
            default_level: 默认日志级别 ("debug", "info", "warning", "error", "critical")
            format_str: 日志格式字符串
            module_levels: 模块特定日志级别的字典 {"module_name": "level"}
        """
        # 设置默认日志级别
        if default_level:
            self.default_level = LOG_LEVELS.get(default_level.lower(), logging.INFO)
        
        # 设置日志格式
        if format_str:
            self.format = format_str
        
        # 配置根日志记录器
        logging.basicConfig(
            level=self.default_level,
            format=self.format
        )
        
        # 配置模块特定的日志级别
        if module_levels:
            self.module_levels = {
                module: LOG_LEVELS.get(level.lower(), logging.INFO)
                for module, level in module_levels.items()
            }
            
            # 应用模块特定的日志级别
            for module, level in self.module_levels.items():
                logging.getLogger(module).setLevel(level)
    
    def configure_from_env(self) -> None:
        """从环境变量加载日志配置"""
        # 获取默认日志级别
        default_level = os.getenv("LOG_LEVEL", "info")
        
        # 获取日志格式
        format_str = os.getenv("LOG_FORMAT", DEFAULT_FORMAT)
        
        # 获取模块特定的日志级别
        module_levels = {}
        
        # 查找所有LOG_LEVEL_前缀的环境变量
        for key, value in os.environ.items():
            if key.startswith("LOG_LEVEL_") and key != "LOG_LEVEL":
                module_name = key[10:].lower().replace("_", ".")
                module_levels[module_name] = value
        
        # 配置日志
        self.configure_logging(default_level, format_str, module_levels)
    
    def set_module_level(self, module_name: str, level: str) -> None:
        """
        设置特定模块的日志级别
        
        Args:
            module_name: 模块名称
            level: 日志级别 ("debug", "info", "warning", "error", "critical")
        """
        if level.lower() in LOG_LEVELS:
            level_value = LOG_LEVELS[level.lower()]
            self.module_levels[module_name] = level_value
            logging.getLogger(module_name).setLevel(level_value)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取配置好的logger
        
        Args:
            name: 日志记录器名称
            
        Returns:
            logging.Logger: 配置好的日志记录器
        """
        logger = logging.getLogger(name)
        
        # 如果该模块有特定的日志级别设置
        if name in self.module_levels:
            logger.setLevel(self.module_levels[name])
        
        return logger

# 创建全局日志配置实例
logging_config = LoggingConfig() 