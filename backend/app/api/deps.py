from typing import Generator, Optional

from fastapi import Depends, HTTPException, status, Path
from fastapi.security import OAuth2PasswordBearer
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.base import User, Tenant, UserTenantAssociation
from app.core.security import decode_token
from app.core.roles import TenantRole, PlatformRole, is_tenant_admin_role, get_role_permission
from app.schemas.auth import TokenData

# 指定令牌URL，与登录路由一致
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")

async def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    """
    获取当前用户
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的身份验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 解码令牌
        token_data = decode_token(token)
        if token_data is None:
            raise credentials_exception
        
        user_id = token_data.user_id
        if user_id is None:
            raise credentials_exception
            
    except (jwt.JWTError, ValidationError):
        raise credentials_exception
        
    # 查询用户
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise credentials_exception
        
    return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前活跃用户
    """
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="账户已禁用")
    return current_user

async def get_current_tenant(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> Optional[Tenant]:
    """
    获取当前租户
    """
    token_data = decode_token(token)
    
    if token_data and token_data.tenant_id:
        tenant = db.query(Tenant).filter(Tenant.id == token_data.tenant_id).first()
        return tenant
    return None

async def get_current_user_tenant_association(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
) -> UserTenantAssociation:
    """
    获取当前用户在当前租户中的关联记录
    """
    if not current_tenant:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="未指定当前租户"
        )
        
    association = db.query(UserTenantAssociation).filter(
        UserTenantAssociation.user_id == current_user.id,
        UserTenantAssociation.tenant_id == current_tenant.id
    ).first()
    
    if not association:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户不属于当前租户"
        )
        
    return association

async def check_tenant_permission(
    association: UserTenantAssociation = Depends(get_current_user_tenant_association),
    permission: str = None
) -> UserTenantAssociation:
    """
    检查用户在租户中的权限
    """
    # 获取用户角色
    role_key = association.role_key
    
    # 如果是拥有者角色，拥有所有权限
    if role_key == TenantRole.OWNER:
        return association
    
    # 检查角色权限
    if permission and not get_role_permission(role_key, permission):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
        
    return association

async def check_tenant_permission_by_id(
    user_id: str,
    tenant_id: str,
    db: Session
) -> UserTenantAssociation:
    """
    根据用户ID和租户ID检查用户是否有权限访问该租户
    """
    # 检查用户是否存在于该租户
    association = db.query(UserTenantAssociation).filter(
        UserTenantAssociation.user_id == user_id,
        UserTenantAssociation.tenant_id == tenant_id
    ).first()
    
    if not association:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户不属于该租户"
        )
    
    return association

async def get_tenant_admin(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: str = None
) -> User:
    """
    检查用户是否为指定租户的管理员
    如果是租户拥有者或管理员角色，则允许访问
    """
    # 首先检查用户是否存在于该租户
    association = db.query(UserTenantAssociation).filter(
        UserTenantAssociation.user_id == current_user.id,
        UserTenantAssociation.tenant_id == tenant_id
    ).first()
    
    if not association:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户不属于该租户"
        )
    
    # 获取用户角色并检查是否为管理员
    if not is_tenant_admin_role(association.role_key):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    return current_user

def get_tenant_admin_for_path():
    """
    创建一个依赖函数，用于从路径参数tenant_id中获取tenant_id并验证用户权限
    """
    async def _get_tenant_admin_for_path(
        tenant_id: str = Path(..., description="租户ID"),
        db: Session = Depends(get_db),
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        return await get_tenant_admin(db=db, current_user=current_user, tenant_id=tenant_id)
    
    return _get_tenant_admin_for_path 

async def is_super_admin(user: User) -> bool:
    """
    判断用户是否为超级管理员
    超级管理员通过用户的extra字段标记
    """
    if user.extra and isinstance(user.extra, dict) and user.extra.get("platform_role") == PlatformRole.SUPER_ADMIN:
        return True
    return False

async def get_super_admin(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    检查用户是否为超级管理员
    超级管理员拥有对系统的最高权限，可以管理所有租户和用户
    """
    if await is_super_admin(current_user):
        return current_user
    
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="需要超级管理员权限"
    ) 