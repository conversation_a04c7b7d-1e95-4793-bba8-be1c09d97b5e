
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from langgraph.prebuilt import create_react_agent
from app.db.session import get_db
from app.api.deps import get_current_user, get_current_active_user, get_tenant_admin, get_tenant_admin_for_path,check_tenant_permission_by_id
from app.services.tool import (
    ToolService, TenantToolService, AgentToolService, 
    MCPServerService, ToolExecutionLogService
)
from app.core.agent_service.agent_tool import AgentToolService
from app.schemas.tool import (
    Tool, ToolCreate, ToolUpdate, ToolListResponse, ToolDetail,
    TenantTool, TenantToolCreate, TenantToolUpdate, TenantToolListResponse,
    AgentTool, AgentToolCreate, AgentToolUpdate, AgentToolListResponse,
    MCPServer, MCPServerCreate, MCPServerUpdate, MCPServerListResponse,
    ToolExecutionLog, ToolExecutionLogListResponse
)
from app.models.base import User, Tenant
from langchain.chat_models import init_chat_model

router = APIRouter()

# ============= 工具管理 =============
# @router.post("/tenants/{tenant_id}/tools", response_model=Tool, tags=["工具管理"])
# def create_tool(
#     tool_create: ToolCreate,
#     tenant_id: str = Path(..., description="租户ID"),
#     db: Session = Depends(get_db),
#     current_user: User = Depends(get_tenant_admin_for_path())
# ):
#     """创建工具"""
#     try:
#         tool = ToolService.create_tool(db, tool_create, tenant_id)
#         return tool
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))




@router.get("/tools", response_model=ToolListResponse, tags=["工具管理"])
def list_tools(
    skip: int = 0,
    limit: int = 100,
    name: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取工具列表"""
    tools, total = ToolService.list_tools(
        db, skip=skip, limit=limit, name=name, is_active=is_active
    )
    return {
        "total": total,
        "items": tools
    }

@router.get("/tools/{tool_id}", response_model=ToolDetail, tags=["工具管理"])
def get_tool(

    tool_id: str = Path(..., description="工具ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取工具详情"""
    tool = ToolService.get_tool(db, tool_id)
    if not tool:
        raise HTTPException(status_code=404, detail="工具不存在")
    return tool

# @router.put("/tenants/{tenant_id}/tools/{tool_id}", response_model=Tool, tags=["工具管理"])
# def update_tool(
#     tool_update: ToolUpdate,
#     tenant_id: str = Path(..., description="租户ID"),
#     tool_id: str = Path(..., description="工具ID"),
#     db: Session = Depends(get_db),
#     current_user: User = Depends(get_tenant_admin_for_path())
# ):
#     """更新工具信息"""
#     try:
#         tool = ToolService.update_tool(db, tool_id, tool_update, tenant_id)
#         if not tool:
#             raise HTTPException(status_code=404, detail="工具不存在")
#         return tool
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))

# @router.delete("/tenants/{tenant_id}/tools/{tool_id}", tags=["工具管理"])
# def delete_tool(
#     tenant_id: str = Path(..., description="租户ID"),
#     tool_id: str = Path(..., description="工具ID"),
#     db: Session = Depends(get_db),
#     current_user: User = Depends(get_tenant_admin_for_path())
# ):
#     """删除工具"""
#     try:
#         success = ToolService.delete_tool(db, tool_id, tenant_id)
#         if not success:
#             raise HTTPException(status_code=404, detail="工具不存在")
#         return {"message": "工具已删除"}
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))

# ============= 租户工具管理 =============
@router.post("/tenants/{tenant_id}/tools", response_model=TenantTool, tags=["租户工具管理"])
async def create_tenant_tool(
    tenant_tool_create: TenantToolCreate,
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path())
):
    """为租户配置工具"""
    # 确认路径参数与请求体一致
    if tenant_id != tenant_tool_create.tenant_id:
        raise HTTPException(status_code=400, detail="租户ID不匹配")
    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    try:
        tenant_tool = TenantToolService.create_tenant_tool(db, tenant_tool_create)
        return tenant_tool
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tenants/{tenant_id}/tools", response_model=TenantToolListResponse, tags=["租户工具管理"])
async def list_tenant_tools(
    tenant_id: str = Path(..., description="租户ID"),
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    
    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    """获取租户工具配置列表"""
    tenant_tools, total = TenantToolService.list_tenant_tools(
        db, tenant_id=tenant_id, skip=skip, limit=limit, is_active=is_active
    )
    return {
        "total": total,
        "items": tenant_tools
    }

@router.get("/tenants/{tenant_id}/tools/{tool_id}", response_model=TenantTool, tags=["租户工具管理"])
async def get_tenant_tool(
    tenant_id: str = Path(..., description="租户ID"),
    tool_id: str = Path(..., description="工具ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):

    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    """获取租户工具配置详情"""
    tenant_tool = TenantToolService.get_tenant_tool(db, tenant_id, tool_id)
    if not tenant_tool:
        raise HTTPException(status_code=404, detail="租户工具配置不存在")
    return tenant_tool

@router.put("/tenants/{tenant_id}/tools/{tool_id}", response_model=TenantTool, tags=["租户工具管理"])
async def update_tenant_tool(
    tenant_tool_update: TenantToolUpdate,
    tenant_id: str = Path(..., description="租户ID"),
    tool_id: str = Path(..., description="工具ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path())
):
    """更新租户工具配置"""
    
    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    try:
        tenant_tool = TenantToolService.update_tenant_tool(db, tenant_id, tool_id, tenant_tool_update)
        if not tenant_tool:
            raise HTTPException(status_code=404, detail="租户工具配置不存在")
        return tenant_tool
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/tenants/{tenant_id}/tools/{tool_id}", tags=["租户工具管理"])
async def delete_tenant_tool(
    tenant_id: str = Path(..., description="租户ID"),
    tool_id: str = Path(..., description="工具ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path())
):
    """删除租户工具配置"""
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    try:
        success = TenantToolService.delete_tenant_tool(db, tenant_id, tool_id)
        if not success:
            raise HTTPException(status_code=404, detail="租户工具配置不存在")
        return {"message": "租户工具配置已删除"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tenants/{tenant_id}/tools-available", response_model=List[TenantTool], tags=["租户工具管理"])
async def list_tenant_available_tools(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
): 
    """获取租户可用工具列表"""
    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    return TenantToolService.list_tenant_available_tools(db, tenant_id)


# # ============= Agent工具管理 =============
# @router.post("/tenants/{tenant_id}/agents/{agent_id}/tools", response_model=AgentTool, tags=["Agent工具管理"])
# def create_agent_tool(
#     agent_tool_create: AgentToolCreate,
#     tenant_id: str = Path(..., description="租户ID"),
#     agent_id: str = Path(..., description="Agent ID"),
#     db: Session = Depends(get_db),
#     current_user: User = Depends(get_current_active_user)
# ):
#     """为Agent配置工具"""
#     # 确认路径参数与请求体一致
#     if agent_id != agent_tool_create.agent_id:
#         raise HTTPException(status_code=400, detail="Agent ID不匹配")
    
#     # 确保租户ID正确
#     if hasattr(agent_tool_create, "tenant_id"):
#         agent_tool_create.tenant_id = tenant_id
    
#     try:
#         agent_tool = AgentToolService.create_agent_tool(db, agent_tool_create, tenant_id)
#         return agent_tool
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))

# @router.get("/tenants/{tenant_id}/agents/{agent_id}/tools", response_model=AgentToolListResponse, tags=["Agent工具管理"])
# def list_agent_tools(
#     tenant_id: str = Path(..., description="租户ID"),
#     agent_id: str = Path(..., description="Agent ID"),
#     skip: int = 0,
#     limit: int = 100,
#     is_active: Optional[bool] = None,
#     db: Session = Depends(get_db),
#     current_user: User = Depends(get_current_active_user)
# ):
#     """获取Agent工具配置列表"""
#     agent_tools, total = AgentToolService.list_agent_tools(
#         db, agent_id=agent_id, tenant_id=tenant_id, skip=skip, limit=limit, is_active=is_active
#     )
#     return {
#         "total": total,
#         "items": agent_tools
#     }

# @router.get("/tenants/{tenant_id}/agents/{agent_id}/tools/{tool_id}", response_model=AgentTool, tags=["Agent工具管理"])
# def get_agent_tool(
#     tenant_id: str = Path(..., description="租户ID"),
#     agent_id: str = Path(..., description="Agent ID"),
#     tool_id: str = Path(..., description="工具ID"),
#     db: Session = Depends(get_db),
#     current_user: User = Depends(get_current_active_user)
# ):
#     """获取Agent工具配置详情"""
#     agent_tool = AgentToolService.get_agent_tool(db, agent_id, tool_id, tenant_id)
#     if not agent_tool:
#         raise HTTPException(status_code=404, detail="Agent工具配置不存在")
#     return agent_tool

# @router.put("/tenants/{tenant_id}/agents/{agent_id}/tools/{tool_id}", response_model=AgentTool, tags=["Agent工具管理"])
# def update_agent_tool(
#     agent_tool_update: AgentToolUpdate,
#     tenant_id: str = Path(..., description="租户ID"),
#     agent_id: str = Path(..., description="Agent ID"),
#     tool_id: str = Path(..., description="工具ID"),
#     db: Session = Depends(get_db),
#     current_user: User = Depends(get_current_active_user)
# ):
#     """更新Agent工具配置"""
#     try:
#         agent_tool = AgentToolService.update_agent_tool(db, agent_id, tool_id, agent_tool_update, tenant_id)
#         if not agent_tool:
#             raise HTTPException(status_code=404, detail="Agent工具配置不存在")
#         return agent_tool
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))

# @router.delete("/tenants/{tenant_id}/agents/{agent_id}/tools/{tool_id}", tags=["Agent工具管理"])
# def delete_agent_tool(
#     tenant_id: str = Path(..., description="租户ID"),
#     agent_id: str = Path(..., description="Agent ID"),
#     tool_id: str = Path(..., description="工具ID"),
#     db: Session = Depends(get_db),
#     current_user: User = Depends(get_current_active_user)
# ):
#     """删除Agent工具配置"""
#     try:
#         success = AgentToolService.delete_agent_tool(db, agent_id, tool_id, tenant_id)
#         if not success:
#             raise HTTPException(status_code=404, detail="Agent工具配置不存在")
#         return {"message": "Agent工具配置已删除"}
#     except ValueError as e:
#         raise HTTPException(status_code=400, detail=str(e))

# ============= MCP服务器管理 =============
@router.post("/tenants/{tenant_id}/mcp-servers", response_model=MCPServer, tags=["MCP服务器管理"])
async def create_mcp_server(
    server_create: MCPServerCreate,
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path())
):
    """创建MCP服务器"""
    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    try:
        server = await MCPServerService.create_mcp_server(db, server_create, tenant_id)  # 恢复 await
        return server
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tenants/{tenant_id}/mcp-servers", response_model=MCPServerListResponse, tags=["MCP服务器管理"])
async def list_mcp_servers(
    tenant_id: str = Path(..., description="租户ID"),
    skip: int = 0,
    limit: int = 100,
    name: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取MCP服务器列表"""
    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    servers, total = MCPServerService.list_mcp_servers(
        db, tenant_id=tenant_id, skip=skip, limit=limit, name=name, status=status
    )
    return {
        "total": total,
        "items": servers
    }

@router.get("/tenants/{tenant_id}/mcp-servers/{server_id}", response_model=MCPServer, tags=["MCP服务器管理"])
async def get_mcp_server(
    tenant_id: str = Path(..., description="租户ID"),
    server_id: str = Path(..., description="服务器ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取MCP服务器详情"""
    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    server = MCPServerService.get_mcp_server(db, server_id, tenant_id)
    if not server:
        raise HTTPException(status_code=404, detail="MCP服务器不存在")
    return server

@router.put("/tenants/{tenant_id}/mcp-servers/{server_id}", response_model=MCPServer, tags=["MCP服务器管理"])
async def update_mcp_server(
    server_update: MCPServerUpdate,
    tenant_id: str = Path(..., description="租户ID"),
    server_id: str = Path(..., description="服务器ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path())
):
    """更新MCP服务器信息"""
    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    try:
        server = await MCPServerService.update_mcp_server(db, server_id, server_update, tenant_id)  # 恢复 await
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")
        return server
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/tenants/{tenant_id}/mcp-servers/{server_id}", tags=["MCP服务器管理"])
async def delete_mcp_server(
    tenant_id: str = Path(..., description="租户ID"),
    server_id: str = Path(..., description="服务器ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path())
):
    """删除MCP服务器"""
    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    try:
        success = MCPServerService.delete_mcp_server(db,  tenant_id,server_id)
        if not success:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")
        return {"message": "MCP服务器已删除"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tenants/{tenant_id}/mcp-servers-available", response_model=List[MCPServer], tags=["MCP服务器管理"])
async def list_tenant_available_mcp_servers(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)   
):
    """获取租户可用的MCP服务器列表"""
    # 检查当前用户是否有管理该租户的权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    return MCPServerService.list_tenant_available_mcp_servers(db, tenant_id)

@router.get("/tenants/{tenant_id}/{tool_ids}/test/{server_ids}", tags=["测试获取mcp服务器"])
async def check_mcp_server_is_available(
    tenant_id: str = Path(..., description="租户ID"),
    tool_ids: str = Path(..., description="工具ID"),
    server_ids: str = Path(..., description="服务器ID"),
    content: str = Query(..., description="要执行的内容"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path())
):

     SUCCESS = await AgentToolService.get_agent_tools(db, tenant_id,tool_ids.split(","),server_ids.split(","))
     print(SUCCESS)
     api_key = "sk-ztsuqtfqwmolufqmpzykpvccirlcnjtxszdrktlhhugivuyf"
     base_url = "https://api.siliconflow.cn/v1"

     llm = init_chat_model("Qwen/Qwen3-32B", model_provider="openai", api_key=api_key, base_url=base_url)
     agent = create_react_agent(
        model=llm,
        tools=SUCCESS,
        prompt="You are a helpful assistant that can answer questions and help with tasks. you can use the tools to get the information you need."
        )
     response = await agent.ainvoke({"messages": [{"role": "user", "content": content}]})
    #返回ai消息
    #  print(response["messages"][-1]["content"])
     print(response)

    # wolfram_alpha = WolframAlphaAPIWrapper(wolfram_alpha_appid="T5QV4J-EHPQWVTKXX")
    # s = wolfram_alpha.run("What is 2x+5 = -3x + 7?")
    # print(s)
     return {"message": response["messages"][-1].content}
# # ============= 工具执行记录 =============
# @router.get("/tenants/{tenant_id}/tool-execution-logs", response_model=ToolExecutionLogListResponse, tags=["工具执行记录"])
# def list_tool_execution_logs(
#     tenant_id: str = Path(..., description="租户ID"),
#     session_id: Optional[str] = None,
#     tool_id: Optional[str] = None,
#     agent_tool_id: Optional[str] = None,
#     status: Optional[str] = None,
#     skip: int = 0,
#     limit: int = 100,
#     db: Session = Depends(get_db),
#     current_user: User = Depends(get_current_active_user)
# ):
#     """获取工具执行记录列表"""
#     logs, total = ToolExecutionLogService.list_execution_logs(
#         db, 
#         tenant_id=tenant_id,
#         session_id=session_id,
#         tool_id=tool_id,
#         agent_tool_id=agent_tool_id,
#         status=status,
#         skip=skip, 
#         limit=limit
#     )
#     return {
#         "total": total,
#         "items": logs
#     }