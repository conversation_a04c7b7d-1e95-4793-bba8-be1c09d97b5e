from typing import Any, List
from fastapi import APIRouter, Depends, Path, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.role import Role, RoleWithPermissions, RoleListResponse
from app.core.roles import get_tenant_roles, ROLE_PERMISSIONS, TenantRole
from app.api.deps import get_current_active_user, get_tenant_admin

router = APIRouter()

@router.get("/roles", response_model=List[Role], summary="获取所有可用角色")
async def list_all_roles(
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    获取所有可用的租户角色
    """
    return get_tenant_roles()

@router.get("/tenants/{tenant_id}/roles", response_model=RoleListResponse, summary="获取租户角色列表")
async def list_tenant_roles(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    获取租户下的角色列表（固定角色）
    """
    roles = get_tenant_roles()
    return {
        "total": len(roles),
        "items": roles
    }

@router.get("/tenants/{tenant_id}/roles/{role_key}", response_model=RoleWithPermissions, summary="获取角色详情")
async def get_role(
    tenant_id: str = Path(..., description="租户ID"),
    role_key: str = Path(..., description="角色标识"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
) -> Any:
    """
    获取角色详情和权限
    """
    # 确保角色存在
    if role_key not in TenantRole.__members__.values():
        return {
            "key": role_key,
            "name": role_key,
            "description": "未知角色",
            "permissions": {}
        }
    
    role_info = ROLE_PERMISSIONS.get(role_key, {})
    permissions = {k: v for k, v in role_info.items() if k.startswith("can_")}
    
    return {
        "key": role_key,
        "name": role_info.get("name", role_key),
        "description": role_info.get("description", ""),
        "permissions": permissions
    } 