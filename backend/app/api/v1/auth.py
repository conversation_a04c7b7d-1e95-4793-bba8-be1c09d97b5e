from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, status, Form
from sqlalchemy.orm import Session
from fastapi.security import OAuth2PasswordRequestForm

from app.db.session import get_db
from app.schemas.auth import (
    UserRegister, User<PERSON>ogin, Token, TenantCreate, InviteUserRequest, AcceptInvitationRequest,
    UserRegisterResponse, InvitationResponse, AcceptInvitationResponse, TenantCreateResponse,
    InvitationDetailResponse
)
from app.services.user import UserService
from app.services.tenant import TenantService
from app.api.deps import get_current_active_user

router = APIRouter()

@router.post("/register", response_model=UserRegisterResponse, status_code=status.HTTP_201_CREATED, summary="用户注册")
async def register(
    user_data: UserRegister,
    db: Session = Depends(get_db)
) -> UserRegisterResponse:
    """
    注册新用户并创建个人组织空间
    """
    user_service = UserService(db)
    user, tenant = await user_service.register_user(user_data)
    
    return UserRegisterResponse(
        message="注册成功",
        user={
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "is_verified": user.is_verified if hasattr(user, "is_verified") else False
        },
        personal_tenant={
            "id": tenant["id"],
            "name": tenant["name"]
        }
    )

@router.post("/login", response_model=Token, summary="用户登录")
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
) -> Token:
    """
    用户登录并获取访问令牌
    
    使用OAuth2兼容表单：
    - username: 用户名或邮箱
    - password: 密码
    """
    login_data = UserLogin(
        username=form_data.username,
        password=form_data.password
    )
    
    user_service = UserService(db)
    token = await user_service.login(login_data)
    return token

@router.post("/tenants", response_model=TenantCreateResponse, status_code=status.HTTP_201_CREATED, summary="创建组织")
async def create_tenant(
    tenant_data: TenantCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> TenantCreateResponse:
    """
    创建新组织
    """
    tenant_service = TenantService(db)
    tenant = await tenant_service.create_tenant(tenant_data, current_user.id)
    
    return TenantCreateResponse(
        message="组织创建成功",
        tenant={
            "id": tenant.id,
            "name": tenant.name,
            "description": tenant.description
        }
    )

@router.post("/tenants/{tenant_id}/invite", response_model=InvitationResponse, summary="邀请用户")
async def invite_user(
    tenant_id: str,
    invite_data: InviteUserRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> InvitationResponse:
    """
    邀请用户加入组织
    """
    tenant_service = TenantService(db)
    result = await tenant_service.invite_user(tenant_id, invite_data, current_user.id)
    
    return InvitationResponse(
        message=result["message"],
        invitation=result["invitation"]
    )

@router.get("/invitations/{invitation_token}", response_model=InvitationDetailResponse, summary="获取邀请详情")
async def get_invitation_details(
    invitation_token: str,
    db: Session = Depends(get_db)
) -> InvitationDetailResponse:
    """
    获取邀请详情（无需认证）
    """
    tenant_service = TenantService(db)
    result = await tenant_service.get_invitation_details(invitation_token)
    
    return InvitationDetailResponse(
        invitation=result["invitation"],
        tenant_name=result["tenant_name"],
        tenant_description=result["tenant_description"],
        inviter_name=result["inviter_name"],
        is_expired=result["is_expired"]
    )

@router.post("/accept-invitation", response_model=AcceptInvitationResponse, summary="接受邀请")
async def accept_invitation(
    accept_data: AcceptInvitationRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> AcceptInvitationResponse:
    """
    接受组织邀请
    """
    tenant_service = TenantService(db)
    result = await tenant_service.accept_invitation(accept_data, current_user.id)
    
    return AcceptInvitationResponse(
        success=result["success"],
        message=result["message"],
        tenant_id=result["tenant_id"]
    ) 