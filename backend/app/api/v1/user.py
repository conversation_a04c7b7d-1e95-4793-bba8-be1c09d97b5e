from typing import Any, Dict, List
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.user import (
    User, UserCreate, UserUpdate, UserDetail, 
    PasswordReset, UserTenantAssociationCreate, UserTenantAssociationUpdate,
    UserListResponse, OperationResponse, UserTenantAssociationResponse,
    UserWithRole, UserWithRoleListResponse
)
from app.services.user import UserService
from app.api.deps import get_current_active_user, check_tenant_permission, get_tenant_admin, is_super_admin, check_tenant_permission_by_id
from app.core.roles import TenantRole

router = APIRouter()


@router.get("/tenants/{tenant_id}/users", response_model=UserWithRoleListResponse, summary="获取租户下的用户列表")
async def list_tenant_users(
    tenant_id: str = Path(..., description="租户ID"),
    skip: int = Query(0, description="跳过记录数"),
    limit: int = Query(100, description="返回记录数"),
    username: str = Query(None, description="按用户名过滤"),
    is_active: bool = Query(None, description="按状态过滤"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
) -> Any:
    """
    获取租户下的用户列表，包含每个用户的角色信息
    """
    user_service = UserService(db)
    return await user_service.get_tenant_users(
        tenant_id=tenant_id,
        skip=skip, 
        limit=limit, 
        username=username,
        is_active=is_active
    )

@router.get("/tenants/{tenant_id}/users/{user_id}", response_model=UserWithRole, summary="获取租户中的用户详情")
async def get_tenant_user(
    tenant_id: str = Path(..., description="租户ID"),
    user_id: str = Path(..., description="用户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    获取租户中用户的详情，包含角色信息
    """
    # 检查是否有权限查看该租户中的用户
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    user_service = UserService(db)
    return await user_service.get_tenant_user(tenant_id, user_id)

@router.get("/users/me", response_model=User, summary="获取当前用户信息")
async def get_current_user_info(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    获取当前登录用户的信息
    """
    user_service = UserService(db)
    return await user_service.get_user(current_user.id)

@router.get("/users/me/detail", response_model=UserDetail, summary="获取当前用户详细信息")
async def get_current_user_detail(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    获取当前登录用户的详细信息，包括所属租户和角色
    """
    user_service = UserService(db)
    return await user_service.get_user_detail(current_user.id)

@router.put("/users/me", response_model=User, summary="更新当前用户信息")
async def update_current_user(
    user_data: UserUpdate = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    更新当前登录用户的信息
    """
    user_service = UserService(db)
    return await user_service.update_user(current_user.id, user_data)

@router.post("/users/me/change-password", response_model=OperationResponse, summary="修改当前用户密码")
async def change_current_user_password(
    password_data: PasswordReset = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    修改当前登录用户的密码，需要提供原密码进行验证
    """
    user_service = UserService(db)
    return await user_service.reset_password(current_user.id, password_data)

@router.post("/tenants/{tenant_id}/users", response_model=UserTenantAssociationResponse, summary="添加用户到租户")
async def add_user_to_tenant(
    tenant_id: str = Path(..., description="租户ID"),
    user_data: UserTenantAssociationCreate = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
) -> Any:
    """
    将用户添加到租户
    """
    # 检查角色是否有效
    if user_data.role_key not in TenantRole.__members__.values():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的角色标识: {user_data.role_key}"
        )
    
    # 不允许普通管理员添加拥有者角色
    association = await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    if association.role_key != TenantRole.OWNER and user_data.role_key == TenantRole.OWNER:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有租户拥有者可以添加拥有者角色"
        )
    
    user_service = UserService(db)
    return await user_service.add_user_to_tenant(user_data)

@router.put("/tenants/{tenant_id}/users/{user_id}", response_model=UserTenantAssociationResponse, summary="更新用户角色")
async def update_user_role(
    tenant_id: str = Path(..., description="租户ID"),
    user_id: str = Path(..., description="用户ID"),
    role_data: UserTenantAssociationUpdate = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
) -> Any:
    """
    更新用户在租户中的角色
    """
    # 检查角色是否有效
    if role_data.role_key not in TenantRole.__members__.values():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的角色标识: {role_data.role_key}"
        )
    
    # 不允许管理员修改拥有者，不允许修改自己的角色
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能修改自己的角色"
        )
    
    # 检查目标用户当前角色
    target_association = await check_tenant_permission_by_id(user_id, tenant_id, db)
    
    # 检查当前用户角色
    current_association = await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    # 只有拥有者可以修改拥有者角色
    if (target_association.role_key == TenantRole.OWNER or role_data.role_key == TenantRole.OWNER) and current_association.role_key != TenantRole.OWNER:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有租户拥有者可以修改拥有者角色"
        )
    
    user_service = UserService(db)
    return await user_service.update_user_role(user_id, tenant_id, role_data)

@router.delete("/tenants/{tenant_id}/users/{user_id}", response_model=OperationResponse, summary="将用户从租户中移除")
async def remove_user_from_tenant(
    tenant_id: str = Path(..., description="租户ID"),
    user_id: str = Path(..., description="用户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
) -> Any:
    """
    将用户从租户中移除
    """    
    # 不能移除自己
    if current_user.id == user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能将自己从租户中移除"
        )
    
    # 检查目标用户当前角色
    target_association = await check_tenant_permission_by_id(user_id, tenant_id, db)
    
    # 检查当前用户角色
    current_association = await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    # 只有拥有者可以移除拥有者
    if target_association.role_key == TenantRole.OWNER and current_association.role_key != TenantRole.OWNER:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有租户拥有者可以移除拥有者"
        )
        
    user_service = UserService(db)
    return await user_service.remove_user_from_tenant(user_id, tenant_id)

