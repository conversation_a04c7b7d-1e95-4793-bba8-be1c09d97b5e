from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, UploadFile, File
from sqlalchemy.orm import Session

from app.schemas.agent import (
    Agent, AgentListResponse, AgentUpdate, AgentDetail,
    AgentIconUploadResponse, AgentIconDeleteResponse,
    AgentDraftConfig, AgentPublishRequest, 
    AgentRollbackRequest, AgentConfigValidation, AgentCreateResponse,
    AgentPublishResponse, AgentRollbackResponse,
    AgentTemplatesResponse, AgentHistoryResponse,
    AgentDraftUpdateRequest, AgentDraftUpdateResponse, AgentConfigWizardResponse,
    AgentCreateRequest, AgentBasicInfoUpdate, AvailableResources
)
from app.services.agent import AgentService
from app.db.session import get_db
from app.api.deps import get_current_active_user, get_tenant_admin, check_tenant_permission_by_id

router = APIRouter()


# 1. 获取Agent创建模板
@router.get("/tenants/{tenant_id}/agents/templates", response_model=AgentTemplatesResponse)
async def get_agent_templates(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    获取Agent创建模板
    
    返回可用的Agent模板列表，包含预设配置和向导步骤
    """
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    service = AgentService(db)
    return await service.get_agent_templates(tenant_id)

# 获取可用资源API
@router.get("/tenants/{tenant_id}/agents/available-knowledge-bases")
async def get_available_knowledge_bases(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    获取租户下可用的知识库列表
    
    用于Agent创建和编辑时选择关联的知识库
    """
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    from app.models.knowledge import KnowledgeBase
    knowledge_bases = db.query(KnowledgeBase).filter(
        KnowledgeBase.tenant_id == tenant_id,
        KnowledgeBase.is_active == True
    ).all()
    
    return [
        {
            "id": kb.id,
            "name": kb.name,
            "description": kb.description,
            "created_at": kb.created_at,
            "updated_at": kb.updated_at
        }
        for kb in knowledge_bases
    ]

@router.get("/tenants/{tenant_id}/agents/available-tools")
async def get_available_tools(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    获取租户下可用的工具列表
    
    用于Agent创建和编辑时选择关联的工具
    """
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    from app.models.tool import TenantTool, Tool
    tenant_tools = db.query(TenantTool).join(Tool).filter(
        TenantTool.tenant_id == tenant_id,
        TenantTool.is_active == True,
        Tool.is_active == True
    ).all()
    
    return [
        {
            "id": tt.tool.id,
            "tenant_tool_id": tt.id,
            "name": tt.tool.name,
            "key": tt.tool.key,
            "description": tt.tool.description,
            "config_schema": tt.tool.config_schema,
            "tenant_config": tt.config,
            "created_at": tt.created_at
        }
        for tt in tenant_tools
    ]

@router.get("/tenants/{tenant_id}/agents/available-mcp-servers")
async def get_available_mcp_servers(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    获取租户下可用的MCP服务器列表
    
    用于Agent创建和编辑时选择关联的MCP服务器
    """
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    from app.models.tool import MCPServer
    mcp_servers = db.query(MCPServer).filter(
        MCPServer.tenant_id == tenant_id,
        MCPServer.status == "active"
    ).all()
    
    return [
        {
            "id": server.id,
            "name": server.name,
            "endpoint": server.endpoint,
            "transport_type": server.transport_type,
            "status": server.status,
            "last_heartbeat": server.last_heartbeat,
            "created_at": server.created_at,
            "updated_at": server.updated_at
        }
        for server in mcp_servers
    ]

# Agent 基础API
@router.get("/tenants/{tenant_id}/agents", response_model=AgentListResponse)
async def get_agents(
    tenant_id: str = Path(..., description="租户ID"),
    skip: int = Query(0, description="跳过记录数"),
    limit: int = Query(100, description="返回记录数"),
    name: Optional[str] = Query(None, description="按名称过滤"),
    agent_type: Optional[str] = Query(None, description="按类型过滤"),
    is_active: Optional[bool] = Query(None, description="按状态过滤"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """获取Agent列表"""
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    service = AgentService(db)
    return await service.get_agents(tenant_id, skip, limit, name, agent_type, is_active)


@router.post("/tenants/{tenant_id}/agents", response_model=AgentCreateResponse)
async def create_agent(
    agent_data: AgentCreateRequest,
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
):
    """
    创建Agent - 草稿模式
    
    **创建方式：**
    
    1. **基于模板创建（推荐）**：
       - 指定 `template_id`，系统会基于模板的默认配置创建Agent草稿
       
    2. **自定义创建**：
       - 不指定 `template_id`，自定义所有配置
       - 必须指定 `agent_type`（supervisor、general）
    
    **创建流程：**
    - 所有Agent创建后都处于草稿状态
    - 可以立即修改基本信息（name、description等）
    - 配置信息需要通过草稿接口完善后发布
    
    **后续操作：**
    1. 修改基本信息：`PUT /agents/{agent_id}/basic-info`（立即生效）
    2. 完善配置：`PUT /agents/{agent_id}/draft`（草稿保存）
    3. 验证配置：`POST /agents/{agent_id}/validate`
    4. 发布Agent：`POST /agents/{agent_id}/publish`
    """
    service = AgentService(db)
    return await service.create_agent_unified(tenant_id, agent_data)


@router.get("/tenants/{tenant_id}/agents/{agent_id}", response_model=AgentDetail)
async def get_agent(
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """获取Agent详情，包含版本信息和模型信息"""
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    service = AgentService(db)
    return await service.get_agent_detail(tenant_id, agent_id)


@router.put("/tenants/{tenant_id}/agents/{agent_id}/basic-info", response_model=Agent)
async def update_agent_basic_info(
    agent_update: AgentBasicInfoUpdate,
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
):
    """
    更新Agent基本信息 - 立即生效
    
    **基本信息**（立即更新，无需发布）：
    - `name`：Agent名称
    - `description`：Agent描述
    - `is_active`：是否激活
    
    **注意**：
    - 基本信息修改立即生效，不影响配置版本
    - 配置相关信息请使用 `/draft` 接口
    - 图标管理请使用专门的图标接口
    """
    service = AgentService(db)
    # 转换为 AgentUpdate 格式以兼容现有服务
    update_data = AgentUpdate(
        name=agent_update.name,
        description=agent_update.description,
        is_active=agent_update.is_active
    )
    return await service.update_agent(tenant_id, agent_id, update_data)


@router.put("/tenants/{tenant_id}/agents/{agent_id}", response_model=Agent)
async def update_agent(
    agent_update: AgentUpdate,
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
):
    """
    更新Agent信息 - 兼容性接口
    
    ⚠️ **推荐使用专门的接口**：
    - 基本信息：`PUT /agents/{agent_id}/basic-info`
    - 配置信息：`PUT /agents/{agent_id}/draft`
    
    此接口保留向后兼容性，但建议迁移到专门接口。
    """
    service = AgentService(db)
    return await service.update_agent(tenant_id, agent_id, agent_update)


@router.delete("/tenants/{tenant_id}/agents/{agent_id}")
async def delete_agent(
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
):
    """删除/停用Agent"""
    service = AgentService(db)
    return await service.delete_agent(tenant_id, agent_id)


# Agent图标管理API
@router.post("/tenants/{tenant_id}/agents/{agent_id}/icon", response_model=AgentIconUploadResponse)
async def upload_agent_icon(
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    icon: UploadFile = File(..., description="图标文件"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
):
    """
    上传Agent图标
    
    - **支持格式**: JPEG、PNG、GIF、SVG、WebP
    - **文件大小**: 最大2MB
    - **建议尺寸**: 256x256像素，正方形
    - **存储路径**: `{tenant_id}/agents/{agent_id}/icon.{ext}`
    
    上传成功后会自动删除之前的图标文件（如果存在）
    """
    service = AgentService(db)
    return await service.upload_icon(tenant_id, agent_id, icon)

@router.delete("/tenants/{tenant_id}/agents/{agent_id}/icon", response_model=AgentIconDeleteResponse)
async def delete_agent_icon(
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
):
    """
    删除Agent图标
    
    删除Agent的图标文件和相关元数据
    """
    service = AgentService(db)
    return await service.delete_icon(tenant_id, agent_id)

@router.get("/tenants/{tenant_id}/agents/{agent_id}/icon-url")
async def get_agent_icon_url(
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    获取Agent图标的访问URL
    
    返回可直接访问的图标URL，用于前端展示
    """
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    service = AgentService(db)
    icon_url = service.get_icon_url(tenant_id, agent_id)
    
    if not icon_url:
        raise HTTPException(status_code=404, detail="Agent图标不存在")
    
    return {"icon_url": icon_url}


# 2. 获取Agent草稿配置
@router.get("/tenants/{tenant_id}/agents/{agent_id}/draft", response_model=AgentDraftConfig)
async def get_agent_draft(
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    获取Agent配置信息 - 草稿模式
    
    **配置信息**包括：
    - `model_id`：LLM模型ID
    - `prompt`：提示词配置  
    - `knowledge_base_ids`：知识库关联
    - `tool_ids`：工具关联
    - `mcp_server_ids`：MCP服务器关联
    - `llm_model_config`：LLM模型参数（温度、token数等）
    - `knowledge_base_config`：知识库检索配置
    
    **说明**：
    - 如果没有草稿，会基于当前发布版本创建草稿
    - 配置修改通过草稿->发布流程，确保质量
    - 基本信息（name、description）请使用 `/basic-info` 接口
    - 使用结构化配置字段，后端自动处理JSON存储
    """
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    service = AgentService(db)
    return await service.get_agent_draft(tenant_id, agent_id)


# 3. 更新Agent草稿配置
@router.put("/tenants/{tenant_id}/agents/{agent_id}/draft", response_model=AgentDraftUpdateResponse)
async def update_agent_draft(
    config_data: AgentDraftUpdateRequest,
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
):
    """
    更新Agent配置信息 - 草稿保存
    
    **可更新的配置信息**：
    - `model_id`：LLM模型ID
    - `prompt`：提示词内容
    - `knowledge_base_ids`：关联知识库列表
    - `tool_ids`：关联工具列表
    - `mcp_server_ids`：关联MCP服务器列表
    - `llm_model_config`：LLM模型参数配置
    - `knowledge_base_config`：知识库检索配置
    
    **结构化配置说明**：
    - 使用结构化字段而非原始JSON，提升开发体验
    - 后端自动将配置合并到数据库的config字段
    - 前端无需关心底层JSON结构
    
    **草稿模式特点**：
    - 自动保存，不影响当前运行版本
    - 支持增量更新，只传入需要修改的字段
    - 更新后需要调用 `/publish` 发布才生效
    - 可以随时通过 `/validate` 验证配置完整性
    
    **注意**：
    - 此接口只处理配置信息，不处理基本信息
    - 基本信息（name、description）请使用 `/basic-info` 接口
    """
    service = AgentService(db)
    return await service.update_agent_draft(tenant_id, agent_id, config_data)


# 4. 发布Agent配置
@router.post("/tenants/{tenant_id}/agents/{agent_id}/publish", response_model=AgentPublishResponse)
async def publish_agent_config(
    publish_request: AgentPublishRequest,
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
):
    """
    发布Agent配置 - 草稿生效
    
    **发布流程**：
    1. 验证草稿配置完整性
    2. 创建新的配置版本
    3. 将新版本设为当前生效版本
    4. 基于新版本创建新草稿（供下次修改）
    
    **版本管理**：
    - 自动生成语义化版本号（如 1.0.0 -> 1.1.0）
    - 保留历史版本，支持回滚
    - 发布后立即生效，Agent开始使用新配置
    
    **说明**：
    - 只影响配置信息，不影响基本信息
    - 基本信息修改立即生效，无需发布
    """
    service = AgentService(db)
    return await service.publish_agent_config(tenant_id, agent_id, publish_request)


# 6. 获取版本历史
@router.get("/tenants/{tenant_id}/agents/{agent_id}/history", response_model=AgentHistoryResponse)
async def get_agent_history(
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    获取发布历史
    
    获取所有已发布的版本历史
    """
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    service = AgentService(db)
    versions = await service.get_agent_history(tenant_id, agent_id)
    return {"versions": versions}


# 7. 回滚到指定版本
@router.post("/tenants/{tenant_id}/agents/{agent_id}/rollback/{version_id}", response_model=AgentRollbackResponse)
async def rollback_agent_version(
    rollback_request: AgentRollbackRequest,
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    version_id: str = Path(..., description="版本ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_tenant_admin)
):
    """
    回滚到指定版本
    
    回滚到指定历史版本，立即生效并创建新草稿
    """
    service = AgentService(db)
    return await service.rollback_agent_version(tenant_id, agent_id, version_id, rollback_request)

# 8. 验证配置
@router.post("/tenants/{tenant_id}/agents/{agent_id}/validate", response_model=AgentConfigValidation)
async def validate_agent_config(
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    验证Agent配置
    
    检查当前草稿配置是否完整和有效
    """
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    service = AgentService(db)
    return await service.validate_agent_config(tenant_id, agent_id)

# 9. 获取配置向导步骤
@router.get("/tenants/{tenant_id}/agents/{agent_id}/wizard", response_model=AgentConfigWizardResponse)
async def get_config_wizard(
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    获取配置向导步骤
    
    返回Agent配置的向导步骤和当前进度
    """
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    # 获取Agent信息
    service = AgentService(db)
    agent = await service.get_agent(tenant_id, agent_id)
    
    # 根据Agent类型返回向导步骤
    templates = service._get_agent_templates()
    
    # 找到匹配的模板
    template = None
    for t in templates:
        if t["agent_type"] == agent.agent_type.value:
            template = t
            break
    
    if not template:
        template = templates[0]  # 使用默认模板
    
    # 获取当前草稿配置
    draft_config = await service.get_agent_draft(tenant_id, agent_id)
    
    # 计算完成进度
    wizard_steps = template.get("config_wizard", [])
    completed_steps = 0
    
    for step in wizard_steps:
        field = step.get("field")
        if field == "model_id" and draft_config.get("model_id"):
            completed_steps += 1
        elif field == "prompt" and draft_config.get("prompt"):
            completed_steps += 1
        elif field == "knowledge_base_ids" and draft_config.get("knowledge_base_ids"):
            completed_steps += 1
        elif field == "tool_ids" and draft_config.get("tool_ids"):
            completed_steps += 1
    
    return {
        "wizard_steps": wizard_steps,
        "current_progress": {
            "completed_steps": completed_steps,
            "total_steps": len(wizard_steps),
            "progress_percentage": (completed_steps / len(wizard_steps)) * 100 if wizard_steps else 100
        },
        "template_info": {
            "template_id": template["template_id"],
            "name": template["name"],
            "description": template["description"]
        }
    }


