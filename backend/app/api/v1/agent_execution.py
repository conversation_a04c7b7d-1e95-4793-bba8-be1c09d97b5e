"""
多租户Agent执行服务API

提供Agent实例管理、对话执行和会话管理的REST API端点
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel
import json

from app.core.agent_service import (
    ensure_agent_service_initialized,
    SessionInfo, SessionStatus
)
from app.db.session import get_db
from app.api.deps import get_current_active_user, check_tenant_permission_by_id

router = APIRouter()

# Pydantic模型定义
class ChatRequest(BaseModel):
    """对话请求模型"""
    message: str
    thread_id: str = "default"
    user_id: Optional[str] = None
    user_name: Optional[str] = None

class ChatStreamRequest(BaseModel):
    """流式对话请求模型"""
    message: str
    thread_id: str = "default" 
    user_id: Optional[str] = None
    user_name: Optional[str] = None

class CacheClearRequest(BaseModel):
    """缓存清理请求模型"""
    agent_id: Optional[str] = None

class ChatResponse(BaseModel):
    """对话响应模型"""
    success: bool
    response: Optional[str] = None
    error: Optional[str] = None
    session_id: str
    tenant_id: str
    agent_id: str
    thread_id: str
    user_id: Optional[str] = None

# ==================== 对话API ====================

@router.post("/tenants/{tenant_id}/agents/{agent_id}/chat", response_model=ChatResponse)
async def chat_with_agent(
    request: ChatRequest,
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    与指定Agent进行对话
    
    - **message**: 用户消息内容
    - **thread_id**: 会话线程ID，默认为"default"
    - **user_id**: 用户ID（可选）
    - **user_name**: 用户名（可选）
    
    返回包含Agent响应的结果以及会话信息。
    """
    # 检查租户权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    try:
        # 初始化Agent执行服务
        service = await ensure_agent_service_initialized(db)
        
        # 调用对话服务
        result = await service.chat_with_agent(
            tenant_id=tenant_id,
            agent_id=agent_id,
            message=request.message,
            thread_id=request.thread_id,
            user_id=request.user_id or str(current_user.id),
            user_name=request.user_name or current_user.username
        )
        
        return ChatResponse(**result)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"对话失败: {str(e)}"
        )

@router.post("/tenants/{tenant_id}/agents/{agent_id}/chat/stream")
async def chat_with_agent_stream(
    request: ChatStreamRequest,
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: str = Path(..., description="Agent ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    与指定Agent进行流式对话
    
    返回Server-Sent Events (SSE)格式的流式响应。
    每个事件包含增量内容或状态信息。
    """
    # 检查租户权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    async def generate():
        try:
            # 初始化Agent执行服务
            service = await ensure_agent_service_initialized(db)
            
            # 调用流式对话服务
            async for chunk in service.chat_with_agent_stream(
                tenant_id=tenant_id,
                agent_id=agent_id,
                message=request.message,
                thread_id=request.thread_id,
                user_id=request.user_id or str(current_user.id),
                user_name=request.user_name or current_user.username
            ):
                # 将chunk转换为SSE格式
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
        
        except Exception as e:
            # 发送错误事件
            error_event = {
                "type": "error",
                "data": str(e),
                "tenant_id": tenant_id,
                "agent_id": agent_id
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )

# ==================== 会话管理API ====================

@router.get("/tenants/{tenant_id}/sessions", response_model=List[SessionInfo])
async def list_sessions(
    tenant_id: str = Path(..., description="租户ID"),
    agent_id: Optional[str] = Query(None, description="按Agent ID过滤"),
    user_id: Optional[str] = Query(None, description="按用户ID过滤"),
    status: Optional[SessionStatus] = Query(None, description="按状态过滤"),
    skip: int = Query(0, description="跳过记录数"),
    limit: int = Query(100, description="返回记录数"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    列出指定租户的会话
    
    可通过Agent ID、用户ID、状态等条件进行过滤。
    """
    # 检查租户权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    try:
        # 初始化Agent执行服务
        service = await ensure_agent_service_initialized(db)
        
        # 获取会话列表
        sessions = await service.list_sessions(
            tenant_id=tenant_id,
            agent_id=agent_id,
            user_id=user_id,
            status=status,
            skip=skip,
            limit=limit
        )
        
        return sessions
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话列表失败: {str(e)}"
        )

@router.get("/tenants/{tenant_id}/sessions/{session_id}", response_model=SessionInfo)
async def get_session(
    tenant_id: str = Path(..., description="租户ID"),
    session_id: str = Path(..., description="会话ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    获取指定会话的详细信息
    """
    # 检查租户权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    try:
        # 初始化Agent执行服务
        service = await ensure_agent_service_initialized(db)
        
        # 获取会话信息
        session = await service.get_session(tenant_id, session_id)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在"
            )
        
        return session
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话信息失败: {str(e)}"
        )

# ==================== Agent实例管理API ====================

@router.post("/tenants/{tenant_id}/agents/cache/clear")
async def clear_agent_cache(
    request: CacheClearRequest,
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    清理Agent实例缓存
    
    可以清理指定Agent的缓存，或清理整个租户的所有Agent缓存。
    """
    # 检查租户权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    try:
        # 初始化Agent执行服务
        service = await ensure_agent_service_initialized(db)
        
        # 清理缓存
        await service.clear_agent_instance_cache(tenant_id, request.agent_id)
        
        return {
            "success": True,
            "message": f"Agent实例缓存清理成功: {tenant_id}" + (f"/{request.agent_id}" if request.agent_id else "")
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理缓存失败: {str(e)}"
        )

# ==================== 服务管理API ====================

@router.get("/execution/stats")
async def get_execution_stats(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    获取Agent执行服务的统计信息
    
    包括缓存Agent实例数量、活跃会话数等统计数据。
    """
    try:
        # 初始化Agent执行服务
        service = await ensure_agent_service_initialized(db)
        
        # 获取统计信息
        stats = await service.get_execution_stats()
        
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )

@router.post("/tenants/{tenant_id}/sessions/cleanup")
async def cleanup_expired_sessions(
    tenant_id: str = Path(..., description="租户ID"),
    expiry_hours: int = Query(24, description="会话过期时间（小时）"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    清理指定租户的过期会话
    """
    # 检查租户权限
    await check_tenant_permission_by_id(current_user.id, tenant_id, db)
    
    try:
        # 初始化Agent执行服务
        service = await ensure_agent_service_initialized(db)
        
        # 清理过期会话
        cleaned_count = await service.cleanup_expired_sessions(tenant_id, expiry_hours)
        
        return {
            "success": True,
            "cleaned_sessions": cleaned_count,
            "expiry_hours": expiry_hours,
            "tenant_id": tenant_id
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理过期会话失败: {str(e)}"
        )

@router.post("/sessions/cleanup/all")
async def cleanup_all_expired_sessions(
    expiry_hours: int = Query(24, description="会话过期时间（小时）"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """
    清理所有租户的过期会话（管理员功能）
    """
    # 这里可以添加管理员权限检查
    # if not current_user.is_superuser:
    #     raise HTTPException(status_code=403, detail="需要管理员权限")
    
    try:
        # 初始化Agent执行服务
        service = await ensure_agent_service_initialized(db)
        
        # 清理所有过期会话
        cleaned_count = await service.cleanup_expired_sessions(None, expiry_hours)
        
        return {
            "success": True,
            "cleaned_sessions": cleaned_count,
            "expiry_hours": expiry_hours,
            "scope": "all_tenants"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理过期会话失败: {str(e)}"
        )

# ==================== 健康检查API ====================

@router.get("/health")
async def health_check(
    db: Session = Depends(get_db)
):
    """
    Agent执行服务健康检查
    """
    try:
        # 初始化Agent执行服务
        service = await ensure_agent_service_initialized(db)
        
        # 获取服务统计
        stats = await service.get_execution_stats()
        
        return {
            "status": "healthy",
            "service": "multi-tenant-agent-execution-service",
            "version": "1.0.0",
            "stats": stats
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Agent执行服务不可用: {str(e)}"
        ) 