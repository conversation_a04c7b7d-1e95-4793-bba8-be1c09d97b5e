"""
任务队列API接口
"""
from typing import Optional, List
from fastapi import APIRouter, Depends, Body, Path, Query, HTTPException
from sqlalchemy.orm import Session
from starlette import status

from app.db.session import get_db
from app.api.deps import get_current_active_tenant
from app.models.task import Task, TaskStatus, TaskType
from app.services.task_queue import task_queue_service
from app.schemas.task import (
    TaskResponse, TaskListResponse, TaskCreate,
    TaskStatusUpdate, TaskReindexKB
)

router = APIRouter()


@router.post("/tenants/{tenant_id}/tasks", response_model=TaskResponse)
def create_task(
    tenant_id: str = Path(...),
    task_create: TaskCreate = Body(...),
    db: Session = Depends(get_db),
    current_tenant_id: str = Depends(get_current_active_tenant)
):
    """
    创建新任务
    """
    if tenant_id != current_tenant_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this tenant"
        )
    
    # 根据任务类型创建不同的任务
    task = None
    
    if task_create.task_type == TaskType.INDEX_DOCUMENT:
        task = Task.create_index_document_task(
            tenant_id=tenant_id,
            file_id=task_create.params.get("file_id"),
            chunk_size=task_create.params.get("chunk_size", 1000),
            chunk_overlap=task_create.params.get("chunk_overlap", 200),
            priority=task_create.priority or 10,
            max_retries=task_create.max_retries or 3
        )
    elif task_create.task_type == TaskType.DELETE_FILE_INDEX:
        task = Task.create_delete_file_index_task(
            tenant_id=tenant_id,
            file_id=task_create.params.get("file_id"),
            priority=task_create.priority or 10,
            max_retries=task_create.max_retries or 3
        )
    elif task_create.task_type == TaskType.REINDEX_KNOWLEDGE_BASE:
        task = Task.create_reindex_kb_task(
            tenant_id=tenant_id,
            kb_id=task_create.params.get("kb_id"),
            chunk_size=task_create.params.get("chunk_size", 1000),
            chunk_overlap=task_create.params.get("chunk_overlap", 200),
            priority=task_create.priority or 20,
            max_retries=task_create.max_retries or 3
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported task type: {task_create.task_type}"
        )
    
    # 添加任务到队列
    task = task_queue_service.add_task(db, task)
    return task.to_dict()


@router.post("/tenants/{tenant_id}/knowledge/bases/{kb_id}/reindex", response_model=TaskResponse)
def reindex_knowledge_base(
    tenant_id: str = Path(...),
    kb_id: str = Path(...),
    reindex_data: TaskReindexKB = Body(...),
    db: Session = Depends(get_db),
    current_tenant_id: str = Depends(get_current_active_tenant)
):
    """
    重新索引知识库
    """
    if tenant_id != current_tenant_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this tenant"
        )
    
    # 创建重新索引知识库任务
    task = Task.create_reindex_kb_task(
        tenant_id=tenant_id,
        kb_id=kb_id,
        chunk_size=reindex_data.chunk_size,
        chunk_overlap=reindex_data.chunk_overlap,
        priority=reindex_data.priority or 20,
        max_retries=reindex_data.max_retries or 3
    )
    
    # 添加任务到队列
    task = task_queue_service.add_task(db, task)
    return task.to_dict()


@router.get("/tenants/{tenant_id}/tasks", response_model=TaskListResponse)
def get_tasks(
    tenant_id: str = Path(...),
    status: Optional[str] = Query(None, description="任务状态过滤"),
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db),
    current_tenant_id: str = Depends(get_current_active_tenant)
):
    """
    获取任务列表
    """
    if tenant_id != current_tenant_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this tenant"
        )
    
    # 转换状态和任务类型字符串为枚举
    task_status = TaskStatus(status) if status else None
    task_type_enum = TaskType(task_type) if task_type else None
    
    # 获取任务列表
    tasks, total = task_queue_service.get_tasks(
        db=db,
        tenant_id=tenant_id,
        status=task_status,
        task_type=task_type_enum,
        skip=skip,
        limit=limit
    )
    
    # 转换为响应格式
    task_list = [task.to_dict() for task in tasks]
    return {
        "items": task_list,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.get("/tenants/{tenant_id}/tasks/{task_id}", response_model=TaskResponse)
def get_task(
    tenant_id: str = Path(...),
    task_id: str = Path(...),
    db: Session = Depends(get_db),
    current_tenant_id: str = Depends(get_current_active_tenant)
):
    """
    获取任务详情
    """
    if tenant_id != current_tenant_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this tenant"
        )
    
    # 获取任务
    task = task_queue_service.get_task(db, task_id)
    
    if not task or task.tenant_id != tenant_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    return task.to_dict()


@router.put("/tenants/{tenant_id}/tasks/{task_id}/status", response_model=TaskResponse)
def update_task_status(
    tenant_id: str = Path(...),
    task_id: str = Path(...),
    status_update: TaskStatusUpdate = Body(...),
    db: Session = Depends(get_db),
    current_tenant_id: str = Depends(get_current_active_tenant)
):
    """
    更新任务状态 (取消或重试)
    """
    if tenant_id != current_tenant_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this tenant"
        )
    
    action = status_update.action.lower()
    success = False
    
    if action == "cancel":
        success = task_queue_service.cancel_task(db, task_id, tenant_id)
    elif action == "retry":
        success = task_queue_service.retry_task(db, task_id, tenant_id)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported action: {action}"
        )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task not found or cannot be {action}ed"
        )
    
    # 获取更新后的任务
    task = task_queue_service.get_task(db, task_id)
    return task.to_dict() 