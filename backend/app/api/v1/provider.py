from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
import jsonschema
from jsonschema import validate, ValidationError
from fastapi import APIRouter, Depends, HTTPException, Query, Path, BackgroundTasks, Body
from sqlalchemy.orm import Session
import time

from app.db.session import get_db
from app.api.deps import get_tenant_admin_for_path
from app.services.provider import AccessCredentialService
from app.schemas.provider import (
    AccessCredentialCreate, ProviderType,
    AvailableProviderItem, AvailableProvidersResponse,
    EnabledProviderItem, EnabledProvidersResponse, 
    EnableProviderResponse, DisableProviderResponse,
    TestProviderConfigResponse, ModelInfo, ProviderModelsResponse,
    ProviderBasicInfo, GenericProviderConfigRequest, GenericProviderTestRequest,
    EditProviderConfigRequest, EditProviderConfigResponse
)
from app.models.base import User
from app.models.model import Provider as ProviderModel, Model as ModelORM
from app.core.model_provider_initializer import model_provider_initializer

router = APIRouter()

# 简单的内存缓存，用于缓存提供商模型列表
# 格式: {credential_id: {"models": [...], "timestamp": timestamp}}
_models_cache = {}
_CACHE_EXPIRY_SECONDS = 1200  # 20分钟过期

def validate_credentials_against_schema(credentials: Dict[str, Any], config_schema: Dict[str, Any]) -> None:
    """
    根据 config_schema 验证 credentials 是否有效
    
    Args:
        credentials: 要验证的凭证数据
        config_schema: JSON Schema 配置定义
        
    Raises:
        ValidationError: 当验证失败时抛出异常
    """
    if not config_schema:
        # 如果没有 schema，跳过验证
        return
    
    try:
        # 使用 jsonschema 验证
        validate(instance=credentials, schema=config_schema)
    except ValidationError as e:
        # 构造更友好的错误消息
        error_path = " -> ".join(str(p) for p in e.path) if e.path else "根字段"
        raise ValidationError(f"配置验证失败 [{error_path}]: {e.message}")

def _get_cached_models(credential_id: str) -> Optional[List[Dict[str, Any]]]:
    """
    从缓存中获取模型列表
    
    Args:
        credential_id: 凭证ID
        
    Returns:
        缓存的模型列表，如果缓存不存在或已过期则返回None
    """
    if credential_id not in _models_cache:
        return None
    
    cache_entry = _models_cache[credential_id]
    current_time = time.time()
    
    # 检查是否过期
    if current_time - cache_entry["timestamp"] > _CACHE_EXPIRY_SECONDS:
        # 清理过期缓存
        del _models_cache[credential_id]
        return None
    
    return cache_entry["models"]

def _cache_models(credential_id: str, models: List[Dict[str, Any]]) -> None:
    """
    缓存模型列表
    
    Args:
        credential_id: 凭证ID
        models: 要缓存的模型列表
    """
    _models_cache[credential_id] = {
        "models": models,
        "timestamp": time.time()
    }

# ============= 简化的提供商管理API =============

@router.get("/tenants/{tenant_id}/available-providers", response_model=AvailableProvidersResponse, tags=["提供商管理"])
def get_available_providers(
    tenant_id: str = Path(..., description="租户ID"),
    provider_type: Optional[ProviderType] = Query(None, description="筛选提供商类型"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path)
):
    """
    获取可供租户启用的所有提供商列表（包括内置和自定义OpenAI兼容提供商）
    """
    # 查询所有活跃的提供商
    query = db.query(ProviderModel).filter(ProviderModel.is_active == True)
    
    # 根据提供商类型筛选
    if provider_type == ProviderType.CUSTOM_OPENAI:
        query = query.filter(ProviderModel.provider_type == ProviderType.CUSTOM_OPENAI)
    elif provider_type == ProviderType.BUILTIN:
        query = query.filter(ProviderModel.provider_type == ProviderType.BUILTIN)
    
    providers = query.all()
    
    # 构建结果
    result = []
    for provider in providers:
        provider_item = AvailableProviderItem(
            id=provider.id,
            name=provider.name,
            key=provider.key,
            provider_type=provider.provider_type.value,
            auth_type=provider.auth_type.value,
            description=provider.description,
            website=provider.website,
            icon=provider.icon,
            api_base=provider.api_base,
            config_schema=provider.config_schema
        )
        result.append(provider_item)
    
    return AvailableProvidersResponse(
        total=len(result),
        items=result
    )

@router.get("/tenants/{tenant_id}/enabled-providers", response_model=EnabledProvidersResponse, tags=["提供商管理"])
def get_enabled_providers(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path)
):
    """
    获取租户已启用的提供商配置列表（同一提供商可能有多个不同配置）
    """
    # 查询租户已启用的提供商凭证
    from app.models.model import ProviderAccessCredential
    tenant_credentials = db.query(ProviderAccessCredential).filter(
        ProviderAccessCredential.tenant_id == tenant_id,
        ProviderAccessCredential.is_active == True
    ).all()
    
    # 获取对应的提供商信息
    result = []
    for credential in tenant_credentials:
        provider = db.query(ProviderModel).filter(
            ProviderModel.id == credential.provider_id,
            ProviderModel.is_active == True
        ).first()
        
        if provider:
            # 统计该配置下的模型数量
            model_count = db.query(ModelORM).filter(
                ModelORM.provider_access_credential_id == credential.id,
                ModelORM.is_active == True
            ).count()
            
            provider_basic_info = ProviderBasicInfo(
                id=provider.id,
                name=provider.name,
                key=provider.key,
                provider_type=provider.provider_type.value,
                auth_type=provider.auth_type.value,
                description=provider.description,
                website=provider.website,
                icon=provider.icon,
                api_base=provider.api_base
                # config_schema=provider.config_schema
            )
            
            enabled_item = EnabledProviderItem(
                credential_id=credential.id,
                credential_name=credential.name,
                provider=provider_basic_info,
                model_count=model_count,
                credentials=credential.credentials,
                created_at=credential.created_at,
                updated_at=credential.updated_at
            )
            result.append(enabled_item)
    
    return EnabledProvidersResponse(
        total=len(result),
        items=result
    )

@router.post("/tenants/{tenant_id}/enable-provider/{provider_id}", response_model=EnableProviderResponse, tags=["提供商管理"])
async def enable_provider(
    tenant_id: str = Path(..., description="租户ID"),
    provider_id: str = Path(..., description="提供商ID"),
    config_request: GenericProviderConfigRequest = Body(..., description="提供商配置信息"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path)
):
    """
    为租户启用提供商（添加新的配置）
    同一个提供商可以启用多次，使用不同的配置
    
    请求体格式：
    {
        "name": "配置名称",
        "credentials": {
            "api_key": "your-api-key",
            // 其他认证信息
        }
    }
    """
    # 检查提供商是否存在且活跃
    provider = db.query(ProviderModel).filter(
        ProviderModel.id == provider_id,
        ProviderModel.is_active == True
    ).first()
    
    if not provider:
        raise HTTPException(status_code=404, detail="提供商不存在或未激活")
    
    # 确定 API 兼容类型
    api_compatible = provider.api_compatible or "openai_compatible"
    
    # 使用 config_schema 验证凭证数据
    try:
        validate_credentials_against_schema(config_request.credentials, provider.config_schema)
        validated_credentials = config_request.credentials
    except ValidationError as e:
        raise HTTPException(
            status_code=400,
            detail=f"配置参数验证失败: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"配置参数验证失败: {str(e)}"
        )
    
    # 检查配置名称是否已存在（同一租户同一提供商下）
    from app.models.model import ProviderAccessCredential
    existing_credential = db.query(ProviderAccessCredential).filter(
        ProviderAccessCredential.tenant_id == tenant_id,
        ProviderAccessCredential.provider_id == provider_id,
        ProviderAccessCredential.name == config_request.name,
        ProviderAccessCredential.is_active == True
    ).first()
    
    if existing_credential:
        raise HTTPException(
            status_code=400, 
            detail=f"该提供商下已存在名为 '{config_request.name}' 的配置"
        )
    
    try:
        # 创建访问凭证
        credential_create = AccessCredentialCreate(
            tenant_id=tenant_id,
            provider_id=provider_id,
            name=config_request.name,
            credentials=validated_credentials
        )
        
        credential = AccessCredentialService.create_credential(db, credential_create)
        
        # 测试凭证是否有效（只进行连接测试，不同步模型）
        try:
            if provider.provider_type == ProviderType.CUSTOM_OPENAI:
                models = model_provider_initializer.fetch_models_from_custom_provider(
                    provider.api_base,
                    provider.config_schema,
                    credential.credentials
                )
            else:
                models = model_provider_initializer.fetch_models_from_provider(
                    provider.key, 
                    credential.credentials
                )
            
            # 只提交凭证创建，不同步模型到数据库
            db.commit()
            
            provider_basic_info = ProviderBasicInfo(
                id=provider.id,
                name=provider.name,
                key=provider.key,
                provider_type=provider.provider_type.value,
                auth_type=provider.auth_type.value,
                description=provider.description,
                website=provider.website,
                icon=provider.icon,
                api_base=provider.api_base,
                config_schema=provider.config_schema
            )
            
            return EnableProviderResponse(
                success=True,
                message=f"成功启用提供商 {provider.name} 的配置 '{credential.name}'，连接测试通过",
                credential_id=credential.id,
                credential_name=credential.name,
                provider=provider_basic_info,
                models_discovered=len(models)
            )
            
        except Exception as e:
            # 如果测试失败，删除刚创建的凭证
            AccessCredentialService.delete_credential(db, credential.id)
            raise HTTPException(
                status_code=400, 
                detail=f"提供商配置无效，无法连接: {str(e)}"
            )
            
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启用提供商失败: {str(e)}")

@router.delete("/tenants/{tenant_id}/disable-provider/{credential_id}", response_model=DisableProviderResponse, tags=["提供商管理"])
def disable_provider(
    tenant_id: str = Path(..., description="租户ID"),
    credential_id: str = Path(..., description="提供商配置ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path)
):
    """
    禁用租户的特定提供商配置
    """
    # 查询对应的凭证
    from app.models.model import ProviderAccessCredential
    credential = db.query(ProviderAccessCredential).filter(
        ProviderAccessCredential.id == credential_id,
        ProviderAccessCredential.tenant_id == tenant_id,
        ProviderAccessCredential.is_active == True
    ).first()
    
    if not credential:
        raise HTTPException(status_code=404, detail="未找到指定的提供商配置")
    
    # 获取提供商信息用于返回消息
    provider = db.query(ProviderModel).filter(
        ProviderModel.id == credential.provider_id
    ).first()
    
    try:
        # 禁用凭证（软删除）
        credential.is_active = False
        credential.updated_at = datetime.now(timezone.utc)
        
        # 同时禁用相关的模型
        models = db.query(ModelORM).filter(
            ModelORM.provider_access_credential_id == credential_id,
            ModelORM.is_active == True
        ).all()
        
        for model in models:
            model.is_active = False
            model.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        
        return DisableProviderResponse(
            success=True,
            message=f"已成功禁用提供商 {provider.name if provider else 'Unknown'} 的配置 '{credential.name}'",
            disabled_models=len(models)
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"禁用提供商配置失败: {str(e)}")

@router.put("/tenants/{tenant_id}/provider-credentials/{credential_id}", response_model=EditProviderConfigResponse, tags=["提供商管理"])
async def edit_provider_config(
    tenant_id: str = Path(..., description="租户ID"),
    credential_id: str = Path(..., description="提供商配置ID"),
    edit_request: EditProviderConfigRequest = Body(..., description="编辑配置信息"),
    validate_config: bool = Query(False, description="是否验证配置有效性"),
    resync_models: bool = Query(False, description="是否重新同步模型"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path)
):
    """
    编辑已启用的提供商配置
    
    支持部分更新：
    - name: 更新配置名称
    - credentials: 更新凭证信息
    - is_active: 启用/禁用配置
    
    可选验证：
    - validate_config=true: 验证更新后的配置是否有效
    - resync_models=true: 重新同步模型列表
    """
    # 验证凭证是否属于当前租户
    from app.models.model import ProviderAccessCredential
    credential = db.query(ProviderAccessCredential).filter(
        ProviderAccessCredential.id == credential_id,
        ProviderAccessCredential.tenant_id == tenant_id,
        ProviderAccessCredential.is_active == True
    ).first()
    
    if not credential:
        raise HTTPException(status_code=404, detail="未找到指定的提供商配置")
    
    # 获取提供商信息
    provider = db.query(ProviderModel).filter(
        ProviderModel.id == credential.provider_id,
        ProviderModel.is_active == True
    ).first()
    
    if not provider:
        raise HTTPException(status_code=404, detail="提供商不存在或未激活")
    
    # 确定 API 兼容类型
    api_compatible = provider.api_compatible or "openai_compatible"
    
    updated_fields = []
    validation_performed = False
    models_resynced = None
    
    try:
        # 更新配置名称
        if edit_request.name is not None:
            # 检查新名称是否与同一提供商下的其他配置冲突
            existing_credential = db.query(ProviderAccessCredential).filter(
                ProviderAccessCredential.tenant_id == tenant_id,
                ProviderAccessCredential.provider_id == credential.provider_id,
                ProviderAccessCredential.name == edit_request.name,
                ProviderAccessCredential.id != credential_id,
                ProviderAccessCredential.is_active == True
            ).first()
            
            if existing_credential:
                raise HTTPException(
                    status_code=400,
                    detail=f"该提供商下已存在名为 '{edit_request.name}' 的配置"
                )
            
            credential.name = edit_request.name
            updated_fields.append("name")
        
        # 更新凭证信息
        if edit_request.credentials is not None:
            # 使用 config_schema 验证新的凭证数据
            try:
                validate_credentials_against_schema(edit_request.credentials, provider.config_schema)
                credential.credentials = edit_request.credentials
                updated_fields.append("credentials")
            except ValidationError as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"凭证数据验证失败: {str(e)}"
                )
            except Exception as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"凭证数据验证失败: {str(e)}"
                )
        
        # 更新激活状态
        if edit_request.is_active is not None:
            credential.is_active = edit_request.is_active
            updated_fields.append("is_active")
            
            # 如果禁用配置，同时禁用相关模型
            if not edit_request.is_active:
                models = db.query(ModelORM).filter(
                    ModelORM.provider_access_credential_id == credential_id,
                    ModelORM.is_active == True
                ).all()
                
                for model in models:
                    model.is_active = False
                    model.updated_at = datetime.now(timezone.utc)
        
        # 更新时间戳
        credential.updated_at = datetime.now(timezone.utc)
        
        # 如果要求验证配置有效性
        if validate_config and credential.is_active and edit_request.credentials is not None:
            try:
                if provider.provider_type == ProviderType.CUSTOM_OPENAI:
                    api_base = credential.credentials.get('api_base', provider.api_base)
                    models = model_provider_initializer.fetch_models_from_custom_provider(
                        api_base,
                        provider.config_schema or {},
                        credential.credentials
                    )
                else:
                    models = model_provider_initializer.fetch_models_from_provider(
                        provider.key,
                        credential.credentials
                    )
                validation_performed = True
                
                # 如果要求重新同步模型
                if resync_models:
                    # 禁用现有模型
                    existing_models = db.query(ModelORM).filter(
                        ModelORM.provider_access_credential_id == credential_id
                    ).all()
                    
                    for model in existing_models:
                        model.is_active = False
                        model.updated_at = datetime.now(timezone.utc)
                    
                    # 添加新模型
                    new_models_count = 0
                    for model_info in models:
                        existing_model = db.query(ModelORM).filter(
                            ModelORM.key == model_info["key"],
                            ModelORM.provider_access_credential_id == credential_id
                        ).first()
                        
                        if existing_model:
                            # 重新激活现有模型
                            existing_model.name = model_info["name"]
                            existing_model.description = model_info.get("description", "")
                            existing_model.is_active = True
                            existing_model.updated_at = datetime.now(timezone.utc)
                        else:
                            # 创建新模型
                            new_model = ModelORM(
                                tenant_id=tenant_id,
                                provider_id=provider.id,
                                key=model_info["key"],
                                name=model_info["name"],
                                provider_access_credential_id=credential_id,
                                model_type=model_info.get("model_type", "llm"),
                                description=model_info.get("description", ""),
                                is_active=True,
                                created_at=datetime.now(timezone.utc)
                            )
                            db.add(new_model)
                            new_models_count += 1
                    
                    models_resynced = len(models)
                
            except Exception as e:
                # 验证失败但不影响配置更新
                validation_performed = False
                if validate_config:
                    raise HTTPException(
                        status_code=400,
                        detail=f"配置验证失败: {str(e)}"
                    )
        
        # 提交所有更改
        db.commit()
        
        # 构建响应
        provider_basic_info = ProviderBasicInfo(
            id=provider.id,
            name=provider.name,
            key=provider.key,
            provider_type=provider.provider_type.value,
            auth_type=provider.auth_type.value,
            description=provider.description,
            website=provider.website,
            icon=provider.icon,
            api_base=provider.api_base,
            config_schema=provider.config_schema
        )
        
        return EditProviderConfigResponse(
            success=True,
            message=f"成功更新提供商 {provider.name} 的配置 '{credential.name}'",
            credential_id=credential.id,
            credential_name=credential.name,
            provider=provider_basic_info,
            updated_fields=updated_fields,
            validation_performed=validation_performed,
            models_resynced=models_resynced
        )
        
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新提供商配置失败: {str(e)}")

# ============= 辅助功能API =============

@router.post("/tenants/{tenant_id}/test-provider-config", response_model=TestProviderConfigResponse, tags=["提供商管理"])
async def test_provider_config(
    tenant_id: str = Path(..., description="租户ID"),
    test_request: GenericProviderTestRequest = Body(..., description="测试配置"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path)
):
    """
    测试提供商配置是否有效（在正式启用前测试）
    
    请求体格式：
    {
        "provider_id": "提供商ID",
        "credentials": {
            "api_key": "your-api-key",
            // 其他认证信息
        }
    }
    """
    # 查询提供商
    provider = db.query(ProviderModel).filter(
        ProviderModel.id == test_request.provider_id,
        ProviderModel.is_active == True
    ).first()
    
    if not provider:
        raise HTTPException(status_code=404, detail="提供商不存在或未激活")
    
    # 确定 API 兼容类型
    api_compatible = provider.api_compatible or "openai_compatible"
    
    # 使用 config_schema 验证凭证数据
    try:
        validate_credentials_against_schema(test_request.credentials, provider.config_schema)
        validated_credentials = test_request.credentials
    except ValidationError as e:
        raise HTTPException(
            status_code=400,
            detail=f"配置参数验证失败: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"配置参数验证失败: {str(e)}"
        )
    
    try:
        # 根据提供商类型测试连接
        if provider.provider_type == ProviderType.CUSTOM_OPENAI:
            # 对于自定义提供商，使用配置中的 api_base 覆盖默认值
            api_base = validated_credentials.get('api_base', provider.api_base)
            models = model_provider_initializer.fetch_models_from_custom_provider(
                api_base,
                provider.config_schema or {},
                validated_credentials
            )
        else:
            models = model_provider_initializer.fetch_models_from_provider(
                provider.key,
                validated_credentials
            )
        
        provider_basic_info = ProviderBasicInfo(
            id=provider.id,
            name=provider.name,
            key=provider.key,
            provider_type=provider.provider_type.value,
            auth_type=provider.auth_type.value,
            description=provider.description,
            website=provider.website,
            icon=provider.icon,
            api_base=provider.api_base,
            config_schema=provider.config_schema
        )
        
        return TestProviderConfigResponse(
            success=True,
            message=f"提供商 {provider.name} 配置测试成功",
            provider=provider_basic_info,
            models_available=len(models),
            models=models[:5]  # 只返回前5个模型作为示例
        )
    
    except Exception as e:
        provider_basic_info = ProviderBasicInfo(
            id=provider.id,
            name=provider.name,
            key=provider.key,
            provider_type=provider.provider_type.value,
            auth_type=provider.auth_type.value,
            description=provider.description,
            website=provider.website,
            icon=provider.icon,
            api_base=provider.api_base,
            config_schema=provider.config_schema
        )
        
        return TestProviderConfigResponse(
            success=False,
            message=f"提供商配置测试失败: {str(e)}",
            provider=provider_basic_info,
            error_details=str(e)
        )

@router.get("/tenants/{tenant_id}/provider-models/{credential_id}", response_model=ProviderModelsResponse, tags=["提供商管理"])
def get_provider_models(
    tenant_id: str = Path(..., description="租户ID"),
    credential_id: str = Path(..., description="提供商配置ID"),
    refresh: bool = Query(False, description="是否强制刷新缓存"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path)
):
    """
    获取特定提供商配置下的模型列表（带10分钟内存缓存）
    """
    # 验证凭证是否属于当前租户
    from app.models.model import ProviderAccessCredential
    credential = db.query(ProviderAccessCredential).filter(
        ProviderAccessCredential.id == credential_id,
        ProviderAccessCredential.tenant_id == tenant_id,
        ProviderAccessCredential.is_active == True
    ).first()
    
    if not credential:
        raise HTTPException(status_code=404, detail="未找到指定的提供商配置")
    
    # 获取提供商信息
    provider = db.query(ProviderModel).filter(
        ProviderModel.id == credential.provider_id,
        ProviderModel.is_active == True
    ).first()
    
    if not provider:
        raise HTTPException(status_code=404, detail="提供商不存在或未激活")
    
    try:
        # 首先尝试从缓存获取（除非强制刷新）
        cached_models = None if refresh else _get_cached_models(credential_id)
        if cached_models is not None:
            api_models = cached_models
        else:
            # 缓存不存在或已过期，从提供商API获取模型列表
            if provider.provider_type == ProviderType.CUSTOM_OPENAI:
                # 对于自定义OpenAI兼容提供商
                api_base = credential.credentials.get('api_base', provider.api_base)
                api_models = model_provider_initializer.fetch_models_from_custom_provider(
                    api_base,
                    provider.config_schema or {},
                    credential.credentials
                )
            else:
                # 对于内置提供商
                api_models = model_provider_initializer.fetch_models_from_provider(
                    provider.key,
                    credential.credentials
                )
            
            # 将结果缓存起来
            _cache_models(credential_id, api_models)
        
        # 构建返回结果
        result = []
        for idx, model_data in enumerate(api_models):
            model_info = ModelInfo(
                id=model_data["key"],
                key=model_data["key"],
                name=model_data["name"],
                description=model_data.get("description", "")
            )
            result.append(model_info)
        
        return ProviderModelsResponse(
            models=result,
            total=len(result)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"获取提供商模型列表失败: {str(e)}"
        )

@router.get("/tenants/{tenant_id}/provider-config-schema/{provider_id}", response_model=Dict[str, Any], tags=["提供商管理"])
def get_provider_config_schema(
    tenant_id: str = Path(..., description="租户ID"),
    provider_id: str = Path(..., description="提供商ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path)
):
    """
    获取特定提供商的配置 Schema，用于前端动态生成配置表单
    """
    # 查询提供商
    provider = db.query(ProviderModel).filter(
        ProviderModel.id == provider_id,
        ProviderModel.is_active == True
    ).first()
    
    if not provider:
        raise HTTPException(status_code=404, detail="提供商不存在或未激活")
    
    return {
        "provider_id": provider.id,
        "provider_name": provider.name,
        "provider_type": provider.provider_type.value,
        "config_schema": provider.config_schema or {},
        "description": f"用于配置 {provider.name} 提供商的参数"
    } 