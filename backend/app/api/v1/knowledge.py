from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status, UploadFile, File, Form, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import and_
from sympy import false

from app.api.deps import get_db
from app.services.knowledge import (
    KnowledgeBaseService, KnowledgeFileService, 
    KnowledgeChunkService, KnowledgeSearchService
)
from app.schemas.knowledge import (
    KnowledgeBaseCreate, KnowledgeBaseUpdate, KnowledgeBaseResponse,
    KnowledgeFileCreate, KnowledgeFileUpdate, KnowledgeFileResponse,
    KnowledgeChunkResponse, KnowledgeBaseVersionCreate, KnowledgeBaseVersionResponse,
    KnowledgeSearchResponse,
    KnowledgeBaseListResponse, KnowledgeFileListResponse, KnowledgeBaseVersionListResponse,
    KnowledgeFileUrlResponse, KnowledgeBaseUsageResponse,
    KnowledgeBaseConfigUpdate,
    KnowledgeBaseFullConfig
)
from app.core.rag.retrieval import SearchMethod, Retriever
from app.core.rag.indexing import Indexer
import logging
logger = logging.getLogger(__name__)

router = APIRouter()


# 知识库管理接口
@router.post("/tenants/{tenant_id}/knowledge/bases", response_model=KnowledgeBaseResponse, status_code=status.HTTP_201_CREATED)
def create_knowledge_base(
    knowledge_base: KnowledgeBaseCreate,
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db)
):
    """
    创建知识库
    
    - **tenant_id**: 租户ID
    - **name**: 知识库名称
    - **description**: 知识库描述（可选）
    - **config**: 知识库配置（可选）
    """
    # 确保tenant_id一致性
    if hasattr(knowledge_base, "tenant_id"):
        knowledge_base.tenant_id = tenant_id
    
    return KnowledgeBaseService.create_knowledge_base(db, knowledge_base)


@router.get("/tenants/{tenant_id}/knowledge/bases", response_model=KnowledgeBaseListResponse)
def list_knowledge_bases(
    tenant_id: str = Path(..., description="租户ID"),
    skip: int = Query(0, ge=0, description="分页起始位置"),
    limit: int = Query(20, ge=1, le=100, description="分页大小"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    db: Session = Depends(get_db)
):
    """
    获取知识库列表
    
    - **tenant_id**: 租户ID
    - **skip**: 分页起始位置
    - **limit**: 分页大小
    - **is_active**: 是否激活（可选）
    """
    knowledge_bases, total = KnowledgeBaseService.get_knowledge_bases(
        db, tenant_id, skip, limit, is_active
    )
    
    return {
        "items": knowledge_bases,
        "total": total,
        "page": skip // limit + 1 if limit > 0 else 1,
        "size": limit
    }


@router.get("/tenants/{tenant_id}/knowledge/bases/{kb_id}", response_model=KnowledgeBaseResponse)
def get_knowledge_base(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    db: Session = Depends(get_db)
):
    """
    获取知识库详情
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    """
    knowledge_base = KnowledgeBaseService.get_knowledge_base(db, kb_id, tenant_id)
    if not knowledge_base:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    return knowledge_base


@router.get("/tenants/{tenant_id}/knowledge/bases/{kb_id}/usage", response_model=KnowledgeBaseUsageResponse)
def get_knowledge_base_usage(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    db: Session = Depends(get_db)
):
    """
    获取知识库使用情况
    
    返回使用该知识库的Agent列表及统计信息
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    
    返回信息包括：
    - 知识库基本信息
    - 使用该知识库的Agent总数
    - 激活状态的Agent数量
    - 详细的Agent列表（包括版本信息）
    """
    usage_info = KnowledgeBaseService.get_knowledge_base_usage(db, kb_id, tenant_id)
    if not usage_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    return usage_info


@router.put("/tenants/{tenant_id}/knowledge/bases/{kb_id}", response_model=KnowledgeBaseResponse)
def update_knowledge_base(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    knowledge_base: KnowledgeBaseUpdate = Body(..., description="知识库更新信息"),
    db: Session = Depends(get_db)
):
    """
    更新知识库
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    - **name**: 知识库名称（可选）
    - **description**: 知识库描述（可选）
    - **config**: 知识库配置（可选，包含embedding和retrieval等设置）
    - **is_active**: 是否激活（可选）
    
    知识库配置可以包含以下内容：
    1. embedding: Embedding模型配置
       - model_name: 模型名称
       - model_id: 模型ID
    2. retrieval: 检索配置
       - default_search_type: 默认检索类型 (vector/keyword/hybrid)
       - vector_search: 向量检索配置
       - keyword_search: 关键词检索配置
       - hybrid_search: 混合检索配置
    3. chunk_size: 分块大小
    4. chunk_overlap: 分块重叠大小
    """
    updated_kb = KnowledgeBaseService.update_knowledge_base(db, kb_id, tenant_id, knowledge_base)
    if not updated_kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    return updated_kb


@router.delete("/tenants/{tenant_id}/knowledge/bases/{kb_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_knowledge_base(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    db: Session = Depends(get_db)
):
    """
    删除知识库（软删除）
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    """
    success = KnowledgeBaseService.delete_knowledge_base(db, kb_id, tenant_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    return None


# 知识库版本管理接口
@router.post("/tenants/{tenant_id}/knowledge/bases/{kb_id}/versions", response_model=KnowledgeBaseVersionResponse)
def create_knowledge_base_version(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    version_data: KnowledgeBaseVersionCreate = Body(...),
    db: Session = Depends(get_db)
):
    """
    创建知识库版本
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    - **version_name**: 版本名称
    - **description**: 版本描述（可选）
    """
    # 确保knowledge_base_id一致性
    if version_data.knowledge_base_id != kb_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="知识库ID不匹配"
        )
    
    version = KnowledgeBaseService.create_version(db, version_data, tenant_id)
    if not version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    return version


@router.get("/tenants/{tenant_id}/knowledge/bases/{kb_id}/versions", response_model=KnowledgeBaseVersionListResponse)
def list_knowledge_base_versions(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    skip: int = Query(0, ge=0, description="分页起始位置"),
    limit: int = Query(20, ge=1, le=100, description="分页大小"),
    db: Session = Depends(get_db)
):
    """
    获取知识库版本历史
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    - **skip**: 分页起始位置
    - **limit**: 分页大小
    """
    versions, total = KnowledgeBaseService.get_versions(db, kb_id, tenant_id, skip, limit)
    
    return {
        "items": versions,
        "total": total,
        "page": skip // limit + 1 if limit > 0 else 1,
        "size": limit
    }


# 知识文件管理接口
@router.post("/tenants/{tenant_id}/knowledge/files", response_model=KnowledgeFileResponse, status_code=status.HTTP_201_CREATED)
def create_knowledge_file(
    file: KnowledgeFileCreate,
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db)
):
    """
    创建知识文件（通过已有路径）
    
    - **tenant_id**: 租户ID
    - **knowledge_base_id**: 知识库ID
    - **file_name**: 文件名称
    - **file_path**: 文件路径
    - **file_type**: 文件类型（可选）
    - **file_size**: 文件大小（可选）
    - **meta_data**: 元数据（可选）
    """
    db_file = KnowledgeFileService.create_file(db, file, tenant_id)
    if not db_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    return db_file


@router.post("/tenants/{tenant_id}/knowledge/upload", response_model=KnowledgeFileResponse, status_code=status.HTTP_201_CREATED)
async def upload_knowledge_file(
    tenant_id: str = Path(..., description="租户ID"),
    file: UploadFile = File(...),
    knowledge_base_id: str = Form(...),
    db: Session = Depends(get_db)
):
    """
    上传知识文件到S3并创建记录
    
    - **tenant_id**: 租户ID
    - **file**: 要上传的文件（multipart/form-data）
    - **knowledge_base_id**: 知识库ID（form字段）
    """
    if not file.filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文件名不能为空"
        )
    
    # 验证文件类型（可选）
    # 这里可以根据需要添加对文件类型的验证
    
    # 上传文件到S3并创建记录
    db_file = await KnowledgeFileService.upload_file(db, file, knowledge_base_id, tenant_id)
    
    if not db_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    return db_file


@router.get("/tenants/{tenant_id}/knowledge/files/{file_id}/download", response_model=KnowledgeFileUrlResponse)
def get_file_download_url(
    tenant_id: str = Path(..., description="租户ID"),
    file_id: str = Path(..., description="文件ID"),
    expires_in: int = Query(3600, description="URL有效期（秒）", ge=60, le=86400),
    db: Session = Depends(get_db)
):
    """
    获取文件下载URL
    
    - **tenant_id**: 租户ID
    - **file_id**: 文件ID
    - **expires_in**: URL有效期（秒）
    """
    url_info = KnowledgeFileService.get_file_download_url(db, file_id, tenant_id, expires_in)
    
    if not url_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在或无法获取下载链接"
        )
    
    return url_info


@router.get("/tenants/{tenant_id}/knowledge/bases/{kb_id}/files", response_model=KnowledgeFileListResponse)
def list_knowledge_files(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    skip: int = Query(0, ge=0, description="分页起始位置"),
    limit: int = Query(20, ge=1, le=100, description="分页大小"),
    status: Optional[str] = Query(None, description="文件状态"),
    db: Session = Depends(get_db)
):
    """
    获取知识库文件列表
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    - **skip**: 分页起始位置
    - **limit**: 分页大小
    - **status**: 文件状态（可选）
    """
    files, total = KnowledgeFileService.get_files(db, kb_id, tenant_id, skip, limit, status)
    
    return {
        "items": files,
        "total": total,
        "page": skip // limit + 1 if limit > 0 else 1,
        "size": limit
    }


@router.get("/tenants/{tenant_id}/knowledge/files/{file_id}", response_model=KnowledgeFileResponse)
def get_knowledge_file(
    tenant_id: str = Path(..., description="租户ID"),
    file_id: str = Path(..., description="文件ID"),
    db: Session = Depends(get_db)
):
    """
    获取知识文件详情
    
    - **tenant_id**: 租户ID
    - **file_id**: 文件ID
    """
    file = KnowledgeFileService.get_file(db, file_id, tenant_id)
    if not file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在或无权访问"
        )
    
    return file


@router.put("/tenants/{tenant_id}/knowledge/files/{file_id}", response_model=KnowledgeFileResponse)
def update_knowledge_file(
    tenant_id: str = Path(..., description="租户ID"),
    file_id: str = Path(..., description="文件ID"),
    file_update: KnowledgeFileUpdate = Body(...),
    db: Session = Depends(get_db)
):
    """
    更新知识文件
    
    - **tenant_id**: 租户ID
    - **file_id**: 文件ID
    - **file_name**: 文件名称（可选）
    - **meta_data**: 元数据（可选）
    - **status**: 文件状态（可选）
    """
    updated_file = KnowledgeFileService.update_file(db, file_id, tenant_id, file_update)
    if not updated_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在或无权访问"
        )
    
    return updated_file


@router.delete("/tenants/{tenant_id}/knowledge/files/{file_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_knowledge_file(
    tenant_id: str = Path(..., description="租户ID"),
    file_id: str = Path(..., description="文件ID"),
    db: Session = Depends(get_db)
):
    """
    删除知识文件（包括从S3删除）
    
    - **tenant_id**: 租户ID
    - **file_id**: 文件ID
    """
    success = KnowledgeFileService.delete_file(db, file_id, tenant_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在或无权访问"
        )
    
    return success


# 知识分块接口
@router.get("/tenants/{tenant_id}/knowledge/files/{file_id}/chunks", response_model=List[KnowledgeChunkResponse])
def list_knowledge_chunks(
    tenant_id: str = Path(..., description="租户ID"),
    file_id: str = Path(..., description="文件ID"),
    skip: int = Query(0, ge=0, description="分页起始位置"),
    limit: int = Query(20, ge=1, le=100, description="分页大小"),
    db: Session = Depends(get_db)
):
    """
    获取文件的知识分块列表
    
    - **tenant_id**: 租户ID
    - **file_id**: 文件ID
    - **skip**: 分页起始位置
    - **limit**: 分页大小
    """
    chunks, _ = KnowledgeChunkService.get_chunks(db, file_id, tenant_id, skip, limit)
    return chunks


# 知识搜索接口
@router.get("/tenants/{tenant_id}/knowledge/{kb_id}/search", response_model=KnowledgeSearchResponse)
def search_knowledge(
    query: str = Query(..., description="搜索查询"),
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    top_k: Optional[int] = Query(None, description="返回结果数量，如不指定则使用知识库默认配置"),
    session_id: Optional[str] = Query(None, description="会话ID"),
    save_search: bool = Query(True, description="是否保存搜索记录"),
    db: Session = Depends(get_db)
):
    """
    搜索知识库
    
    - **tenant_id**: 租户ID
    - **knowledge_base_id**: 知识库ID
    - **query**: 搜索查询
    - **session_id**: 会话ID（可选）
    - **search_params**: 搜索参数（可选）
    """
    search_result = KnowledgeSearchService.search(db, query, tenant_id, kb_id, top_k, session_id, save_search)
    
    return KnowledgeSearchResponse(
        id=search_result.id,
        knowledge_base_id=search_result.knowledge_base_id,
        query=search_result.query,
        session_id=search_result.session_id,
        results=search_result.results,
        created_at=search_result.created_at
    )


@router.get("/tenants/{tenant_id}/knowledge/bases/{kb_id}/search-history", response_model=List[KnowledgeSearchResponse])
def get_knowledge_search_history(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    session_id: Optional[str] = Query(None, description="会话ID"),
    skip: int = Query(0, ge=0, description="分页起始位置"),
    limit: int = Query(20, ge=1, le=100, description="分页大小"),
    db: Session = Depends(get_db)
):
    """
    获取知识库搜索历史
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    - **session_id**: 会话ID（可选）
    - **skip**: 分页起始位置
    - **limit**: 分页大小
    """
    searches, _ = KnowledgeSearchService.get_search_history(
        db, kb_id, tenant_id, session_id, skip, limit
    )
    return searches

@router.get("/tenants/{tenant_id}/knowledge/{kb_id}/retrieve", response_model=KnowledgeSearchResponse)
def retrieve_knowledge_documents(
    query: str = Query(..., description="搜索查询"),
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    db: Session = Depends(get_db)
):
    """
    从知识库中检索文档
    
    使用RAG模块的retrieval功能直接检索文档内容。
    如果不指定搜索参数，将使用知识库中配置的默认参数。
    如果知识库中没有配置检索设置，默认使用关键词检索。
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    - **query**: 搜索查询
    - **top_k**: 返回结果数量，如不指定则使用知识库默认配置
    - **session_id**: 会话ID（可选）
    - **save_search**: 是否保存搜索记录
    """
    search_result = KnowledgeSearchService.search(db=db, query=query, tenant_id=tenant_id, kb_id=kb_id, save_search=false)
    
    response = KnowledgeSearchResponse(
        id=search_result.id,
        knowledge_base_id=search_result.knowledge_base_id,
        query=search_result.query,
        session_id=search_result.session_id,
        results=search_result.results,
        created_at=search_result.created_at
    )
    return response


@router.post("/tenants/{tenant_id}/knowledge/{kb_id}/reindex-task", response_model=Dict[str, Any])
def create_reindex_task(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    chunk_size: int = Query(512, description="文本块大小"),
    chunk_overlap: int = Query(128, description="文本块重叠大小"),
    db: Session = Depends(get_db)
):
    """
    创建知识库重索引任务
    
    这个操作会创建一个任务队列中的任务，用于重索引知识库中的所有文件。
    适用于大型知识库或需要在后台执行的情况。
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    - **chunk_size**: 文本块大小（字符数）
    - **chunk_overlap**: 文本块重叠大小（字符数）
    """
    # 验证知识库存在且属于当前租户
    knowledge_base = KnowledgeBaseService.get_knowledge_base(db, kb_id, tenant_id)
    if not knowledge_base:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    # 检查知识库配置
    kb_config = knowledge_base.config or {}
    embedding_model_id = kb_config.get("embedding_model_id")
    if not embedding_model_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="知识库未配置嵌入模型，请先配置embedding_model_id"
        )
    
    # 创建任务
    from app.models.task import Task
    from app.services.task_queue import task_queue_service
    
    task = Task.create_reindex_kb_task(
        tenant_id=tenant_id,
        kb_id=kb_id,
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap
    )
    
    # 添加到任务队列
    task_id = task_queue_service.add_task(db, task)
    
    return {
        "status": "scheduled",
        "message": "知识库重索引任务已创建，将在任务队列中执行",
        "knowledge_base_id": kb_id,
        "tenant_id": tenant_id,
        "task_id": task_id,
        "chunk_size": chunk_size,
        "chunk_overlap": chunk_overlap
    }

@router.post("/tenants/{tenant_id}/knowledge/{kb_id}/reindex", response_model=Dict[str, Any])
async def reindex_knowledge_base(
    background_tasks: BackgroundTasks,
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    chunk_size: int = Query(1000, description="文本块大小"),
    chunk_overlap: int = Query(200, description="文本块重叠大小"),
    db: Session = Depends(get_db)
):
    """
    重新索引知识库中的所有文件
    
    这个操作会删除现有的索引，并对知识库中的所有文件重新进行分块和向量化。
    由于操作可能耗时较长，任务会在后台运行。
    适用于小型知识库。对于大型知识库，建议使用 /reindex-task 接口。
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    - **chunk_size**: 文本块大小（字符数）
    - **chunk_overlap**: 文本块重叠大小（字符数）
    """
    # 验证知识库存在且属于当前租户
    knowledge_base = KnowledgeBaseService.get_knowledge_base(db, kb_id, tenant_id)
    if not knowledge_base:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    # 检查知识库配置
    kb_config = knowledge_base.config or {}
    embedding_model_id = kb_config.get("embedding_model_id")
    if not embedding_model_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="知识库未配置嵌入模型，请先配置embedding_model_id"
        )
    
    # 创建后台任务来执行重索引
    async def _reindex_task():
        try:
            result = await KnowledgeBaseService.reindex_knowledge_base(
                db=db,
                kb_id=kb_id,
                tenant_id=tenant_id,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap
            )
            logger.info(f"知识库 {kb_id} 重索引完成: {result}")
        except Exception as e:
            logger.error(f"知识库 {kb_id} 重索引失败: {str(e)}")
            # 可以考虑更新知识库状态或发送通知
    
    # 将重索引任务添加到后台任务
    background_tasks.add_task(_reindex_task)
    
    return {
        "status": "processing",
        "message": "知识库重索引任务已提交，正在后台处理",
        "knowledge_base_id": kb_id,
        "tenant_id": tenant_id,
        "chunk_size": chunk_size,
        "chunk_overlap": chunk_overlap
    }

@router.get("/tenants/{tenant_id}/knowledge/{kb_id}/reindex-status/{task_id}", response_model=Dict[str, Any])
def get_reindex_task_status(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    task_id: str = Path(..., description="任务ID"),
    db: Session = Depends(get_db)
):
    """
    获取知识库重索引任务状态
    
    用于检查通过 /reindex-task 接口创建的重索引任务的执行状态。
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    - **task_id**: 任务ID
    """
    # 验证知识库存在且属于当前租户
    knowledge_base = KnowledgeBaseService.get_knowledge_base(db, kb_id, tenant_id)
    if not knowledge_base:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    # 获取任务
    from app.models.task import Task, TaskType
    task = db.query(Task).filter(
        Task.id == task_id,
        Task.tenant_id == tenant_id,
        Task.task_type == TaskType.REINDEX_KNOWLEDGE_BASE
    ).first()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="重索引任务不存在或无权访问"
        )
    
    # 确认任务参数中的知识库ID与请求的知识库ID一致
    task_kb_id = task.params.get("kb_id")
    if task_kb_id != kb_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="任务ID与知识库ID不匹配"
        )
    
    # 返回任务状态
    response = {
        "task_id": task.id,
        "status": task.status,
        "created_at": task.created_at.isoformat() if task.created_at else None,
        "started_at": task.started_at.isoformat() if task.started_at else None,
        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        "knowledge_base_id": kb_id,
        "tenant_id": tenant_id,
        "retry_count": task.retry_count,
        "max_retries": task.max_retries
    }
    
    # 如果任务已完成或失败，添加结果或错误信息
    if task.status == "completed" and task.result:
        response["result"] = task.result
    
    if task.status == "failed" and task.error:
        response["error"] = task.error
    
    return response

# 新增：知识库配置更新接口
@router.put("/tenants/{tenant_id}/knowledge/config/{kb_id}", response_model=KnowledgeBaseResponse)
def update_knowledge_base_config(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    config_update: KnowledgeBaseConfigUpdate = Body(..., description="知识库配置更新"),
    db: Session = Depends(get_db)
):
    """
    更新知识库配置
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    - **config_update**: 知识库配置更新信息
    
    配置更新可以包含以下内容：
    1. embedding: Embedding模型配置（必选）
       - model_name: 模型名称
       - model_id: 模型ID
    2. retrieval: 检索配置（可选，默认使用关键词检索）
       - default_search_type: 默认检索类型 (vector/keyword/hybrid)
       - vector_search: 向量检索配置
         - top_k: 返回结果数量
         - score_threshold: 分数阈值
         - rerank: Rerank模型配置
       - keyword_search: 关键词检索配置
         - top_k: 返回结果数量
         - score_threshold: 分数阈值
         - rerank: Rerank模型配置
       - hybrid_search: 混合检索配置
         - top_k: 返回结果数量
         - score_threshold: 分数阈值
         - rerank: Rerank模型配置
         - vector_weight: 向量检索权重
         - keyword_weight: 关键词检索权重
    3. chunk_size: 分块大小
    4. chunk_overlap: 分块重叠大小
    """
    # 转换配置更新为字典格式
    config_dict = config_update.model_dump(exclude_none=True)
    
    # 创建知识库更新对象
    kb_update = KnowledgeBaseUpdate(config=KnowledgeBaseFullConfig(**config_dict))
    
    # 更新知识库
    updated_kb = KnowledgeBaseService.update_knowledge_base(db, kb_id, tenant_id, kb_update)
    if not updated_kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    # 如果更新了embedding模型，可能需要重新索引
    if "embedding" in config_dict:
        logger.info(f"知识库 {kb_id} 的Embedding模型已更新，可能需要重新索引")
    
    return updated_kb


# 新增：获取知识库配置接口
@router.get("/tenants/{tenant_id}/knowledge/config/{kb_id}", response_model=Dict[str, Any])
def get_knowledge_base_config(
    tenant_id: str = Path(..., description="租户ID"),
    kb_id: str = Path(..., description="知识库ID"),
    db: Session = Depends(get_db)
):
    """
    获取知识库配置
    
    - **tenant_id**: 租户ID
    - **kb_id**: 知识库ID
    
    返回知识库的完整配置信息，包括：
    1. embedding: Embedding模型配置
    2. retrieval: 检索配置
    3. chunk_size: 分块大小
    4. chunk_overlap: 分块重叠大小
    """
    # 获取知识库
    knowledge_base = KnowledgeBaseService.get_knowledge_base(db, kb_id, tenant_id)
    if not knowledge_base:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在或无权访问"
        )
    
    # 返回配置
    config = knowledge_base.config or {}
    
    # 如果没有配置embedding，添加一个错误提示
    if "embedding" not in config:
        config["_error"] = "知识库未配置Embedding模型，请先配置"
    
    return config

