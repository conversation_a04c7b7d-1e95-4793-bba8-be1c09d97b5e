import json
from typing import List, Optional, Dict, Any
from datetime import datetime, UTC
from fastapi import APIRouter, Depends, HTTPException, Query, Path, BackgroundTasks, Body
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.db.session import get_db
from app.api.deps import get_current_user, get_current_active_user, get_tenant_admin, get_tenant_admin_for_path
from app.services.model import ModelService
from app.schemas.model import (
    Model as ModelSchema, ModelCreate, ModelUpdate, ModelListResponse,
    ModelType, ModelTypeListResponse
)
from app.models.base import User, Tenant
from app.models.model import Model as ModelORM

router = APIRouter()


# ============= 模型管理 =============
@router.post("/tenants/{tenant_id}/models", response_model=ModelSchema, tags=["模型管理"])
def create_model(
    model_create: ModelCreate,
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """创建模型"""
    try:
        model = ModelService.create_model(db, model_create, tenant_id=tenant_id)
        return model
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tenants/{tenant_id}/models", response_model=ModelListResponse, tags=["模型管理"])
def list_models(
    tenant_id: str = Path(..., description="租户ID"),
    skip: int = 0,
    limit: int = 100,
    name: Optional[str] = None,
    provider_id: Optional[str] = None,
    model_type: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取租户模型列表"""
    models, total = ModelService.list_models(
        db, skip=skip, limit=limit, name=name, 
        provider_id=provider_id, model_type=model_type, is_active=is_active,
        tenant_id=tenant_id
    )
    return ModelListResponse(
        total=total,
        items=models
    )

@router.get("/tenants/{tenant_id}/models/{model_id}", response_model=ModelSchema, tags=["模型管理"])
def get_model(
    tenant_id: str = Path(..., description="租户ID"),
    model_id: str = Path(..., description="模型ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取租户模型详情"""
    model = ModelService.get_model(db, model_id, tenant_id=tenant_id)
    if not model:
        raise HTTPException(status_code=404, detail="模型不存在")
    return model

@router.put("/tenants/{tenant_id}/models/{model_id}", response_model=ModelSchema, tags=["模型管理"])
def update_model(
    model_update: ModelUpdate,
    tenant_id: str = Path(..., description="租户ID"),
    model_id: str = Path(..., description="模型ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path)
):
    """更新租户模型信息"""
    try:
        model = ModelService.update_model(db, model_id, model_update, tenant_id=tenant_id)
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        return model
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/tenants/{tenant_id}/models/{model_id}", tags=["模型管理"])
def delete_model(
    tenant_id: str = Path(..., description="租户ID"),
    model_id: str = Path(..., description="模型ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_tenant_admin_for_path)
):
    """删除租户模型"""
    try:
        success = ModelService.delete_model(db, model_id, tenant_id=tenant_id)
        if not success:
            raise HTTPException(status_code=404, detail="模型不存在")
        return {"message": "模型已删除"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/model-types", response_model=ModelTypeListResponse, tags=["模型管理"])
def get_model_types(
    current_user: User = Depends(get_current_active_user)
):
    """获取支持的模型类型列表"""
    model_types = [
        ModelType(
            key="llm",
            name="大语言模型",
            description="用于文本生成、对话、问答等任务的大型语言模型"
        ),
        ModelType(
            key="embedding",
            name="向量模型",
            description="用于将文本转换为向量表示的嵌入模型"
        ),
    ]
    
    return ModelTypeListResponse(items=model_types)

