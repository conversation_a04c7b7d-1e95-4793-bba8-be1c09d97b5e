from typing import Any, Dict, List
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.tenant import TenantBase, TenantCreate, TenantUpdate, Tenant, TenantWithStats, TenantListResponse
from app.services.tenant import TenantService
from app.api.deps import get_current_active_user, get_current_user

router = APIRouter()

@router.get("/tenants", response_model=TenantListResponse, summary="获取我的租户列表")
async def list_tenants(
    skip: int = Query(0, description="跳过记录数"),
    limit: int = Query(100, description="返回记录数"),
    name: str = Query(None, description="按名称过滤"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    获取租户列表
    """
    tenant_service = TenantService(db)
    return await tenant_service.get_user_tenants(
        user_id=current_user.id, 
        skip=skip, 
        limit=limit, 
        name=name
    )


@router.get("/tenants/{tenant_id}", response_model=TenantBase, summary="获取租户详情")
async def get_tenant(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    获取租户详情
    """
    tenant_service = TenantService(db)
    return await tenant_service.get_tenant(tenant_id)

@router.get("/tenants/{tenant_id}/stats", response_model=TenantWithStats, summary="获取租户统计信息")
async def get_tenant_stats(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    获取租户详情及统计信息
    """
    tenant_service = TenantService(db)
    return await tenant_service.get_tenant_with_stats(tenant_id)

@router.put("/tenants/{tenant_id}", response_model=TenantBase, summary="更新租户")
async def update_tenant(
    tenant_id: str = Path(..., description="租户ID"),
    tenant_data: TenantUpdate = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    更新租户信息
    """
    tenant_service = TenantService(db)
    return await tenant_service.update_tenant(tenant_id, tenant_data, current_user.id)

@router.delete("/tenants/{tenant_id}", response_model=Dict[str, Any], summary="删除租户")
async def delete_tenant(
    tenant_id: str = Path(..., description="租户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
) -> Any:
    """
    删除租户
    """
    tenant_service = TenantService(db)
    return await tenant_service.delete_tenant(tenant_id, current_user.id) 