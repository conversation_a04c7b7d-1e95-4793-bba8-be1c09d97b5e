from typing import Any, Dict, List
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.user import (
    User, UserCreate, UserUpdate, UserDetail, UserListResponse, OperationResponse
)
from app.schemas.tenant import TenantBase, TenantListResponse
from app.services.user import UserService
from app.services.tenant import TenantService
from app.api.deps import get_super_admin

router = APIRouter(prefix="/admin")

# 超级管理员用户相关接口
@router.get("/users", response_model=UserListResponse, summary="超级管理员查看所有用户", tags=["超级管理员"])
async def admin_list_all_users(
    skip: int = Query(0, description="跳过记录数"),
    limit: int = Query(100, description="返回记录数"),
    username: str = Query(None, description="按用户名过滤"),
    email: str = Query(None, description="按邮箱过滤"),
    is_active: bool = Query(None, description="按状态过滤"),
    db: Session = Depends(get_db),
    current_user = Depends(get_super_admin)  # 使用超级管理员权限检查
) -> Any:
    """
    超级管理员查看系统中的所有用户
    """
    user_service = UserService(db)
    return await user_service.get_users(
        skip=skip, 
        limit=limit, 
        username=username,
        email=email,
        is_active=is_active
    )

@router.put("/users/{user_id}/status", response_model=User, summary="超级管理员更改用户状态", tags=["超级管理员"])
async def admin_update_user_status(
    user_id: str = Path(..., description="用户ID"),
    is_active: bool = Query(..., description="是否激活"),
    db: Session = Depends(get_db),
    current_user = Depends(get_super_admin)  # 使用超级管理员权限检查
) -> Any:
    """
    超级管理员更改用户的激活状态
    """
    user_service = UserService(db)
    return await user_service.update_user_status(user_id, is_active)

@router.post("/make-super-admin", response_model=OperationResponse, summary="设置超级管理员", tags=["超级管理员"])
async def make_super_admin(
    user_id: str = Query(..., description="用户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_super_admin)  # 使用超级管理员权限检查
) -> Any:
    """
    将用户设置为超级管理员
    """
    user_service = UserService(db)
    return await user_service.set_super_admin(user_id, True)

@router.post("/remove-super-admin", response_model=OperationResponse, summary="移除超级管理员", tags=["超级管理员"])
async def remove_super_admin(
    user_id: str = Query(..., description="用户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_super_admin)  # 使用超级管理员权限检查
) -> Any:
    """
    移除用户的超级管理员权限
    """
    # 不能移除自己的超级管理员权限
    if current_user.id == user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能移除自己的超级管理员权限"
        )
        
    user_service = UserService(db)
    return await user_service.set_super_admin(user_id, False)

# 超级管理员租户相关接口
@router.get("/tenants", response_model=TenantListResponse, summary="超级管理员查看所有租户", tags=["超级管理员"])
async def admin_list_all_tenants(
    skip: int = Query(0, description="跳过记录数"),
    limit: int = Query(100, description="返回记录数"), 
    name: str = Query(None, description="按名称过滤"),
    is_active: bool = Query(None, description="按状态过滤"),
    db: Session = Depends(get_db),
    current_user = Depends(get_super_admin)  # 使用超级管理员权限检查
) -> Any:
    """
    超级管理员查看系统中的所有租户列表
    """
    tenant_service = TenantService(db)
    return await tenant_service.get_all_tenants(skip=skip, limit=limit, name=name, is_active=is_active)

@router.put("/tenants/{tenant_id}/status", response_model=TenantBase, summary="超级管理员更改租户状态", tags=["超级管理员"])
async def admin_update_tenant_status(
    tenant_id: str = Path(..., description="租户ID"),
    is_active: bool = Query(..., description="是否激活"),
    db: Session = Depends(get_db),
    current_user = Depends(get_super_admin)  # 使用超级管理员权限检查
) -> Any:
    """
    超级管理员更改租户的激活状态
    """
    tenant_service = TenantService(db)
    return await tenant_service.update_tenant_status(tenant_id, is_active)

@router.delete("/tenants/{tenant_id}", response_model=Dict[str, Any], summary="超级管理员强制删除租户", tags=["超级管理员"])
async def admin_force_delete_tenant(
    tenant_id: str = Path(..., description="租户ID"),
    force: bool = Query(False, description="是否强制删除"),
    db: Session = Depends(get_db),
    current_user = Depends(get_super_admin)  # 使用超级管理员权限检查
) -> Any:
    """
    超级管理员强制删除租户（包括所有关联数据）
    """
    tenant_service = TenantService(db)
    return await tenant_service.force_delete_tenant(tenant_id, force) 