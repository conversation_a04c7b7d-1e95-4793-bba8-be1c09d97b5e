"""
数据库会话和连接管理
"""
import logging
from typing import Generator

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool

from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 根据配置创建引擎
engine = create_engine(
    settings.SQLALCHEMY_DATABASE_URI,
    pool_pre_ping=settings.SQLALCHEMY_POOL_PRE_PING,
    pool_size=settings.SQLALCHEMY_POOL_SIZE,
    max_overflow=settings.SQLALCHEMY_MAX_OVERFLOW,
    pool_timeout=settings.SQLALCHEMY_POOL_TIMEOUT,
    pool_recycle=settings.SQLALCHEMY_POOL_RECYCLE,
    pool_reset_on_return=settings.SQLALCHEMY_POOL_RESET_ON_RETURN,
    poolclass=QueuePool,
    echo=False,  # 设为True可以显示SQL执行日志
    
    # 额外的连接参数
    connect_args={
        "charset": "utf8mb4",
        "autocommit": False,
        "connect_timeout": 10,  # 连接超时
        "read_timeout": 30,     # 读取超时
        "write_timeout": 30,    # 写入超时
    } if settings.DB_TYPE == "mysql" else {}
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话的依赖函数，用于FastAPI依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def test_database_connection() -> bool:
    """
    测试数据库连接是否正常
    """
    try:
        db = SessionLocal()
        db.execute(text("SELECT 1"))
        db.close()
        return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False


def get_connection_pool_status():
    """
    获取连接池状态信息
    """
    try:
        pool = engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
        }
    except Exception as e:
        logger.error(f"获取连接池状态失败: {e}")
        return None


# 打印数据库连接信息
db_type = settings.DB_TYPE
if db_type == "postgresql":
    logger.info(
        f"PostgreSQL 连接: {settings.POSTGRES_USERNAME}@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
    )
elif db_type == "mysql":
    logger.info(
        f"MySQL 连接: {settings.MYSQL_USERNAME}@{settings.MYSQL_SERVER}:{settings.MYSQL_PORT}/{settings.MYSQL_DB}"
    )
    logger.info(f"连接池配置: size={settings.SQLALCHEMY_POOL_SIZE}, max_overflow={settings.SQLALCHEMY_MAX_OVERFLOW}, recycle={settings.SQLALCHEMY_POOL_RECYCLE}s, pre_ping={settings.SQLALCHEMY_POOL_PRE_PING}") 