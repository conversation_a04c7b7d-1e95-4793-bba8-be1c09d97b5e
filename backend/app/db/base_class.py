"""
数据库模型基类
"""
from typing import Any, Dict, List

from sqlalchemy.ext.declarative import as_declarative, declared_attr


@as_declarative()
class Base:
    """
    所有数据库模型的基类
    使用字符串类型的UUIDBase62主键
    """
    id: Any
    
    # 为所有继承的类生成表名
    @declared_attr
    def __tablename__(cls) -> str:
        # 去掉类名中的"Model"后缀，并转换为snake_case格式
        name = cls.__name__
        if name.endswith("Model"):
            name = name[:-5]
        return name.lower()
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> Any:
        """从字典创建模型实例"""
        return cls(**data)
    
    def to_dict(self) -> Dict[str, Any]:
        """将模型转换为字典"""
        result = {}
        for key in self.__mapper__.c.keys():
            result[key] = getattr(self, key)
        return result
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """从字典更新模型实例"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value) 