"""
导入所有模型以便Alembic能够正确生成迁移
"""
from app.db.base_class import Base  # noqa

# 导入所有模型，确保每个模型只被导入一次
from app.models.agent import Agent, AgentVersion
from app.models.base import Tenant, User, UserTenantAssociation, TenantInvitation  # noqa
from app.models.knowledge import KnowledgeBase, KnowledgeFile, KnowledgeChunk, KnowledgeSearch  # noqa
from app.models.model import Provider, Model, ProviderAccessCredential  # noqa
from app.models.tool import Tool, MCPServer, MCPTool, TenantTool, AgentTool, ToolExecutionLog  # noqa
from app.models.task import Task
 