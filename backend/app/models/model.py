from datetime import datetime, UTC
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Foreign<PERSON>ey, Integer, String, Text, JSON, DateTime, Float, Enum
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from app.models.base import Base
from app.utils.id_generator import generate_model_id, generate_provider_id, generate_provider_access_credential_id

class ProviderType(str, PyEnum):
    """提供商类型"""
    BUILTIN = "BUILTIN"                 # 内置提供商
    CUSTOM_OPENAI = "CUSTOM_OPENAI"     # 自定义OpenAI兼容提供商

class AuthType(str, PyEnum):
    """认证类型"""
    API_KEY = "api_key"                 # API密钥认证
    BEARER_TOKEN = "bearer_token"       # Bearer Token认证
    BASIC_AUTH = "basic_auth"          # 基础认证
    OAUTH = "oauth"                    # OAuth认证

class Provider(Base):
    __tablename__ = "provider"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_provider_id)
    name = Column(String(64), nullable=False)
    key = Column(String(64), unique=True, nullable=False)
    provider_type = Column(Enum(ProviderType), default=ProviderType.BUILTIN, nullable=False)
    description = Column(Text)
    icon = Column(String(128))
    website = Column(String(256))
    api_base = Column(String(256))
    api_compatible = Column(String(64))
    auth_type = Column(Enum(AuthType), default=AuthType.API_KEY, nullable=False)
    config_schema = Column(JSON)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    models = relationship("Model", secondary="provider_access_credential", back_populates="provider", overlaps="provider_access_credential,models")
    
    @property
    def is_builtin(self) -> bool:
        """判断是否为内置提供商"""
        return self.provider_type == ProviderType.BUILTIN
    
    @property
    def is_custom_openai(self) -> bool:
        """判断是否为自定义OpenAI兼容提供商"""
        return self.provider_type == ProviderType.CUSTOM_OPENAI

class Model(Base):
    __tablename__ = "model"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_model_id)
    tenant_id = Column(String(32), ForeignKey("tenant.id", use_alter=True), nullable=False, index=True)
    provider_id = Column(String(32), ForeignKey("provider.id", use_alter=True), nullable=False, index=True)
    provider_access_credential_id = Column(String(32), ForeignKey("provider_access_credential.id", use_alter=True), nullable=False)
    name = Column(String(64), nullable=False)
    key = Column(String(64), nullable=False)
    description = Column(Text)
    model_type = Column(String(32), nullable=False)  # llm, embedding, image, audio, etc.
    extra_config = Column(JSON)  # 模型额外配置
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    tenant = relationship("Tenant")
    provider = relationship("Provider", overlaps="models")
    provider_access_credential = relationship("ProviderAccessCredential", back_populates="models", overlaps="models")

class ProviderAccessCredential(Base):
    __tablename__ = "provider_access_credential"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_provider_access_credential_id)
    tenant_id = Column(String(32), ForeignKey("tenant.id", use_alter=True))
    provider_id = Column(String(32), ForeignKey("provider.id", use_alter=True))
    name = Column(String(64), nullable=False)
    credentials = Column(JSON)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    tenant = relationship("Tenant")
    provider = relationship("Provider", overlaps="models")
    models = relationship("Model", back_populates="provider_access_credential", overlaps="models")

 