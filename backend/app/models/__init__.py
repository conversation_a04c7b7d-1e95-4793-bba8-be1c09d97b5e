"""
数据库模型模块
"""
# 导入所有模型，以便Alembic可以发现它们
from app.models.base import (
    Base, Tenant, User, UserTenantAssociation, TenantInvitation
)
from app.models.agent import (
    Agent, AgentVersion
)
from app.models.tool import (
    Tool, MCPServer, MCPTool, TenantTool, AgentTool, ToolExecutionLog
)
from app.models.knowledge import (
    KnowledgeBase, KnowledgeFile
)
from app.models.model import (
    Provider, Model, ProviderAccessCredential
)

__all__ = [
    'Base', 'Tenant', 'User', 'UserTenantAssociation',
    'Agent', 'AgentVersion', 'Tool', 'MCPServer',
    'MCPTool', 'TenantTool', 'AgentTool', 'ToolExecutionLog',
    'KnowledgeBase', 'KnowledgeFile',
    'Provider', 'Model',
    'ProviderAccessCredential'
] 