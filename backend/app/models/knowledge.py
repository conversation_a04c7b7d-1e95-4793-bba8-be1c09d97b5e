from datetime import datetime, UTC
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Foreign<PERSON>ey, Integer, String, Text, JSON, DateTime, Float
from sqlalchemy.orm import relationship

from app.models.base import Base
from app.utils.id_generator import generate_kb_id, generate_knowledge_file_id, generate_knowledge_chunk_id, generate_knowledge_search_id

class KnowledgeBase(Base):
    __tablename__ = "knowledge_base"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_kb_id)
    tenant_id = Column(String(32), ForeignKey("tenant.id", use_alter=True))
    name = Column(String(64), nullable=False)
    description = Column(Text)
    config = Column(JSON)
    
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    tenant = relationship("Tenant", back_populates="knowledge_bases")
    files = relationship("KnowledgeFile", back_populates="knowledge_base")

class KnowledgeFile(Base):
    __tablename__ = "knowledge_file"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_knowledge_file_id)
    knowledge_base_id = Column(String(32), ForeignKey("knowledge_base.id", use_alter=True))
    file_name = Column(String(256), nullable=False)
    file_path = Column(String(512), nullable=False)
    file_type = Column(String(128))
    file_size = Column(Integer)
    status = Column(String(32), default="processing")  # processing, indexed, error
    chunk_count = Column(Integer, default=0)
    meta_data = Column(JSON)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    knowledge_base = relationship("KnowledgeBase", back_populates="files")
    chunks = relationship("KnowledgeChunk", back_populates="file")

class KnowledgeChunk(Base):
    __tablename__ = "knowledge_chunk"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_knowledge_chunk_id)
    file_id = Column(String(32), ForeignKey("knowledge_file.id", use_alter=True))
    content = Column(Text, nullable=False)
    meta_data = Column(JSON)
    embedding = Column(JSON, nullable=True)  # 存储向量嵌入
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    
    # 关系
    file = relationship("KnowledgeFile", back_populates="chunks")

class KnowledgeSearch(Base):
    __tablename__ = "knowledge_search"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_knowledge_search_id)
    knowledge_base_id = Column(String(32), ForeignKey("knowledge_base.id", use_alter=True))
    session_id = Column(String(32), nullable=True)  # 移除外键约束，改为普通字段
    query = Column(Text, nullable=False)
    results = Column(JSON)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    # 关系
    knowledge_base = relationship("KnowledgeBase") 