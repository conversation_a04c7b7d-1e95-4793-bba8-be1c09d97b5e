from datetime import datetime, UTC
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Text, JSON, DateTime, Table
from sqlalchemy.orm import relationship

from app.models.base import Base
from app.utils.id_generator import generate_tool_id, generate_mcp_server_id, generate_mcp_tool_id, generate_tenant_tool_id, generate_agent_tool_id, generate_tool_execution_log_id

# 前向声明以避免循环导入
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .agent import Agent

class Tool(Base):
    __tablename__ = "tool"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_tool_id)
    name = Column(String(64), nullable=False)
    key = Column(String(64), unique=True, nullable=False)
    description = Column(Text)
    param_schema = Column(JSON,default={})
    config_schema = Column(JSON,default={})
    is_active = Column(Boolean, default=True)
    tag = Column(String(256))
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    tenant_tools = relationship("TenantTool", back_populates="tool")
    agent_tools = relationship("AgentTool", back_populates="tool")
    mcp_tools = relationship("MCPTool", back_populates="tool")
    execution_logs = relationship("ToolExecutionLog", back_populates="tool")

class MCPServer(Base):
    __tablename__ = "mcp_server"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_mcp_server_id)
    tenant_id = Column(String(32), ForeignKey("tenant.id", use_alter=True), nullable=False)
    name = Column(String(64), nullable=False)
    endpoint = Column(String(256), nullable=False)

    transport_type = Column(String(32), nullable=False) 
    
    status = Column(String(32), default="active")  # active, inactive, error
    last_heartbeat = Column(DateTime)
    meta_data = Column(JSON) # 元数据 
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    mcp_tools = relationship("MCPTool", back_populates="server")
    tenant = relationship("Tenant", back_populates="mcp_servers")

class MCPTool(Base):
    __tablename__ = "mcp_tool"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_mcp_tool_id)
    server_id = Column(String(32), ForeignKey("mcp_server.id", use_alter=True))
    tool_id = Column(String(32), ForeignKey("tool.id", use_alter=True))
    config = Column(JSON)
    status = Column(String(32), default="active")  # active, inactive, error
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    server = relationship("MCPServer", back_populates="mcp_tools")
    tool = relationship("Tool", back_populates="mcp_tools")

class TenantTool(Base):
    __tablename__ = "tenant_tool"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_tenant_tool_id)
    tenant_id = Column(String(32), ForeignKey("tenant.id", use_alter=True))
    tool_id = Column(String(32), ForeignKey("tool.id", use_alter=True))
    config = Column(JSON)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    tenant = relationship("Tenant", back_populates="tenant_tools")
    tool = relationship("Tool", back_populates="tenant_tools")
    agent_tools = relationship("AgentTool", back_populates="tenant_tool")

class AgentTool(Base):
    __tablename__ = "agent_tool"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_agent_tool_id)
    agent_id = Column(String(32), ForeignKey("agent.id", use_alter=True))
    agent_version = Column(String(32))
    tool_id = Column(String(32), ForeignKey("tool.id", use_alter=True))
    tenant_tool_id = Column(String(32), ForeignKey("tenant_tool.id", use_alter=True), nullable=True)
    config = Column(JSON)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    agent = relationship("Agent", back_populates="agent_tools")
    tool = relationship("Tool", back_populates="agent_tools")
    tenant_tool = relationship("TenantTool", back_populates="agent_tools")
    execution_logs = relationship("ToolExecutionLog", back_populates="agent_tool")

    
class ToolExecutionLog(Base):
    __tablename__ = "tool_execution_log"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_tool_execution_log_id)
    session_id = Column(String(32), nullable=True)  # 移除外键约束，改为普通字段
    tool_id = Column(String(32), ForeignKey("tool.id", use_alter=True))
    agent_tool_id = Column(String(32), ForeignKey("agent_tool.id", use_alter=True))
    input_params = Column(JSON)
    output_result = Column(JSON)
    execution_time = Column(Integer)  # 执行时间(毫秒)
    status = Column(String(32))  # success, error, timeout
    error_message = Column(Text)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    
    # 关系
    tool = relationship("Tool", back_populates="execution_logs")
    agent_tool = relationship("AgentTool", back_populates="execution_logs")
