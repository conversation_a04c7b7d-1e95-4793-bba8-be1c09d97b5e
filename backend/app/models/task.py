"""
任务队列数据库模型
"""
from datetime import datetime, UTC
from enum import Enum
import json
from typing import Dict, Any, Optional

from sqlalchemy import Column, String, Integer, JSON, DateTime, Text, Enum as SQLEnum

from app.db.base_class import Base


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"     # 等待执行
    RUNNING = "running"     # 正在执行
    COMPLETED = "completed" # 已完成
    FAILED = "failed"       # 执行失败
    RETRY = "retry"         # 等待重试


class TaskType(str, Enum):
    """任务类型枚举"""
    INDEX_DOCUMENT = "index_document"           # 索引文档
    DELETE_FILE_INDEX = "delete_file_index"     # 删除文件索引
    REINDEX_KNOWLEDGE_BASE = "reindex_kb"       # 重新索引知识库


class Task(Base):
    """任务队列模型"""
    __tablename__ = "tasks"
    
    id = Column(String(36), primary_key=True, index=True)
    tenant_id = Column(String(36), index=True, nullable=False)
    task_type = Column(SQLEnum(TaskType), index=True, nullable=False)
    status = Column(SQLEnum(TaskStatus), default=TaskStatus.PENDING, index=True)
    
    # 任务参数 (JSON)
    params = Column(JSON, nullable=False, default={})
    
    # 任务结果 (JSON)
    result = Column(JSON, nullable=True)
    
    # 错误信息 (如果任务失败)
    error = Column(Text, nullable=True)
    
    # 重试次数和最大重试次数
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    
    # 优先级 (数字越小优先级越高)
    priority = Column(Integer, default=10, index=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "tenant_id": self.tenant_id,
            "task_type": self.task_type,
            "status": self.status,
            "params": self.params,
            "result": self.result,
            "error": self.error,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "priority": self.priority,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None
        }
    
    @classmethod
    def create_index_document_task(
        cls, 
        tenant_id: str, 
        file_id: str,
        priority: int = 10,
        max_retries: int = 3
    ) -> "Task":
        """创建索引文档任务"""
        from app.utils.id_generator import generate_task_id
        
        return cls(
            id=generate_task_id(),
            tenant_id=tenant_id,
            task_type=TaskType.INDEX_DOCUMENT,
            status=TaskStatus.PENDING,
            params={
                "file_id": file_id
            },
            priority=priority,
            max_retries=max_retries
        )
    
    @classmethod
    def create_delete_file_index_task(
        cls,
        tenant_id: str,
        file_id: str,
        priority: int = 10,
        max_retries: int = 3
    ) -> "Task":
        """创建删除文件索引任务"""
        from app.utils.id_generator import generate_task_id
        
        return cls(
            id=generate_task_id(),
            tenant_id=tenant_id,
            task_type=TaskType.DELETE_FILE_INDEX,
            status=TaskStatus.PENDING,
            params={
                "file_id": file_id
            },
            priority=priority,
            max_retries=max_retries
        )
    
    @classmethod
    def create_reindex_kb_task(
        cls,
        tenant_id: str,
        kb_id: str,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        priority: int = 20,  # 较低优先级
        max_retries: int = 3
    ) -> "Task":
        """创建重新索引知识库任务"""
        from app.utils.id_generator import generate_task_id
        
        return cls(
            id=generate_task_id(),
            tenant_id=tenant_id,
            task_type=TaskType.REINDEX_KNOWLEDGE_BASE,
            status=TaskStatus.PENDING,
            params={
                "kb_id": kb_id,
                "chunk_size": chunk_size,
                "chunk_overlap": chunk_overlap
            },
            priority=priority,
            max_retries=max_retries
        )

    @staticmethod
    def create_reindex_kb_task(
        tenant_id: str,
        kb_id: str,
        chunk_size: int = 1000,
        chunk_overlap: int = 200
    ) -> "Task":
        """创建知识库重索引任务"""
        from app.utils.id_generator import generate_task_id
        
        return Task(
            id=generate_task_id(),
            tenant_id=tenant_id,
            task_type=TaskType.REINDEX_KNOWLEDGE_BASE,
            status=TaskStatus.PENDING,
            priority=1,  # 中等优先级
            params={
                "kb_id": kb_id,
                "chunk_size": chunk_size,
                "chunk_overlap": chunk_overlap
            },
            result=None,
            created_at=datetime.now(UTC)
        ) 