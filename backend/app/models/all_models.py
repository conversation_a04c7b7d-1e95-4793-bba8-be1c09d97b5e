"""
导入所有模型供Alembic使用
"""
# 首先导入Base
from app.models.base import Base  # 从base.py导入实际使用的Base

# 为避免冲突，我们逐个导入各模型文件中的类
from app.models.agent import Agent, AgentVersion
from app.models.base import Tenant, User, UserTenantAssociation, TenantInvitation
from app.models.knowledge import KnowledgeBase, KnowledgeFile, KnowledgeChunk, KnowledgeSearch
from app.models.model import Provider, Model, ProviderAccessCredential
from app.models.tool import Tool, MCPServer, MCPTool, TenantTool, AgentTool, ToolExecutionLog


# 在auth.py中定义的AccessCredential和app.models.model中的ProviderAccessCredential不再冲突
from app.models.auth import AccessCredential, AccessToken 