from datetime import datetime, UTC
from typing import Optional, List, Dict, Any
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, ForeignKey, String, Text, JSON, DateTime, Enum, UniqueConstraint
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from .base import Base
from app.utils.id_generator import generate_agent_id, generate_agent_version_id

class AgentType(str, PyEnum):
    SUPERVISOR = "supervisor"  # 监督者
    GENERAL = "general"  # 通用
    
    @property
    def description(self) -> str:
        """返回枚举值的中文描述"""
        descriptions = {
            self.SUPERVISOR: "监督者",
            self.GENERAL: "通用"
        }
        return descriptions.get(self, "未知类型")
    
    @property
    def is_supervisor(self) -> bool:
        return self == AgentType.SUPERVISOR
    
    @staticmethod
    def get_all_agent_types() -> list[str]:
        return [agent_type.value for agent_type in AgentType]

class Agent(Base):
    __tablename__ = "agent"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_agent_id)
    tenant_id = Column(String(32), ForeignKey("tenant.id", use_alter=True))
    name = Column(String(64), nullable=False)
    agent_type = Column(Enum(AgentType), default=AgentType.GENERAL)
    open_id = Column(String(64), unique=True, nullable=False)
    description = Column(Text)
    # 图标相关字段
    icon_url = Column(String(512), nullable=True, comment="图标S3存储路径")
    icon_metadata = Column(JSON, nullable=True, comment="图标元数据(文件大小、格式等)")
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    tenant = relationship("Tenant", back_populates="agents")
    versions = relationship("AgentVersion", back_populates="agent", cascade="all, delete-orphan")
    
    # 工具关系
    agent_tools = relationship("AgentTool", back_populates="agent")
    
    @property
    def is_supervisor(self) -> bool:
        """判断是否为监督者类型"""
        return self.agent_type == AgentType.SUPERVISOR
    
    @property
    def current_version(self) -> Optional["AgentVersion"]:
        """获取当前活跃版本"""
        return next((v for v in self.versions if v.is_current), None)
    
    @property
    def config(self) -> Optional[Dict]:
        """获取当前版本的配置"""
        current_ver = self.current_version
        return current_ver.config if current_ver else None
    
    @property
    def model_id(self) -> Optional[str]:
        """获取当前版本的模型ID"""
        current_ver = self.current_version
        return current_ver.model_id if current_ver else None
    
    @property
    def prompt(self) -> Optional[str]:
        """获取当前版本的提示词"""
        current_ver = self.current_version
        return current_ver.prompt if current_ver else None

    @property 
    def icon_access_url(self) -> Optional[str]:
        """获取图标的访问URL（如果存在）"""
        if not self.icon_url:
            return None
        
        # 如果已经是完整URL，直接返回
        if self.icon_url.startswith(('http://', 'https://')):
            return self.icon_url
            
        # 如果是S3路径，需要通过存储服务生成访问URL
        from app.services.storage import s3_storage_service
        
        # 从S3路径中提取object_key
        object_key = s3_storage_service.extract_object_key_from_path(self.icon_url)
        if object_key:
            # 使用新的图标访问URL方法
            return s3_storage_service._get_icon_access_url(object_key)
        return None

class AgentVersion(Base):
    __tablename__ = "agent_version"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_agent_version_id)
    # tenant_id = Column(String(32), ForeignKey("tenant.id", use_alter=True))
    agent_id = Column(String(32), ForeignKey("agent.id", use_alter=True))
    version_number = Column(String(16), nullable=False)  # 语义化版本号 (x.y.z)
    _config = Column("config", JSON)  # 重命名数据库列
    prompt = Column(Text)
    description = Column(Text)
    model_id = Column(String(32), ForeignKey("model.id", use_alter=True), nullable=True)
    knowledge_base_ids = Column(JSON, nullable=True)  # 关联的知识库ID列表
    tool_ids = Column(JSON, nullable=True)  # 关联的工具ID列表
    mcp_server_ids = Column(JSON, nullable=True)  # 关联的MCP服务器ID列表
    is_current = Column(Boolean, default=False)
    is_published = Column(Boolean, default=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    
    # 关系
    agent = relationship(
        "Agent", 
        back_populates="versions",
        foreign_keys=[agent_id]
    )
    model = relationship("Model", foreign_keys=[model_id])

    __table_args__ = (
        UniqueConstraint('agent_id', 'version_number', name='uq_agent_version'),
    )
    
    @property
    def config(self) -> Dict:
        """获取配置"""
        return self._config or {}
    
    @config.setter
    def config(self, value):
        """设置config值"""
        self._config = value

    @property
    def knowledge_bases(self) -> List[str]:
        """获取关联的知识库ID列表"""
        return self.knowledge_base_ids or []
    
    @knowledge_bases.setter
    def knowledge_bases(self, value: List[str]):
        """设置关联的知识库ID列表"""
        self.knowledge_base_ids = value if value else []

    @property
    def tools(self) -> List[str]:
        """获取关联的工具ID列表"""
        return self.tool_ids or []
    
    @tools.setter
    def tools(self, value: List[str]):
        """设置关联的工具ID列表"""
        self.tool_ids = value if value else []

    @property
    def mcp_servers(self) -> List[str]:
        """获取关联的MCP服务器ID列表"""
        return self.mcp_server_ids or []
    
    @mcp_servers.setter
    def mcp_servers(self, value: List[str]):
        """设置关联的MCP服务器ID列表"""
        self.mcp_server_ids = value if value else []



