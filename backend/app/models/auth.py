from datetime import datetime, UTC
from sqlalchemy import Column, Foreign<PERSON>ey, Integer, String, DateTime, Boolean, JSON
from sqlalchemy.orm import relationship
from .base import Base
from app.utils.id_generator import generate_access_credential_id, generate_access_token_id

class AccessCredential(Base):
    __tablename__ = "access_credential"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_access_credential_id)
    tenant_id = Column(String(32), ForeignKey("tenant.id", use_alter=True), nullable=False)
    agent_id = Column(String(32), ForeignKey("agent.id", use_alter=True), nullable=False)
    app_id = Column(String(64), unique=True, nullable=False)
    app_secret = Column(String(256), nullable=False)
    allowed_domains = Column(JSON)
    allowed_ips = Column(JSON)
    rate_limit = Column(Integer)
    token_expire_hours = Column(Integer, default=24)
    sign_type = Column(String(32), default="hmac_sha256")
    sign_version = Column(String(16), default="v1")
    nonce_check = Column(Boolean, default=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # 关系
    tenant = relationship("Tenant")
    agent = relationship("Agent", back_populates="credentials")
    tokens = relationship("AccessToken", back_populates="credential")

class AccessToken(Base):
    __tablename__ = "access_token"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_access_token_id)
    credential_id = Column(String(32), ForeignKey("access_credential.id", use_alter=True), nullable=True)
    token = Column(String(256), unique=True, nullable=False)
    expired_at = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(UTC))
    
    # 关系
    credential = relationship("AccessCredential", back_populates="tokens") 