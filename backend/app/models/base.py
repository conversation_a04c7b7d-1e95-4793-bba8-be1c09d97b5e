from datetime import datetime, timedelta, timezone
import uuid
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, ForeignKey, String, Text, JSON, DateTime
from sqlalchemy.orm import declarative_base, relationship

from app.utils.id_generator import (
    generate_user_id, generate_tenant_id, generate_invite_id, generate_token
)

Base = declarative_base()

class Tenant(Base):
    __tablename__ = "tenant"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_tenant_id)
    name = Column(String(64), unique=True, nullable=False)
    description = Column(Text)
    is_personal = Column(Boolean, default=False)  # 标记是否为个人组织
    owner_id = Column(String(32), ForeignKey("user.id"))  # 组织拥有者
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # 关系
    users = relationship("UserTenantAssociation", back_populates="tenant")
    agents = relationship("Agent", back_populates="tenant")
    tenant_tools = relationship("TenantTool", back_populates="tenant")
    knowledge_bases = relationship("KnowledgeBase", back_populates="tenant")
    owner = relationship("User", foreign_keys=[owner_id])
    invitations = relationship("TenantInvitation", back_populates="tenant")
    mcp_servers = relationship("MCPServer", back_populates="tenant")

class User(Base):
    __tablename__ = "user"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_user_id)
    username = Column(String(64), unique=True, nullable=False)
    email = Column(String(128), unique=True, nullable=False)
    password_hash = Column(String(256), nullable=False)
    display_name = Column(String(64))
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)  # 邮箱是否已验证
    verification_token = Column(String(128))  # 邮箱验证令牌
    verification_token_expires = Column(DateTime)  # 验证令牌过期时间
    last_login = Column(DateTime)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    extra = Column(JSON)
    
    # 关系
    tenants = relationship("UserTenantAssociation", back_populates="user")
    owned_tenants = relationship("Tenant", foreign_keys=[Tenant.owner_id], overlaps="owner")
    received_invitations = relationship("TenantInvitation", foreign_keys="TenantInvitation.invitee_email", primaryjoin="User.email == TenantInvitation.invitee_email")

class UserTenantAssociation(Base):
    __tablename__ = "user_tenant_association"
    
    user_id = Column(String(32), ForeignKey("user.id", use_alter=True), primary_key=True)
    tenant_id = Column(String(32), ForeignKey("tenant.id", use_alter=True), primary_key=True)
    role_key = Column(String(32), nullable=False)  # 角色键值，对应core.roles中的TenantRole
    
    # 关系
    user = relationship("User", back_populates="tenants")
    tenant = relationship("Tenant", back_populates="users")

class TenantInvitation(Base):
    __tablename__ = "tenant_invitation"
    
    id = Column(String(32), primary_key=True, index=True, default=generate_invite_id)
    tenant_id = Column(String(32), ForeignKey("tenant.id", use_alter=True))
    inviter_id = Column(String(32), ForeignKey("user.id", use_alter=True))
    invitee_email = Column(String(128), nullable=False, index=True)
    role_key = Column(String(32), nullable=False)  # 角色键值，对应core.roles中的TenantRole
    invitation_token = Column(String(128), unique=True, index=True, default=generate_token)
    status = Column(String(32), default="pending")  # pending, accepted, expired, cancelled
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    expires_at = Column(DateTime, default=lambda: datetime.now(timezone.utc) + timedelta(days=7))
    
    # 关系
    tenant = relationship("Tenant", back_populates="invitations")
    inviter = relationship("User", foreign_keys=[inviter_id])
    
    # 检查状态是否有效
    @property
    def is_valid(self):
        if self.status != "pending":
            return False
        
        # 确保expires_at有时区信息
        if self.expires_at.tzinfo is None:
            # 如果expires_at没有时区信息，假设它是UTC时间
            expires_at_utc = self.expires_at.replace(tzinfo=timezone.utc)
        else:
            expires_at_utc = self.expires_at
            
        return datetime.now(timezone.utc) < expires_at_utc 