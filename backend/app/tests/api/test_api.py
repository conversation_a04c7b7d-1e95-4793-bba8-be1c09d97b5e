"""
FastAPI API测试文件

使用pytest和TestClient对API端点进行测试
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.db.base import Base
from app.db.session import get_db
from app.core.config import settings
from app.services.user import UserService
from app.services.tenant import TenantService

# 创建测试数据库
SQLALCHEMY_TEST_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 测试数据
TEST_USER = {
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpassword123",
    "full_name": "Test User"
}

TEST_TENANT = {
    "name": "测试组织",
    "description": "这是一个测试组织",
    "type": "company"
}

@pytest.fixture(scope="module")
def test_db():
    # 创建测试数据库表
    Base.metadata.create_all(bind=engine)
    
    # 覆盖依赖项
    def override_get_db():
        db = TestingSessionLocal()
        try:
            yield db
        finally:
            db.close()
    
    app.dependency_overrides[get_db] = override_get_db
    
    yield
    
    # 清理
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="module")
def client(test_db):
    with TestClient(app) as c:
        yield c

@pytest.fixture(scope="module")
def test_user(client):
    # 注册测试用户
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json=TEST_USER
    )
    assert response.status_code == 201
    
    # 登录获取token
    login_response = client.post(
        f"{settings.API_V1_STR}/auth/login",
        json={
            "username": TEST_USER["email"],
            "password": TEST_USER["password"]
        }
    )
    assert login_response.status_code == 200
    
    token = login_response.json()["access_token"]
    return {
        "token": token,
        "headers": {"Authorization": f"Bearer {token}"},
        "user_data": response.json()["user"]
    }

# 健康检查测试
def test_health_check(client):
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

# 测试用户认证相关API
class TestAuth:
    def test_register_user(self, client):
        """测试用户注册"""
        response = client.post(
            f"{settings.API_V1_STR}/auth/register",
            json={
                "username": "newuser",
                "email": "<EMAIL>",
                "password": "newpassword123",
                "full_name": "New User"
            }
        )
        assert response.status_code == 201
        assert "user" in response.json()
        assert response.json()["user"]["email"] == "<EMAIL>"
    
    def test_login(self, client):
        """测试用户登录"""
        response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            json={
                "username": "<EMAIL>",
                "password": "newpassword123"
            }
        )
        assert response.status_code == 200
        assert "access_token" in response.json()
        assert "token_type" in response.json()
        assert response.json()["token_type"] == "bearer"

# 测试租户相关API
class TestTenant:
    def test_create_tenant(self, client, test_user, test_tenant):
        """测试获取租户信息"""
        # 获取特定租户
        response = client.get(
            f"{settings.API_V1_STR}/tenants/{test_tenant['id']}",
            headers=test_user["headers"]
        )
        assert response.status_code == 200
        assert response.json()["name"] == test_tenant["name"]
    
    def test_get_tenants(self, client, test_user):
        """测试获取租户列表"""
        response = client.get(
            f"{settings.API_V1_STR}/tenants",
            headers=test_user["headers"]
        )
        assert response.status_code == 200
        assert isinstance(response.json(), list)
        assert len(response.json()) > 0
    
    def test_invite_user(self, client, test_user, test_tenant):
        """测试邀请用户"""
        response = client.post(
            f"{settings.API_V1_STR}/auth/tenants/{test_tenant['id']}/invite",
            headers=test_user["headers"],
            json={
                "email": "<EMAIL>",
                "role_id": 2  # 假设2是成员角色ID
            }
        )
        assert response.status_code == 200
        assert "invitation" in response.json()
        assert response.json()["invitation"]["email"] == "<EMAIL>"

# 测试用户相关API
class TestUser:
    def test_get_current_user(self, client, test_user):
        """测试获取当前用户信息"""
        response = client.get(
            f"{settings.API_V1_STR}/users/me",
            headers=test_user["headers"]
        )
        assert response.status_code == 200
        assert response.json()["email"] == test_user["user_data"]["email"]
    
    def test_get_users(self, client, test_user):
        """测试获取用户列表"""
        response = client.get(
            f"{settings.API_V1_STR}/users",
            headers=test_user["headers"]
        )
        assert response.status_code == 200
        assert isinstance(response.json(), list)
    
    def test_update_user(self, client, test_user):
        """测试更新用户信息"""
        response = client.patch(
            f"{settings.API_V1_STR}/users/me",
            headers=test_user["headers"],
            json={
                "full_name": "Updated Name"
            }
        )
        assert response.status_code == 200
        assert response.json()["full_name"] == "Updated Name"

# 测试Agent相关API
class TestAgent:
    def test_get_agent(self, client, test_user, test_agent):
        """测试获取特定Agent"""
        response = client.get(
            f"{settings.API_V1_STR}/agents/{test_agent['id']}",
            headers=test_user["headers"]
        )
        assert response.status_code == 200
        assert response.json()["name"] == test_agent["name"]
    
    def test_get_agents(self, client, test_user):
        """测试获取Agent列表"""
        response = client.get(
            f"{settings.API_V1_STR}/agents",
            headers=test_user["headers"]
        )
        assert response.status_code == 200
        assert isinstance(response.json(), list)
        assert len(response.json()) > 0
    
    def test_update_agent(self, client, test_user, test_agent):
        """测试更新Agent"""
        response = client.patch(
            f"{settings.API_V1_STR}/agents/{test_agent['id']}",
            headers=test_user["headers"],
            json={
                "description": "更新的Agent描述"
            }
        )
        assert response.status_code == 200
        assert response.json()["description"] == "更新的Agent描述"

# 测试工具相关API
class TestTool:
    def test_get_tool(self, client, test_user, test_tool):
        """测试获取特定工具"""
        response = client.get(
            f"{settings.API_V1_STR}/tools/{test_tool['id']}",
            headers=test_user["headers"]
        )
        assert response.status_code == 200
        assert response.json()["name"] == test_tool["name"]
    
    def test_get_tools(self, client, test_user):
        """测试获取工具列表"""
        response = client.get(
            f"{settings.API_V1_STR}/tools",
            headers=test_user["headers"]
        )
        assert response.status_code == 200
        assert isinstance(response.json(), list)
    
    def test_update_tool(self, client, test_user, test_tool):
        """测试更新工具"""
        response = client.patch(
            f"{settings.API_V1_STR}/tools/{test_tool['id']}",
            headers=test_user["headers"],
            json={
                "description": "更新的工具描述"
            }
        )
        assert response.status_code == 200
        assert response.json()["description"] == "更新的工具描述" 