"""
测试配置文件

定义可重用的pytest fixture和测试环境设置
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.db.base import Base
from app.db.session import get_db
from app.core.config import settings

# 创建测试数据库
SQLALCHEMY_TEST_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 测试数据
TEST_USER = {
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpassword123",
    "full_name": "Test User"
}

TEST_TENANT = {
    "name": "测试组织",
    "description": "这是一个测试组织",
    "type": "company"
}

TEST_AGENT = {
    "name": "测试Agent",
    "description": "这是一个测试Agent",
    "model": "gpt-4",
    "tenant_id": 1  # 默认个人租户ID
}

TEST_TOOL = {
    "name": "测试工具",
    "description": "这是一个测试工具",
    "type": "function",
    "api_endpoint": "https://api.example.com/tool",
    "tenant_id": 1  # 默认个人租户ID
}

@pytest.fixture(scope="session")
def test_db():
    # 创建测试数据库表
    Base.metadata.create_all(bind=engine)
    
    # 覆盖依赖项
    def override_get_db():
        db = TestingSessionLocal()
        try:
            yield db
        finally:
            db.close()
    
    app.dependency_overrides[get_db] = override_get_db
    
    yield
    
    # 清理
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="session")
def client(test_db):
    with TestClient(app) as c:
        yield c

@pytest.fixture(scope="session")
def test_user(client):
    # 注册测试用户
    response = client.post(
        f"{settings.API_V1_STR}/auth/register",
        json=TEST_USER
    )
    assert response.status_code == 201
    
    # 登录获取token
    login_response = client.post(
        f"{settings.API_V1_STR}/auth/login",
        json={
            "username": TEST_USER["email"],
            "password": TEST_USER["password"]
        }
    )
    assert login_response.status_code == 200
    
    token = login_response.json()["access_token"]
    return {
        "token": token,
        "headers": {"Authorization": f"Bearer {token}"},
        "user_data": response.json()["user"]
    }

@pytest.fixture(scope="session")
def test_tenant(client, test_user):
    # 创建测试租户
    response = client.post(
        f"{settings.API_V1_STR}/auth/tenants",
        headers=test_user["headers"],
        json=TEST_TENANT
    )
    assert response.status_code == 201
    return response.json()["tenant"]

@pytest.fixture(scope="session")
def test_agent(client, test_user):
    # 创建测试Agent
    response = client.post(
        f"{settings.API_V1_STR}/agents",
        headers=test_user["headers"],
        json=TEST_AGENT
    )
    assert response.status_code == 201
    return response.json()

@pytest.fixture(scope="session")
def test_tool(client, test_user):
    # 创建测试工具
    response = client.post(
        f"{settings.API_V1_STR}/tools",
        headers=test_user["headers"],
        json=TEST_TOOL
    )
    assert response.status_code == 201
    return response.json() 