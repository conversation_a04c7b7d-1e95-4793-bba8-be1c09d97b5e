#!/usr/bin/env python
"""
测试运行脚本

提供一个简单的方法来运行所有API测试或特定测试
"""

import os
import sys
import pytest

def main():
    """运行测试的主函数"""
    # 当前文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 默认运行所有测试
    test_path = current_dir
    
    # 如果提供了特定测试文件或测试类，则运行特定测试
    if len(sys.argv) > 1:
        test_path = os.path.join(current_dir, sys.argv[1])
    
    # 运行测试
    result = pytest.main(["-v", test_path])
    
    return result

if __name__ == "__main__":
    sys.exit(main()) 