# API 测试指南

本目录包含了云知问FastAPI应用的API测试代码。测试使用pytest框架和FastAPI的TestClient来验证API的正确性。

## 测试结构

- `conftest.py`: 包含测试所需的全局fixture
- `api/test_api.py`: 包含所有API端点的单元测试
- `run_tests.py`: 便捷的测试运行脚本

## 安装测试依赖

在运行测试之前，确保已安装所有必要的依赖:

```bash
pip install pytest pytest-cov requests httpx
```

## 运行测试

### 运行所有测试

```bash
python -m app.tests.run_tests
```

或者直接使用pytest:

```bash
pytest -v app/tests/
```

### 运行特定的测试文件

```bash
python -m app.tests.run_tests api/test_api.py
```

### 运行特定的测试类

```bash
pytest -v app/tests/api/test_api.py::TestAuth
```

### 运行特定的测试方法

```bash
pytest -v app/tests/api/test_api.py::TestAuth::test_login
```

## 测试覆盖率报告

要生成测试覆盖率报告，可以使用以下命令:

```bash
pytest --cov=app app/tests/
```

生成HTML报告:

```bash
pytest --cov=app --cov-report=html app/tests/
```

## 测试数据

测试使用SQLite内存数据库，所有测试数据在测试会话结束后会被自动清理。在`conftest.py`中定义了以下测试数据：

- 测试用户
- 测试租户
- 测试Agent
- 测试工具

## 注意事项

1. 测试会创建临时数据库，不会影响实际的数据库
2. 测试环境使用了不同的配置，确保测试不会发送真实的邮件或触发外部服务
3. 某些需要外部服务的测试可能需要模拟(mock)这些服务 