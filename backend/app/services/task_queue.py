"""
基于数据库的任务队列服务
"""
from datetime import datetime, UTC, timedelta
from typing import List, Dict, Any, Optional, Tuple, Union
import asyncio
import traceback
import logging
from threading import Thread, Event
import time

from sqlalchemy import asc, desc
from sqlalchemy.orm import Session

from app.models.task import Task, TaskStatus, TaskType
from app.db.session import SessionLocal
from app.core.rag.indexing import Indexer


logger = logging.getLogger(__name__)


class TaskQueueService:
    """基于数据库的任务队列服务"""
    
    def __init__(self):
        """初始化任务队列服务"""
        self._stop_event = Event()
        self._worker_thread = None
        self._check_interval = 2  # 秒
        self._max_concurrent_tasks = 2  # 最大并行任务数
        self._running_tasks = 0
    
    def start_worker(self):
        """启动工作线程"""
        if self._worker_thread is not None and self._worker_thread.is_alive():
            logger.warning("Worker thread already running")
            return
        
        self._stop_event.clear()
        self._worker_thread = Thread(target=self._worker_loop, daemon=True)
        self._worker_thread.start()
        logger.info("Task queue worker started")
    
    def stop_worker(self):
        """停止工作线程"""
        if self._worker_thread is None:
            return
        
        self._stop_event.set()
        self._worker_thread.join(timeout=30)
        logger.info("Task queue worker stopped")
    
    def _worker_loop(self):
        """工作线程主循环"""
        while not self._stop_event.is_set():
            try:
                # 检查是否可以执行更多任务
                if self._running_tasks < self._max_concurrent_tasks:
                    # 获取待处理任务
                    with SessionLocal() as db:
                        task = self._get_next_task(db)
                        
                        if task:
                            # 更新任务状态为运行中
                            task.status = TaskStatus.RUNNING
                            task.started_at = datetime.now(UTC)
                            db.commit()
                            
                            # 增加运行中任务计数
                            self._running_tasks += 1
                            
                            # 异步执行任务
                            Thread(target=self._execute_task, args=(task.id,), daemon=True).start()
                
                # 等待检查间隔
                time.sleep(self._check_interval)
                
            except Exception as e:
                logger.error(f"Error in task queue worker: {str(e)}")
                traceback.print_exc()
                time.sleep(5)  # 出错后等待一段时间
    
    def _get_next_task(self, db: Session) -> Optional[Task]:
        """获取下一个待处理任务"""
        # 按优先级和创建时间排序查询
        next_task = db.query(Task).filter(
            Task.status.in_([TaskStatus.PENDING, TaskStatus.RETRY])
        ).order_by(
            asc(Task.priority),  # 优先级数字小的先执行
            asc(Task.created_at)
        ).first()
        
        return next_task
    
    def _execute_task(self, task_id: str):
        """执行任务"""
        try:
            with SessionLocal() as db:
                # 获取任务
                task = db.query(Task).filter(Task.id == task_id).first()
                if not task or task.status != TaskStatus.RUNNING:
                    return
                
                try:
                    # 根据任务类型执行不同的操作
                    result = None
                    
                    if task.task_type == TaskType.INDEX_DOCUMENT:
                        result = self._execute_index_document(db, task)
                    elif task.task_type == TaskType.DELETE_FILE_INDEX:
                        result = self._execute_delete_file_index(db, task)
                    elif task.task_type == TaskType.REINDEX_KNOWLEDGE_BASE:
                        result = self._execute_reindex_kb(db, task)
                    
                    # 更新任务状态为完成
                    task.status = TaskStatus.COMPLETED
                    task.result = result
                    task.completed_at = datetime.now(UTC)
                    db.commit()
                    
                    logger.info(f"Task completed: {task_id}")
                    
                except Exception as e:
                    # 处理任务执行错误
                    logger.error(f"Task execution error: {str(e)}")
                    traceback.print_exc()
                    
                    # 判断是否需要重试
                    if task.retry_count < task.max_retries:
                        task.status = TaskStatus.RETRY
                        task.retry_count += 1
                        task.error = str(e)
                    else:
                        task.status = TaskStatus.FAILED
                        task.error = str(e)
                        task.completed_at = datetime.now(UTC)
                    
                    db.commit()
        finally:
            # 减少运行中任务计数
            self._running_tasks -= 1
    
    def _execute_index_document(self, db: Session, task: Task) -> Dict[str, Any]:
        """执行索引文档任务"""
        params = task.params
        file_id = params.get("file_id")
        tenant_id = task.tenant_id
        
        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 创建索引器并执行索引操作
            logger.info(f"Index document task: {file_id}, {tenant_id}")
            indexer = Indexer(db)
            num_chunks, chunk_ids = loop.run_until_complete(
                indexer.index_document(
                    file_id=file_id,
                    tenant_id=tenant_id
                )
            )
            logger.info(f"Index document result: {num_chunks}, {chunk_ids}")

            return {
                "file_id": file_id,
                "num_chunks": num_chunks,
                "chunk_ids": chunk_ids
            }
        finally:
            loop.close()
    
    def _execute_delete_file_index(self, db: Session, task: Task) -> Dict[str, Any]:
        """执行删除文件索引任务"""
        params = task.params
        file_id = params.get("file_id")
        tenant_id = task.tenant_id
        logger.info(f"Delete file index task: {file_id}, {tenant_id}")
        # 创建索引器并执行删除操作
        indexer = Indexer(db)
        result = indexer.delete_file_index(
            file_id=file_id,
            tenant_id=tenant_id
        )
        logger.info(f"Delete file index result: {result}")
        return result
    
    def _execute_reindex_kb(self, db: Session, task: Task) -> Dict[str, Any]:
        """执行重新索引知识库任务"""
        params = task.params
        kb_id = params.get("kb_id")
        tenant_id = task.tenant_id
        
        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 创建索引器并执行重新索引操作
            logger.info(f"Reindex knowledge base task: {kb_id}, {tenant_id}")
            indexer = Indexer(db)
            result = loop.run_until_complete(
                indexer.reindex_knowledge_base(
                    kb_id=kb_id,
                    tenant_id=tenant_id
                )
            )
            logger.info(f"Reindex knowledge base result: {result}")
            return result
        finally:
            loop.close()
    
    # 任务管理方法
    
    def add_task(self, db: Session, task: Task) -> Task:
        """
        添加任务到队列
        
        Args:
            db: 数据库会话
            task: 任务对象
            
        Returns:
            添加的任务对象
        """
        db.add(task)
        db.commit()
        db.refresh(task)
        logger.info(f"Task added: {task.id}")
        return task
    
    def get_task(self, db: Session, task_id: str) -> Optional[Task]:
        """获取任务详情"""
        return db.query(Task).filter(Task.id == task_id).first()
    
    def get_tasks(
        self, 
        db: Session, 
        tenant_id: str,
        status: Optional[TaskStatus] = None,
        task_type: Optional[TaskType] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Task], int]:
        """获取任务列表"""
        query = db.query(Task).filter(Task.tenant_id == tenant_id)
        
        if status:
            query = query.filter(Task.status == status)
        
        if task_type:
            query = query.filter(Task.task_type == task_type)
        
        total = query.count()
        tasks = query.order_by(desc(Task.created_at)).offset(skip).limit(limit).all()
        
        return tasks, total
    
    def cancel_task(self, db: Session, task_id: str, tenant_id: str) -> bool:
        """取消任务"""
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.tenant_id == tenant_id,
            Task.status.in_([TaskStatus.PENDING, TaskStatus.RETRY])
        ).first()
        
        if not task:
            return False
        
        task.status = TaskStatus.FAILED
        task.error = "Task cancelled by user"
        task.completed_at = datetime.now(UTC)
        db.commit()
        
        return True
    
    def retry_task(self, db: Session, task_id: str, tenant_id: str) -> bool:
        """重试失败的任务"""
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.tenant_id == tenant_id,
            Task.status == TaskStatus.FAILED
        ).first()
        
        if not task:
            return False
        
        task.status = TaskStatus.RETRY
        task.retry_count = 0
        task.error = None
        task.updated_at = datetime.now(UTC)
        db.commit()
        
        return True
    
    def clean_completed_tasks(self, db: Session, days: int = 7) -> int:
        """清理完成的任务"""
        cutoff_date = datetime.now(UTC) - timedelta(days=days)
        
        deleted_count = db.query(Task).filter(
            Task.status.in_([TaskStatus.COMPLETED, TaskStatus.FAILED]),
            Task.completed_at < cutoff_date
        ).delete()
        
        db.commit()
        return deleted_count


# 创建单例实例
task_queue_service = TaskQueueService() 