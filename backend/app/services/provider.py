from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import json
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app.models.model import Provider, ProviderAccessCredential
from app.schemas.provider import (
    ProviderCreate, ProviderUpdate,
    AccessCredentialCreate, AccessCredentialUpdate
)

class ProviderService:
    """提供商服务类，提供提供商相关的业务逻辑"""
    
    @staticmethod
    def create_provider(db: Session, provider_create: ProviderCreate) -> Provider:
        """创建新提供商"""
        # 检查key是否已存在
        if db.query(Provider).filter(Provider.key == provider_create.key).first():
            raise ValueError(f"提供商标识 '{provider_create.key}' 已存在")
        
        # 创建提供商实例
        provider = Provider(
            name=provider_create.name,
            key=provider_create.key,
            provider_type=getattr(provider_create, 'provider_type', None),
            description=provider_create.description,
            icon=provider_create.icon,
            website=provider_create.website,
            api_base=getattr(provider_create, 'api_base', None),
            api_compatible=getattr(provider_create, 'api_compatible', None),
            auth_type=getattr(provider_create, 'auth_type', None),
            config_schema=provider_create.config_schema,
            is_active=True
        )
        
        db.add(provider)
        db.commit()
        db.refresh(provider)
        return provider
    
    @staticmethod
    def update_provider(db: Session, provider_id: str, provider_update: ProviderUpdate) -> Optional[Provider]:
        """更新提供商信息"""
        provider = db.query(Provider).filter(Provider.id == provider_id).first()
        if not provider:
            return None
        
        # 更新提供商属性
        if provider_update.name is not None:
            provider.name = provider_update.name
        if hasattr(provider_update, 'provider_type') and provider_update.provider_type is not None:
            provider.provider_type = provider_update.provider_type
        if provider_update.description is not None:
            provider.description = provider_update.description
        if provider_update.icon is not None:
            provider.icon = provider_update.icon
        if provider_update.website is not None:
            provider.website = provider_update.website
        if hasattr(provider_update, 'api_base') and provider_update.api_base is not None:
            provider.api_base = provider_update.api_base
        if hasattr(provider_update, 'api_compatible') and provider_update.api_compatible is not None:
            provider.api_compatible = provider_update.api_compatible
        if hasattr(provider_update, 'auth_type') and provider_update.auth_type is not None:
            provider.auth_type = provider_update.auth_type
        if provider_update.config_schema is not None:
            provider.config_schema = provider_update.config_schema
        if provider_update.is_active is not None:
            provider.is_active = provider_update.is_active
        
        db.commit()
        db.refresh(provider)
        return provider
    
    @staticmethod
    def get_provider(db: Session, provider_id: str) -> Optional[Provider]:
        """获取提供商详情"""
        return db.query(Provider).filter(Provider.id == provider_id).first()
    
    @staticmethod
    def get_provider_by_key(db: Session, key: str) -> Optional[Provider]:
        """通过标识获取提供商"""
        return db.query(Provider).filter(Provider.key == key).first()
    
    @staticmethod
    def list_providers(
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        name: Optional[str] = None,
        is_active: Optional[bool] = None,
        provider_type: Optional[str] = None
    ) -> Tuple[List[Provider], int]:
        """获取提供商列表"""
        query = db.query(Provider)
        
        # 应用过滤条件
        if name:
            query = query.filter(Provider.name.ilike(f"%{name}%"))
        if is_active is not None:
            query = query.filter(Provider.is_active == is_active)
        if provider_type:
            query = query.filter(Provider.provider_type == provider_type)
        
        # 获取总数
        total = query.count()
        
        # 应用分页并返回结果
        providers = query.order_by(Provider.created_at.desc()).offset(skip).limit(limit).all()
        return providers, total
    
    @staticmethod
    def delete_provider(db: Session, provider_id: str) -> bool:
        """删除提供商"""
        provider = db.query(Provider).filter(Provider.id == provider_id).first()
        if not provider:
            return False
        
        # 检查是否有依赖关系
        credentials_count = db.query(ProviderAccessCredential).filter(
            ProviderAccessCredential.provider_id == provider_id
        ).count()
        if credentials_count > 0:
            raise ValueError("该提供商已有访问凭证，无法删除")
        
        db.delete(provider)
        db.commit()
        return True
    
    @staticmethod
    def get_available_providers(
        db: Session,
        tenant_id: Optional[str] = None,
        provider_type: Optional[str] = None
    ) -> List[Provider]:
        """获取可用的提供商列表"""
        query = db.query(Provider).filter(Provider.is_active == True)
        
        # 根据提供商类型筛选
        if provider_type:
            query = query.filter(Provider.provider_type == provider_type)
        
        providers = query.order_by(Provider.name).all()
        return providers
    
    @staticmethod
    def get_enabled_providers_for_tenant(
        db: Session,
        tenant_id: str
    ) -> List[Dict[str, Any]]:
        """获取租户已启用的提供商配置列表"""
        # 查询租户已启用的提供商凭证
        tenant_credentials = db.query(ProviderAccessCredential).filter(
            ProviderAccessCredential.tenant_id == tenant_id,
            ProviderAccessCredential.is_active == True
        ).all()
        
        # 获取对应的提供商信息
        result = []
        for credential in tenant_credentials:
            provider = db.query(Provider).filter(
                Provider.id == credential.provider_id,
                Provider.is_active == True
            ).first()
            
            if provider:
                # 统计该配置下的模型数量
                from app.models.model import Model
                model_count = db.query(Model).filter(
                    Model.provider_access_credential_id == credential.id,
                    Model.is_active == True
                ).count()
                
                credential_info = {
                    "credential_id": credential.id,
                    "credential_name": credential.name,
                    "provider": {
                        "id": provider.id,
                        "name": provider.name,
                        "key": provider.key,
                        "provider_type": provider.provider_type.value if provider.provider_type else None,
                        "auth_type": provider.auth_type.value if provider.auth_type else None,
                        "description": provider.description,
                        "website": provider.website,
                        "icon": provider.icon,
                        "api_base": provider.api_base
                    },
                    "model_count": model_count,
                    "created_at": credential.created_at,
                    "updated_at": credential.updated_at
                }
                result.append(credential_info)
        
        return result


class AccessCredentialService:
    """访问凭证服务类，提供访问凭证相关的业务逻辑"""
    
    @staticmethod
    def create_credential(db: Session, credential_create: AccessCredentialCreate) -> ProviderAccessCredential:
        """创建新访问凭证"""
        # 检查租户是否存在
        from app.models.base import Tenant
        tenant = db.query(Tenant).filter(Tenant.id == credential_create.tenant_id).first()
        if not tenant:
            raise ValueError(f"租户ID '{credential_create.tenant_id}' 不存在")
        
        # 检查提供商是否存在
        provider = db.query(Provider).filter(Provider.id == credential_create.provider_id).first()
        if not provider:
            raise ValueError(f"提供商ID '{credential_create.provider_id}' 不存在")
        
        # 检查同一租户同一提供商下是否已存在相同名称的凭证
        existing_credential = db.query(ProviderAccessCredential).filter(
            ProviderAccessCredential.tenant_id == credential_create.tenant_id,
            ProviderAccessCredential.provider_id == credential_create.provider_id,
            ProviderAccessCredential.name == credential_create.name,
            ProviderAccessCredential.is_active == True
        ).first()
        
        if existing_credential:
            raise ValueError(f"该提供商下已存在名为 '{credential_create.name}' 的配置")
        
        # 创建访问凭证实例
        credential = ProviderAccessCredential(
            tenant_id=credential_create.tenant_id,
            provider_id=credential_create.provider_id,
            name=credential_create.name,
            credentials=credential_create.credentials,
            is_active=True
        )
        
        db.add(credential)
        db.commit()
        db.refresh(credential)
        return credential
    
    @staticmethod
    def update_credential(db: Session, credential_id: str, credential_update: AccessCredentialUpdate) -> Optional[ProviderAccessCredential]:
        """更新访问凭证信息"""
        credential = db.query(ProviderAccessCredential).filter(ProviderAccessCredential.id == credential_id).first()
        if not credential:
            return None
        
        # 更新访问凭证属性
        if credential_update.name is not None:
            credential.name = credential_update.name
        if credential_update.credentials is not None:
            credential.credentials = credential_update.credentials
        if credential_update.is_active is not None:
            credential.is_active = credential_update.is_active
        
        db.commit()
        db.refresh(credential)
        return credential
    
    @staticmethod
    def get_credential(db: Session, credential_id: str) -> Optional[ProviderAccessCredential]:
        """获取访问凭证详情"""
        return db.query(ProviderAccessCredential).filter(ProviderAccessCredential.id == credential_id).first()
    
    @staticmethod
    def get_credential_by_tenant_and_provider(
        db: Session, 
        tenant_id: str, 
        provider_id: str,
        credential_name: Optional[str] = None
    ) -> Optional[ProviderAccessCredential]:
        """根据租户和提供商获取凭证"""
        query = db.query(ProviderAccessCredential).filter(
            ProviderAccessCredential.tenant_id == tenant_id,
            ProviderAccessCredential.provider_id == provider_id,
            ProviderAccessCredential.is_active == True
        )
        
        if credential_name:
            query = query.filter(ProviderAccessCredential.name == credential_name)
        
        return query.first()
    
    @staticmethod
    def list_credentials(
        db: Session, 
        tenant_id: str,
        skip: int = 0, 
        limit: int = 100,
        provider_id: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> Tuple[List[ProviderAccessCredential], int]:
        """获取访问凭证列表"""
        query = db.query(ProviderAccessCredential).filter(ProviderAccessCredential.tenant_id == tenant_id)
        
        # 应用过滤条件
        if provider_id:
            query = query.filter(ProviderAccessCredential.provider_id == provider_id)
        if is_active is not None:
            query = query.filter(ProviderAccessCredential.is_active == is_active)
        
        # 获取总数
        total = query.count()
        
        # 应用分页并返回结果
        credentials = query.order_by(ProviderAccessCredential.created_at.desc()).offset(skip).limit(limit).all()
        return credentials, total
    
    @staticmethod
    def delete_credential(db: Session, credential_id: str) -> bool:
        """删除访问凭证（硬删除）"""
        credential = db.query(ProviderAccessCredential).filter(ProviderAccessCredential.id == credential_id).first()
        if not credential:
            return False
        
        db.delete(credential)
        db.commit()
        return True
    
    @staticmethod
    def disable_credential(db: Session, credential_id: str) -> bool:
        """禁用访问凭证（软删除）"""
        credential = db.query(ProviderAccessCredential).filter(ProviderAccessCredential.id == credential_id).first()
        if not credential:
            return False
        
        credential.is_active = False
        credential.updated_at = datetime.now()
        
        # 同时禁用相关的模型
        from app.models.model import Model
        models = db.query(Model).filter(
            Model.provider_access_credential_id == credential_id,
            Model.is_active == True
        ).all()
        
        for model in models:
            model.is_active = False
            model.updated_at = datetime.now()
        
        db.commit()
        return True
    
    @staticmethod
    def test_credential_connection(
        db: Session,
        credential_id: str,
        test_function: callable
    ) -> Dict[str, Any]:
        """测试凭证连接"""
        credential = db.query(ProviderAccessCredential).filter(
            ProviderAccessCredential.id == credential_id
        ).first()
        
        if not credential:
            return {
                "success": False,
                "message": "凭证不存在"
            }
        
        provider = db.query(Provider).filter(
            Provider.id == credential.provider_id
        ).first()
        
        if not provider:
            return {
                "success": False,
                "message": "提供商不存在"
            }
        
        try:
            # 调用测试函数
            result = test_function(provider, credential.credentials)
            return {
                "success": True,
                "message": "连接测试成功",
                "data": result
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"连接测试失败: {str(e)}"
            } 