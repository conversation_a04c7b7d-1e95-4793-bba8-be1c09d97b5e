from datetime import datetime, timedelta, UTC
import hashlib
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from fastapi import HTTPException, status

from app.models.base import User, Tenant, UserTenantAssociation
from app.schemas.user import (
    UserCreate, UserUpdate, PasswordReset, 
    UserTenantAssociationCreate, UserTenantAssociationUpdate,
    User as UserSchema, UserDetail as UserDetailSchema,
    UserListResponse, OperationResponse, UserTenantAssociationResponse,
    UserWithRole as UserWithRoleSchema, UserWithRoleListResponse
)
from app.schemas.auth import UserRegister, UserLogin, Token
from app.core.security import verify_password, get_password_hash, create_access_token, generate_verification_token
from app.core.roles import TenantRole, get_role_name, get_role_description, ROLE_PERMISSIONS

class UserService:
    def __init__(self, db: Session):
        self.db = db
    
    def _hash_password(self, password: str) -> str:
        """对密码进行哈希处理"""
        # 实际项目中应使用更安全的方式，如bcrypt
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码是否正确"""
        return self._hash_password(plain_password) == hashed_password
    
    async def verify_tenant_exists(self, tenant_id: str) -> None:
        """验证租户是否存在"""
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="租户不存在"
            )
    
    async def verify_role_exists(self, role_key: str) -> None:
        """验证角色是否存在"""
        if role_key not in TenantRole.__members__.values():
            raise HTTPException(
                status_code=404,
                detail=f"角色不存在: {role_key}"
            )
    
    async def get_users(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        username: Optional[str] = None,
        email: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> UserListResponse:
        """获取用户列表"""
        query = self.db.query(User)
        
        if username:
            query = query.filter(User.username.ilike(f"%{username}%"))
        
        if email:
            query = query.filter(User.email.ilike(f"%{email}%"))
            
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        total = query.count()
        users = query.order_by(desc(User.created_at)).offset(skip).limit(limit).all()
        
        # 将SQLAlchemy模型转换为可序列化的对象
        user_schemas = [
            UserSchema(
                id=user.id,
                username=user.username,
                email=user.email,
                display_name=user.display_name,
                is_active=user.is_active,
                last_login=user.last_login,
                created_at=user.created_at,
                extra=user.extra
            ) for user in users
        ]
        
        return UserListResponse(
            total=total,
            items=user_schemas
        )
    
    async def get_tenant_users(
        self, 
        tenant_id: str,
        skip: int = 0, 
        limit: int = 100, 
        username: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> UserWithRoleListResponse:
        """获取租户下的用户列表，包含角色信息"""
        await self.verify_tenant_exists(tenant_id)
        
        # 获取租户下的用户关联信息
        associations_query = self.db.query(UserTenantAssociation)\
            .filter(UserTenantAssociation.tenant_id == tenant_id)
        
        # 构建用户查询
        user_ids = [assoc.user_id for assoc in associations_query.all()]
        
        if not user_ids:
            return UserWithRoleListResponse(total=0, items=[])
        
        query = self.db.query(User).filter(User.id.in_(user_ids))
        
        if username:
            query = query.filter(User.username.ilike(f"%{username}%"))
            
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        total = query.count()
        users = query.order_by(desc(User.created_at)).offset(skip).limit(limit).all()
        
        # 获取用户的角色信息
        user_roles = {}
        for assoc in associations_query.all():
            user_roles[assoc.user_id] = assoc.role_key
        
        # 将SQLAlchemy模型转换为包含角色信息的对象
        user_with_role_schemas = [
            UserWithRoleSchema(
                id=user.id,
                username=user.username,
                email=user.email,
                display_name=user.display_name,
                is_active=user.is_active,
                last_login=user.last_login,
                created_at=user.created_at,
                extra=user.extra,
                role_key=user_roles.get(user.id, "member")  # 默认为member角色
            ) for user in users
        ]
        
        return UserWithRoleListResponse(
            total=total,
            items=user_with_role_schemas
        )
    
    async def get_user(self, user_id: str) -> UserSchema:
        """获取单个用户详情"""
        user = self.db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
            
        # 转换为Pydantic模型
        return UserSchema(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            is_active=user.is_active,
            last_login=user.last_login,
            created_at=user.created_at,
            extra=user.extra
        )
    
    async def get_tenant_user(self, tenant_id: str, user_id: str) -> UserWithRoleSchema:
        """获取租户中的用户详情，包含角色信息"""
        # 验证用户是否在租户中，并获取角色信息
        assoc = self.db.query(UserTenantAssociation).filter(
            UserTenantAssociation.tenant_id == tenant_id,
            UserTenantAssociation.user_id == user_id
        ).first()
        
        if not assoc:
            raise HTTPException(status_code=404, detail="用户不在该租户中")
        
        # 获取用户基本信息
        user = await self.get_user(user_id)
        
        # 返回包含角色信息的用户
        return UserWithRoleSchema(
            **user.dict(),
            role_key=assoc.role_key
        )
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """通过用户名获取用户"""
        return self.db.query(User).filter(User.username == username).first()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """通过邮箱获取用户"""
        return self.db.query(User).filter(User.email == email).first()
    
    async def get_user_detail(self, user_id: str) -> UserDetailSchema:
        """获取用户详情，包括所属租户和角色"""
        user = await self.get_user(user_id)
        
        # 查询用户的租户和角色信息
        associations = self.db.query(UserTenantAssociation)\
            .filter(UserTenantAssociation.user_id == user_id).all()
        
        # 查询租户和构建角色信息
        from app.schemas.tenant import Tenant as TenantSchema
        from app.schemas.role import Role as RoleSchema
        
        tenants_roles = []
        for assoc in associations:
            tenant = self.db.query(Tenant).filter(Tenant.id == assoc.tenant_id).first()
            role_key = assoc.role_key
            
            if tenant:
                # 转换租户为Pydantic模型
                tenant_schema = TenantSchema(
                    id=tenant.id,
                    name=tenant.name,
                    description=tenant.description,
                    is_personal=tenant.is_personal,
                    owner_id=tenant.owner_id,
                    created_at=tenant.created_at,
                    updated_at=tenant.updated_at
                )
                
                # 构建角色信息
                role_schema = RoleSchema(
                    key=role_key,
                    name=get_role_name(role_key),
                    description=get_role_description(role_key)
                )
                
                tenants_roles.append({
                    "user_id": user_id,
                    "tenant_id": tenant.id,
                    "role_key": role_key,
                    "tenant": tenant_schema,
                    "role": role_schema
                })
        
        return UserDetailSchema(
            **user.dict(),
            tenants=tenants_roles
        )
    
    async def register_user(self, user_data: UserRegister) -> Tuple[UserSchema, Dict[str, Any]]:
        """注册新用户并创建个人组织"""
        # 验证用户名和邮箱唯一性
        existing_username = await self.get_user_by_username(user_data.username)
        if existing_username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        existing_email = await self.get_user_by_email(user_data.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
        
        # 创建用户
        verification_token = generate_verification_token()
        token_expires = datetime.now(UTC) + timedelta(hours=48)
        
        user = User(
            username=user_data.username,
            email=user_data.email,
            password_hash=get_password_hash(user_data.password),
            display_name=user_data.display_name or user_data.username,
            is_active=True,
            is_verified=False,
            verification_token=verification_token,
            verification_token_expires=token_expires,
            created_at=datetime.now(UTC)
        )
        
        self.db.add(user)
        self.db.flush()  # 获取用户ID但不提交事务
        
        # 创建个人组织
        personal_tenant = Tenant(
            name=f"{user_data.username}的个人空间",
            description="个人工作空间",
            is_personal=True,
            owner_id=user.id,
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC)
        )
        
        self.db.add(personal_tenant)
        self.db.flush()
        
        # 将用户添加到个人组织，使用拥有者角色
        user_tenant_association = UserTenantAssociation(
            user_id=user.id,
            tenant_id=personal_tenant.id,
            role_key=TenantRole.OWNER
        )
        
        self.db.add(user_tenant_association)
        self.db.commit()
        self.db.refresh(user)
        self.db.refresh(personal_tenant)
        
        # TODO: 发送验证邮件
        
        # 转换为Pydantic模型
        user_schema = UserSchema(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            is_active=user.is_active,
            last_login=user.last_login,
            created_at=user.created_at,
            extra=user.extra
        )
        
        tenant_dict = {
            "id": personal_tenant.id,
            "name": personal_tenant.name,
            "description": personal_tenant.description,
            "is_personal": personal_tenant.is_personal,
            "owner_id": personal_tenant.owner_id,
            "created_at": personal_tenant.created_at,
            "updated_at": personal_tenant.updated_at
        }
        
        return user_schema, tenant_dict
    
    async def login(self, login_data: UserLogin) -> Token:
        """用户登录"""
        # 根据用户名或邮箱查找用户
        user = None
        
        # 首先尝试作为用户名查找
        if login_data.username:
            user = await self.get_user_by_username(login_data.username)
            
        # 如果没找到，尝试作为邮箱查找
        if not user and login_data.username and '@' in login_data.username:
            email = login_data.username
            user = await self.get_user_by_email(email)
        
        # 最后尝试email字段（如果存在）
        if not user and login_data.email:
            user = await self.get_user_by_email(login_data.email)
            
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
            
        # 验证密码
        if not verify_password(login_data.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
            
        # 验证用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户已禁用"
            )
            
        # 更新最后登录时间
        user.last_login = datetime.now(UTC)
        self.db.commit()
        
        # 获取用户的第一个租户ID
        tenant_association = self.db.query(UserTenantAssociation)\
            .filter(UserTenantAssociation.user_id == user.id).first()
            
        tenant_id = tenant_association.tenant_id if tenant_association else None
        
        # 创建访问令牌
        access_token, expires_at = create_access_token(
            subject=user.id,
            tenant_id=tenant_id
        )
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_at=expires_at,
            user_id=user.id,
            username=user.username
        )
    
    async def create_user(self, user_data: UserCreate) -> UserSchema:
        """创建新用户"""
        # 检查用户名是否已存在
        existing_username = self.db.query(User).filter(User.username == user_data.username).first()
        
        if existing_username:
            raise HTTPException(status_code=400, detail="用户名已存在")
        
        # 检查邮箱是否已存在
        existing_email = self.db.query(User).filter(User.email == user_data.email).first()
        
        if existing_email:
            raise HTTPException(status_code=400, detail="邮箱已存在")
        
        # 创建用户
        user = User(
            username=user_data.username,
            email=user_data.email,
            password_hash=get_password_hash(user_data.password),
            display_name=user_data.display_name or user_data.username,
            is_active=user_data.is_active,
            created_at=datetime.now(UTC),
            extra=user_data.extra
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        # 转换为Pydantic模型
        return UserSchema(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            is_active=user.is_active,
            last_login=user.last_login,
            created_at=user.created_at,
            extra=user.extra
        )
    
    async def update_user(self, user_id: str, user_data: UserUpdate) -> UserSchema:
        """更新用户信息"""
        user = self.db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 如果要更新用户名，检查是否已存在
        if user_data.username and user_data.username != user.username:
            existing = self.db.query(User).filter(
                User.username == user_data.username,
                User.id != user_id
            ).first()
            
            if existing:
                raise HTTPException(status_code=400, detail="用户名已存在")
        
        # 如果要更新邮箱，检查是否已存在
        if user_data.email and user_data.email != user.email:
            existing = self.db.query(User).filter(
                User.email == user_data.email,
                User.id != user_id
            ).first()
            
            if existing:
                raise HTTPException(status_code=400, detail="邮箱已存在")
        
        # 更新非None字段
        for key, value in user_data.dict(exclude_unset=True).items():
            setattr(user, key, value)
        
        self.db.commit()
        self.db.refresh(user)
        
        # 转换为Pydantic模型
        return UserSchema(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            is_active=user.is_active,
            last_login=user.last_login,
            created_at=user.created_at,
            extra=user.extra
        )
    
    async def reset_password(self, user_id: str, password_data: PasswordReset) -> OperationResponse:
        """重置用户密码"""
        user = self.db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 验证原密码
        if not verify_password(password_data.current_password, user.password_hash):
            raise HTTPException(status_code=400, detail="原密码不正确")
        
        # 更新密码
        user.password_hash = get_password_hash(password_data.new_password)
        
        self.db.commit()
        
        return OperationResponse(success=True, message="密码已更新")
    
    async def delete_user(self, user_id: str) -> OperationResponse:
        """删除用户"""
        user = self.db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 删除用户与租户的关联
        self.db.query(UserTenantAssociation).filter(
            UserTenantAssociation.user_id == user_id
        ).delete()
        
        # 删除用户
        self.db.delete(user)
        self.db.commit()
        
        return OperationResponse(success=True, message="用户已删除")
    
    async def add_user_to_tenant(self, association_data: UserTenantAssociationCreate) -> UserTenantAssociationResponse:
        """将用户添加到租户"""
        # 验证数据
        user = self.db.query(User).filter(User.id == association_data.user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
            
        await self.verify_tenant_exists(association_data.tenant_id)
        await self.verify_role_exists(association_data.role_key)
        
        # 检查是否已存在
        existing = self.db.query(UserTenantAssociation).filter(
            UserTenantAssociation.user_id == association_data.user_id,
            UserTenantAssociation.tenant_id == association_data.tenant_id
        ).first()
        
        if existing:
            raise HTTPException(status_code=400, detail="用户已在该租户中")
        
        # 创建关联
        association = UserTenantAssociation(
            user_id=association_data.user_id,
            tenant_id=association_data.tenant_id,
            role_key=association_data.role_key
        )
        
        self.db.add(association)
        self.db.commit()
        self.db.refresh(association)
        
        # 返回响应模型
        return UserTenantAssociationResponse(
            user_id=association.user_id,
            tenant_id=association.tenant_id,
            role_key=association.role_key,
            success=True,
            message="用户已添加到租户"
        )
    
    async def update_user_role(
        self, user_id: str, tenant_id: str, role_data: UserTenantAssociationUpdate
    ) -> UserTenantAssociationResponse:
        """更新用户在租户中的角色"""
        # 验证数据
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
            
        await self.verify_tenant_exists(tenant_id)
        await self.verify_role_exists(role_data.role_key)
        
        # 检查关联是否存在
        association = self.db.query(UserTenantAssociation).filter(
            UserTenantAssociation.user_id == user_id,
            UserTenantAssociation.tenant_id == tenant_id
        ).first()
        
        if not association:
            raise HTTPException(status_code=404, detail="用户不在该租户中")
        
        # 更新角色
        association.role_key = role_data.role_key
        
        self.db.commit()
        self.db.refresh(association)
        
        # 返回响应模型
        return UserTenantAssociationResponse(
            user_id=association.user_id,
            tenant_id=association.tenant_id,
            role_key=association.role_key,
            success=True,
            message="用户角色已更新"
        )
    
    async def remove_user_from_tenant(self, user_id: str, tenant_id: str) -> OperationResponse:
        """将用户从租户中移除"""
        # 验证数据
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
            
        await self.verify_tenant_exists(tenant_id)
        
        # 查找关联
        association = self.db.query(UserTenantAssociation).filter(
            UserTenantAssociation.user_id == user_id,
            UserTenantAssociation.tenant_id == tenant_id
        ).first()
        
        if not association:
            raise HTTPException(status_code=404, detail="用户不在该租户中")
        
        # 检查是否为个人租户
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        if tenant.is_personal and tenant.owner_id == user_id:
            raise HTTPException(status_code=400, detail="不能从个人组织中移除用户")
            
        # 删除关联
        self.db.delete(association)
        self.db.commit()
        
        return OperationResponse(success=True, message="用户已从租户中移除")
    
    async def update_user_status(self, user_id: str, is_active: bool) -> Dict[str, Any]:
        """
        更新用户状态（超级管理员专用）
        """
        user = self.db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 更新状态
        user.is_active = is_active
        self.db.commit()
        self.db.refresh(user)
        
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "display_name": user.display_name,
            "is_active": user.is_active,
            "created_at": user.created_at
        }
    
    async def set_super_admin(self, user_id: str, is_super_admin: bool) -> Dict[str, Any]:
        """
        设置或移除超级管理员权限（超级管理员专用）
        """
        user = self.db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 初始化extra字段（如果不存在）
        if not user.extra:
            user.extra = {}
        
        # 更新平台角色
        if is_super_admin:
            user.extra["platform_role"] = "super_admin"
        else:
            if "platform_role" in user.extra:
                del user.extra["platform_role"]
        
        self.db.commit()
        
        return {
            "success": True,
            "message": f"已{'设置' if is_super_admin else '移除'}超级管理员权限",
            "user_id": user_id
        } 