from typing import List, Optional, <PERSON><PERSON>
import json
from sqlalchemy.orm import Session

from app.models.model import Provider, Model, ProviderAccessCredential
from app.schemas.model import (
    ModelCreate, ModelUpdate, Model as ModelSchema
)

class ModelService:
    """模型服务类，提供模型相关的业务逻辑"""
    
    @staticmethod
    def get_model_info(db: Session, model_id: str, tenant_id: str) -> Optional[Tuple[Model, Provider, ProviderAccessCredential]]:
        """
        获取模型的完整信息，包括模型、提供商和访问凭证
        
        Args:
            db: 数据库会话
            model_id: 模型ID
            tenant_id: 租户ID
            
        Returns:
            (Model, Provider, ProviderAccessCredential) 元组，如果获取失败返回None
        """
        try:
            # 获取模型信息
            model = db.query(Model).filter(
                Model.id == model_id,
                Model.is_active == True
            ).first()
            
            if not model:
                return None
            
            # 获取访问凭证信息
            credential = db.query(ProviderAccessCredential).filter(
                ProviderAccessCredential.id == model.provider_access_credential_id,
                ProviderAccessCredential.is_active == True
            ).first()
            
            if not credential:
                return None
            
            # 如果凭证不属于当前租户，查找租户下该提供商的任意可用凭证
            if credential.tenant_id != tenant_id:
                credential = db.query(ProviderAccessCredential).filter(
                    ProviderAccessCredential.tenant_id == tenant_id,
                    ProviderAccessCredential.provider_id == credential.provider_id,
                    ProviderAccessCredential.is_active == True
                ).first()
                
                if not credential:
                    return None
            
            # 获取提供商信息
            provider = db.query(Provider).filter(
                Provider.id == credential.provider_id,
                Provider.is_active == True
            ).first()
            
            if not provider:
                return None
                
            return (model, provider, credential)
            
        except Exception:
            return None
    
    @staticmethod
    def create_model(db: Session, model_create: ModelCreate, tenant_id: Optional[str] = None) -> Model:
        """创建新模型"""
        # 检查访问凭证是否存在
        if not model_create.provider_access_credential_id:
            raise ValueError("必须提供提供商访问凭证ID")
            
        credential = db.query(ProviderAccessCredential).filter(
            ProviderAccessCredential.id == model_create.provider_access_credential_id
        ).first()
        
        if not credential:
            raise ValueError(f"提供商访问凭证ID '{model_create.provider_access_credential_id}' 不存在")
        
        # 如果有租户ID，需要确保凭证属于该租户
        if tenant_id and credential.tenant_id != tenant_id:
            raise ValueError(f"提供商访问凭证不属于该租户")
        
        # 使用凭证中的租户ID
        actual_tenant_id = tenant_id or credential.tenant_id
        
        # 检查key是否已存在（在同一租户下）
        existing_model = db.query(Model).filter(
            Model.key == model_create.key,
            Model.tenant_id == actual_tenant_id
        ).first()
            
        if existing_model:
            raise ValueError(f"模型标识 '{model_create.key}' 在该租户下已存在")
        
        # 从凭证获取提供商ID
        provider_id = credential.provider_id
        
        # 创建模型实例
        model_data = {
            "tenant_id": actual_tenant_id,
            "provider_id": provider_id,
            "provider_access_credential_id": model_create.provider_access_credential_id,
            "name": model_create.name,
            "key": model_create.key,
            "model_type": model_create.model_type,
            "extra_config": model_create.extra_config,
            "is_active": True
        }
            
        model = Model(**model_data)
        
        db.add(model)
        db.commit()
        db.refresh(model)
        return model
    
    @staticmethod
    def update_model(db: Session, model_id: str, model_update: ModelUpdate, tenant_id: Optional[str] = None) -> Optional[Model]:
        """更新模型信息"""
        # 构建查询
        query = db.query(Model).filter(Model.id == model_id)
        
        if tenant_id:
            query = query.filter(Model.tenant_id == tenant_id)
            
        model = query.first()
        if not model:
            return None
        
        # 检查访问凭证是否存在（如果要更新）
        if model_update.provider_access_credential_id:
            credential = db.query(ProviderAccessCredential).filter(
                ProviderAccessCredential.id == model_update.provider_access_credential_id
            ).first()
            
            if not credential:
                raise ValueError(f"提供商访问凭证ID '{model_update.provider_access_credential_id}' 不存在")
            
            # 如果有租户ID，需要确保凭证属于该租户
            if tenant_id and credential.tenant_id != tenant_id:
                raise ValueError(f"提供商访问凭证不属于该租户")
        
        # 更新模型属性
        if model_update.provider_access_credential_id is not None:
            # 更新凭证ID时，同时更新provider_id
            credential = db.query(ProviderAccessCredential).filter(
                ProviderAccessCredential.id == model_update.provider_access_credential_id
            ).first()
            if credential:
                model.provider_access_credential_id = model_update.provider_access_credential_id
                model.provider_id = credential.provider_id
        if model_update.name is not None:
            model.name = model_update.name
        if model_update.model_type is not None:
            model.model_type = model_update.model_type
        if model_update.extra_config is not None:
            model.extra_config = model_update.extra_config
        if model_update.is_active is not None:
            model.is_active = model_update.is_active
        
        db.commit()
        db.refresh(model)
        return model
    
    @staticmethod
    def get_model(db: Session, model_id: str, tenant_id: Optional[str] = None) -> Optional[Model]:
        """获取模型详情"""
        query = db.query(Model).filter(Model.id == model_id)
        
        if tenant_id:
            query = query.filter(Model.tenant_id == tenant_id)
            
        return query.first()
    
    @staticmethod
    def get_model_by_key(db: Session, key: str, tenant_id: Optional[str] = None) -> Optional[Model]:
        """通过标识获取模型"""
        query = db.query(Model).filter(Model.key == key)
        
        if tenant_id:
            query = query.filter(Model.tenant_id == tenant_id)
            
        return query.first()
    
    @staticmethod
    def list_models(
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        name: Optional[str] = None,
        provider_id: Optional[str] = None,
        model_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        tenant_id: Optional[str] = None
    ) -> Tuple[List[ModelSchema], int]:
        """获取模型列表"""
        query = db.query(Model)
        
        # 应用过滤条件
        if name:
            query = query.filter(Model.name.ilike(f"%{name}%"))
        if provider_id:
            query = query.filter(Model.provider_id == provider_id)
        if model_type:
            query = query.filter(Model.model_type == model_type)
        if is_active is not None:
            query = query.filter(Model.is_active == is_active)
        if tenant_id:
            query = query.filter(Model.tenant_id == tenant_id)
        
        # 获取总数
        total = query.count()
        
        # 应用分页并返回结果
        models = query.order_by(Model.created_at.desc()).offset(skip).limit(limit).all()
        
        # 获取提供商信息
        result = []
        # 创建提供商ID到提供商的映射
        provider_ids = [model.provider_id for model in models if model.provider_id]
        provider_map = {}
        
        if provider_ids:
            providers = db.query(Provider).filter(Provider.id.in_(provider_ids)).all()
            provider_map = {provider.id: provider for provider in providers}
        
        # 构建结果
        for model in models:
            # 准备提供商信息
            provider_info = None
            if model.provider_id and model.provider_id in provider_map:
                provider = provider_map[model.provider_id]
                from app.schemas.provider import ProviderReference
                provider_info = ProviderReference(
                    id=provider.id,
                    name=provider.name,
                    key=provider.key
                )
            
            # 创建 Model schema 对象
            model_schema = ModelSchema(
                id=model.id,
                tenant_id=model.tenant_id,
                provider_id=model.provider_id,
                provider_access_credential_id=model.provider_access_credential_id,
                name=model.name,
                key=model.key,
                model_type=model.model_type,
                description=model.description if hasattr(model, "description") else None,
                is_active=model.is_active,
                extra_config=model.extra_config if hasattr(model, "extra_config") else None,
                provider=provider_info
            )
            
            result.append(model_schema)
            
        return result, total
    
    @staticmethod
    def delete_model(db: Session, model_id: str, tenant_id: Optional[str] = None) -> bool:
        """
        删除模型
        
        Args:
            db: 数据库会话
            model_id: 模型ID
            tenant_id: 租户ID
        """
        query = db.query(Model).filter(Model.id == model_id)
        
        if tenant_id:
            query = query.filter(Model.tenant_id == tenant_id)
            
        model = query.first()
        if not model:
            return False
        
        # 检查是否有 agent_version 引用了这个模型
        from app.models.agent import AgentVersion
        agent_versions = db.query(AgentVersion).filter(AgentVersion.model_id == model_id).all()
        
        if agent_versions:
            # 分离当前版本和历史版本
            current_agent_names = []
            historical_versions = []
            
            for agent_version in agent_versions:
                if agent_version.is_current:
                    # 当前版本，收集用于错误提示
                    if agent_version.agent and agent_version.agent.name:
                        current_agent_names.append(f"{agent_version.agent.name} (版本 {agent_version.version_number})")
                else:
                    # 历史版本，可以直接清理
                    historical_versions.append(agent_version)
            
            # 如果有当前版本正在使用，不允许删除
            if current_agent_names:
                raise ValueError(f"无法删除模型，以下Agent的当前版本正在使用该模型：{', '.join(current_agent_names[:3])}{'等' if len(current_agent_names) > 3 else ''}。请先为这些Agent选择其他模型。")
            
            # 清理历史版本的model_id引用
            for historical_version in historical_versions:
                historical_version.model_id = None
            
            # 提交历史版本的更改
            if historical_versions:
                db.commit()
        
        # 删除模型
        db.delete(model)
        db.commit()
        return True

 