from typing import Dict, Any
from urllib.parse import urljoin

from app.core.config import settings
from app.models.base import TenantInvitation, Tenant


class InvitationService:
    """邀请链接服务"""
    
    @staticmethod
    def generate_invitation_link(
        invitation: TenantInvitation, 
        tenant: Tenant = None
    ) -> str:
        """
        生成邀请链接
        
        Args:
            invitation: 邀请对象
            tenant: 租户对象（可选，用于更丰富的链接信息）
        
        Returns:
            完整的前端邀请链接
        """
        # 构建邀请链接路径
        invitation_path = f"/invitations/accept?token={invitation.invitation_token}"
        
        # 如果有租户信息，可以添加更多参数
        if tenant:
            invitation_path += f"&tenant_id={tenant.id}&tenant_name={tenant.name}"
        
        # 生成完整的前端URL
        invitation_url = urljoin(settings.FRONTEND_URL, invitation_path)
        
        return invitation_url
    
    @staticmethod
    def create_invitation_info(
        invitation: TenantInvitation, 
        tenant: Tenant = None,
        inviter_name: str = None
    ) -> Dict[str, Any]:
        """
        创建邀请信息（为构建响应模型准备数据）
        
        Args:
            invitation: 邀请对象
            tenant: 租户对象
            inviter_name: 邀请人姓名
        
        Returns:
            结构化的邀请信息字典
        """
        invitation_link = InvitationService.generate_invitation_link(invitation, tenant)
        
        # 构建租户信息
        tenant_info = None
        if tenant:
            tenant_info = {
                "id": tenant.id,
                "name": tenant.name
            }
        
        return {
            "id": invitation.id,
            "token": invitation.invitation_token,
            "email": invitation.invitee_email,
            "role_key": invitation.role_key,
            "expires_at": invitation.expires_at,
            "invitation_link": invitation_link,
            "tenant_info": tenant_info,
            "inviter_name": inviter_name,
            "status": invitation.status
        }
    
    @staticmethod
    def generate_email_template_data(
        invitation: TenantInvitation,
        tenant: Tenant,
        inviter_name: str
    ) -> Dict[str, Any]:
        """
        为邮件模板生成数据
        
        Args:
            invitation: 邀请对象
            tenant: 租户对象
            inviter_name: 邀请人姓名
            
        Returns:
            邮件模板数据
        """
        invitation_link = InvitationService.generate_invitation_link(invitation, tenant)
        
        return {
            "invitee_email": invitation.invitee_email,
            "inviter_name": inviter_name,
            "tenant_name": tenant.name,
            "tenant_description": tenant.description,
            "role_key": invitation.role_key,
            "invitation_link": invitation_link,
            "expires_at": invitation.expires_at,
            "platform_name": "云知问",
            "platform_url": settings.FRONTEND_URL
        } 