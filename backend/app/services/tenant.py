from datetime import datetime, UTC, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from fastapi import HTTPException, status

from app.models.base import Tenant, User, UserTenantAssociation, TenantInvitation
from app.core.roles import TenantRole
from app.models.agent import Agent
from app.schemas.tenant import TenantCreate, TenantUpdate
from app.schemas.auth import InviteUserRequest, AcceptInvitationRequest
from app.services.invitation import InvitationService

class TenantService:
    def __init__(self, db: Session):
        self.db = db
    
    async def get_tenants(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        name: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取租户列表"""
        query = self.db.query(Tenant)
        
        if name:
            query = query.filter(Tenant.name.ilike(f"%{name}%"))
        
        total = query.count()
        tenants_db = query.order_by(desc(Tenant.created_at)).offset(skip).limit(limit).all()
        
        # 将SQLAlchemy模型转换为字典
        tenants = [
            {
                "id": tenant.id,
                "name": tenant.name,
                "description": tenant.description,
                "is_personal": tenant.is_personal,
                "created_at": tenant.created_at,
                "updated_at": tenant.updated_at
            }
            for tenant in tenants_db
        ]
        
        return {
            "total": total,
            "items": tenants
        }
    
    async def get_user_tenants(
        self,
        user_id: str,
        skip: int = 0,
        limit: int = 100,
        name: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取用户所属的所有租户"""
        # 获取用户所属租户ID
        tenant_ids = self.db.query(UserTenantAssociation.tenant_id)\
            .filter(UserTenantAssociation.user_id == user_id).all()
        tenant_ids = [t[0] for t in tenant_ids]
        
        # 构建查询
        query = self.db.query(Tenant).filter(Tenant.id.in_(tenant_ids))
        
        if name:
            query = query.filter(Tenant.name.ilike(f"%{name}%"))
        
        total = query.count()
        tenants_db = query.order_by(desc(Tenant.created_at)).offset(skip).limit(limit).all()
        
        # 将SQLAlchemy模型转换为字典
        tenants = [
            {
                "id": tenant.id,
                "name": tenant.name,
                "description": tenant.description,
                "is_personal": tenant.is_personal,
                "created_at": tenant.created_at,
                "updated_at": tenant.updated_at
            }
            for tenant in tenants_db
        ]
        
        return {
            "total": total,
            "items": tenants
        }
    
    async def get_tenant(self, tenant_id: int) -> Dict[str, Any]:
        """获取单个租户详情"""
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        
        if not tenant:
            raise HTTPException(status_code=404, detail="租户不存在")
        
        # 将SQLAlchemy模型转换为字典
        return {
            "id": tenant.id,
            "name": tenant.name,
            "description": tenant.description,
            "is_personal": tenant.is_personal,
            "created_at": tenant.created_at,
            "updated_at": tenant.updated_at
        }
    
    async def get_tenant_with_stats(self, tenant_id: int) -> Dict[str, Any]:
        """获取租户详情，包括统计信息"""
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        
        if not tenant:
            raise HTTPException(status_code=404, detail="租户不存在")
        
        # 用户数量
        user_count = self.db.query(func.count(UserTenantAssociation.user_id))\
            .filter(UserTenantAssociation.tenant_id == tenant_id).scalar() or 0
        
        # Agent数量
        agent_count = self.db.query(func.count(Agent.id))\
            .filter(Agent.tenant_id == tenant_id).scalar() or 0
        
        # 返回字典而不是直接返回SQLAlchemy模型
        return {
            "id": tenant.id,
            "name": tenant.name,
            "description": tenant.description,
            "is_personal": tenant.is_personal,
            "created_at": tenant.created_at,
            "updated_at": tenant.updated_at,
            "user_count": user_count,
            "agent_count": agent_count
        }
    
    async def create_tenant(self, tenant_data: TenantCreate, user_id: int) -> Tenant:
        """创建新租户/组织"""
        # 检查名称是否已存在
        existing = self.db.query(Tenant).filter(Tenant.name == tenant_data.name).first()
        
        if existing:
            raise HTTPException(status_code=400, detail="组织名称已存在")
        
        # 创建租户
        tenant = Tenant(
            name=tenant_data.name,
            description=tenant_data.description,
            is_personal=False,  # 非个人组织
            owner_id=user_id,   # 设置创建者为拥有者
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(tenant)
        self.db.flush()  # 获取ID但不提交事务
        
        
        # 将创建者添加为拥有者
        user_tenant_association = UserTenantAssociation(
            user_id=user_id,
            tenant_id=tenant.id,
            role_key=TenantRole.OWNER.value
        )
        
        self.db.add(user_tenant_association)
        self.db.commit()
        self.db.refresh(tenant)
        
        return tenant
    
    async def update_tenant(self, tenant_id: str, tenant_data: TenantUpdate, user_id: str) -> Dict[str, Any]:
        """更新租户信息"""
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        
        if not tenant:
            raise HTTPException(status_code=404, detail="组织不存在")
            
        # 检查权限：只有拥有者可以修改组织信息
        is_owner = self.db.query(UserTenantAssociation)\
            .filter(
                UserTenantAssociation.user_id == user_id,
                UserTenantAssociation.tenant_id == tenant_id,
                UserTenantAssociation.role_key == TenantRole.OWNER.value
            ).first()
            
        if not is_owner:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, 
                detail="只有组织拥有者可以修改组织信息"
            )
        
        # 如果要更新名称，检查名称是否已存在
        if tenant_data.name and tenant_data.name != tenant.name:
            existing = self.db.query(Tenant).filter(
                Tenant.name == tenant_data.name,
                Tenant.id != tenant_id
            ).first()
            
            if existing:
                raise HTTPException(status_code=400, detail="组织名称已存在")
        
        # 更新非None字段
        for key, value in tenant_data.dict(exclude_unset=True).items():
            setattr(tenant, key, value)
        
        # 更新时间
        tenant.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(tenant)
        
        # 将SQLAlchemy模型转换为字典返回
        return {
            "id": tenant.id,
            "name": tenant.name,
            "description": tenant.description,
            "is_personal": tenant.is_personal,
            "created_at": tenant.created_at,
            "updated_at": tenant.updated_at
        }
    
    async def delete_tenant(self, tenant_id: str, user_id: str) -> Dict[str, Any]:
        """删除租户"""
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        
        if not tenant:
            raise HTTPException(status_code=404, detail="组织不存在")
            
        # 不能删除个人组织
        if tenant.is_personal:
            raise HTTPException(status_code=400, detail="不能删除个人组织")
            
        # 检查权限：只有拥有者可以删除组织
        is_owner = self.db.query(UserTenantAssociation)\
            .filter(
                UserTenantAssociation.user_id == user_id,
                UserTenantAssociation.tenant_id == tenant_id,
                UserTenantAssociation.role_key == TenantRole.OWNER.value
            ).first()
            
        if not is_owner:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, 
                detail="只有组织拥有者可以删除组织"
            )
        
     
        # 删除用户与租户的关联
        self.db.query(UserTenantAssociation).filter(
            UserTenantAssociation.tenant_id == tenant_id
        ).delete()
        
        
        # 删除租户
        self.db.delete(tenant)
        self.db.commit()
        
        return {"success": True, "message": "组织已删除"}
    
    async def invite_user(
        self, 
        tenant_id: str, 
        invite_data: InviteUserRequest, 
        inviter_id: str
    ) -> Dict[str, Any]:
        """邀请用户加入租户（增强版，包含邀请链接）"""
        # 检查租户是否存在
        tenant_dict = await self.get_tenant(tenant_id)
        # 重新获取完整的租户对象
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        
        # 检查邀请人是否有权限邀请
        inviter_assoc = self.db.query(UserTenantAssociation)\
            .filter(
                UserTenantAssociation.user_id == inviter_id,
                UserTenantAssociation.tenant_id == tenant_id,
                (UserTenantAssociation.role_key == TenantRole.OWNER.value) | (UserTenantAssociation.role_key == TenantRole.ADMIN.value)  # 拥有者或管理员
            ).first()
            
        if not inviter_assoc:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="您没有权限邀请用户加入此组织"
            )
            
        # 获取邀请人信息
        inviter = self.db.query(User).filter(User.id == inviter_id).first()
        inviter_name = inviter.display_name or inviter.username if inviter else "未知用户"
            
        # 检查角色是否存在
        role_key = invite_data.role_key
        if role_key not in [role.value for role in TenantRole]:
            raise HTTPException(status_code=400, detail="指定的角色不存在")
        
        # 检查被邀请的邮箱是否已在组织中
        existing_user = self.db.query(User).filter(User.email == invite_data.email).first()
        
        if existing_user:
            # 检查用户是否已在租户中
            existing_assoc = self.db.query(UserTenantAssociation).filter(
                UserTenantAssociation.user_id == existing_user.id,
                UserTenantAssociation.tenant_id == tenant_id
            ).first()
            
            if existing_assoc:
                raise HTTPException(status_code=400, detail="该用户已在组织中")
                
        # 检查是否已有未过期的邀请
        existing_invitations = self.db.query(TenantInvitation).filter(
            TenantInvitation.tenant_id == tenant_id,
            TenantInvitation.invitee_email == invite_data.email,
            TenantInvitation.status == "pending"
        ).all()
        
        # 检查是否有有效的邀请
        valid_invitation = None
        for inv in existing_invitations:
            if inv.is_valid:
                valid_invitation = inv
                break
        
        if valid_invitation:
            raise HTTPException(status_code=400, detail="已向该邮箱发送过邀请，请等待回应或邀请过期")
            
        # 创建邀请
        invitation = TenantInvitation(
            tenant_id=tenant_id,
            inviter_id=inviter_id,
            invitee_email=invite_data.email,
            role_key=invite_data.role_key,
            created_at=datetime.now(UTC),
            expires_at=datetime.now(UTC) + timedelta(days=7)
        )
        
        self.db.add(invitation)
        self.db.commit()
        self.db.refresh(invitation)
        
        # 使用邀请服务生成完整的邀请信息
        invitation_info = InvitationService.create_invitation_info(
            invitation=invitation,
            tenant=tenant,
            inviter_name=inviter_name
        )
        
        # TODO: 发送邀请邮件
        # email_data = InvitationService.generate_email_template_data(invitation, tenant, inviter_name)
        # await send_invitation_email(email_data)
        
        return {
            "message": "邀请已发送",
            "invitation": invitation_info
        }
        
    async def accept_invitation(
        self, 
        accept_data: AcceptInvitationRequest, 
        user_id: str
    ) -> Dict[str, Any]:
        """接受邀请"""
        # 查询邀请
        invitation = self.db.query(TenantInvitation).filter(
            TenantInvitation.invitation_token == accept_data.invitation_token,
            TenantInvitation.status == "pending"
        ).first()
        
        if not invitation:
            raise HTTPException(status_code=404, detail="邀请不存在或已失效")
            
        # 检查邀请是否过期
        if not invitation.is_valid:
            invitation.status = "expired"
            self.db.commit()
            raise HTTPException(status_code=400, detail="邀请已过期")
            
        # 获取用户信息
        user = self.db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
            
        # 验证用户邮箱是否匹配邀请邮箱
        if user.email.lower() != invitation.invitee_email.lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="此邀请不是发送给您的"
            )
            
        # 检查用户是否已在租户中
        existing_assoc = self.db.query(UserTenantAssociation).filter(
            UserTenantAssociation.user_id == user_id,
            UserTenantAssociation.tenant_id == invitation.tenant_id
        ).first()
        
        if existing_assoc:
            # 更新邀请状态
            invitation.status = "accepted"
            self.db.commit()
            
            return {
                "success": True, 
                "message": "您已经是组织成员", 
                "tenant_id": invitation.tenant_id
            }
        
        # 创建用户与租户的关联
        association = UserTenantAssociation(
            user_id=user_id,
            tenant_id=invitation.tenant_id,
            role_key=invitation.role_key
        )
        
        # 更新邀请状态
        invitation.status = "accepted"
        
        self.db.add(association)
        self.db.commit()
        
        return {
            "success": True, 
            "message": "已成功加入组织", 
            "tenant_id": invitation.tenant_id
        }

    async def get_invitation_details(self, invitation_token: str) -> Dict[str, Any]:
        """获取邀请详情（供前端显示邀请信息）"""
        # 查询邀请
        invitation = self.db.query(TenantInvitation).filter(
            TenantInvitation.invitation_token == invitation_token
        ).first()
        
        if not invitation:
            raise HTTPException(status_code=404, detail="邀请不存在")
        
        # 获取租户信息
        tenant = self.db.query(Tenant).filter(Tenant.id == invitation.tenant_id).first()
        if not tenant:
            raise HTTPException(status_code=404, detail="租户不存在")
        
        # 获取邀请人信息
        inviter = self.db.query(User).filter(User.id == invitation.inviter_id).first()
        inviter_name = inviter.display_name or inviter.username if inviter else "未知用户"
        
        # 生成邀请信息
        invitation_info = InvitationService.create_invitation_info(
            invitation=invitation,
            tenant=tenant,
            inviter_name=inviter_name
        )
        
        return {
            "invitation": invitation_info,
            "tenant_name": tenant.name,
            "tenant_description": tenant.description,
            "inviter_name": inviter_name,
            "is_expired": not invitation.is_valid
        }

    # 超级管理员专用方法
    async def get_all_tenants(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        name: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        获取系统中的所有租户列表（超级管理员专用）
        """
        query = self.db.query(Tenant)
        
        if name:
            query = query.filter(Tenant.name.ilike(f"%{name}%"))
            
        # 添加状态过滤
        if is_active is not None:
            # 这里需要在Tenant模型中添加is_active字段
            # 由于我们当前可能没有这个字段，所以这里注释掉
            # query = query.filter(Tenant.is_active == is_active)
            pass
        
        total = query.count()
        tenants_db = query.order_by(desc(Tenant.created_at)).offset(skip).limit(limit).all()
        
        # 将SQLAlchemy模型转换为字典
        tenants = []
        for tenant in tenants_db:
            # 获取租户拥有者信息
            owner = None
            if tenant.owner_id:
                owner_user = self.db.query(User).filter(User.id == tenant.owner_id).first()
                if owner_user:
                    owner = {
                        "id": owner_user.id,
                        "username": owner_user.username,
                        "email": owner_user.email,
                        "display_name": owner_user.display_name
                    }
            
            # 获取租户用户数量
            user_count = self.db.query(func.count(UserTenantAssociation.user_id))\
                .filter(UserTenantAssociation.tenant_id == tenant.id).scalar() or 0
            
            tenant_info = {
                "id": tenant.id,
                "name": tenant.name,
                "description": tenant.description,
                "is_personal": tenant.is_personal,
                "owner": owner,
                "user_count": user_count,
                "created_at": tenant.created_at,
                "updated_at": tenant.updated_at
            }
            
            # 添加is_active字段（如果存在）
            # if hasattr(tenant, 'is_active'):
            #     tenant_info["is_active"] = tenant.is_active
            
            tenants.append(tenant_info)
        
        return {
            "total": total,
            "items": tenants
        }
    
    async def update_tenant_status(self, tenant_id: str, is_active: bool) -> Dict[str, Any]:
        """
        更新租户状态（超级管理员专用）
        
        注意：此方法需要Tenant模型中有is_active字段
        由于当前模型可能没有此字段，实际使用时需要先迁移数据库添加字段
        """
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        
        if not tenant:
            raise HTTPException(status_code=404, detail="租户不存在")
        
        # 更新状态
        # tenant.is_active = is_active
        # self.db.commit()
        
        # 由于当前可能没有is_active字段，返回未实现的提示
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="租户状态更新功能尚未实现，需要在Tenant模型中添加is_active字段"
        )
        
        # 操作成功后应返回更新后的租户信息
        # return {
        #     "id": tenant.id,
        #     "name": tenant.name,
        #     "description": tenant.description,
        #     "is_personal": tenant.is_personal,
        #     "is_active": tenant.is_active,
        #     "created_at": tenant.created_at,
        #     "updated_at": tenant.updated_at
        # }
    
    async def force_delete_tenant(self, tenant_id: str, force: bool = False) -> Dict[str, Any]:
        """
        强制删除租户及其所有关联数据（超级管理员专用）
        
        force: 是否强制删除，设为True时将删除所有关联数据
        """
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        
        if not tenant:
            raise HTTPException(status_code=404, detail="租户不存在")
        
        if not force:
            # 不强制删除时，检查租户是否有关联数据
            user_count = self.db.query(func.count(UserTenantAssociation.user_id))\
                .filter(UserTenantAssociation.tenant_id == tenant_id).scalar() or 0
                
            if user_count > 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"租户下有 {user_count} 个用户，删除前请先移除用户或使用强制删除"
                )
            
            # 可以添加其他数据检查，如知识库、Agent等
        
        try:
            # 开始事务
            # 删除租户关联的所有数据
            
            # 1. 删除用户关联
            self.db.query(UserTenantAssociation)\
                .filter(UserTenantAssociation.tenant_id == tenant_id)\
                .delete(synchronize_session=False)
            
            # 2. 删除邀请
            self.db.query(TenantInvitation)\
                .filter(TenantInvitation.tenant_id == tenant_id)\
                .delete(synchronize_session=False)
            
            # 4. 删除租户工具、知识库等其他关联数据
            # 在实际应用中需要添加所有关联表的删除
            
            # 5. 最后删除租户本身
            self.db.delete(tenant)
            
            # 提交事务
            self.db.commit()
            
            return {
                "success": True,
                "message": "租户已删除",
                "tenant_id": tenant_id
            }
            
        except Exception as e:
            # 回滚事务
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"删除租户失败: {str(e)}"
            ) 