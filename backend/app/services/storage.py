import os
import uuid
from typing import Binary<PERSON>, Op<PERSON>, <PERSON><PERSON>, Dict, Any
import boto3
from botocore.exceptions import ClientError
from fastapi import UploadFile, HTTPException
from PIL import Image
import io
from app.core.config import settings

class S3StorageService:
    """S3兼容对象存储服务"""
    
    # 支持的图标格式
    ALLOWED_ICON_FORMATS = {
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
        'image/gif': ['.gif'],
        'image/svg+xml': ['.svg'],
        'image/webp': ['.webp']
    }
    
    # 图标大小限制（字节）
    MAX_ICON_SIZE = 2 * 1024 * 1024  # 2MB
    
    def __init__(self):
        """初始化S3客户端连接"""
        endpoint_url = settings.S3_ENDPOINT_URL
        access_key = settings.S3_ACCESS_KEY
        secret_key = settings.S3_SECRET_KEY
        region = settings.S3_REGION
        
        # 通用文件bucket
        self.bucket_name = settings.S3_BUCKET_NAME
        # 图标专用bucket
        self.icon_bucket_name = settings.S3_ICON_BUCKET_NAME
        
        self.s3_client = boto3.client(
            's3',
            endpoint_url=endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region
        )
        
        # 确保存储桶存在
        self._ensure_bucket_exists(self.bucket_name)
        self._ensure_bucket_exists(self.icon_bucket_name)
    
    def _ensure_bucket_exists(self, bucket_name: str) -> None:
        """确保存储桶存在，不存在则创建"""
        try:
            self.s3_client.head_bucket(Bucket=bucket_name)
        except ClientError:
            # 如果存储桶不存在，则创建
            self.s3_client.create_bucket(Bucket=bucket_name)
    
    def _get_icon_access_url(self, object_key: str) -> str:
        """
        获取图标的访问URL
        优先使用CDN，否则使用S3预签名URL
        """
        # 如果配置了CDN，使用CDN地址
        if settings.S3_ICON_CDN_URL:
            cdn_url = settings.S3_ICON_CDN_URL.rstrip('/')
            return f"{cdn_url}/{object_key}"
        
        # 否则使用S3预签名URL
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.icon_bucket_name,
                    'Key': object_key
                },
                ExpiresIn=settings.S3_ICON_URL_EXPIRE_SECONDS
            )
            return url
        except ClientError:
            return ""
    
    def _validate_icon_file(self, file: UploadFile, file_content: bytes) -> Dict[str, Any]:
        """
        验证图标文件格式和大小
        
        Args:
            file: 上传的文件对象
            file_content: 文件内容
            
        Returns:
            Dict: 验证结果和图片信息
            
        Raises:
            HTTPException: 验证失败时抛出异常
        """
        # 检查文件大小
        if len(file_content) > self.MAX_ICON_SIZE:
            raise HTTPException(
                status_code=400, 
                detail=f"图标文件大小不能超过 {self.MAX_ICON_SIZE // 1024 // 1024}MB"
            )
        
        # 检查MIME类型
        content_type = file.content_type
        if content_type not in self.ALLOWED_ICON_FORMATS:
            allowed_formats = ', '.join(self.ALLOWED_ICON_FORMATS.keys())
            raise HTTPException(
                status_code=400,
                detail=f"不支持的图标格式，仅支持: {allowed_formats}"
            )
        
        # 检查文件扩展名
        file_ext = os.path.splitext(file.filename)[1].lower() if file.filename else ""
        if file_ext not in self.ALLOWED_ICON_FORMATS[content_type]:
            raise HTTPException(
                status_code=400,
                detail=f"文件扩展名与MIME类型不匹配"
            )
        
        # 对于非SVG文件，验证图片格式
        image_info = {}
        if content_type != 'image/svg+xml':
            try:
                with Image.open(io.BytesIO(file_content)) as img:
                    image_info = {
                        "width": img.width,
                        "height": img.height,
                        "format": img.format,
                        "mode": img.mode
                    }
                    
                    # 建议图标尺寸为正方形
                    if abs(img.width - img.height) > min(img.width, img.height) * 0.2:
                        # 不是致命错误，只是警告
                        image_info["warning"] = "建议使用正方形图标以获得最佳显示效果"
                        
            except Exception as e:
                raise HTTPException(
                    status_code=400,
                    detail="图片格式验证失败，请确保上传的是有效的图片文件"
                )
        
        return {
            "content_type": content_type,
            "file_ext": file_ext,
            "image_info": image_info
        }

    async def upload_agent_icon(
        self, 
        file: UploadFile, 
        tenant_id: str, 
        agent_id: str
    ) -> Tuple[str, Dict[str, Any]]:
        """
        上传Agent图标到S3存储
        
        Args:
            file: 上传的图标文件
            tenant_id: 租户ID
            agent_id: Agent ID
            
        Returns:
            Tuple[str, Dict]: (S3路径, 图标元数据)
        """
        # 获取文件内容
        file_content = await file.read()
        
        # 验证图标文件
        validation_result = self._validate_icon_file(file, file_content)
        
        # 生成图标存储路径
        file_ext = validation_result["file_ext"]
        object_key = f"{tenant_id}/agents/{agent_id}/icon{file_ext}"
        
        # 上传到S3
        try:
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=object_key,
                Body=file_content,
                ContentType=validation_result["content_type"],
                # 设置缓存策略
                CacheControl='public, max-age=86400'  # 缓存1天
            )
            
            # 构建图标元数据
            metadata = {
                "content_type": validation_result["content_type"],
                "original_filename": file.filename,
                "file_size": len(file_content),
                "bucket": self.bucket_name,
                "object_key": object_key,
                "image_info": validation_result["image_info"],
                "uploaded_at": os.environ.get('TZ', 'UTC')
            }
            
            # 返回S3路径和元数据
            s3_path = f"s3://{self.bucket_name}/{object_key}"
            
            # 重置文件指针
            await file.seek(0)
            
            return s3_path, metadata
            
        except ClientError as e:
            raise HTTPException(
                status_code=500,
                detail=f"图标上传失败: {str(e)}"
            )

    async def upload_agent_icon_atomic(
        self, 
        file: UploadFile, 
        tenant_id: str, 
        agent_id: str
    ) -> Tuple[str, Dict[str, Any]]:
        """
        原子性上传Agent图标到S3存储
        先上传到临时位置，成功后替换正式图标
        
        Args:
            file: 上传的图标文件
            tenant_id: 租户ID
            agent_id: Agent ID
            
        Returns:
            Tuple[str, Dict]: (S3路径, 图标元数据)
        """
        import time
        
        # 获取文件内容
        file_content = await file.read()
        
        # 验证图标文件
        validation_result = self._validate_icon_file(file, file_content)
        
        # 生成临时存储路径和正式存储路径（使用图标专用bucket）
        file_ext = validation_result["file_ext"]
        temp_object_key = f"{tenant_id}/agents/{agent_id}/icon_temp_{int(time.time())}{file_ext}"
        final_object_key = f"{tenant_id}/agents/{agent_id}/icon{file_ext}"
        
        # 先上传到临时位置（使用图标专用bucket）
        try:
            self.s3_client.put_object(
                Bucket=self.icon_bucket_name,  # 使用图标专用bucket
                Key=temp_object_key,
                Body=file_content,
                ContentType=validation_result["content_type"],
                # 设置缓存策略
                CacheControl='public, max-age=86400'  # 缓存1天
            )
            
            # 临时上传成功后，将文件复制到正式位置
            self.s3_client.copy_object(
                Bucket=self.icon_bucket_name,  # 使用图标专用bucket
                CopySource={'Bucket': self.icon_bucket_name, 'Key': temp_object_key},
                Key=final_object_key,
                MetadataDirective='COPY'
            )
            
            # 删除临时文件
            self.s3_client.delete_object(
                Bucket=self.icon_bucket_name,  # 使用图标专用bucket
                Key=temp_object_key
            )
            
            # 构建图标元数据
            metadata = {
                "content_type": validation_result["content_type"],
                "original_filename": file.filename,
                "file_size": len(file_content),
                "bucket": self.icon_bucket_name,  # 记录实际使用的bucket
                "object_key": final_object_key,
                "image_info": validation_result["image_info"],
                "uploaded_at": os.environ.get('TZ', 'UTC'),
                "access_url": self._get_icon_access_url(final_object_key)  # 添加访问URL
            }
            
            # 返回S3路径和元数据
            s3_path = f"s3://{self.icon_bucket_name}/{final_object_key}"
            
            # 重置文件指针
            await file.seek(0)
            
            return s3_path, metadata
            
        except ClientError as e:
            # 如果失败，尝试清理临时文件
            try:
                self.s3_client.delete_object(
                    Bucket=self.icon_bucket_name,  # 使用图标专用bucket
                    Key=temp_object_key
                )
            except:
                pass  # 忽略清理失败
                
            raise HTTPException(
                status_code=500,
                detail=f"图标上传失败: {str(e)}"
            )

    def delete_agent_icon(self, tenant_id: str, agent_id: str) -> bool:
        """
        删除Agent图标
        
        Args:
            tenant_id: 租户ID
            agent_id: Agent ID
            
        Returns:
            bool: 是否删除成功
        """
        # 列出该Agent的所有图标文件（支持不同格式）
        prefix = f"{tenant_id}/agents/{agent_id}/icon"
        
        try:
            # 列出所有以icon开头的文件（从图标专用bucket中）
            objects = self.s3_client.list_objects_v2(
                Bucket=self.icon_bucket_name,  # 使用图标专用bucket
                Prefix=prefix
            )
            
            if 'Contents' not in objects:
                return True  # 没有文件需要删除
                
            # 删除所有匹配的文件
            for obj in objects['Contents']:
                self.s3_client.delete_object(
                    Bucket=self.icon_bucket_name,  # 使用图标专用bucket
                    Key=obj['Key']
                )
            return True
            
        except ClientError:
            return False

    async def upload_file(
        self, 
        file: UploadFile, 
        tenant_id: str, 
        knowledge_base_id: str
    ) -> Tuple[str, Dict[str, Any]]:
        """
        上传文件到S3存储
        
        Args:
            file: 上传的文件对象
            tenant_id: 租户ID
            knowledge_base_id: 知识库ID
            
        Returns:
            Tuple[str, Dict]: (文件路径, 文件元数据)
        """
        # 生成唯一的文件路径
        file_ext = os.path.splitext(file.filename)[1].lower() if file.filename else ""
        object_key = f"{tenant_id}/{knowledge_base_id}/{uuid.uuid4()}{file_ext}"
        
        # 获取文件内容
        file_content = await file.read()
        file_size = len(file_content)
        
        # 检测MIME类型
        content_type = file.content_type
        
        # 上传到S3
        try:
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=object_key,
                Body=file_content,
                ContentType=content_type
            )
            
            # 构建文件元数据
            meta_data = {
                "content_type": content_type,
                "original_filename": file.filename,
                "file_size": file_size,
                "bucket": self.bucket_name,
                "object_key": object_key
            }
            
            # 返回S3路径和元数据
            s3_path = f"s3://{self.bucket_name}/{object_key}"
            
            # 重置文件指针，以便其他操作可以再次读取
            await file.seek(0)
            
            return s3_path, meta_data
            
        except ClientError as e:
            # 处理上传错误
            raise Exception(f"文件上传失败: {str(e)}")
    
    def download_file(self, object_key: str) -> BinaryIO:
        """
        从S3下载文件
        
        Args:
            object_key: 对象键名
            
        Returns:
            BinaryIO: 文件内容
        """
        try:
            response = self.s3_client.get_object(
                Bucket=self.bucket_name,
                Key=object_key
            )
            return response['Body'].read()
        except ClientError as e:
            raise Exception(f"文件下载失败: {str(e)}")
    
    def delete_file(self, object_key: str) -> bool:
        """
        从S3删除文件
        
        Args:
            object_key: 对象键名
            
        Returns:
            bool: 是否删除成功
        """
        try:
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=object_key
            )
            return True
        except ClientError:
            return False
    
    def get_file_url(self, object_key: str, expires_in: int = 3600) -> str:
        """
        获取文件的预签名URL
        
        Args:
            object_key: 对象键名
            expires_in: URL有效期（秒）
            
        Returns:
            str: 预签名URL
        """
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': object_key
                },
                ExpiresIn=expires_in
            )
            return url
        except ClientError:
            return ""
    
    def extract_object_key_from_path(self, s3_path: str) -> Optional[str]:
        """
        从S3路径中提取对象键名
        支持通用bucket和图标专用bucket
        
        Args:
            s3_path: S3路径 (格式: s3://bucket/key)
            
        Returns:
            str: 对象键名
        """
        if not s3_path.startswith("s3://"):
            return None
            
        parts = s3_path[5:].split("/", 1)
        if len(parts) < 2:
            return None
            
        bucket, key = parts
        
        # 支持通用bucket和图标专用bucket
        if bucket in [self.bucket_name, self.icon_bucket_name]:
            return key
            
        return None


# 创建服务实例
s3_storage_service = S3StorageService() 