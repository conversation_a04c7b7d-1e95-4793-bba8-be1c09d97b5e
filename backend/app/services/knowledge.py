from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, UTC, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy import and_, desc
import os
import logging
import json

from app.models.knowledge import KnowledgeBase, KnowledgeFile, KnowledgeChunk, KnowledgeSearch
from app.schemas.knowledge import (
    KnowledgeBaseCreate, KnowledgeBaseUpdate,
    KnowledgeFileCreate, KnowledgeFileUpdate,
    KnowledgeBaseVersionCreate, KnowledgeSearchCreate,
    KnowledgeChunkCreate, KnowledgeChunkBatchCreate
)

from app.core.rag.retrieval import SearchMethod, Retriever
from app.utils.id_generator import generate_kb_id, generate_knowledge_file_id, generate_knowledge_search_id, generate_knowledge_snapshot_id
from app.services.storage import s3_storage_service
from fastapi import UploadFile, HTTPException, status

# 导入任务队列服务
from app.models.task import Task
from app.services.task_queue import task_queue_service

logger = logging.getLogger(__name__)

# 默认分词配置
DEFAULT_CHUNK_CONFIG = {
    "chunk_size": 1000,  # 默认分块大小
    "chunk_overlap": 200  # 默认分块重叠大小
}

# 导入Retriever类中的默认检索配置
from app.core.rag.retrieval import DEFAULT_RETRIEVAL_CONFIG

class KnowledgeBaseService:
    """知识库服务"""
    
    @staticmethod
    def create_knowledge_base(db: Session, knowledge_base: KnowledgeBaseCreate) -> KnowledgeBase:
        """创建知识库"""
        # 转换配置为字典
        config = None
        if knowledge_base.config:
            config = knowledge_base.config.model_dump(exclude_none=True)
            # 验证配置
            KnowledgeBaseService._validate_config(config)
            
            # 确保配置包含检索配置
            if "retrieval" not in config:
                config["retrieval"] = DEFAULT_RETRIEVAL_CONFIG
            
            # 确保配置包含分词配置
            if "chunk_size" not in config:
                config["chunk_size"] = DEFAULT_CHUNK_CONFIG["chunk_size"]
            if "chunk_overlap" not in config:
                config["chunk_overlap"] = DEFAULT_CHUNK_CONFIG["chunk_overlap"]
            
        db_knowledge_base = KnowledgeBase(
            id=generate_kb_id(),
            tenant_id=knowledge_base.tenant_id,
            name=knowledge_base.name,
            description=knowledge_base.description,
            config=config,
            is_active=True
        )
        db.add(db_knowledge_base)
        db.commit()
        db.refresh(db_knowledge_base)
        return db_knowledge_base
    
    @staticmethod
    def get_knowledge_base(db: Session, kb_id: str, tenant_id: str) -> Optional[KnowledgeBase]:
        """获取单个知识库"""
        return db.query(KnowledgeBase).filter(
            and_(
                KnowledgeBase.id == kb_id,
                KnowledgeBase.tenant_id == tenant_id
            )
        ).first()
    
    @staticmethod
    def get_knowledge_bases(
        db: Session, 
        tenant_id: str,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None
    ) -> Tuple[List[KnowledgeBase], int]:
        """获取知识库列表"""
        query = db.query(KnowledgeBase).filter(KnowledgeBase.tenant_id == tenant_id)
        
        if is_active is not None:
            query = query.filter(KnowledgeBase.is_active == is_active)
        
        total = query.count()
        knowledge_bases = query.order_by(desc(KnowledgeBase.updated_at)).offset(skip).limit(limit).all()
        
        return knowledge_bases, total
    
    @staticmethod
    def update_knowledge_base(
        db: Session,
        kb_id: str,
        tenant_id: str,
        knowledge_base: KnowledgeBaseUpdate
    ) -> Optional[KnowledgeBase]:
        """更新知识库"""
        db_knowledge_base = KnowledgeBaseService.get_knowledge_base(db, kb_id, tenant_id)
        if not db_knowledge_base:
            return None
            
        # 转换为字典，排除未设置的字段
        update_data = knowledge_base.model_dump(exclude_unset=True)
        
        # 如果更新包含配置，验证并合并配置
        if "config" in update_data and update_data["config"]:
            # 将Pydantic模型转换为字典
            new_config = update_data["config"]
            
            # 验证新配置
            KnowledgeBaseService._validate_config(new_config)
            
            # 合并配置
            current_config = db_knowledge_base.config or {}
            merged_config = KnowledgeBaseService._merge_config(current_config, new_config)
            update_data["config"] = merged_config
            
            # 记录配置变更
            logger.info(f"知识库 {kb_id} 配置已更新: {json.dumps(new_config, ensure_ascii=False)}")
        
        # 更新字段
        for key, value in update_data.items():
            setattr(db_knowledge_base, key, value)

        # 如果更新了配置，记录变更
        if "config" in update_data:
            flag_modified(db_knowledge_base, "config")
            
        db_knowledge_base.updated_at = datetime.now(UTC)
        db.commit()
        db.refresh(db_knowledge_base)
        return db_knowledge_base
    
    @staticmethod
    def delete_knowledge_base(db: Session, kb_id: str, tenant_id: str) -> bool:
        """删除知识库"""
        db_knowledge_base = KnowledgeBaseService.get_knowledge_base(db, kb_id, tenant_id)
        if not db_knowledge_base:
            return False
            
        # 软删除，将is_active设置为False
        db_knowledge_base.is_active = False
        db_knowledge_base.updated_at = datetime.now(UTC)
        db.commit()
        return True
    
    @staticmethod
    def _validate_config(config: Dict[str, Any]) -> None:
        """
        验证知识库配置
        
        Args:
            config: 知识库配置
            
        Raises:
            ValueError: 配置无效时抛出
        """
        # 检查embedding配置
        if "embedding" in config:
            embedding = config["embedding"]
            if not isinstance(embedding, dict):
                raise ValueError("embedding配置必须是一个对象")
                
            # 检查必填字段
            if "model_id" not in embedding:
                raise ValueError("embedding配置必须包含model_id字段")
            if "model_name" not in embedding:
                raise ValueError("embedding配置必须包含model_name字段")
        
        # 检查旧版embedding配置
        elif "embedding_model_id" in config:
            # 旧版配置，不需要额外验证
            pass
        
        # 检查retrieval配置
        if "retrieval" in config:
            retrieval = config["retrieval"]
            if not isinstance(retrieval, dict):
                raise ValueError("retrieval配置必须是一个对象")
                
            # 检查搜索类型
            if "default_search_type" in retrieval:
                search_type = retrieval["default_search_type"]
                if search_type not in ["vector", "keyword", "hybrid"]:
                    raise ValueError("default_search_type必须是vector、keyword或hybrid之一")
                    
                # 检查对应的搜索配置
                if search_type == "vector" and "vector_search" not in retrieval:
                    logger.warning("默认搜索类型为vector，但未提供vector_search配置")
                elif search_type == "keyword" and "keyword_search" not in retrieval:
                    logger.warning("默认搜索类型为keyword，但未提供keyword_search配置")
                elif search_type == "hybrid" and "hybrid_search" not in retrieval:
                    logger.warning("默认搜索类型为hybrid，但未提供hybrid_search配置")
            
            # 检查向量搜索配置
            if "vector_search" in retrieval:
                vector_search = retrieval["vector_search"]
                if not isinstance(vector_search, dict):
                    raise ValueError("vector_search配置必须是一个对象")
                    
                # 检查top_k
                if "top_k" in vector_search and (
                    not isinstance(vector_search["top_k"], int) or 
                    vector_search["top_k"] < 1 or 
                    vector_search["top_k"] > 100
                ):
                    raise ValueError("top_k必须是1到100之间的整数")
                    
                # 检查score_threshold
                if "score_threshold" in vector_search and (
                    not isinstance(vector_search["score_threshold"], (int, float)) or 
                    vector_search["score_threshold"] < 0 or 
                    vector_search["score_threshold"] > 1
                ):
                    raise ValueError("score_threshold必须是0到1之间的数字")
                    
                # 检查rerank配置
                if "rerank" in vector_search:
                    rerank = vector_search["rerank"]
                    if not isinstance(rerank, dict):
                        raise ValueError("rerank配置必须是一个对象")
                        
                    if "enabled" in rerank and rerank["enabled"] and (
                        "model_id" not in rerank or "model_name" not in rerank
                    ):
                        raise ValueError("启用rerank时，必须提供model_id和model_name")
            
            # 检查关键词搜索配置
            if "keyword_search" in retrieval:
                keyword_search = retrieval["keyword_search"]
                if not isinstance(keyword_search, dict):
                    raise ValueError("keyword_search配置必须是一个对象")
                    
                # 检查top_k
                if "top_k" in keyword_search and (
                    not isinstance(keyword_search["top_k"], int) or 
                    keyword_search["top_k"] < 1 or 
                    keyword_search["top_k"] > 100
                ):
                    raise ValueError("top_k必须是1到100之间的整数")
                    
                # 检查score_threshold
                if "score_threshold" in keyword_search and (
                    not isinstance(keyword_search["score_threshold"], (int, float)) or 
                    keyword_search["score_threshold"] < 0 or 
                    keyword_search["score_threshold"] > 1
                ):
                    raise ValueError("score_threshold必须是0到1之间的数字")
                    
                # 检查rerank配置
                if "rerank" in keyword_search:
                    rerank = keyword_search["rerank"]
                    if not isinstance(rerank, dict):
                        raise ValueError("rerank配置必须是一个对象")
                        
                    if "enabled" in rerank and rerank["enabled"] and (
                        "model_id" not in rerank or "model_name" not in rerank
                    ):
                        raise ValueError("启用rerank时，必须提供model_id和model_name")
            
            # 检查混合搜索配置
            if "hybrid_search" in retrieval:
                hybrid_search = retrieval["hybrid_search"]
                if not isinstance(hybrid_search, dict):
                    raise ValueError("hybrid_search配置必须是一个对象")
                    
                # 检查top_k
                if "top_k" in hybrid_search and (
                    not isinstance(hybrid_search["top_k"], int) or 
                    hybrid_search["top_k"] < 1 or 
                    hybrid_search["top_k"] > 100
                ):
                    raise ValueError("top_k必须是1到100之间的整数")
                    
                # 检查score_threshold
                if "score_threshold" in hybrid_search and (
                    not isinstance(hybrid_search["score_threshold"], (int, float)) or 
                    hybrid_search["score_threshold"] < 0 or 
                    hybrid_search["score_threshold"] > 1
                ):
                    raise ValueError("score_threshold必须是0到1之间的数字")
                    
                # 检查权重
                if "vector_weight" in hybrid_search and "keyword_weight" in hybrid_search:
                    vector_weight = hybrid_search["vector_weight"]
                    keyword_weight = hybrid_search["keyword_weight"]
                    
                    if not isinstance(vector_weight, (int, float)) or vector_weight < 0 or vector_weight > 1:
                        raise ValueError("vector_weight必须是0到1之间的数字")
                        
                    if not isinstance(keyword_weight, (int, float)) or keyword_weight < 0 or keyword_weight > 1:
                        raise ValueError("keyword_weight必须是0到1之间的数字")
                        
                    if abs(vector_weight + keyword_weight - 1.0) > 0.01:
                        raise ValueError("vector_weight和keyword_weight之和必须为1")
                        
                # 检查rerank配置
                if "rerank" in hybrid_search:
                    rerank = hybrid_search["rerank"]
                    if not isinstance(rerank, dict):
                        raise ValueError("rerank配置必须是一个对象")
                        
                    if "enabled" in rerank and rerank["enabled"] and (
                        "model_id" not in rerank or "model_name" not in rerank
                    ):
                        raise ValueError("启用rerank时，必须提供model_id和model_name")
        
        # 检查分块配置
        if "chunk_size" in config:
            chunk_size = config["chunk_size"]
            if not isinstance(chunk_size, int) or chunk_size < 100 or chunk_size > 10000:
                raise ValueError("chunk_size必须是100到10000之间的整数")
                
        if "chunk_overlap" in config:
            chunk_overlap = config["chunk_overlap"]
            if not isinstance(chunk_overlap, int) or chunk_overlap < 0:
                raise ValueError("chunk_overlap必须是非负整数")
                
            if "chunk_size" in config and chunk_overlap >= config["chunk_size"]:
                raise ValueError("chunk_overlap必须小于chunk_size")
    
    @staticmethod
    def _merge_config(current_config: Dict[str, Any], new_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并知识库配置
        
        Args:
            current_config: 当前配置
            new_config: 新配置
            
        Returns:
            Dict[str, Any]: 合并后的配置
        """
        result = current_config.copy()
        
        # 处理embedding配置
        if "embedding" in new_config:
            result["embedding"] = new_config["embedding"]
        
        # 处理retrieval配置
        if "retrieval" in new_config:
            if "retrieval" not in result:
                result["retrieval"] = {}
                
            # 更新搜索类型
            if "default_search_type" in new_config["retrieval"]:
                result["retrieval"]["default_search_type"] = new_config["retrieval"]["default_search_type"]
                
            # 更新向量搜索配置
            if "vector_search" in new_config["retrieval"]:
                result["retrieval"]["vector_search"] = new_config["retrieval"]["vector_search"]
                
            # 更新关键词搜索配置
            if "keyword_search" in new_config["retrieval"]:
                result["retrieval"]["keyword_search"] = new_config["retrieval"]["keyword_search"]
                
            # 更新混合搜索配置
            if "hybrid_search" in new_config["retrieval"]:
                result["retrieval"]["hybrid_search"] = new_config["retrieval"]["hybrid_search"]

            if "default_search_type" in new_config["retrieval"]:
                result["retrieval"]["default_search_type"] = new_config["retrieval"]["default_search_type"]
        
        # 处理分块配置
        if "chunk_size" in new_config:
            result["chunk_size"] = new_config["chunk_size"]
        if "chunk_overlap" in new_config:
            result["chunk_overlap"] = new_config["chunk_overlap"]
        
        return result
    
    @staticmethod
    def create_version(db: Session, version_data: KnowledgeBaseVersionCreate, tenant_id: str) -> Optional[Dict]:
        """创建知识库版本"""
        # 确认知识库存在且属于当前租户
        db_knowledge_base = KnowledgeBaseService.get_knowledge_base(
            db, 
            version_data.knowledge_base_id, 
            tenant_id
        )
        if not db_knowledge_base:
            return None
        
        # 获取知识库的当前状态，创建版本快照
        version_id = generate_knowledge_snapshot_id()
        version = {
            "id": version_id,
            "knowledge_base_id": db_knowledge_base.id,
            "version_name": version_data.version_name,
            "description": version_data.description,
            "snapshot": {
                "name": db_knowledge_base.name,
                "description": db_knowledge_base.description,
                "config": db_knowledge_base.config,
                "files": [
                    {
                        "id": file.id,
                        "file_name": file.file_name,
                        "file_path": file.file_path,
                        "file_type": file.file_type,
                        "status": file.status
                    }
                    for file in db_knowledge_base.files if file.status == "indexed"
                ]
            },
            "created_at": datetime.now(UTC).isoformat()
        }
        
        # 在实际系统中，会将版本存储到数据库或文件系统
        # 这里简化处理，返回版本信息
        return version
    
    @staticmethod
    def get_versions(db: Session, kb_id: str, tenant_id: str, skip: int = 0, limit: int = 100) -> Tuple[List[Dict], int]:
        """获取知识库版本历史"""
        # 确认知识库存在且属于当前租户
        db_knowledge_base = KnowledgeBaseService.get_knowledge_base(db, kb_id, tenant_id)
        if not db_knowledge_base:
            return [], 0
        
        # 在实际系统中，会从数据库或文件系统获取版本历史
        # 这里简化处理，返回空列表
        versions = []
        total = 0
        
        return versions[skip:skip+limit], total

    @staticmethod
    def get_knowledge_base_usage(db: Session, kb_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """获取知识库的使用情况，包括使用该知识库的Agent列表"""
        # 确认知识库存在且属于当前租户
        db_knowledge_base = KnowledgeBaseService.get_knowledge_base(db, kb_id, tenant_id)
        if not db_knowledge_base:
            return None
        
        # 导入Agent相关模型
        from app.models.agent import Agent, AgentVersion
        from sqlalchemy import text
        
        # 使用原生SQL查询，兼容不同数据库
        try:
            # 尝试使用MySQL的JSON_CONTAINS函数
            sql = text("""
                SELECT av.id as version_id, av.agent_id, av.version_number, av.is_current,
                       a.name, a.agent_type, a.description, a.is_active, a.created_at, a.updated_at
                FROM agent_version av
                JOIN agent a ON av.agent_id = a.id
                WHERE JSON_CONTAINS(av.knowledge_base_ids, :kb_id_json)
                AND a.tenant_id = :tenant_id
                ORDER BY a.name, av.version_number
            """)
            result = db.execute(sql, {
                "kb_id_json": f'"{kb_id}"',
                "tenant_id": tenant_id
            })
            rows = result.fetchall()
        except Exception:
            # 如果JSON_CONTAINS不支持，尝试使用LIKE查询（不够精确但兼容性好）
            try:
                sql = text("""
                    SELECT av.id as version_id, av.agent_id, av.version_number, av.is_current,
                           a.name, a.agent_type, a.description, a.is_active, a.created_at, a.updated_at
                    FROM agent_version av
                    JOIN agent a ON av.agent_id = a.id
                    WHERE av.knowledge_base_ids LIKE :kb_id_pattern
                    AND a.tenant_id = :tenant_id
                    ORDER BY a.name, av.version_number
                """)
                result = db.execute(sql, {
                    "kb_id_pattern": f'%"{kb_id}"%',
                    "tenant_id": tenant_id
                })
                rows = result.fetchall()
            except Exception:
                # 最后的备选方案：使用ORM查询所有版本然后在Python中过滤
                all_versions = db.query(AgentVersion).join(Agent).filter(
                    Agent.tenant_id == tenant_id
                ).all()
                
                rows = []
                for version in all_versions:
                    if version.knowledge_base_ids and kb_id in version.knowledge_base_ids:
                        rows.append({
                            'version_id': version.id,
                            'agent_id': version.agent_id,
                            'version_number': version.version_number,
                            'is_current': version.is_current,
                            'name': version.agent.name,
                            'agent_type': version.agent.agent_type,
                            'description': version.agent.description,
                            'is_active': version.agent.is_active,
                            'created_at': version.agent.created_at,
                            'updated_at': version.agent.updated_at
                        })
        
        # 构建Agent使用信息列表和统计信息
        agent_usage_list = []
        unique_agents = {}  # 用于去重统计：agent_id -> agent_info
        
        for row in rows:
            # 处理不同的数据结构（原生SQL结果 vs ORM对象）
            if hasattr(row, '_asdict'):
                # 原生SQL结果
                row_dict = row._asdict()
            elif isinstance(row, dict):
                # 字典格式
                row_dict = row
            else:
                # 元组格式，转换为字典
                row_dict = {
                    'version_id': row.version_id,
                    'agent_id': row.agent_id,
                    'version_number': row.version_number,
                    'is_current': row.is_current,
                    'name': row.name,
                    'agent_type': row.agent_type,
                    'description': row.description,
                    'is_active': row.is_active,
                    'created_at': row.created_at,
                    'updated_at': row.updated_at
                }
            
            # 添加到版本列表（保留所有版本信息）
            agent_usage_list.append({
                "agent_id": row_dict["agent_id"],
                "agent_name": row_dict["name"],
                "agent_type": row_dict["agent_type"],
                "description": row_dict["description"],
                "is_active": row_dict["is_active"],
                "version_number": row_dict["version_number"],
                "version_id": row_dict["version_id"],
                "is_current_version": row_dict["is_current"],
                "created_at": row_dict["created_at"],
                "updated_at": row_dict["updated_at"]
            })
            
            # 去重统计（只计算当前版本的Agent）
            agent_id = row_dict["agent_id"]
            is_current = row_dict["is_current"]
            if is_current and agent_id not in unique_agents:
                unique_agents[agent_id] = {
                    "agent_id": agent_id,
                    "agent_name": row_dict["name"],
                    "agent_type": row_dict["agent_type"],
                    "description": row_dict["description"],
                    "is_active": row_dict["is_active"],
                    "created_at": row_dict["created_at"],
                    "updated_at": row_dict["updated_at"]
                }
        
        # 基于去重后的Agent计算统计信息
        total_agents = len(unique_agents)
        active_agents = len([agent for agent in unique_agents.values() if agent["is_active"]])
        
        return {
            "knowledge_base_id": db_knowledge_base.id,
            "knowledge_base_name": db_knowledge_base.name,
            "total_agents": total_agents,
            "active_agents": active_agents,
            "agents": agent_usage_list
        }

    @staticmethod
    async def reindex_knowledge_base(
        db: Session, 
        kb_id: str, 
        tenant_id: str
    ) -> Dict[str, Any]:
        """
        重新索引知识库中的所有文件
        
        Args:
            db: 数据库会话
            kb_id: 知识库ID
            tenant_id: 租户ID
            chunk_size: 文本块大小
            chunk_overlap: 文本块重叠大小
            
        Returns:
            Dict[str, Any]: 重索引结果
        """
        from app.core.rag.indexing import Indexer
        
        # 验证知识库存在且属于当前租户
        db_knowledge_base = KnowledgeBaseService.get_knowledge_base(db, kb_id, tenant_id)
        if not db_knowledge_base:
            raise ValueError(f"知识库不存在或无权访问: {kb_id}")
        
        # 检查知识库配置
        kb_config = db_knowledge_base.config or {}
        
        # 确保配置包含embedding模型信息
        embedding_model_id = None
        if "embedding" in kb_config and "model_id" in kb_config["embedding"]:
            embedding_model_id = kb_config["embedding"]["model_id"]

            
        if not embedding_model_id:
            raise ValueError(f"知识库未配置嵌入模型，请先配置embedding模型: {kb_id}")
        
        # 使用知识库配置的分块参数，如果未指定则使用默认值

        chunk_size = kb_config.get("chunk_size", DEFAULT_CHUNK_CONFIG["chunk_size"])
        chunk_overlap = kb_config.get("chunk_overlap", DEFAULT_CHUNK_CONFIG["chunk_overlap"])

        
        # 创建索引器
        indexer = Indexer(db)
        
        # 执行重索引
        logger.info(f"开始重索引知识库: {kb_id}, 分块大小: {chunk_size}, 分块重叠: {chunk_overlap}")
        try:
            result = await indexer.reindex_knowledge_base(
                kb_id=kb_id,
                tenant_id=tenant_id,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap
            )
            
            # 更新知识库状态
            db_knowledge_base.updated_at = datetime.now(UTC)
            db.commit()
            
            logger.info(f"知识库重索引完成: {kb_id}")
            return result
        except Exception as e:
            logger.error(f"知识库重索引失败: {kb_id}, 错误: {str(e)}")
            raise


class KnowledgeFileService:
    """知识文件服务"""
    
    @staticmethod
    async def upload_file(
        db: Session, 
        file: UploadFile, 
        knowledge_base_id: str, 
        tenant_id: str
    ) -> Optional[KnowledgeFile]:
        """
        上传文件到S3并创建知识文件记录
        
        Args:
            db: 数据库会话
            file: 上传的文件
            knowledge_base_id: 知识库ID
            tenant_id: 租户ID
            
        Returns:
            Optional[KnowledgeFile]: 创建的知识文件记录
        """
        # 确认知识库存在且属于当前租户
        kb = db.query(KnowledgeBase).filter(
            and_(
                KnowledgeBase.id == knowledge_base_id,
                KnowledgeBase.tenant_id == tenant_id
            )
        ).first()
        
        if not kb:
            return None
        
        # 上传文件到S3
        s3_path, meta_data = await s3_storage_service.upload_file(
            file, 
            tenant_id, 
            knowledge_base_id
        )
        
        # 确定文件类型
        file_type = meta_data.get("content_type", "application/octet-stream")
        
        # 创建文件记录
        db_file = KnowledgeFile(
            id=generate_knowledge_file_id(),
            knowledge_base_id=knowledge_base_id,
            file_name=file.filename,
            file_path=s3_path,
            file_type=file_type,
            file_size=meta_data.get("file_size", 0),
            status="processing",  # 初始状态为处理中
            meta_data=meta_data
        )
        
        db.add(db_file)
        db.commit()
        db.refresh(db_file)
        
        # 创建索引文档任务并添加到队列
        index_task = Task.create_index_document_task(
            tenant_id=tenant_id,
            file_id=db_file.id
        )
        task_queue_service.add_task(db, index_task)
        
        return db_file
    
    @staticmethod
    def create_file(db: Session, file: KnowledgeFileCreate, tenant_id: str) -> Optional[KnowledgeFile]:
        """创建知识文件（通过已有路径）"""
        # 确认知识库存在且属于当前租户
        kb = db.query(KnowledgeBase).filter(
            and_(
                KnowledgeBase.id == file.knowledge_base_id,
                KnowledgeBase.tenant_id == tenant_id
            )
        ).first()
        
        if not kb:
            return None
        
        db_file = KnowledgeFile(
            id=generate_knowledge_file_id(),
            knowledge_base_id=file.knowledge_base_id,
            file_name=file.file_name,
            file_path=file.file_path,
            file_type=file.file_type,
            file_size=file.file_size,
            status="processing",
            meta_data=file.meta_data
        )
        
        db.add(db_file)
        db.commit()
        db.refresh(db_file)
        
        # 创建索引文档任务并添加到队列
        index_task = Task.create_index_document_task(
            tenant_id=tenant_id,
            file_id=db_file.id
        )
        task_queue_service.add_task(db, index_task)
        
        return db_file
    
    @staticmethod
    def get_file(db: Session, file_id: str, tenant_id: str) -> Optional[KnowledgeFile]:
        """获取单个文件"""
        return db.query(KnowledgeFile).join(KnowledgeBase).filter(
            and_(
                KnowledgeFile.id == file_id,
                KnowledgeBase.tenant_id == tenant_id
            )
        ).first()
    
    @staticmethod
    def get_files(
        db: Session,
        kb_id: str,
        tenant_id: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None
    ) -> Tuple[List[KnowledgeFile], int]:
        """获取文件列表"""
        # 确认知识库存在且属于当前租户
        kb = db.query(KnowledgeBase).filter(
            and_(
                KnowledgeBase.id == kb_id,
                KnowledgeBase.tenant_id == tenant_id
            )
        ).first()
        
        if not kb:
            return [], 0
        
        query = db.query(KnowledgeFile).filter(KnowledgeFile.knowledge_base_id == kb_id)
        
        if status:
            query = query.filter(KnowledgeFile.status == status)
        
        total = query.count()
        files = query.order_by(desc(KnowledgeFile.updated_at)).offset(skip).limit(limit).all()
        
        return files, total
    
    @staticmethod
    def update_file(
        db: Session,
        file_id: str,
        tenant_id: str,
        file_update: KnowledgeFileUpdate
    ) -> Optional[KnowledgeFile]:
        """更新文件"""
        db_file = KnowledgeFileService.get_file(db, file_id, tenant_id)
        if not db_file:
            return None
        
        update_data = file_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_file, key, value)
        
        db_file.updated_at = datetime.now(UTC)
        db.commit()
        db.refresh(db_file)
        return db_file
    
    @staticmethod
    def delete_file(db: Session, file_id: str, tenant_id: str) -> bool:
        """删除文件"""
        db_file = KnowledgeFileService.get_file(db, file_id, tenant_id)
        if not db_file:
            return False
        
        # 从S3删除文件
        if db_file.file_path.startswith("s3://"):
            object_key = s3_storage_service.extract_object_key_from_path(db_file.file_path)
            if object_key:
                s3_storage_service.delete_file(object_key)

        # 创建删除文件索引任务
        delete_task = Task.create_delete_file_index_task(
            tenant_id=tenant_id,
            file_id=file_id
        )
        task_queue_service.add_task(db, delete_task)
        
        # 删除文件记录
        db.delete(db_file)
        db.commit()
        return True
    
    @staticmethod
    def get_file_download_url(db: Session, file_id: str, tenant_id: str, expires_in: int = 3600) -> Optional[Dict[str, Any]]:
        """
        获取文件下载URL
        
        Args:
            db: 数据库会话
            file_id: 文件ID
            tenant_id: 租户ID
            expires_in: URL有效期（秒）
            
        Returns:
            Optional[Dict[str, Any]]: 文件下载信息，包含URL
        """
        db_file = KnowledgeFileService.get_file(db, file_id, tenant_id)
        if not db_file:
            return None
            
        # 仅处理S3路径
        if not db_file.file_path.startswith("s3://"):
            return None
            
        object_key = s3_storage_service.extract_object_key_from_path(db_file.file_path)
        if not object_key:
            return None
            
        download_url = s3_storage_service.get_file_url(object_key, expires_in)
        
        # 计算URL过期时间
        expires_at = datetime.now(UTC) + timedelta(seconds=expires_in)
        
        return {
            "id": db_file.id,
            "file_name": db_file.file_name,
            "download_url": download_url,
            "expires_at": expires_at
        }


class KnowledgeChunkService:
    """知识分块服务"""
    
    @staticmethod
    def get_chunks(
        db: Session,
        file_id: str,
        tenant_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[KnowledgeChunk], int]:
        """获取文件的分块列表"""
        # 确认文件存在且属于当前租户
        file_exists = db.query(KnowledgeFile).join(KnowledgeBase).filter(
            and_(
                KnowledgeFile.id == file_id,
                KnowledgeBase.tenant_id == tenant_id
            )
        ).first() is not None
        
        if not file_exists:
            return [], 0
        
        query = db.query(KnowledgeChunk).filter(KnowledgeChunk.file_id == file_id)
        
        total = query.count()
        chunks = query.offset(skip).limit(limit).all()
        
        return chunks, total
    
    @staticmethod
    def create_chunk(
        db: Session,
        chunk_data: KnowledgeChunkCreate,
        tenant_id: str
    ) -> Optional[KnowledgeChunk]:
        """
        创建单个知识块
        
        Args:
            db: 数据库会话
            chunk_data: 知识块数据
            tenant_id: 租户ID
            
        Returns:
            Optional[KnowledgeChunk]: 创建的知识块记录
        """
        # 确认文件存在且属于当前租户
        file_exists = db.query(KnowledgeFile).join(KnowledgeBase).filter(
            and_(
                KnowledgeFile.id == chunk_data.file_id,
                KnowledgeBase.tenant_id == tenant_id
            )
        ).first() is not None
        
        if not file_exists:
            return None
        
        # 创建知识块记录
        db_chunk = KnowledgeChunk(
            id=chunk_data.id,
            file_id=chunk_data.file_id,
            content=chunk_data.content,
            meta_data=chunk_data.meta_data,
            embedding=chunk_data.embedding
        )
        
        db.add(db_chunk)
        db.commit()
        db.refresh(db_chunk)
        
        return db_chunk
    
    @staticmethod
    def batch_create_chunks(
        db: Session,
        batch_data: KnowledgeChunkBatchCreate,
        tenant_id: str
    ) -> Tuple[List[KnowledgeChunk], int]:
        """
        批量创建知识块
        
        Args:
            db: 数据库会话
            batch_data: 批量知识块数据
            tenant_id: 租户ID
            
        Returns:
            Tuple[List[KnowledgeChunk], int]: 创建的知识块列表和成功数量
        """
        if not batch_data.chunks:
            return [], 0
        
        # 获取所有涉及的文件ID
        file_ids = {chunk.file_id for chunk in batch_data.chunks}
        
        # 验证所有文件是否存在且属于同一租户
        valid_file_ids = set()
        for file_id in file_ids:
            file_exists = db.query(KnowledgeFile).join(KnowledgeBase).filter(
                and_(
                    KnowledgeFile.id == file_id,
                    KnowledgeBase.tenant_id == tenant_id
                )
            ).first() is not None
            
            if file_exists:
                valid_file_ids.add(file_id)
        
        # 创建知识块
        created_chunks = []
        for chunk_data in batch_data.chunks:
            if chunk_data.file_id in valid_file_ids:
                db_chunk = KnowledgeChunk(
                    id=chunk_data.id,
                    file_id=chunk_data.file_id,
                    content=chunk_data.content,
                    meta_data=chunk_data.meta_data,
                    embedding=chunk_data.embedding
                )
                created_chunks.append(db_chunk)
        
        # 批量保存
        if created_chunks:
            db.add_all(created_chunks)
            db.commit()
            
            # 刷新数据
            for chunk in created_chunks:
                db.refresh(chunk)
            
            # 更新文件的分块数量
            file_chunk_counts = {}
            for chunk in created_chunks:
                file_id = chunk.file_id
                if file_id in file_chunk_counts:
                    file_chunk_counts[file_id] += 1
                else:
                    file_chunk_counts[file_id] = 1
            
            # 更新每个文件的分块数量
            for file_id, count in file_chunk_counts.items():
                db_file = db.query(KnowledgeFile).filter(KnowledgeFile.id == file_id).first()
                if db_file:
                    db_file.chunk_count += count
                    db_file.updated_at = datetime.now(UTC)
            
            db.commit()
        
        return created_chunks, len(created_chunks)
    
    @staticmethod
    def delete_chunks_by_file(
        db: Session,
        file_id: str,
        tenant_id: str
    ) -> int:
        """
        删除文件的所有知识块
        
        Args:
            db: 数据库会话
            file_id: 文件ID
            tenant_id: 租户ID
            
        Returns:
            int: 删除的知识块数量
        """
        # 确认文件存在且属于当前租户
        file_exists = db.query(KnowledgeFile).join(KnowledgeBase).filter(
            and_(
                KnowledgeFile.id == file_id,
                KnowledgeBase.tenant_id == tenant_id
            )
        ).first() is not None
        
        if not file_exists:
            return 0
        
        # 获取删除前的块数量
        count_before = db.query(KnowledgeChunk).filter(KnowledgeChunk.file_id == file_id).count()
        
        # 删除知识块
        db.query(KnowledgeChunk).filter(KnowledgeChunk.file_id == file_id).delete()
        
        # 更新文件的分块数量
        db_file = db.query(KnowledgeFile).filter(KnowledgeFile.id == file_id).first()
        if db_file:
            db_file.chunk_count = 0
            db_file.updated_at = datetime.now(UTC)
        
        db.commit()
        
        return count_before


class KnowledgeSearchService:
    """知识搜索服务"""
    
    @staticmethod
    def search(db: Session, query: str, tenant_id: str, kb_id: str, search_method: Optional[str] = None, top_k: Optional[int] = None, session_id: Optional[str] = None, save_search: bool = True) -> Optional[KnowledgeSearch]:
        """搜索知识库"""
        # 创建搜索记录
        results = []
        search_record = KnowledgeSearch(
            id=generate_knowledge_search_id(),
            knowledge_base_id=kb_id,
            session_id=session_id,
            query=query,
            results=results,
            created_at=datetime.now(UTC)
        )
        
        # 保存搜索记录
        if save_search:
            db.add(search_record)
            db.commit()
        
        # 调用RAG服务进行搜索
        try:
            # 使用重构后的Retriever类获取检索结果
            # 所有配置参数现在都从知识库配置中获取
            retriever = Retriever(db)
            retriever_result = retriever.retrieve_documents(
                kb_id=kb_id,
                tenant_id=tenant_id,
                query=query
            )

            # 获取格式化后的结果
            results = retriever_result.get("results", [])
            
            # 更新搜索记录
            search_record.results = results
            if save_search:
                db.commit()
                
            return search_record
        except Exception as e:
            logger.error(f"检索文档时发生错误: {str(e)}")
            search_record.error = str(e)
            if save_search:
                db.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"检索文档时发生错误: {str(e)}"
            )
        
        return search_record
    
    @staticmethod
    def get_search_history(
        db: Session,
        kb_id: str,
        tenant_id: str,
        session_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 20
    ) -> Tuple[List[KnowledgeSearch], int]:
        """获取搜索历史"""
        # 确认知识库存在且属于当前租户
        kb_exists = db.query(KnowledgeBase).filter(
            and_(
                KnowledgeBase.id == kb_id,
                KnowledgeBase.tenant_id == tenant_id
            )
        ).first() is not None
        
        if not kb_exists:
            return [], 0
        
        query = db.query(KnowledgeSearch).filter(KnowledgeSearch.knowledge_base_id == kb_id)
        
        if session_id:
            query = query.filter(KnowledgeSearch.session_id == session_id)
        
        total = query.count()
        searches = query.order_by(desc(KnowledgeSearch.created_at)).offset(skip).limit(limit).all()
        
        return searches, total