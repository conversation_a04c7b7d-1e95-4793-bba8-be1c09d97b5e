from sqlalchemy import func
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import UTC, datetime

from sqlalchemy.orm import Session
from sqlalchemy import <PERSON><PERSON><PERSON>,JSON, true
from app.models.tool import Tool, Tenant<PERSON>ool, <PERSON><PERSON>ool, MCPServer, ToolExecutionLog
from app.models.agent import AgentVersion
from app.schemas.tool import ToolCreate, ToolUpdate, TenantToolCreate, TenantToolUpdate
from app.schemas.tool import AgentToolCreate, AgentToolUpdate, MCPServerCreate, MCPServerUpdate
from typing import  Optional
from app.core.platform_tool import PlatformToolService
from langchain_mcp_adapters.client import MultiServerMCPClient
from jsonschema import validate
from threading import Timer
from app.db.session import SessionLocal
import json
class ToolService:
    """工具服务类，提供工具相关的业务逻辑"""
    
    @staticmethod
    def create_tool(db: Session, tool_create: ToolCreate) -> Tool:
        """创建新工具"""
        # 检查key是否已存在
        if db.query(Tool).filter(Tool.key == tool_create.key).first():
            raise ValueError(f"工具标识 '{tool_create.key}' 已存在")
        
        # 创建工具实例
        tool = Tool(
            name=tool_create.name,
            key=tool_create.key,
            description=tool_create.description,
            config_schema=tool_create.config_schema,
            is_active=True
        )
        
        db.add(tool)
        db.commit()
        db.refresh(tool)
        return tool
    
    @staticmethod
    def update_tool(db: Session, tool_id: str, tool_update: ToolUpdate) -> Optional[Tool]:
        """更新工具信息"""
        tool = db.query(Tool).filter(Tool.id == tool_id).first()
        if not tool:
            return None
        
        # 更新工具属性
        if tool_update.name is not None:
            tool.name = tool_update.name
        if tool_update.description is not None:
            tool.description = tool_update.description
        if tool_update.config_schema is not None:
            tool.config_schema = tool_update.config_schema
        if tool_update.is_active is not None:
            tool.is_active = tool_update.is_active
        
        db.commit()
        db.refresh(tool)
        return tool
    
    @staticmethod
    def get_tool(db: Session, tool_id: str) -> Optional[Tool]:
        """获取工具详情"""
        return db.query(Tool).filter(Tool.id == tool_id).first()
    
    @staticmethod
    def get_tool_by_key(db: Session, key: str) -> Optional[Tool]:
        """通过标识获取工具"""
        return db.query(Tool).filter(Tool.key == key).first()
    
    @staticmethod
    def list_tools(
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        name: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> Tuple[List[Tool], int]:
        """获取工具列表"""
        query = db.query(Tool)
        
        # 应用过滤条件
        if name:
            query = query.filter(Tool.name.ilike(f"%{name}%"))
        if is_active is not None:
            query = query.filter(Tool.is_active == is_active)
        
        # 获取总数
        total = query.count()
        
        # 应用分页并返回结果
        tools = query.order_by(Tool.created_at.desc()).offset(skip).limit(limit).all()
        return tools, total
    
    @staticmethod
    def delete_tool(db: Session, tool_id: str) -> bool:
        """删除工具"""
        tool = db.query(Tool).filter(Tool.id == tool_id).first()
        if not tool:
            return False
        
        # 检查是否有依赖关系
        tenant_tools_count = db.query(TenantTool).filter(TenantTool.tool_id == tool_id).count()
        if tenant_tools_count > 0:
            raise ValueError("该工具已被租户使用，无法删除")
        tool.is_active = False
        db.commit()
        db.refresh(tool)
        return True

    @staticmethod
    def init_platform_tools(db: Session) -> bool:
        """初始化平台内置工具"""
        #获取PlatformToolService中的所有方法

        platform_tools = PlatformToolService.get_all_tools()
        
        # 检查每个工具是否已存在
        for platform_tool in platform_tools:
            
            existing_tool = db.query(Tool).filter(Tool.key == platform_tool["name"]).first()
            if not existing_tool:
                # 创建新工具

                tool = Tool(
                    name=platform_tool["name"],
                    key=platform_tool["name"],
                    description=platform_tool["description"],
                    config_schema=platform_tool["config_schema"],
                    param_schema=platform_tool["param_schema"],
                    tag=platform_tool["tag"],
                    is_active=platform_tool["is_active"]
                    )
                db.add(tool)
            # 如果存在更新
            else:
                existing_tool.name = platform_tool["name"]
                existing_tool.key = platform_tool["name"]
                existing_tool.description = platform_tool["description"]
                existing_tool.config_schema = platform_tool["config_schema"]
                existing_tool.param_schema=platform_tool["param_schema"]
                existing_tool.config_schema = platform_tool["config_schema"]
                existing_tool.tag = platform_tool["tag"]
                existing_tool.is_active = platform_tool["is_active"]
                db.add(existing_tool)

        db.commit()
        return Boolean(True)

class TenantToolService:
    """租户工具服务类，提供租户工具配置相关的业务逻辑"""
    secret_field = "********"
    @staticmethod
    def create_tenant_tool(db: Session, tenant_tool_create: TenantToolCreate) -> TenantTool:
        """为租户配置工具"""
        # 检查租户工具是否已存在
        existing = db.query(TenantTool).filter(
            TenantTool.tenant_id == tenant_tool_create.tenant_id,
            TenantTool.tool_id == tenant_tool_create.tool_id
        ).first()
        
        if existing:
            raise ValueError("该租户已配置此工具")
        #校验参数是否都有
        tool = db.query(Tool).filter(Tool.id == tenant_tool_create.tool_id).first()
        if not tool:
            raise ValueError("工具不存在")
        config_schema = tool.config_schema
        check_config = TenantToolService.check_tenant_tool_config(config_schema, tenant_tool_create.config)
        if not check_config:
            raise ValueError("配置参数不符合工具参数规则")
        # 创建租户工具配置
        tenant_tool = TenantTool(
            tenant_id=tenant_tool_create.tenant_id,
            tool_id=tenant_tool_create.tool_id,
            config=tenant_tool_create.config,
            is_active=tenant_tool_create.is_active or True
        )
        
        db.add(tenant_tool)
        db.commit()
        db.refresh(tenant_tool)
        return tenant_tool
    
    @staticmethod
    def check_tenant_tool_config(tool_config_schema: JSON, tenant_config: JSON) -> bool:
        """检查租户工具配置是否有效"""
        #tool_config_schema为工具需要的配置参数为json_schema格式，tenant_config为租户配置的参数，需要检验组合配置的参数是否符合工具参数的规则
        try:
            validate(instance=tenant_config, schema=tool_config_schema)
        except Exception as e:
            print(e)
            return False
        return True

    @staticmethod
    def update_tenant_tool(
        db: Session, 
        tenant_id: str, 
        tool_id: str,
        tenant_tool_update: TenantToolUpdate
    ) -> Optional[TenantTool]:
        """更新租户工具配置"""
        tenant_tool = db.query(TenantTool).filter(
            TenantTool.tenant_id == tenant_id,
            TenantTool.tool_id == tool_id
        ).first()
    
        if not tenant_tool:
            return None
        #校验参数是否都有
        tool = db.query(Tool).filter(Tool.id == tenant_tool.tool_id).first()
        if not tool:
            raise ValueError("工具不存在")
        config_schema = tool.config_schema
        check_config = TenantToolService.check_tenant_tool_config(config_schema, tenant_tool_update.config)
        if not check_config:
            raise ValueError("配置参数不符合工具参数规则")
        # 更新租户工具属性
        if tenant_tool_update.config is not None:
            #如果传输的为某个字段为********，则不更新
            for key in tenant_tool_update.config:
                if tenant_tool_update.config[key] == TenantToolService.secret_field:
                    tenant_tool_update.config[key] = tenant_tool.config[key]
            tenant_tool.config = tenant_tool_update.config
        if tenant_tool_update.is_active is not None:
            tenant_tool.is_active = tenant_tool_update.is_active
        
        db.commit()
        db.refresh(tenant_tool)
        return tenant_tool
    
    @staticmethod
    def get_tenant_tool(db: Session, tenant_id: str, tool_id: str) -> Optional[TenantTool]:
        """获取租户工具配置详情"""
        tenant_tool = db.query(TenantTool).filter(
            TenantTool.tenant_id == tenant_id,
            TenantTool.tool_id == tool_id
        ).first()
        if tenant_tool:
            tenant_tool = TenantToolService.secretConfig(tenant_tool)
        return tenant_tool
    
    @staticmethod
    def get_tenant_tool_by_id(db: Session, tenant_tool_id: str) -> Optional[TenantTool]:
        """通过ID获取租户工具配置"""
        tenant_tool = db.query(TenantTool).filter(TenantTool.id == tenant_tool_id).first()
        if tenant_tool:
            tenant_tool = TenantToolService.secretConfig(tenant_tool)
        return tenant_tool
    
    @staticmethod
    def list_tenant_tools(
        db: Session, 
        tenant_id: str,
        skip: int = 0, 
        limit: int = 100,
        is_active: Optional[bool] = None
    ) -> Tuple[List[TenantTool], int]:
        """获取租户工具配置列表"""
        query = db.query(TenantTool).filter(TenantTool.tenant_id == tenant_id)
        
        # 应用过滤条件
        if is_active is not None:
            query = query.filter(TenantTool.is_active == is_active)
        
        # 获取总数
        total = query.count()
        
        # 应用分页并返回结果
        tenant_tools = query.order_by(TenantTool.created_at.desc()).offset(skip).limit(limit).all()
        #加密展示tool的config中需要字段加密的字段,如果字段包括配置secret,则需要加密展示
        for tenant_tool in tenant_tools:
            tenant_tool = TenantToolService.secretConfig(tenant_tool)
        return tenant_tools, total

    def secretConfig(tenant_tool:TenantTool) -> TenantTool:
            config_schema = tenant_tool.tool.config_schema
            # 如果config_schema为None,则不需要加密展示
            if config_schema:
                config = tenant_tool.config  
            
                for key in config_schema["properties"]:
                    if config_schema["properties"][key].get("secret"):
                        config[key] = TenantToolService.secret_field
            tenant_tool.config = config
            return tenant_tool

    @staticmethod
    def delete_tenant_tool(db: Session, tenant_id: str, tool_id: str) -> bool:
        """删除租户工具配置"""
        tenant_tool = db.query(TenantTool).filter(
            TenantTool.tenant_id == tenant_id,
            TenantTool.tool_id == tool_id
        ).first()
        
        if not tenant_tool:
            return False

        # 检查是否有依赖关系
        agent_tools_count = db.query(AgentVersion).filter(
            func.json_contains(AgentVersion.tool_ids, f'["{tool_id}"]')).count()
        print(agent_tools_count)
        if agent_tools_count > 0:
            raise ValueError("该租户工具已被Agent使用，无法删除")
        tenant_tool.is_active = False
        db.commit()
        db.refresh(tenant_tool)
        return True



    @staticmethod
    def list_tenant_available_tools(db: Session, tenant_id: str) -> List[TenantTool]:
        """获取租户可用工具配置列表"""
        tenant_tools = db.query(TenantTool).filter(TenantTool.tenant_id == tenant_id,TenantTool.is_active==1).all()
        return tenant_tools

class AgentToolService:
    """Agent工具服务类，提供Agent工具配置相关的业务逻辑"""
    
    @staticmethod
    def create_agent_tool(db: Session, agent_tool_create: AgentToolCreate) -> AgentTool:
        """为Agent配置工具"""
        # 检查Agent工具是否已存在
        existing = db.query(AgentTool).filter(
            AgentTool.agent_id == agent_tool_create.agent_id,
            AgentTool.tool_id == agent_tool_create.tool_id
        ).first()
        
        if existing:
            raise ValueError("该Agent已配置此工具")
        
        # 创建Agent工具配置
        agent_tool = AgentTool(
            agent_id=agent_tool_create.agent_id,
            tool_id=agent_tool_create.tool_id,
            tenant_tool_id=agent_tool_create.tenant_tool_id,
            config=agent_tool_create.config,
            is_active=agent_tool_create.is_active or True
        )
        
        db.add(agent_tool)
        db.commit()
        db.refresh(agent_tool)
        return agent_tool
    
    @staticmethod
    def update_agent_tool(
        db: Session, 
        agent_id: str, 
        tool_id: str,
        agent_tool_update: AgentToolUpdate
    ) -> Optional[AgentTool]:
        """更新Agent工具配置"""
        agent_tool = db.query(AgentTool).filter(
            AgentTool.agent_id == agent_id,
            AgentTool.tool_id == tool_id
        ).first()
        
        if not agent_tool:
            return None
        
        # 更新Agent工具属性
        if agent_tool_update.config is not None:
            agent_tool.config = agent_tool_update.config
        if agent_tool_update.is_active is not None:
            agent_tool.is_active = agent_tool_update.is_active
        
        db.commit()
        db.refresh(agent_tool)
        return agent_tool
    
    @staticmethod
    def get_agent_tool(db: Session, agent_id: str, tool_id: str) -> Optional[AgentTool]:
        """获取Agent工具配置详情"""
        return db.query(AgentTool).filter(
            AgentTool.agent_id == agent_id,
            AgentTool.tool_id == tool_id
        ).first()
    
    @staticmethod
    def get_agent_tool_by_id(db: Session, agent_tool_id: str) -> Optional[AgentTool]:
        """通过ID获取Agent工具配置"""
        return db.query(AgentTool).filter(AgentTool.id == agent_tool_id).first()
    
    @staticmethod
    def list_agent_tools(
        db: Session, 
        agent_id: str,
        skip: int = 0, 
        limit: int = 100,
        is_active: Optional[bool] = None
    ) -> Tuple[List[AgentTool], int]:
        """获取Agent工具配置列表"""
        query = db.query(AgentTool).filter(AgentTool.agent_id == agent_id)
        
        # 应用过滤条件
        if is_active is not None:
            query = query.filter(AgentTool.is_active == is_active)
        
        # 获取总数
        total = query.count()
        
        # 应用分页并返回结果
        agent_tools = query.order_by(AgentTool.created_at.desc()).offset(skip).limit(limit).all()
        return agent_tools, total
    
    @staticmethod
    def delete_agent_tool(db: Session, agent_id: str, tool_id: str) -> bool:
        """删除Agent工具配置"""
        agent_tool = db.query(AgentTool).filter(
            AgentTool.agent_id == agent_id,
            AgentTool.tool_id == tool_id
        ).first()
        
        if not agent_tool:
            return False
        
        db.delete(agent_tool)
        db.commit()
        return True

class MCPServerService:
    """MCP服务器服务类，提供服务器相关的业务逻辑"""
    
    @staticmethod
    async def create_mcp_server(db: Session, server_create: MCPServerCreate, tenant_id: str) -> Optional[MCPServer]:
        """创建MCP服务器"""
        #先链接一次，检查是否能链接
        success = await MCPServerService.check_mcp_server(server_create.name, server_create.endpoint,server_create.transport_type,server_create.meta_data)
        if not success:
            raise ValueError("链接MCP服务器失败")
        # 创建MCP服务器实例
        server = MCPServer(
            tenant_id=tenant_id,
            name=server_create.name,
            endpoint=server_create.endpoint,

            transport_type=server_create.transport_type,
            status=server_create.status or "active",
            last_heartbeat=datetime.now(UTC),
            meta_data=server_create.meta_data
        )
    
        db.add(server)
        db.commit()
        db.refresh(server)
        return server
    
    @staticmethod
    async def check_mcp_server(name:str, endpoint:str,transport_type:str,meta_data:Any) -> bool:
        #先链接一次，检查是否能链接
        try:
            MCPServerService.check_mcp_server_config(transport_type, meta_data)
            mcp_server_config = MCPServerService.get_mcp_server_config(endpoint,transport_type,meta_data)
            logging.info(f"MCP服务器配置: {mcp_server_config}")
            client = MultiServerMCPClient({name: mcp_server_config})
            client.get_tools()  # 异步调用
            logging.info(f"链接MCP服务器成功")
        except Exception as e:
            logging.error(f"链接MCP服务器失败，原始异常：{e}", exc_info=True)
            raise ValueError(f"链接MCP服务器失败：{str(e)}") from e
        return True

    def validate_auth_config(config: Dict[str, str], required_fields: List[str]) -> None:
            """
            公共校验函数：检查认证配置是否存在且包含必需字段
            :param config: 认证配置字典
            :param required_fields: 必需的字段列表
            :raises ValueError: 配置缺失或字段无效时抛出异常
            """
            if not config:
                raise ValueError("认证配置缺失")
            for field in required_fields:
                if not config.get(field) or not config.get(field).strip():
                    raise ValueError(f"认证配置缺少有效字段：{field}")

    def check_mcp_server_config(transport_type:str, meta_data: Dict[str, str]) -> bool:
        #transport_type支持http,sse
        SUPPORTED_TRANSPORT_TYPES = ["streamable_http", "sse","http"]
        if(transport_type not in SUPPORTED_TRANSPORT_TYPES):
            raise ValueError(f"不支持的传输类型：{transport_type}，有效类型：{SUPPORTED_TRANSPORT_TYPES}")
        # 定义支持的认证类型常量
        SUPPORTED_AUTH_TYPES = ["header", "query"]
        if not meta_data:
            return true

        # 提取认证信息
        auth_type = meta_data.get("auth_type")
        auth_config = meta_data.get("auth_config", {})
        print(auth_type)
        print(auth_config)
        if not auth_type:
            return true
        if(auth_type == "header"):
            MCPServerService.validate_auth_config(auth_config, ["api_token"]),
        elif(auth_type == "query"):
            MCPServerService.validate_auth_config(auth_config, ["query"])
        else:
            raise ValueError(f"不支持的认证类型：{auth_type}，有效类型：{SUPPORTED_AUTH_TYPES}")


    @staticmethod
    def get_mcp_server_config(endpoint: str, transport_type: str, meta_data: Dict[str, str]) -> Dict[str, str]:
        """
        生成MCP服务器配置（支持多种认证方式）
        :param endpoint: 服务器端点URL
        :param transport_type: 传输类型
        :param meta_data: 元数据字典，包含认证类型(auth_type)和认证配置(auth_config)
        :return: 服务器配置字典
        """
        server_config = {
            "url": endpoint,
            "transport": transport_type,
            "header": {}
        }
        if not meta_data:
            return server_config
        # 提取认证信息
        auth_type = meta_data.get("auth_type")
        auth_config = meta_data.get("auth_config", {})

        if not auth_type:
            return server_config
        
        # 处理认证配置
        def handle_header():
            header_param = {"Authorization": f"Bearer {auth_config['api_token'].strip()}"}
            print(header_param)
            server_config["header"].update(header_param)
            
        def handle_query():
            query_param = auth_config.get('query', '')
            server_config["url"] = f"{server_config['url']}?{query_param}"

        auth_handlers = {
            "header": handle_header,
            "query": handle_query
        }

        # 执行认证逻辑
        auth_handlers.get(auth_type, lambda: None)()
    
        return server_config
    
  

    @staticmethod
    async def update_mcp_server(db: Session, server_id: str, server_update: MCPServerUpdate, tenant_id: str) -> Optional[MCPServer]:
        """更新MCP服务器信息"""
        server = db.query(MCPServer).filter(MCPServer.id == server_id).filter(MCPServer.tenant_id == tenant_id).first()
        if not server:
            return None
        success = MCPServerService.check_mcp_server(server_update.name,server_update.endpoint,server_update.transport_type,server_update.meta_data)
        if not success:
            raise ValueError("链接MCP服务器失败")
        # 更新服务器属性
        if server_update.name is not None:
            server.name = server_update.name
        if server_update.endpoint is not None:
            server.endpoint = server_update.endpoint
        
        if server_update.status is not None:
            server.status = server_update.status
        if server_update.meta_data is not None:
            server.meta_data = server_update.meta_data
        if server_update.transport_type is not None:
            server.transport_type = server_update.transport_type
        server.last_heartbeat = datetime.now(UTC)
        
        db.commit()
        db.refresh(server)
        return server
    
    @staticmethod
    def get_mcp_server(db: Session, server_id: str, tenant_id: str) -> Optional[MCPServer]:
        """获取MCP服务器详情"""
        return db.query(MCPServer).filter(
            MCPServer.id == server_id,
            MCPServer.tenant_id == tenant_id
        ).first()
    
    @staticmethod
    def list_mcp_servers(
        db: Session, 
        tenant_id: str,
        skip: int = 0, 
        limit: int = 100,
        name: Optional[str] = None,
        status: Optional[str] = None
    ) -> Tuple[List[MCPServer], int]:
        """获取MCP服务器列表"""
        query = db.query(MCPServer).filter(MCPServer.tenant_id == tenant_id)
        
        # 应用过滤条件
        if name:
            query = query.filter(MCPServer.name.ilike(f"%{name}%"))
        if status:
            query = query.filter(MCPServer.status == status)
        
        # 获取总数
        total = query.count()
        
        # 应用分页并返回结果
        servers = query.order_by(MCPServer.created_at.desc()).offset(skip).limit(limit).all()
        return servers, total
    
    @staticmethod
    def delete_mcp_server(db: Session,tenant_id:str, server_id: str) -> bool:
        """删除MCP服务器"""
        server = db.query(MCPServer).filter(MCPServer.id == server_id,MCPServer.tenant_id == tenant_id).first()
        if not server:
            return False
        
        # 检查是否有依赖关系
        mcp_tools_count = db.query(AgentVersion).filter(
            func.json_contains(AgentVersion.mcp_server_ids, f'["{server_id}"]')).count()
        if mcp_tools_count > 0:
            raise ValueError("该服务器已有工具配置，无法删除")
        server.status = "inactive"
        db.commit()
        db.refresh(server)
        return True

    
    @staticmethod
    def list_tenant_available_mcp_servers(db: Session, tenant_id: str) -> List[MCPServer]:
        """获取租户可用MCP服务器列表"""
        servers = db.query(MCPServer).filter(MCPServer.tenant_id == tenant_id,MCPServer.status=="active").all()
        return servers


    @classmethod
    async def check_all_mcp_status(cls):
        """检查所有已配置MCP服务器的状态"""
        # 获取所有已配置的MCP服务器
        with SessionLocal() as db:
            mcp_servers = db.query(MCPServer).filter(MCPServer.status != "inactive").all()

            for server in mcp_servers:
                status = await cls.check_mcp_server_status(server)  # 恢复 await，因为是异步方法
                # 处理状态结果更新mcp_servers
                # 更新数据库中的状态
                server.status = status["status"]
                if status["status"] == "active":
                    server.last_heartbeat = status["last_check"]
                db.commit()
                db.refresh(server)
        


    async def check_mcp_server_status(server: MCPServer) -> Dict[str, Any]:
        """检查单个MCP服务器状态"""
        # ... 现有实现 ...
        try:
            # 尝试连接服务器并获取状态
            mcp_server_config = MCPServerService.get_mcp_server_config(server.endpoint,server.transport_type,server.meta_data)
            
            client = MultiServerMCPClient({server.name: mcp_server_config})
            client.get_tools()  # 同步调用
            return {
                "server_id": server.id,
                "status": "active",
                "last_check": datetime.now(UTC)
            }
        except Exception as e:
            logging.info(f"链接MCP服务器失败，原始异常：{e}", exc_info=True)
            return {
                "server_id": server.id,
                "status": "error",
                "last_check": datetime.now(UTC)
            }

    
    @classmethod
    def start_mcp_status_monitor(cls):
        """启动MCP状态监控定时任务"""
        
        def run_check():
            try:
                import asyncio
                asyncio.run(cls.check_all_mcp_status())  # 使用asyncio.run运行异步方法
            except Exception as e:
                import logging
                logging.error(f"MCP状态检查任务执行失败: {str(e)}", exc_info=True)  # 记录详细错误堆栈
            finally:
                t = Timer(120*60, run_check)
                t.daemon = True
                t.start()
        
        # 首次启动定时器
        t = Timer(100, run_check)
        t.daemon = True
        t.start()
       

    
class ToolExecutionLogService:
    """工具执行记录服务类，提供工具执行日志相关的业务逻辑"""
    
    @staticmethod
    def create_execution_log(
        db: Session, 
        session_id: str,
        tool_id: str,
        agent_tool_id: str,
        input_params: Dict[str, Any],
        output_result: Optional[Dict[str, Any]] = None,
        execution_time: int = 0,
        status: str = "success",
        error_message: Optional[str] = None
    ) -> ToolExecutionLog:
        """创建工具执行记录"""
        log = ToolExecutionLog(
            session_id=session_id,
            tool_id=tool_id,
            agent_tool_id=agent_tool_id,
            input_params=input_params,
            output_result=output_result,
            execution_time=execution_time,
            status=status,
            error_message=error_message
        )
        
        db.add(log)
        db.commit()
        db.refresh(log)
        return log
    
    @staticmethod
    def list_execution_logs(
        db: Session,
        session_id: Optional[str] = None, 
        tool_id: Optional[str] = None,
        agent_tool_id: Optional[str] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[ToolExecutionLog], int]:
        """获取工具执行记录列表"""
        query = db.query(ToolExecutionLog)
        
        # 应用过滤条件
        if session_id:
            query = query.filter(ToolExecutionLog.session_id == session_id)
        if tool_id:
            query = query.filter(ToolExecutionLog.tool_id == tool_id)
        if agent_tool_id:
            query = query.filter(ToolExecutionLog.agent_tool_id == agent_tool_id)
        if status:
            query = query.filter(ToolExecutionLog.status == status)
        
        # 获取总数
        total = query.count()
        
        # 应用分页并返回结果
        logs = query.order_by(ToolExecutionLog.created_at.desc()).offset(skip).limit(limit).all()
        return logs, total