from datetime import datetime, UTC
import uuid
from typing import List, Optional, Dict, Any, Tu<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import desc
from fastapi import HTTPException, UploadFile

from app.models.agent import Agent, AgentType, AgentVersion
from app.models.base import Tenant
from app.schemas.agent import (
     AgentListResponse, AgentRollbackRequest, AgentUpdate, Agent as AgentSchema,
     AgentIconUploadResponse, AgentIconDeleteResponse,
    AgentCreateRequest,
    AgentPublishRequest,
    AgentDraftUpdateRequest
)
from app.services.storage import s3_storage_service

class AgentService:
    def __init__(self, db: Session):
        self.db = db
    
    def _get_current_utc_time(self) -> datetime:
        """获取当前UTC时间"""
        return datetime.now(UTC)
    
    def _generate_open_id(self) -> str:
        """生成唯一的open_id"""
        return str(uuid.uuid4()).replace('-', '')
    
    async def _validate_model_exists(self, model_id: Optional[str]) -> None:
        """验证模型是否存在"""
        if model_id:
            from app.models.model import Model
            model = self.db.query(Model).filter(Model.id == model_id).first()
            if not model:
                raise HTTPException(status_code=404, detail="模型不存在")
    
    async def _validate_knowledge_bases(self, knowledge_base_ids: Optional[List[str]], tenant_id: str) -> None:
        """验证知识库是否存在且属于当前租户"""
        if knowledge_base_ids:
            from app.models.knowledge import KnowledgeBase
            for kb_id in knowledge_base_ids:
                kb = self.db.query(KnowledgeBase).filter(
                    KnowledgeBase.id == kb_id,
                    KnowledgeBase.tenant_id == tenant_id,
                    KnowledgeBase.is_active == True
                ).first()
                if not kb:
                    raise HTTPException(status_code=404, detail=f"知识库 {kb_id} 不存在或无权访问")
    
    async def _validate_tools(self, tool_ids: Optional[List[str]], tenant_id: str) -> None:
        """验证工具是否存在且租户已配置"""
        if tool_ids:
            from app.models.tool import Tool, TenantTool
            for tool_id in tool_ids:
                # 检查工具是否存在且激活
                tool = self.db.query(Tool).filter(
                    Tool.id == tool_id,
                    Tool.is_active == True
                ).first()
                if not tool:
                    raise HTTPException(status_code=404, detail=f"工具 {tool_id} 不存在或未激活")
                
                # 检查租户是否已配置此工具
                tenant_tool = self.db.query(TenantTool).filter(
                    TenantTool.tool_id == tool_id,
                    TenantTool.tenant_id == tenant_id,
                    TenantTool.is_active == True
                ).first()
                if not tenant_tool:
                    raise HTTPException(status_code=403, detail=f"租户未配置工具 {tool.name}({tool_id})，请先在工具管理中添加")
    
    async def _validate_mcp_servers(self, mcp_server_ids: Optional[List[str]], tenant_id: str) -> None:
        """验证MCP服务器是否存在且属于当前租户"""
        if mcp_server_ids:
            from app.models.tool import MCPServer
            for server_id in mcp_server_ids:
                server = self.db.query(MCPServer).filter(
                    MCPServer.id == server_id,
                    MCPServer.tenant_id == tenant_id,
                    MCPServer.status == "active"
                ).first()
                if not server:
                    raise HTTPException(status_code=404, detail=f"MCP服务器 {server_id} 不存在或无权访问")
    
    async def _validate_agent_version_data(self, agent_data, tenant_id: str) -> None:
        """验证Agent版本相关数据"""
        await self._validate_model_exists(getattr(agent_data, 'model_id', None))
        await self._validate_knowledge_bases(getattr(agent_data, 'knowledge_base_ids', None), tenant_id)
        await self._validate_tools(getattr(agent_data, 'tool_ids', None), tenant_id)
        await self._validate_mcp_servers(getattr(agent_data, 'mcp_server_ids', None), tenant_id)
    
    def _get_knowledge_bases_info(self, knowledge_base_ids: List[str], tenant_id: str) -> List[Dict[str, Any]]:
        """获取知识库信息"""
        if not knowledge_base_ids:
            return []
        
        from app.models.knowledge import KnowledgeBase
        knowledge_bases = self.db.query(KnowledgeBase).filter(
            KnowledgeBase.id.in_(knowledge_base_ids),
            KnowledgeBase.tenant_id == tenant_id,
            KnowledgeBase.is_active == True
        ).all()
        
        return [
            {
                "id": kb.id,
                "name": kb.name,
                "description": kb.description,
                "created_at": kb.created_at,
                "updated_at": kb.updated_at
            }
            for kb in knowledge_bases
        ]
    
    def _get_tools_info(self, tool_ids: List[str]) -> List[Dict[str, Any]]:
        """获取工具信息"""
        if not tool_ids:
            return []
        
        from app.models.tool import Tool
        tools = self.db.query(Tool).filter(
            Tool.id.in_(tool_ids),
            Tool.is_active == True
        ).all()
        
        return [
            {
                "id": tool.id,
                "name": tool.name,
                "key": tool.key,
                "description": tool.description,
                "created_at": tool.created_at,
                "updated_at": tool.updated_at
            }
            for tool in tools
        ]
    
    def _get_mcp_servers_info(self, mcp_server_ids: List[str], tenant_id: str) -> List[Dict[str, Any]]:
        """获取MCP服务器信息"""
        if not mcp_server_ids:
            return []
        
        from app.models.tool import MCPServer
        servers = self.db.query(MCPServer).filter(
            MCPServer.id.in_(mcp_server_ids),
            MCPServer.tenant_id == tenant_id,
            MCPServer.status == "active"
        ).all()
        
        return [
            {
                "id": server.id,
                "name": server.name,
                "endpoint": server.endpoint,
                "transport_type": server.transport_type,
                "status": server.status,
                "created_at": server.created_at,
                "updated_at": server.updated_at
            }
            for server in servers
        ]
    
    def _set_version_as_current(self, agent_id: str, version_id: str) -> None:
        """设置指定版本为当前版本"""
        # 将该Agent的所有其他版本设为非当前版本
        self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.id != version_id
        ).update({"is_current": False})
    
    async def verify_tenant_access(self, tenant_id: str, agent_id: str) -> None:
        """验证租户对Agent的访问权限"""
        agent = self.db.query(Agent).filter(
            Agent.id == agent_id, 
            Agent.tenant_id == tenant_id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=404,
                detail="Agent不存在或您无权访问该Agent"
            )
    
    async def get_agents(
        self, 
        tenant_id: str, 
        skip: int = 0, 
        limit: int = 100, 
        name: Optional[str] = None,
        agent_type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> AgentListResponse:
        """获取Agent列表"""
        query = self.db.query(Agent).filter(Agent.tenant_id == tenant_id)
        
        if name:
            query = query.filter(Agent.name.ilike(f"%{name}%"))
        
        if agent_type:
            query = query.filter(Agent.agent_type == agent_type)
            
        if is_active is not None:
            query = query.filter(Agent.is_active == is_active)
        
        total = query.count()
        agents = query.order_by(desc(Agent.created_at)).offset(skip).limit(limit).all()
        
        agent_schemas = []
        
        for agent in agents:
            agent_schema = AgentSchema(
                id=agent.id,
                tenant_id=agent.tenant_id,
                open_id=agent.open_id,
                is_active=agent.is_active,
                name=agent.name,
                agent_type=agent.agent_type,
                is_supervisor=agent.is_supervisor,
                description=agent.description,
                model_id=agent.model_id,
                prompt=agent.prompt,
                created_at=agent.created_at,
                updated_at=agent.updated_at,
                icon_url=agent.icon_url,
                icon_access_url=agent.icon_access_url,
                icon_metadata=agent.icon_metadata,
                config=agent.config,
            )
            agent_schemas.append(agent_schema)
        
        return AgentListResponse(
            total=total,
            items=agent_schemas
        )
    
    async def get_agent(self, tenant_id: str, agent_id: str) -> Agent:
        """获取单个Agent详情"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        agent = self.db.query(Agent).filter(Agent.id == agent_id).first()
        return agent
    
    async def get_agent_detail(self, tenant_id: str, agent_id: str) -> Dict[str, Any]:
        """获取Agent详情，包括版本列表"""
        agent = await self.get_agent(tenant_id, agent_id)
        
        versions = await self.get_versions(tenant_id, agent_id)
        
        # 获取当前版本关联的资源信息
        current_version = agent.current_version
        knowledge_bases_info = []
        tools_info = []
        mcp_servers_info = []
        
        if current_version:
            knowledge_bases_info = self._get_knowledge_bases_info(
                current_version.knowledge_base_ids or [], tenant_id
            )
            tools_info = self._get_tools_info(current_version.tool_ids or [])
            mcp_servers_info = self._get_mcp_servers_info(
                current_version.mcp_server_ids or [], tenant_id
            )
        
        # 构建详细信息，包含所有字段
        agent_detail = {
            "id": agent.id,
            "tenant_id": agent.tenant_id,
            "name": agent.name,
            "agent_type": agent.agent_type,
            "is_supervisor": agent.is_supervisor,
            "open_id": agent.open_id,
            "description": agent.description,
            "config": agent.config,
            "is_active": agent.is_active,
            "model_id": agent.model_id,
            "prompt": agent.prompt,
            "created_at": agent.created_at,
            "updated_at": agent.updated_at,
            # 添加图标相关信息
            "icon_url": agent.icon_url,
            "icon_access_url": agent.icon_access_url,
            "icon_metadata": agent.icon_metadata,
            # 版本和关联资源信息
            "versions": versions,
            "knowledge_bases": knowledge_bases_info,
            "tools": tools_info,
            "mcp_servers": mcp_servers_info
        }
        
        return agent_detail
    
    def _merge_config(
        self, 
        base_config: Optional[Dict[str, Any]], 
        llm_model_config: Optional[Any], 
        knowledge_base_config: Optional[Any]
    ) -> Dict[str, Any]:
        """
        合并配置字段
        
        Args:
            base_config: 基础配置
            llm_model_config: 模型配置
            knowledge_base_config: 知识库配置
            
        Returns:
            Dict: 合并后的配置
        """
        config = base_config or {}
        
        # 添加模型配置
        if llm_model_config:
            if hasattr(llm_model_config, 'model_dump'):
                config['model_config'] = llm_model_config.model_dump(exclude_none=True)
            elif isinstance(llm_model_config, dict):
                config['model_config'] = {k: v for k, v in llm_model_config.items() if v is not None}
        
        # 添加知识库配置
        if knowledge_base_config:
            if hasattr(knowledge_base_config, 'model_dump'):
                config['knowledge_base_config'] = knowledge_base_config.model_dump(exclude_none=True)
            elif isinstance(knowledge_base_config, dict):
                config['knowledge_base_config'] = {k: v for k, v in knowledge_base_config.items() if v is not None}
        
        return config

    async def create_agent_unified(self, tenant_id: str, agent_data: AgentCreateRequest) -> Dict[str, Any]:
        """统一的Agent创建方法，草稿模式"""
        # 验证租户是否存在
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        if not tenant:
            raise HTTPException(status_code=404, detail="租户不存在")

        # 检查名称是否已存在
        existing = self.db.query(Agent).filter(
            Agent.name == agent_data.name,
            Agent.tenant_id == tenant_id
        ).first()
        
        if existing:
            raise HTTPException(status_code=400, detail="Agent名称已存在")
        
        # 获取模板配置
        template = None
        if agent_data.template_id:
            templates = self._get_agent_templates()
            template = next((t for t in templates if t["template_id"] == agent_data.template_id), None)
            if not template:
                raise HTTPException(status_code=404, detail="模板不存在")
        
        # 确定Agent类型
        agent_type = agent_data.agent_type
        if template and not agent_type:
            agent_type = AgentType(template["agent_type"])
        elif not agent_type:
            agent_type = AgentType.GENERAL
        
        # 验证Agent类型
        if agent_type not in AgentType.get_all_agent_types():
            raise HTTPException(status_code=400, detail="Agent类型不正确")
        
        # 合并描述
        description = agent_data.description
        if template and not description:
            description = template["description"]
        
        # 创建Agent基础信息
        agent = Agent(
            tenant_id=tenant_id,
            name=agent_data.name,
            agent_type=agent_type,
            open_id=self._generate_open_id(),
            description=description,
            is_active=True,
            created_at=self._get_current_utc_time()
        )
        
        self.db.add(agent)
        self.db.flush()  # 获取agent.id
        
        # 创建草稿版本
        result = await self._create_draft_version(agent, template, agent_data)
        
        self.db.commit()
        self.db.refresh(agent)
        
        return result

    async def _create_draft_version(self, agent: Agent, template: Optional[Dict], agent_data: AgentCreateRequest) -> Dict[str, Any]:
        """创建草稿版本"""
      
        await self._validate_model_exists(agent_data.model_id)
        await self._validate_knowledge_bases(agent_data.knowledge_base_ids, agent.tenant_id)
        await self._validate_tools(agent_data.tool_ids, agent.tenant_id)
        await self._validate_mcp_servers(agent_data.mcp_server_ids, agent.tenant_id)
        
        # 合并配置 - 优先级：用户指定 > 模板默认值
        default_config = template["default_config"] if template else {}
        custom_config = {}
        merged_config = {**default_config, **custom_config}
        
        # 合并LLM模型配置
        if agent_data.llm_model_config:
            merged_config["model_config"] = agent_data.llm_model_config.model_dump(exclude_none=True)
        
        # 合并知识库配置
        if agent_data.knowledge_base_config:
            merged_config["knowledge_base_config"] = agent_data.knowledge_base_config.model_dump(exclude_none=True)
        
        # 确定提示词
        prompt = agent_data.prompt
        if template and not prompt:
            prompt = template["default_prompt"]
        if not prompt:
            prompt = "你是一个有用的AI助手。"
        
        # 创建草稿版本
        draft_version = AgentVersion(
            agent_id=agent.id,
            version_number="draft",
            config=merged_config,
            prompt=prompt,
            description="初始草稿",
            model_id=agent_data.model_id,
            knowledge_base_ids=agent_data.knowledge_base_ids or [],
            tool_ids=agent_data.tool_ids or [],
            mcp_server_ids=agent_data.mcp_server_ids or [],
            is_current=False,
            is_published=False,
            created_at=self._get_current_utc_time(),
            updated_at=self._get_current_utc_time()
        )
        
        self.db.add(draft_version)
        
        # 生成草稿配置响应
        draft_config = {
            "model_id": agent_data.model_id,
            "prompt": prompt,
            "knowledge_base_ids": agent_data.knowledge_base_ids or [],
            "tool_ids": agent_data.tool_ids or [],
            "mcp_server_ids": agent_data.mcp_server_ids or [],
            "llm_model_config": agent_data.llm_model_config,
            "knowledge_base_config": agent_data.knowledge_base_config,
            "custom_config": merged_config,
            "has_changes": False,
            "is_ready_to_publish": bool(agent_data.model_id),  # 有模型就可以发布
            "validation_errors": [],
            "last_saved_at": draft_version.updated_at,
            "last_published_at": None
        }
        
        # 生成下一步提示
        next_steps = []
        if template and template.get("config_wizard"):
            next_steps = [step["title"] for step in template["config_wizard"]]
        else:
            if not agent_data.model_id:
                next_steps.append("选择LLM模型")
            if not agent_data.prompt or agent_data.prompt == prompt:
                next_steps.append("完善提示词")
            if not agent_data.knowledge_base_ids:
                next_steps.append("添加知识库（可选）")
            if not agent_data.tool_ids:
                next_steps.append("添加工具（可选）")
            if not next_steps:
                next_steps = ["配置完成，可以发布"]
        
        return {
            "agent": agent,
            "draft_config": draft_config,
            "next_steps": next_steps
        }

    async def update_agent(self, tenant_id: str, agent_id: str, agent_data: AgentUpdate) -> Agent:
        """更新Agent信息"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        agent = self.db.query(Agent).filter(Agent.id == agent_id).first()
        
        # 如果要更新名称，检查名称是否已存在
        if agent_data.name and agent_data.name != agent.name:
            existing = self.db.query(Agent).filter(
                Agent.name == agent_data.name,
                Agent.tenant_id == tenant_id,
                Agent.id != agent_id
            ).first()
            
            if existing:
                raise HTTPException(status_code=400, detail="Agent名称已存在")
        
        
        # 更新非None字段（不包括config和model_id，这些在版本中管理）
        update_data = agent_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(agent, key, value)
        
        
        # 更新时间戳
        agent.updated_at = self._get_current_utc_time()
        
        self.db.commit()
        self.db.refresh(agent)
        
        return agent
    
    async def delete_agent(self, tenant_id: str, agent_id: str) -> Dict[str, Any]:
        """删除Agent"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        agent = self.db.query(Agent).filter(Agent.id == agent_id).first()
        
        # 不直接删除，而是设置为非活动状态
        agent.is_active = False
        self.db.commit()
        
        return {"success": True, "message": "Agent已停用"}
    
    
    async def get_versions(self, tenant_id: str, agent_id: str) -> List[AgentVersion]:
        """获取Agent版本列表"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        versions = self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id
        ).order_by(desc(AgentVersion.created_at)).all()
        
        return versions
    
    
    async def get_version(self, tenant_id: str, agent_id: str, version_id: str) -> AgentVersion:
        """获取特定Agent版本"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        version = self.db.query(AgentVersion).filter(
            AgentVersion.id == version_id,
            AgentVersion.agent_id == agent_id
        ).first()
        
        if not version:
            raise HTTPException(status_code=404, detail="版本不存在")
            
        return version
    
    async def publish_version(self, tenant_id: str, agent_id: str, version_id: str) -> Agent:
        """发布指定版本"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        version = self.db.query(AgentVersion).filter(
            AgentVersion.id == version_id,
            AgentVersion.agent_id == agent_id
        ).first()
        
        if not version:
            raise HTTPException(status_code=404, detail="版本不存在")
        
        # 更新为发布状态
        version.is_published = True
        
        # 设置为当前版本
        self._set_version_as_current(agent_id, version_id)
        version.is_current = True
        
        self.db.commit()
        
        # 刷新Agent
        agent = self.db.query(Agent).filter(Agent.id == agent_id).first()
        self.db.refresh(agent)
        
        return agent
    
    async def upload_icon(
        self, 
        tenant_id: str, 
        agent_id: str, 
        icon_file: UploadFile
    ) -> AgentIconUploadResponse:
        """
        为Agent上传图标
        
        Args:
            tenant_id: 租户ID
            agent_id: Agent ID
            icon_file: 图标文件
            
        Returns:
            AgentIconUploadResponse: 上传响应
        """
        # 验证Agent存在且属于当前租户
        await self.verify_tenant_access(tenant_id, agent_id)
        
        agent = self.db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.tenant_id == tenant_id
        ).first()
        
        if not agent:
            raise HTTPException(status_code=404, detail="Agent不存在")
        
        try:
            # 使用原子性上传：先上传到临时位置，成功后替换正式图标
            icon_url, metadata = await s3_storage_service.upload_agent_icon_atomic(
                icon_file, tenant_id, agent_id
            )
            
            # 上传成功后，更新数据库记录
            agent.icon_url = icon_url
            agent.icon_metadata = metadata
            agent.updated_at = self._get_current_utc_time()
            
            self.db.commit()
            
            # 生成访问URL
            access_url = agent.icon_access_url
            
            return AgentIconUploadResponse(
                success=True,
                message="图标上传成功",
                icon_url=icon_url,
                icon_access_url=access_url,
                metadata=metadata
            )
            
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"图标上传失败: {str(e)}"
            )
    
    async def delete_icon(self, tenant_id: str, agent_id: str) -> AgentIconDeleteResponse:
        """
        删除Agent图标
        
        Args:
            tenant_id: 租户ID
            agent_id: Agent ID
            
        Returns:
            AgentIconDeleteResponse: 删除响应
        """
        # 验证Agent存在且属于当前租户
        await self.verify_tenant_access(tenant_id, agent_id)
        
        agent = self.db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.tenant_id == tenant_id
        ).first()
        
        if not agent:
            raise HTTPException(status_code=404, detail="Agent不存在")
        
        if not agent.icon_url:
            return AgentIconDeleteResponse(
                success=True,
                message="Agent没有设置图标"
            )
        
        try:
            # 从S3删除图标文件
            delete_success = s3_storage_service.delete_agent_icon(tenant_id, agent_id)
            
            # 更新数据库记录
            agent.icon_url = None
            agent.icon_metadata = None
            agent.updated_at = self._get_current_utc_time()
            
            self.db.commit()
            
            if delete_success:
                return AgentIconDeleteResponse(
                    success=True,
                    message="图标删除成功"
                )
            else:
                return AgentIconDeleteResponse(
                    success=True,
                    message="数据库记录已清除，但S3文件删除失败"
                )
                
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"图标删除失败: {str(e)}"
            )
    
    def get_icon_url(self, tenant_id: str, agent_id: str) -> Optional[str]:
        """
        获取Agent图标的访问URL
        
        Args:
            tenant_id: 租户ID
            agent_id: Agent ID
            
        Returns:
            Optional[str]: 图标访问URL，不存在则返回None
        """
        agent = self.db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.tenant_id == tenant_id
        ).first()
        
        if not agent:
            return None
            
        return agent.icon_access_url
    
    # ================= 简化版本管理方案实现 =================
    
    def _get_agent_templates(self) -> List[Dict[str, Any]]:
        """获取Agent创建模板"""
        return [
            {
                "template_id": "general_assistant",
                "name": "通用助手",
                "agent_type": "general",
                "description": "适用于日常对话和问答的通用AI助手",
                "icon_url": None,
                # 与AgentCreateRequest字段保持一致的默认配置
                "default_config": {
                    "model_id": None,  # 需要用户选择
                    "prompt": "你是一个友善、专业的AI助手，能够帮助用户解答问题、提供建议和完成各种任务。",
                    "knowledge_base_ids": [],
                    "tool_ids": [],
                    "mcp_server_ids": [],
                    "llm_model_config": {
                        "temperature": 0.7,
                        "max_tokens": 2000,
                        "top_p": 0.5,
                        "max_retries": 3,
                        "timeout": 10
                    },
                    "knowledge_base_config": {
                        "max_results": 5,
                        "min_score": 0.2
                    }
                },
                "suggested_model_ids": [],  # 实际模型ID，在运行时填充
                "suggested_tool_ids": [],
                "suggested_knowledge_base_ids": [],
                "config_wizard": [
                    {
                        "step": 1,
                        "title": "选择模型",
                        "description": "选择适合的语言模型",
                        "field": "model_id",
                        "required": True,
                        "field_type": "select",
                        "options_source": "available_models"
                    },
                    {
                        "step": 2,
                        "title": "设置提示词",
                        "description": "定义Agent的角色和行为",
                        "field": "prompt",
                        "required": True,
                        "field_type": "textarea",
                        "default_value": "你是一个友善、专业的AI助手，能够帮助用户解答问题、提供建议和完成各种任务。"
                    },
                    {
                        "step": 3,
                        "title": "模型参数配置",
                        "description": "调整LLM模型参数",
                        "field": "llm_model_config",
                        "required": False,
                        "field_type": "object"
                    }
                ]
            },
            {
                "template_id": "knowledge_qa",
                "name": "知识问答助手",
                "agent_type": "general",
                "description": "基于知识库的专业问答助手",
                "icon_url": None,
                "default_config": {
                    "model_id": None,
                    "prompt": "你是一个专业的知识问答助手，基于提供的知识库内容回答用户问题。请确保回答准确、详细，并在必要时引用相关资料。",
                    "knowledge_base_ids": [],
                    "tool_ids": [],
                    "mcp_server_ids": [],
                    "llm_model_config": {
                        "temperature": 0.3,
                        "max_tokens": 2000,
                        "top_p": 0.5,
                        "max_retries": 3,
                        "timeout": 10
                    },
                    "knowledge_base_config": {
                        "max_results": 5,
                        "min_score": 0.7
                    }
                },
                "suggested_model_ids": [],
                "suggested_tool_ids": [],
                "suggested_knowledge_base_ids": [],
                "config_wizard": [
                    {
                        "step": 1,
                        "title": "选择模型",
                        "description": "选择适合的语言模型",
                        "field": "model_id",
                        "required": True,
                        "field_type": "select",
                        "options_source": "available_models"
                    },
                    {
                        "step": 2,
                        "title": "选择知识库",
                        "description": "选择相关的知识库",
                        "field": "knowledge_base_ids",
                        "required": True,
                        "field_type": "multi_select",
                        "options_source": "available_knowledge_bases"
                    },
                    {
                        "step": 3,
                        "title": "设置提示词",
                        "description": "定义Agent的角色和行为",
                        "field": "prompt",
                        "required": True,
                        "field_type": "textarea",
                        "default_value": "你是一个专业的知识问答助手，基于提供的知识库内容回答用户问题。请确保回答准确、详细，并在必要时引用相关资料。"
                    },
                    {
                        "step": 4,
                        "title": "配置检索参数",
                        "description": "调整知识库检索设置",
                        "field": "knowledge_base_config",
                        "required": False,
                        "field_type": "object"
                    }
                ]
            },
            {
                "template_id": "customer_service",
                "name": "客服助手",
                "agent_type": "general",
                "description": "专业的客户服务AI助手",
                "icon_url": None,
                "default_config": {
                    "model_id": None,
                    "prompt": "你是一个专业的客服代表，友善、耐心地帮助客户解决问题。始终保持礼貌和专业的态度。",
                    "knowledge_base_ids": [],
                    "tool_ids": [],
                    "mcp_server_ids": [],
                    "llm_model_config": {
                        "temperature": 0.3,
                        "max_tokens": 1500,
                        "top_p": 0.5,
                        "max_retries": 3,
                        "timeout": 10
                    },
                    "knowledge_base_config": {
                        "max_results": 3,
                        "min_score": 0.6
                    }
                },
                "suggested_model_ids": [],
                "suggested_tool_ids": [],
                "suggested_knowledge_base_ids": [],
                "config_wizard": [
                    {
                        "step": 1,
                        "title": "选择模型",
                        "description": "选择适合的语言模型",
                        "field": "model_id",
                        "required": True,
                        "field_type": "select",
                        "options_source": "available_models"
                    },
                    {
                        "step": 2,
                        "title": "配置客服工具",
                        "description": "选择客服相关的工具",
                        "field": "tool_ids",
                        "required": True,
                        "field_type": "multi_select",
                        "options_source": "available_tools"
                    },
                    {
                        "step": 3,
                        "title": "设置提示词",
                        "description": "定义Agent的客服角色和行为规范",
                        "field": "prompt",
                        "required": True,
                        "field_type": "textarea",
                        "default_value": "你是一个专业的客服代表，友善、耐心地帮助客户解决问题。始终保持礼貌和专业的态度。"
                    },
                    {
                        "step": 4,
                        "title": "配置知识库（可选）",
                        "description": "选择客服知识库以提供更准确的答案",
                        "field": "knowledge_base_ids",
                        "required": False,
                        "field_type": "multi_select",
                        "options_source": "available_knowledge_bases"
                    }
                ]
            }
        ]
    
    async def get_agent_templates(self, tenant_id: str) -> Dict[str, Any]:
        """获取Agent创建模板和可用资源"""
        templates = self._get_agent_templates()
        
        # 获取租户可用资源
        available_resources = await self._get_tenant_available_resources(tenant_id)
        
        # 根据模板类型和available_resources填充建议的资源ID
        filtered_templates = []
        for template in templates:
            template_copy = template.copy()
            
            # 根据模板类型智能推荐模型
            available_model_ids = [m["id"] for m in available_resources.get("models", [])]
            if template["template_id"] == "general_assistant":
                # 通用助手优先推荐GPT-4类模型
                template_copy["suggested_model_ids"] = self._filter_preferred_models(
                    available_model_ids, ["gpt-4", "claude", "qwen"]
                )
            elif template["template_id"] == "knowledge_qa":
                # 知识问答优先推荐高精度模型
                template_copy["suggested_model_ids"] = self._filter_preferred_models(
                    available_model_ids, ["gpt-4", "claude-3", "qwen-max"]
                )
            elif template["template_id"] == "customer_service":
                # 客服助手优先推荐稳定性好的模型
                template_copy["suggested_model_ids"] = self._filter_preferred_models(
                    available_model_ids, ["gpt-3.5-turbo", "qwen-turbo", "claude-instant"]
                )
            elif template["template_id"] == "supervisor_agent":
                # 监督者需要推理能力强的模型
                template_copy["suggested_model_ids"] = self._filter_preferred_models(
                    available_model_ids, ["gpt-4", "claude-3-opus", "qwen-max"]
                )
            else:
                template_copy["suggested_model_ids"] = available_model_ids[:3]  # 默认推荐前3个
            
            # 根据模板类型推荐工具
            available_tool_ids = [t["id"] for t in available_resources.get("tools", [])]
            if template["template_id"] == "knowledge_qa":
                template_copy["suggested_tool_ids"] = self._filter_tools_by_type(
                    available_resources.get("tools", []), ["search", "summarize", "knowledge"]
                )
            elif template["template_id"] == "customer_service":
                template_copy["suggested_tool_ids"] = self._filter_tools_by_type(
                    available_resources.get("tools", []), ["query", "ticket", "order", "knowledge"]
                )
            elif template["template_id"] == "supervisor_agent":
                template_copy["suggested_tool_ids"] = self._filter_tools_by_type(
                    available_resources.get("tools", []), ["agent_call", "workflow", "task"]
                )
            else:
                template_copy["suggested_tool_ids"] = available_tool_ids[:5]  # 默认推荐前5个
            
            # 推荐知识库（通常所有模板都可以选择）
            template_copy["suggested_knowledge_base_ids"] = [
                kb["id"] for kb in available_resources.get("knowledge_bases", [])
            ][:10]  # 最多推荐10个知识库
            
            filtered_templates.append(template_copy)
        
        # 转换为schema对象
        from app.schemas.agent import AvailableResources, AvailableModel, AvailableTool, AvailableKnowledgeBase, AvailableMCPServer
        
        available_resources_schema = AvailableResources(
            models=[AvailableModel(**model) for model in available_resources["models"]],
            tools=[AvailableTool(**tool) for tool in available_resources["tools"]],
            knowledge_bases=[AvailableKnowledgeBase(**kb) for kb in available_resources["knowledge_bases"]],
            mcp_servers=[AvailableMCPServer(**server) for server in available_resources["mcp_servers"]]
        )
        
        return {
            "templates": filtered_templates,
            "available_resources": available_resources_schema
        }
    
    def _filter_preferred_models(self, available_model_ids: List[str], preferred_keys: List[str]) -> List[str]:
        """根据偏好的模型key过滤可用模型ID"""
        # 这里需要根据模型的key来匹配，但我们只有ID
        # 我们可以通过查询数据库来获取匹配的模型ID
        from app.models.model import Model
        
        preferred_model_ids = []
        for key_pattern in preferred_keys:
            models = self.db.query(Model).filter(
                Model.id.in_(available_model_ids),
                Model.key.ilike(f"%{key_pattern}%")
            ).all()
            preferred_model_ids.extend([m.id for m in models])
        
        # 去重并补充其他可用模型
        result = list(dict.fromkeys(preferred_model_ids))  # 去重保持顺序
        remaining = [mid for mid in available_model_ids if mid not in result]
        result.extend(remaining[:3])  # 最多补充3个其他模型
        
        return result[:5]  # 最多返回5个推荐模型
    
    def _filter_tools_by_type(self, available_tools: List[Dict], type_keywords: List[str]) -> List[str]:
        """根据工具类型关键词过滤工具ID"""
        matched_tool_ids = []
        for tool in available_tools:
            tool_key = tool.get("key", "").lower()
            tool_name = tool.get("name", "").lower()
            
            for keyword in type_keywords:
                if keyword.lower() in tool_key or keyword.lower() in tool_name:
                    matched_tool_ids.append(tool["id"])
                    break
        
        # 去重并补充其他工具
        result = list(dict.fromkeys(matched_tool_ids))
        remaining = [t["id"] for t in available_tools if t["id"] not in result]
        result.extend(remaining[:3])  # 最多补充3个其他工具
        
        return result[:8]  # 最多返回8个推荐工具
    
    async def _get_tenant_available_resources(self, tenant_id: str) -> Dict[str, Any]:
        """获取租户可用资源 - 返回详细信息用于前端展示"""
        # 获取可用模型
        from app.models.model import Model
        models = self.db.query(Model).filter(
            Model.tenant_id == tenant_id
        ).all()
        
        # 获取可用工具
        from app.models.tool import TenantTool, Tool
        tenant_tools = self.db.query(TenantTool).join(Tool).filter(
            TenantTool.tenant_id == tenant_id,
            TenantTool.is_active == True,
            Tool.is_active == True
        ).all()
        
        # 获取可用知识库
        from app.models.knowledge import KnowledgeBase
        knowledge_bases = self.db.query(KnowledgeBase).filter(
            KnowledgeBase.tenant_id == tenant_id,
            KnowledgeBase.is_active == True
        ).all()
        
        # 获取可用MCP服务器
        from app.models.tool import MCPServer
        mcp_servers = self.db.query(MCPServer).filter(
            MCPServer.tenant_id == tenant_id,
            MCPServer.status == "active"
        ).all()
        
        return {
            "models": [
                {
                    "id": m.id,
                    "name": m.name,
                    "key": m.key,
                    "description": m.description,
                    "model_type": m.model_type
                }
                for m in models
            ],
            "tools": [
                {
                    "id": tt.tool.id,
                    "tenant_tool_id": tt.id,
                    "name": tt.tool.name,
                    "key": tt.tool.key,
                    "description": tt.tool.description,
                    "config_schema": tt.tool.config_schema,
                    "tenant_config": tt.config
                }
                for tt in tenant_tools
            ],
            "knowledge_bases": [
                {
                    "id": kb.id,
                    "name": kb.name,
                    "description": kb.description,
                    "document_count": getattr(kb, 'document_count', 0),
                    "created_at": kb.created_at,
                    "updated_at": kb.updated_at
                }
                for kb in knowledge_bases
            ],
            "mcp_servers": [
                {
                    "id": server.id,
                    "name": server.name,
                    "endpoint": server.endpoint,
                    "transport_type": server.transport_type,
                    "status": server.status,
                    "last_heartbeat": server.last_heartbeat
                }
                for server in mcp_servers
            ]
        }
    
    async def get_agent_history(self, tenant_id: str, agent_id: str) -> List[Dict[str, Any]]:
        """获取Agent版本历史"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        versions = self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.is_published == True,
            AgentVersion.version_number != "draft"
        ).order_by(desc(AgentVersion.created_at)).all()
        
        return [
            {
                "version_id": v.id,
                "version_number": v.version_number,
                "release_notes": v.description,
                "published_at": v.created_at,
                "is_current": v.is_current,
                "config_summary": self._generate_config_summary(v)
            }
            for v in versions
        ]
    
    async def rollback_agent_version(self, tenant_id: str, agent_id: str, version_id: str, rollback_request: AgentRollbackRequest) -> Dict[str, Any]:
        """回滚到指定版本"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        # 获取目标版本
        target_version = self.db.query(AgentVersion).filter(
            AgentVersion.id == version_id,
            AgentVersion.agent_id == agent_id,
            AgentVersion.is_published == True
        ).first()
        
        if not target_version:
            raise HTTPException(status_code=404, detail="目标版本不存在")
        
        # 将所有版本设为非当前版本
        self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.is_current == True
        ).update({"is_current": False})
        
        # 设置目标版本为当前版本
        target_version.is_current = True
        
        # 删除现有草稿
        self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.version_number == "draft"
        ).delete()
        
        # 基于目标版本创建新草稿
        new_draft = self._create_draft_from_version(agent_id, target_version)
        
        self.db.commit()
        
        return {
            "success": True,
            "current_version": target_version.version_number,
            "rollback_reason": rollback_request.reason,
            "new_draft_created": True
        }
    
    async def validate_agent_config(self, tenant_id: str, agent_id: str) -> Dict[str, Any]:
        """验证Agent配置"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        draft = self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.version_number == "draft"
        ).first()
        
        if not draft:
            raise HTTPException(status_code=404, detail="没有找到草稿配置")
        
        return self._validate_draft_config(draft)
    
    def _generate_config_summary(self, version: AgentVersion) -> Dict[str, Any]:
        """生成配置摘要"""
        return {
            "model": version.model_id or "未配置",
            "knowledge_bases_count": len(version.knowledge_base_ids) if version.knowledge_base_ids else 0,
            "tools_count": len(version.tool_ids) if version.tool_ids else 0,
            "mcp_servers_count": len(version.mcp_server_ids) if version.mcp_server_ids else 0,
            "has_custom_config": bool(version.config)
        }
    
    async def get_agent_draft(self, tenant_id: str, agent_id: str) -> Dict[str, Any]:
        """获取Agent草稿配置"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        # 查找草稿版本
        draft = self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.version_number == "draft"
        ).first()
        
        if not draft:
            # 如果没有草稿，基于当前版本创建草稿
            current_version = self.db.query(AgentVersion).filter(
                AgentVersion.agent_id == agent_id,
                AgentVersion.is_current == True
            ).first()
            
            draft = self._create_draft_from_version(agent_id, current_version)
        
        # 验证配置
        validation = self._validate_draft_config(draft)
        
        # 获取最后发布时间
        last_published_version = self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.is_published == True
        ).order_by(desc(AgentVersion.created_at)).first()
        
        return {
            "model_id": draft.model_id,
            "prompt": draft.prompt,
            "knowledge_base_ids": draft.knowledge_base_ids or [],
            "tool_ids": draft.tool_ids or [],
            "mcp_server_ids": draft.mcp_server_ids or [],
            "llm_model_config": self._extract_model_config(draft.config),
            "knowledge_base_config": self._extract_knowledge_base_config(draft.config),
            "custom_config": draft.config,
            "has_changes": True,  # 草稿总是有变化
            "is_ready_to_publish": validation["is_valid"],
            "validation_errors": validation["errors"],
            "last_saved_at": draft.updated_at,
            "last_published_at": last_published_version.updated_at if last_published_version else None
        }
    
    def _create_draft_from_version(self, agent_id: str, source_version: Optional[AgentVersion]) -> AgentVersion:
        """基于现有版本创建草稿"""
        if source_version:
            draft = AgentVersion(
                agent_id=agent_id,
                version_number="draft",
                config=source_version.config,
                prompt=source_version.prompt,
                description="基于 " + source_version.version_number + " 的草稿",
                model_id=source_version.model_id,
                knowledge_base_ids=source_version.knowledge_base_ids,
                tool_ids=source_version.tool_ids,
                mcp_server_ids=source_version.mcp_server_ids,
                is_current=False,
                is_published=False,
                created_at=self._get_current_utc_time()
            )
        else:
            # 创建空草稿
            draft = AgentVersion(
                agent_id=agent_id,
                version_number="draft",
                config={},
                prompt="你是一个有用的AI助手。",
                description="初始草稿",
                model_id=None,
                knowledge_base_ids=[],
                tool_ids=[],
                mcp_server_ids=[],
                is_current=False,
                is_published=False,
                created_at=self._get_current_utc_time()
            )
        
        self.db.add(draft)
        self.db.commit()
        self.db.refresh(draft)
        return draft

    async def update_agent_draft(self, tenant_id: str, agent_id: str, config_data: AgentDraftUpdateRequest) -> Dict[str, Any]:
        """更新Agent草稿配置"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        # 获取或创建草稿
        draft = self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.version_number == "draft"
        ).first()
        
        if not draft:
            current_version = self.db.query(AgentVersion).filter(
                AgentVersion.agent_id == agent_id,
                AgentVersion.is_current == True
            ).first()
            draft = self._create_draft_from_version(agent_id, current_version)
        
        # 更新配置
        if config_data.model_id:
            draft.model_id = config_data.model_id
        
        if config_data.prompt:
            draft.prompt = config_data.prompt
        
        if config_data.knowledge_base_ids:
            draft.knowledge_base_ids = config_data.knowledge_base_ids
        else:
            draft.knowledge_base_ids = []
        
        if config_data.tool_ids:
            draft.tool_ids = config_data.tool_ids
        else:
            draft.tool_ids = []
        
        if config_data.mcp_server_ids:
            draft.mcp_server_ids = config_data.mcp_server_ids
        else:
            draft.mcp_server_ids = []
        
        # 完全重新创建配置字典，确保 SQLAlchemy 检测到变化
        original_config = draft.config or {}
        
        # 创建全新的配置字典
        new_config = {}
        
        # 首先复制现有配置
        if original_config:
            new_config.update(original_config)
        
        # 处理 LLM 模型配置更新
        if config_data.llm_model_config:
            # 确保正确转换 Pydantic 模型到字典
            if isinstance(config_data.llm_model_config, dict):
                model_config = config_data.llm_model_config.copy()  # 创建副本
            else:
                model_config = config_data.llm_model_config.model_dump(exclude_none=True)
            # 更新模型配置
            new_config["model_config"] = model_config
        
        # 处理知识库配置更新
        if config_data.knowledge_base_config:
            # 同样处理知识库配置
            if isinstance(config_data.knowledge_base_config, dict):
                kb_config = config_data.knowledge_base_config.copy()  # 创建副本
            else:
                kb_config = config_data.knowledge_base_config.model_dump(exclude_none=True)
            new_config["knowledge_base_config"] = kb_config
                
        # 直接设置全新的配置字典
        draft._config = new_config
        
        # 更新时间戳
        draft.updated_at = self._get_current_utc_time()
        
        
        self.db.commit()
        self.db.refresh(draft)  # 刷新对象以获取最新数据
        
        
        # 验证配置
        validation = self._validate_draft_config(draft)
        
        return {
            "success": True,
            "has_changes": True,
            "is_ready_to_publish": validation["is_valid"],
            "validation_errors": validation["errors"],
            "last_saved_at": draft.updated_at
        }
    
    def _validate_draft_config(self, draft: AgentVersion) -> Dict[str, Any]:
        """验证草稿配置"""
        errors = []
        warnings = []
        suggestions = []
        
        # 基础验证
        if not draft.prompt or not draft.prompt.strip():
            errors.append("提示词不能为空")
        
        if not draft.model_id:
            errors.append("必须选择LLM模型")
        
        # 知识库验证
        if draft.knowledge_base_ids:
            # 验证知识库是否存在且可用
            from app.models.knowledge import KnowledgeBase
            agent = self.db.query(Agent).filter(Agent.id == draft.agent_id).first()
            if agent:
                kb_count = self.db.query(KnowledgeBase).filter(
                    KnowledgeBase.id.in_(draft.knowledge_base_ids),
                    KnowledgeBase.tenant_id == agent.tenant_id,
                    KnowledgeBase.is_active == True
                ).count()
                
                if kb_count != len(draft.knowledge_base_ids):
                    warnings.append("部分知识库不可用或不存在")
        
        # 工具验证
        if draft.tool_ids:
            from app.models.tool import TenantTool, Tool
            agent = self.db.query(Agent).filter(Agent.id == draft.agent_id).first()
            if agent:
                tool_count = self.db.query(TenantTool).join(Tool).filter(
                    Tool.id.in_(draft.tool_ids),
                    TenantTool.tenant_id == agent.tenant_id,
                    TenantTool.is_active == True,
                    Tool.is_active == True
                ).count()
                
                if tool_count != len(draft.tool_ids):
                    warnings.append("部分工具不可用或未激活")
        
        # 建议
        if not draft.knowledge_base_ids and not draft.tool_ids:
            suggestions.append("考虑添加知识库或工具以增强Agent能力")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "suggestions": suggestions
        }
    
    def _extract_model_config(self, config: Dict) -> Optional[Dict]:
        """从config中提取模型配置"""
        if not config or "model_config" not in config:
            return None
        return config["model_config"]
    
    def _extract_knowledge_base_config(self, config: Dict) -> Optional[Dict]:
        """从config中提取知识库配置"""
        if not config or "knowledge_base_config" not in config:
            return None
        return config["knowledge_base_config"]

    async def publish_agent_config(self, tenant_id: str, agent_id: str, publish_request: AgentPublishRequest) -> Dict[str, Any]:
        """发布Agent配置"""
        await self.verify_tenant_access(tenant_id, agent_id)
        
        # 获取草稿
        draft = self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.version_number == "draft"
        ).first()
        
        if not draft:
            raise HTTPException(status_code=404, detail="没有找到草稿配置")
        
        # 验证配置
        validation = self._validate_draft_config(draft)
        if not validation["is_valid"]:
            raise HTTPException(status_code=400, detail=f"配置验证失败: {', '.join(validation['errors'])}")
        
        # 生成新版本号 - 默认使用minor
        new_version_number = self._generate_next_version_number(agent_id, "minor")
        
        # 将其他版本设为非当前版本
        self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.is_current == True
        ).update({"is_current": False})
        
        # 更新草稿为正式版本
        draft.version_number = new_version_number
        draft.is_published = True
        draft.is_current = True
        draft.description = publish_request.release_notes or f"发布版本 {new_version_number}"
        
        self.db.commit()
        
        # 自动激活Agent
        agent = self.db.query(Agent).filter(Agent.id == agent_id).first()
        agent.is_active = True
        self.db.commit()
        
        # 创建新的草稿
        new_draft = self._create_draft_from_version(agent_id, draft)
        
        return {
            "success": True,
            "version": {
                "version_id": draft.id,
                "version_number": draft.version_number,
                "release_notes": draft.description,
                "published_at": draft.created_at,
                "is_current": True,
                "config_summary": self._generate_config_summary(draft)
            },
            "agent": agent
        }
    
    def _generate_next_version_number(self, agent_id: str, version_type: str) -> str:
        """生成下一个版本号"""
        # 获取最新的已发布版本
        latest_version = self.db.query(AgentVersion).filter(
            AgentVersion.agent_id == agent_id,
            AgentVersion.is_published == True,
            AgentVersion.version_number != "draft"
        ).order_by(desc(AgentVersion.created_at)).first()
        
        if not latest_version:
            return "1.0.0"
        
        # 解析版本号
        try:
            major, minor, patch = map(int, latest_version.version_number.split('.'))
        except ValueError:
            return "1.0.0"
        
        # 根据类型递增
        if version_type == "major":
            major += 1
            minor = 0
            patch = 0
        elif version_type == "minor":
            minor += 1
            patch = 0
        else:  # patch
            patch += 1
        
        return f"{major}.{minor}.{patch}"
    