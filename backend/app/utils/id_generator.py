"""
ID生成器工具
提供UUIDBase62风格的ID生成，类似Stripe的ID格式
例如: user_1a2b3c4d5e6f7g8h9i0j, tenant_abcdefg123456
"""
import uuid
import time
import base64
import hashlib
import random
import string
from typing import Optional

# Base62字符集，用于ID编码
BASE62_CHARS = string.ascii_lowercase + string.digits + string.ascii_uppercase

def base62_encode(num: int) -> str:
    """
    将整数编码为Base62字符串
    """
    if num == 0:
        return BASE62_CHARS[0]
    
    arr = []
    base = len(BASE62_CHARS)
    
    while num:
        num, rem = divmod(num, base)
        arr.append(BASE62_CHARS[rem])
    
    arr.reverse()
    return ''.join(arr)

def generate_unique_id(prefix: str, length: int = 20) -> str:
    """
    生成Stripe风格的唯一ID
    格式: prefix_随机字符串
    例如: user_1a2b3c4d5e6f7g8h9i0j
    """
    # 使用当前时间戳、随机UUID和随机数作为种子
    timestamp = int(time.time() * 1000)
    random_uuid = uuid.uuid4().hex
    random_num = random.randint(1000000, 9999999)
    
    # 组合种子并进行哈希
    combined = f"{timestamp}_{random_uuid}_{random_num}"
    hash_obj = hashlib.sha256(combined.encode())
    hash_int = int(hash_obj.hexdigest(), 16)
    
    # 编码为Base62并截取指定长度
    unique_part = base62_encode(hash_int)[:length]
    
    # 添加前缀
    return f"{prefix}_{unique_part}"

def generate_user_id() -> str:
    """生成用户ID"""
    return generate_unique_id("user")

def generate_tenant_id() -> str:
    """生成租户ID"""
    return generate_unique_id("tnt")

def generate_role_id() -> str:
    """生成角色ID"""
    return generate_unique_id("role")

def generate_agent_id() -> str:
    """生成Agent ID"""
    return generate_unique_id("agt")

def generate_tool_id() -> str:
    """生成工具ID"""
    return generate_unique_id("tool")

def generate_session_id() -> str:
    """生成会话ID"""
    return generate_unique_id("sess")

def generate_kb_id() -> str:
    """生成知识库ID"""
    return generate_unique_id("kb")

def generate_model_id() -> str:
    """生成模型ID"""
    return generate_unique_id("mdl")

def generate_invite_id() -> str:
    """生成邀请ID"""
    return generate_unique_id("inv")

def generate_token() -> str:
    """生成令牌，不包含前缀"""
    return generate_unique_id("", 24)

def generate_provider_id() -> str:
    """生成提供商ID"""
    return generate_unique_id("prv")

def generate_provider_access_credential_id() -> str:
    """生成提供商访问凭证ID"""
    return generate_unique_id("paci")


def generate_model_call_log_id() -> str:
    """生成模型调用日志ID"""
    return generate_unique_id("mcl")

def generate_knowledge_file_id() -> str:
    """生成知识库文件ID"""
    return generate_unique_id("kbf")

def generate_knowledge_chunk_id() -> str:
    """生成知识库块ID"""
    return generate_unique_id("kbch")

def generate_knowledge_snapshot_id() -> str:
    """生成知识库快照ID"""
    return generate_unique_id("kbsn")

def generate_knowledge_search_id() -> str:
    """生成知识库搜索ID"""
    return generate_unique_id("kbs")

def generate_access_credential_id() -> str:
    """生成访问凭证ID"""
    return generate_unique_id("acc")

def generate_access_token_id() -> str:
    """生成访问令牌ID"""
    return generate_unique_id("atk")

def generate_agent_version_id() -> str:
    """生成Agent版本ID"""
    return generate_unique_id("agv")

def generate_agent_call_association_id() -> str:
    """生成Agent调用关联ID"""
    return generate_unique_id("agca")

def generate_session_message_id() -> str:
    """生成会话消息ID"""
    return generate_unique_id("sms")

def generate_agent_tool_id() -> str:
    """生成Agent工具ID"""
    return generate_unique_id("agt")

def generate_tool_execution_log_id() -> str:
    """生成工具执行日志ID"""
    return generate_unique_id("tel")

def generate_mcp_server_id() -> str:
    """生成MCP服务器ID"""
    return generate_unique_id("mcps")

def generate_mcp_tool_id() -> str:
    """生成MCP工具ID"""
    return generate_unique_id("mcpt")

def generate_tenant_tool_id() -> str:
    """生成租户工具ID"""
    return generate_unique_id("tt")

def generate_task_id() -> str:
    """生成任务ID"""
    return generate_unique_id("tsk")



# 测试
if __name__ == "__main__":
    print(f"用户ID: {generate_user_id()}")
    print(f"租户ID: {generate_tenant_id()}")
    print(f"角色ID: {generate_role_id()}")
    print(f"邀请ID: {generate_invite_id()}")
    print(f"令牌: {generate_token()}") 