# Available Resources Schema 定义

## 概述

`AvailableResources` schema定义了Agent模板系统中可用资源的数据结构，确保类型安全和API文档的完整性。

## Schema层次结构

```
AvailableResources
├── models: List[AvailableModel]
├── tools: List[AvailableTool]  
├── knowledge_bases: List[AvailableKnowledgeBase]
└── mcp_servers: List[AvailableMCPServer]
```

## 详细Schema定义

### 1. AvailableModel - 可用模型

```python
class AvailableModel(BaseModel):
    id: str                    # 模型ID - 主键，用于API调用
    name: str                  # 模型显示名称
    key: str                   # 模型标识符 - 用于内部识别
    description: Optional[str] # 模型描述
    model_type: str           # 模型类型 (chat, completion, embedding等)
```

**字段说明**：
- `id`: 前端应使用此字段进行模型选择和API调用
- `key`: 主要用于内部逻辑和模型匹配
- `model_type`: 用于分类和筛选不同类型的模型

### 2. AvailableTool - 可用工具

```python
class AvailableTool(BaseModel):
    id: str                            # 工具ID - 主键
    tenant_tool_id: str               # 租户工具ID - 租户级别的配置
    name: str                         # 工具显示名称
    key: str                          # 工具标识符
    description: Optional[str]        # 工具描述
    tool_type: str                    # 工具类型
    config_schema: Optional[Dict]     # 工具配置Schema
    tenant_config: Optional[Dict]     # 租户级别的工具配置
```

**字段说明**：
- `id`: 全局工具ID，用于关联Agent
- `tenant_tool_id`: 租户特定的工具实例ID
- `config_schema`: 定义工具配置的JSON Schema
- `tenant_config`: 租户级别的默认配置

### 3. AvailableKnowledgeBase - 可用知识库

```python
class AvailableKnowledgeBase(BaseModel):
    id: str                    # 知识库ID - 主键
    name: str                  # 知识库名称
    description: Optional[str] # 知识库描述
    document_count: int        # 文档数量
    created_at: datetime      # 创建时间
    updated_at: datetime      # 更新时间
```

**字段说明**：
- `document_count`: 知识库中的文档数量，用于显示规模
- `created_at/updated_at`: 时间戳信息，用于排序和显示

### 4. AvailableMCPServer - 可用MCP服务器

```python
class AvailableMCPServer(BaseModel):
    id: str                           # MCP服务器ID - 主键
    name: str                         # 服务器名称
    endpoint: str                     # 服务器端点
    transport_type: str               # 传输类型 (http, websocket等)
    status: str                       # 服务器状态 (active, inactive等)
    last_heartbeat: Optional[datetime] # 最后心跳时间
```

**字段说明**：
- `endpoint`: MCP服务器的连接地址
- `transport_type`: 通信协议类型
- `status`: 实时状态，只返回active状态的服务器
- `last_heartbeat`: 用于健康检查和状态监控

## 使用示例

### API响应格式

```json
{
  "templates": [...],
  "available_resources": {
    "models": [
      {
        "id": "model-uuid-123",
        "name": "GPT-4",
        "key": "gpt-4",
        "description": "最新的GPT-4模型",
        "model_type": "chat"
      }
    ],
    "tools": [
      {
        "id": "tool-uuid-456",
        "tenant_tool_id": "tenant-tool-789",
        "name": "搜索工具",
        "key": "search_tool",
        "description": "用于搜索网络信息",
        "tool_type": "search",
        "config_schema": {
          "type": "object",
          "properties": {
            "api_key": {"type": "string"}
          }
        },
        "tenant_config": {
          "timeout": 30
        }
      }
    ],
    "knowledge_bases": [
      {
        "id": "kb-uuid-101",
        "name": "产品文档",
        "description": "公司产品相关文档",
        "document_count": 150,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-15T00:00:00Z"
      }
    ],
    "mcp_servers": [
      {
        "id": "mcp-uuid-202",
        "name": "数据分析服务",
        "endpoint": "http://localhost:3000",
        "transport_type": "http",
        "status": "active",
        "last_heartbeat": "2024-01-15T12:30:00Z"
      }
    ]
  }
}
```

### 前端使用示例

```typescript
interface AvailableResources {
  models: AvailableModel[];
  tools: AvailableTool[];
  knowledge_bases: AvailableKnowledgeBase[];
  mcp_servers: AvailableMCPServer[];
}

// 使用示例
const handleModelSelect = (modelId: string) => {
  const selectedModel = availableResources.models.find(m => m.id === modelId);
  // 直接使用modelId进行API调用，无需转换
};

// 显示模型选项
const modelOptions = availableResources.models.map(model => ({
  value: model.id,      // 使用ID作为值
  label: model.name,    // 显示名称
  description: model.description
}));
```

## Schema验证优势

1. **类型安全**: Pydantic自动验证数据类型
2. **API文档**: 自动生成OpenAPI文档
3. **前端友好**: 明确的字段定义便于前端开发
4. **数据一致性**: 确保所有接口返回一致的数据格式
5. **错误处理**: 自动处理数据验证错误

## 向后兼容性

- 所有字段都包含在schema中，不会丢失信息
- 可选字段使用`Optional`标记，确保兼容性
- 保持与现有数据库模型的兼容性

## 扩展性

Schema设计考虑了未来扩展：
- 易于添加新的资源类型
- 支持添加新的字段
- 可以通过继承扩展功能

这种强类型的schema定义为Agent模板系统提供了更好的开发体验和更高的代码质量。 