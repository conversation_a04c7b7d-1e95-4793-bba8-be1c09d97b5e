<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -7.999998092651367 2528.565185546875 266" style="max-width: 2528.565185546875px;" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-1749618526849-0btjbtjxq"><style>#mermaid-svg-1749618526849-0btjbtjxq{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#cccccc;}#mermaid-svg-1749618526849-0btjbtjxq .error-icon{fill:#5a1d1d;}#mermaid-svg-1749618526849-0btjbtjxq .error-text{fill:#f48771;stroke:#f48771;}#mermaid-svg-1749618526849-0btjbtjxq .edge-thickness-normal{stroke-width:2px;}#mermaid-svg-1749618526849-0btjbtjxq .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-1749618526849-0btjbtjxq .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-1749618526849-0btjbtjxq .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-1749618526849-0btjbtjxq .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-1749618526849-0btjbtjxq .marker{fill:#cccccc;stroke:#cccccc;}#mermaid-svg-1749618526849-0btjbtjxq .marker.cross{stroke:#cccccc;}#mermaid-svg-1749618526849-0btjbtjxq svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-1749618526849-0btjbtjxq .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#cccccc;}#mermaid-svg-1749618526849-0btjbtjxq .cluster-label text{fill:#e7e7e7;}#mermaid-svg-1749618526849-0btjbtjxq .cluster-label span,#mermaid-svg-1749618526849-0btjbtjxq p{color:#e7e7e7;}#mermaid-svg-1749618526849-0btjbtjxq .label text,#mermaid-svg-1749618526849-0btjbtjxq span,#mermaid-svg-1749618526849-0btjbtjxq p{fill:#cccccc;color:#cccccc;}#mermaid-svg-1749618526849-0btjbtjxq .node rect,#mermaid-svg-1749618526849-0btjbtjxq .node circle,#mermaid-svg-1749618526849-0btjbtjxq .node ellipse,#mermaid-svg-1749618526849-0btjbtjxq .node polygon,#mermaid-svg-1749618526849-0btjbtjxq .node path{fill:#1e1e1e;stroke:#6b6b6b;stroke-width:1px;}#mermaid-svg-1749618526849-0btjbtjxq .flowchart-label text{text-anchor:middle;}#mermaid-svg-1749618526849-0btjbtjxq .node .label{text-align:center;}#mermaid-svg-1749618526849-0btjbtjxq .node.clickable{cursor:pointer;}#mermaid-svg-1749618526849-0btjbtjxq .arrowheadPath{fill:#e1e1e1;}#mermaid-svg-1749618526849-0btjbtjxq .edgePath .path{stroke:#cccccc;stroke-width:2.0px;}#mermaid-svg-1749618526849-0btjbtjxq .flowchart-link{stroke:#cccccc;fill:none;}#mermaid-svg-1749618526849-0btjbtjxq .edgeLabel{background-color:#1e1e1e99;text-align:center;}#mermaid-svg-1749618526849-0btjbtjxq .edgeLabel rect{opacity:0.5;background-color:#1e1e1e99;fill:#1e1e1e99;}#mermaid-svg-1749618526849-0btjbtjxq .labelBkg{background-color:rgba(30, 30, 30, 0.5);}#mermaid-svg-1749618526849-0btjbtjxq .cluster rect{fill:#3a3d41;stroke:#303031;stroke-width:1px;}#mermaid-svg-1749618526849-0btjbtjxq .cluster text{fill:#e7e7e7;}#mermaid-svg-1749618526849-0btjbtjxq .cluster span,#mermaid-svg-1749618526849-0btjbtjxq p{color:#e7e7e7;}#mermaid-svg-1749618526849-0btjbtjxq div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#4d4d4d;border:1px solid #007fd4;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-1749618526849-0btjbtjxq .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#cccccc;}#mermaid-svg-1749618526849-0btjbtjxq :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="6" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1749618526849-0btjbtjxq_flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1749618526849-0btjbtjxq_flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-1749618526849-0btjbtjxq_flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-1749618526849-0btjbtjxq_flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-B" id="L-A-B-0" d="M1188.408,32.87L1025.305,40.933C862.202,48.996,535.995,65.123,372.892,76.47C209.788,87.817,209.788,94.383,209.788,97.667L209.788,100.95"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-C" id="L-A-C-0" d="M1188.408,35.798L1093.652,43.373C998.895,50.948,809.383,66.099,714.626,76.958C619.87,87.817,619.87,94.383,619.87,97.667L619.87,100.95"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-D" id="L-A-D-0" d="M1188.408,52.006L1168.82,56.88C1149.232,61.754,1110.055,71.502,1090.467,79.659C1070.879,87.817,1070.879,94.383,1070.879,97.667L1070.879,100.95"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-E" id="L-A-E-0" d="M1380.355,52.006L1399.943,56.88C1419.531,61.754,1458.708,71.502,1478.296,79.659C1497.884,87.817,1497.884,94.383,1497.884,97.667L1497.884,100.95"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-F" id="L-A-F-0" d="M1380.355,35.985L1472.471,43.529C1564.588,51.073,1748.821,66.162,1840.937,76.989C1933.053,87.817,1933.053,94.383,1933.053,97.667L1933.053,100.95"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-G" id="L-A-G-0" d="M1380.355,32.936L1540.972,40.989C1701.59,49.041,2022.824,65.145,2183.442,76.481C2344.059,87.817,2344.059,94.383,2344.059,97.667L2344.059,100.95"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-B LE-B1" id="L-B-B1-0" d="M130.564,162.5L118.828,166.667C107.091,170.833,83.617,179.167,71.88,186.617C60.143,194.067,60.143,200.633,60.143,203.917L60.143,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-B LE-B2" id="L-B-B2-0" d="M209.788,162.5L209.788,166.667C209.788,170.833,209.788,179.167,209.788,186.617C209.788,194.067,209.788,200.633,209.788,203.917L209.788,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-B LE-B3" id="L-B-B3-0" d="M278.085,162.5L288.203,166.667C298.32,170.833,318.556,179.167,328.674,186.617C338.792,194.067,338.792,200.633,338.792,203.917L338.792,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-C1" id="L-C-C1-0" d="M556.794,156.934L542.55,162.029C528.306,167.123,499.819,177.311,485.575,185.689C471.331,194.067,471.331,200.633,471.331,203.917L471.331,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-C2" id="L-C-C2-0" d="M619.87,162.5L619.87,166.667C619.87,170.833,619.87,179.167,619.87,186.617C619.87,194.067,619.87,200.633,619.87,203.917L619.87,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-C3" id="L-C-C3-0" d="M682.946,155.188L699.267,160.573C715.588,165.959,748.23,176.729,764.551,185.398C780.872,194.067,780.872,200.633,780.872,203.917L780.872,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-D LE-D1" id="L-D-D1-0" d="M999.004,160.708L986.816,165.173C974.628,169.639,950.252,178.569,938.064,186.318C925.876,194.067,925.876,200.633,925.876,203.917L925.876,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-D LE-D2" id="L-D-D2-0" d="M1070.879,162.5L1070.879,166.667C1070.879,170.833,1070.879,179.167,1070.879,186.617C1070.879,194.067,1070.879,200.633,1070.879,203.917L1070.879,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-D LE-D3" id="L-D-D3-0" d="M1142.754,159.331L1156.275,164.026C1169.796,168.721,1196.838,178.11,1210.359,186.089C1223.88,194.067,1223.88,200.633,1223.88,203.917L1223.88,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-E LE-E1" id="L-E-E1-0" d="M1433.245,159.44L1421.185,164.117C1409.124,168.793,1385.003,178.147,1372.943,186.107C1360.882,194.067,1360.882,200.633,1360.882,203.917L1360.882,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-E LE-E2" id="L-E-E2-0" d="M1497.884,162.5L1497.884,166.667C1497.884,170.833,1497.884,179.167,1497.884,186.617C1497.884,194.067,1497.884,200.633,1497.884,203.917L1497.884,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-E LE-E3" id="L-E-E3-0" d="M1562.523,159.44L1574.583,164.117C1586.644,168.793,1610.765,178.147,1622.826,186.107C1634.886,194.067,1634.886,200.633,1634.886,203.917L1634.886,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-F LE-F1" id="L-F-F1-0" d="M1868.607,156.74L1853.834,161.867C1839.061,166.993,1809.516,177.247,1794.743,185.657C1779.971,194.067,1779.971,200.633,1779.971,203.917L1779.971,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-F LE-F2" id="L-F-F2-0" d="M1933.053,162.5L1933.053,166.667C1933.053,170.833,1933.053,179.167,1933.053,186.617C1933.053,194.067,1933.053,200.633,1933.053,203.917L1933.053,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-F LE-F3" id="L-F-F3-0" d="M1997.5,157.987L2010.926,162.906C2024.351,167.825,2051.202,177.662,2064.628,185.864C2078.053,194.067,2078.053,200.633,2078.053,203.917L2078.053,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-G LE-G1" id="L-G-G1-0" d="M2291.93,155.842L2279.118,161.118C2266.305,166.395,2240.68,176.947,2227.868,185.507C2215.055,194.067,2215.055,200.633,2215.055,203.917L2215.055,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-G LE-G2" id="L-G-G2-0" d="M2344.059,162.5L2344.059,166.667C2344.059,170.833,2344.059,179.167,2344.059,186.617C2344.059,194.067,2344.059,200.633,2344.059,203.917L2344.059,207.2"/><path marker-end="url(#mermaid-svg-1749618526849-0btjbtjxq_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-G LE-G3" id="L-G-G3-0" d="M2396.188,155.842L2409.001,161.118C2421.813,166.395,2447.438,176.947,2460.251,185.507C2473.063,194.067,2473.063,200.633,2473.063,203.917L2473.063,207.2"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1284.381498336792, 28.124998092651367)" id="flowchart-A-370" class="node default default flowchart-label"><rect height="56.25" width="191.9466094970703" y="-28.125" x="-95.97330474853516" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-88.47330474853516, -20.625)" style="" class="label"><rect/><foreignObject height="41.25" width="176.9466094970703"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">MultiTenantAgentService<br />(主协调器)</span></div></foreignObject></g></g><g transform="translate(209.7884063720703, 134.3749942779541)" id="flowchart-B-371" class="node default default flowchart-label"><rect height="56.25" width="175.97657775878906" y="-28.125" x="-87.98828887939453" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-80.48828887939453, -20.625)" style="" class="label"><rect/><foreignObject height="41.25" width="160.97657775878906"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">AgentInstanceManager<br />(Agent实例管理)</span></div></foreignObject></g></g><g transform="translate(619.8697853088379, 134.3749942779541)" id="flowchart-C-373" class="node default default flowchart-label"><rect height="56.25" width="126.15234375" y="-28.125" x="-63.076171875" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-55.576171875, -20.625)" style="" class="label"><rect/><foreignObject height="41.25" width="111.15234375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">SessionManager<br />(会话管理)</span></div></foreignObject></g></g><g transform="translate(1070.8788948059082, 134.3749942779541)" id="flowchart-D-375" class="node default default flowchart-label"><rect height="56.25" width="143.75" y="-28.125" x="-71.875" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-64.375, -20.625)" style="" class="label"><rect/><foreignObject height="41.25" width="128.75"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">KnowledgeService<br />(知识库检索)</span></div></foreignObject></g></g><g transform="translate(1497.8841018676758, 134.3749942779541)" id="flowchart-E-377" class="node default default flowchart-label"><rect height="56.25" width="129.27734375" y="-28.125" x="-64.638671875" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-57.138671875, -20.625)" style="" class="label"><rect/><foreignObject height="41.25" width="114.27734375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">WorkflowEngine<br />(工作流引擎)</span></div></foreignObject></g></g><g transform="translate(1933.053367614746, 134.3749942779541)" id="flowchart-F-379" class="node default default flowchart-label"><rect height="56.25" width="128.89322662353516" y="-28.125" x="-64.44661331176758" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56.94661331176758, -20.625)" style="" class="label"><rect/><foreignObject height="41.25" width="113.89322662353516"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">StorageManager<br />(存储管理)</span></div></foreignObject></g></g><g transform="translate(2344.059226989746, 134.3749942779541)" id="flowchart-G-381" class="node default default flowchart-label"><rect height="56.25" width="104.25782012939453" y="-28.125" x="-52.128910064697266" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-44.628910064697266, -20.625)" style="" class="label"><rect/><foreignObject height="41.25" width="89.25782012939453"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">ChatHandler<br />(对话处理)</span></div></foreignObject></g></g><g transform="translate(60.143226623535156, 231.24999237060547)" id="flowchart-B1-383" class="node default default flowchart-label"><rect height="37.5" width="120.28645324707031" y="-18.75" x="-60.143226623535156" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-52.643226623535156, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="105.28645324707031"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">创建Agent实例</span></div></foreignObject></g></g><g transform="translate(209.7884063720703, 231.24999237060547)" id="flowchart-B2-385" class="node default default flowchart-label"><rect height="37.5" width="79.00390625" y="-18.75" x="-39.501953125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.001953125, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="64.00390625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">缓存管理</span></div></foreignObject></g></g><g transform="translate(338.7923126220703, 231.24999237060547)" id="flowchart-B3-387" class="node default default flowchart-label"><rect height="37.5" width="79.00390625" y="-18.75" x="-39.501953125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.001953125, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="64.00390625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">模型配置</span></div></foreignObject></g></g><g transform="translate(471.3313751220703, 231.24999237060547)" id="flowchart-C1-389" class="node default default flowchart-label"><rect height="37.5" width="86.07421875" y="-18.75" x="-43.037109375" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-35.537109375, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="71.07421875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">会话CRUD</span></div></foreignObject></g></g><g transform="translate(619.8697853088379, 231.24999237060547)" id="flowchart-C2-391" class="node default default flowchart-label"><rect height="37.5" width="111.00260162353516" y="-18.75" x="-55.50130081176758" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-48.00130081176758, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="96.00260162353516"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">会话状态管理</span></div></foreignObject></g></g><g transform="translate(780.872386932373, 231.24999237060547)" id="flowchart-C3-393" class="node default default flowchart-label"><rect height="37.5" width="111.00260162353516" y="-18.75" x="-55.50130081176758" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-48.00130081176758, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="96.00260162353516"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">活跃时间更新</span></div></foreignObject></g></g><g transform="translate(925.8756408691406, 231.24999237060547)" id="flowchart-D1-395" class="node default default flowchart-label"><rect height="37.5" width="79.00390625" y="-18.75" x="-39.501953125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.001953125, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="64.00390625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">向量检索</span></div></foreignObject></g></g><g transform="translate(1070.8788948059082, 231.24999237060547)" id="flowchart-D2-397" class="node default default flowchart-label"><rect height="37.5" width="111.00260162353516" y="-18.75" x="-55.50130081176758" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-48.00130081176758, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="96.00260162353516"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">上下文格式化</span></div></foreignObject></g></g><g transform="translate(1223.8801956176758, 231.24999237060547)" id="flowchart-D3-399" class="node default default flowchart-label"><rect height="37.5" width="95" y="-18.75" x="-47.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-40, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="80"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">相关性评分</span></div></foreignObject></g></g><g transform="translate(1360.8821487426758, 231.24999237060547)" id="flowchart-E1-401" class="node default default flowchart-label"><rect height="37.5" width="79.00390625" y="-18.75" x="-39.501953125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.001953125, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="64.00390625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">节点定义</span></div></foreignObject></g></g><g transform="translate(1497.8841018676758, 231.24999237060547)" id="flowchart-E2-403" class="node default default flowchart-label"><rect height="37.5" width="95" y="-18.75" x="-47.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-40, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="80"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">工作流编译</span></div></foreignObject></g></g><g transform="translate(1634.8860549926758, 231.24999237060547)" id="flowchart-E3-405" class="node default default flowchart-label"><rect height="37.5" width="79.00390625" y="-18.75" x="-39.501953125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.001953125, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="64.00390625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">状态管理</span></div></foreignObject></g></g><g transform="translate(1779.970687866211, 231.24999237060547)" id="flowchart-F1-407" class="node default default flowchart-label"><rect height="37.5" width="111.16535949707031" y="-18.75" x="-55.582679748535156" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-48.082679748535156, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="96.16535949707031"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">MongoDB连接</span></div></foreignObject></g></g><g transform="translate(1933.053367614746, 231.24999237060547)" id="flowchart-F2-409" class="node default default flowchart-label"><rect height="37.5" width="95" y="-18.75" x="-47.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-40, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="80"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">检查点存储</span></div></foreignObject></g></g><g transform="translate(2078.053367614746, 231.24999237060547)" id="flowchart-F3-411" class="node default default flowchart-label"><rect height="37.5" width="95" y="-18.75" x="-47.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-40, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="80"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">数据持久化</span></div></foreignObject></g></g><g transform="translate(2215.055320739746, 231.24999237060547)" id="flowchart-G1-413" class="node default default flowchart-label"><rect height="37.5" width="79.00390625" y="-18.75" x="-39.501953125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.001953125, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="64.00390625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">普通对话</span></div></foreignObject></g></g><g transform="translate(2344.059226989746, 231.24999237060547)" id="flowchart-G2-415" class="node default default flowchart-label"><rect height="37.5" width="79.00390625" y="-18.75" x="-39.501953125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.001953125, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="64.00390625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">流式对话</span></div></foreignObject></g></g><g transform="translate(2473.063133239746, 231.24999237060547)" id="flowchart-G3-417" class="node default default flowchart-label"><rect height="37.5" width="79.00390625" y="-18.75" x="-39.501953125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.001953125, -11.25)" style="" class="label"><rect/><foreignObject height="22.5" width="64.00390625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">消息处理</span></div></foreignObject></g></g></g></g></g></svg>