# Agent管理简化方案 API 文档

## 概述

这是一套简化的Agent管理API，采用"草稿+发布"的模式，让Agent配置管理变得像编辑文档一样简单。

## 核心概念

### 1. 草稿模式
- 每个Agent都有一个工作副本（草稿），可以随时编辑
- 草稿不影响正在运行的Agent版本
- 支持自动保存和实时验证

### 2. 版本管理
- 发布时自动生成语义化版本号（如1.0.0, 1.1.0）
- 保留完整的版本历史
- 支持一键回滚到任意历史版本

### 3. 模板化创建
- 提供预设模板快速创建不同类型的Agent
- 包含配置向导指导完成配置

## API 接口

### 1. 获取Agent创建模板
```http
GET /api/v1/tenants/{tenant_id}/agents/templates
```

**功能**: 获取可用的Agent创建模板  
**返回**: 模板列表和租户可用资源

**响应示例**:
```json
{
  "templates": [
    {
      "template_id": "general_assistant",
      "name": "通用助手", 
      "agent_type": "general",
      "description": "适用于日常对话和问答的通用AI助手",
      "default_prompt": "你是一个友善、专业的AI助手...",
      "suggested_models": ["gpt-4", "gpt-3.5-turbo"],
      "config_wizard": [
        {
          "step": 1,
          "title": "选择模型",
          "field": "model_id",
          "required": true
        }
      ]
    }
  ],
  "available_resources": {
    "models": ["gpt-4", "gpt-3.5-turbo"],
    "tools": ["search", "calculator"],
    "knowledge_bases": ["kb_001", "kb_002"]
  }
}
```

### 2. 创建Agent（简化版）
```http
POST /api/v1/tenants/{tenant_id}/agents/simple
```

**功能**: 创建Agent并自动生成草稿配置  
**请求体**:
```json
{
  "name": "我的客服助手",
  "template_id": "customer_service",
  "description": "专门处理客户咨询"
}
```

**响应**:
```json
{
  "agent": {
    "id": "agent_xxx",
    "name": "我的客服助手",
    "is_active": true
  },
  "draft_config": {
    "model_id": null,
    "prompt": "你是一个专业的客服代表...",
    "has_changes": false,
    "is_ready_to_publish": false,
    "validation_errors": ["必须选择LLM模型"]
  },
  "next_steps": ["配置LLM模型", "设置提示词", "选择工具"]
}
```

### 3. 获取Agent草稿配置
```http
GET /api/v1/tenants/{tenant_id}/agents/{agent_id}/draft
```

**功能**: 获取当前可编辑的配置（草稿状态）  
**特点**: 如果没有草稿，自动基于当前版本创建

**响应**:
```json
{
  "model_id": "gpt-4",
  "prompt": "你是一个专业的客服助手...",
  "knowledge_base_ids": ["kb1", "kb2"],
  "tool_ids": ["search", "order_query"],
  "has_changes": true,
  "is_ready_to_publish": true,
  "validation_errors": [],
  "last_saved_at": "2024-01-15T10:30:00Z",
  "last_published_at": "2024-01-10T09:00:00Z"
}
```

### 4. 更新Agent草稿配置
```http
PUT /api/v1/tenants/{tenant_id}/agents/{agent_id}/draft
```

**功能**: 编辑配置，自动保存草稿  
**特点**: 实时保存，不影响运行版本

**请求体**:
```json
{
  "prompt": "更新的提示词...",
  "model_id": "gpt-4",
  "knowledge_base_ids": ["kb1", "kb2", "kb3"],
  "llm_model_config": {
    "temperature": 0.7,
    "max_tokens": 2000
  }
}
```

**响应**:
```json
{
  "success": true,
  "has_changes": true,
  "is_ready_to_publish": true,
  "validation_errors": [],
  "last_saved_at": "2024-01-15T10:35:00Z"
}
```

### 5. 发布Agent配置
```http
POST /api/v1/tenants/{tenant_id}/agents/{agent_id}/publish
```

**功能**: 将草稿发布为正式版本  
**特点**: 自动生成版本号（minor递增），立即生效并自动激活

**请求体**:
```json
{
  "release_notes": "优化了提示词和知识库配置"
}
```

**响应**:
```json
{
  "success": true,
  "version": {
    "version_id": "v_xxx",
    "version_number": "1.1.0",
    "release_notes": "优化了提示词配置",
    "published_at": "2024-01-15T10:40:00Z",
    "is_current": true,
    "config_summary": {
      "model": "gpt-4",
      "knowledge_bases_count": 3,
      "tools_count": 2
    }
  },
  "agent": {
    "id": "agent_xxx",
    "name": "我的客服助手"
  }
}
```

### 6. 获取版本历史
```http
GET /api/v1/tenants/{tenant_id}/agents/{agent_id}/history
```

**功能**: 查看所有发布的版本

**响应**:
```json
{
  "versions": [
    {
      "version_id": "v_xxx",
      "version_number": "1.1.0",
      "release_notes": "优化了提示词配置",
      "published_at": "2024-01-15T10:40:00Z",
      "is_current": true,
      "config_summary": {
        "model": "gpt-4",
        "knowledge_bases_count": 3,
        "tools_count": 2
      }
    },
    {
      "version_id": "v_yyy",
      "version_number": "1.0.0",
      "release_notes": "初始版本",
      "published_at": "2024-01-10T09:00:00Z",
      "is_current": false,
      "config_summary": {
        "model": "gpt-3.5-turbo",
        "knowledge_bases_count": 1,
        "tools_count": 1
      }
    }
  ]
}
```

### 7. 回滚到指定版本
```http
POST /api/v1/tenants/{tenant_id}/agents/{agent_id}/rollback/{version_id}
```

**功能**: 回滚到指定历史版本  
**特点**: 立即生效，创建新草稿

**请求体**:
```json
{
  "reason": "紧急回滚到稳定版本"
}
```

**响应**:
```json
{
  "success": true,
  "current_version": "1.0.0",
  "rollback_reason": "紧急回滚到稳定版本",
  "new_draft_created": true
}
```

### 8. 验证配置
```http
POST /api/v1/tenants/{tenant_id}/agents/{agent_id}/validate
```

**功能**: 验证当前草稿配置是否完整有效

**响应**:
```json
{
  "is_valid": true,
  "errors": [],
  "warnings": ["部分知识库不可用"],
  "suggestions": ["考虑添加更多工具以增强Agent能力"]
}
```

### 9. 获取配置向导
```http
GET /api/v1/tenants/{tenant_id}/agents/{agent_id}/wizard
```

**功能**: 获取Agent配置的向导步骤和当前进度

**响应**:
```json
{
  "wizard_steps": [
    {
      "step": 1,
      "title": "选择模型",
      "description": "选择适合的语言模型",
      "field": "model_id",
      "required": true
    },
    {
      "step": 2,
      "title": "设置提示词",
      "description": "定义Agent的角色和行为",
      "field": "prompt",
      "required": true
    }
  ],
  "current_progress": {
    "completed_steps": 1,
    "total_steps": 2,
    "progress_percentage": 50
  },
  "template_info": {
    "template_id": "general_assistant",
    "name": "通用助手",
    "description": "适用于日常对话的AI助手"
  }
}
```

## 使用流程

### 创建Agent流程
```
1. GET /templates → 选择模板
2. POST /agents/simple → 创建Agent
3. PUT /agents/{id}/draft → 配置Agent
4. POST /agents/{id}/validate → 验证配置
5. POST /agents/{id}/publish → 发布Agent
```

### 编辑Agent流程
```
1. GET /agents/{id}/draft → 获取草稿
2. PUT /agents/{id}/draft → 修改配置
3. POST /agents/{id}/validate → 验证配置
4. POST /agents/{id}/publish → 发布新版本
```

### 版本管理流程
```
1. GET /agents/{id}/history → 查看历史
2. POST /agents/{id}/rollback/{version_id} → 回滚版本
3. PUT /agents/{id}/draft → 基于回滚版本修改
4. POST /agents/{id}/publish → 发布新版本
```

## 版本号规则

采用语义化版本号 (Semantic Versioning):

- **major**: 重大变更 (1.0.0 → 2.0.0)
- **minor**: 功能增加 (1.0.0 → 1.1.0) 
- **patch**: 问题修复 (1.0.0 → 1.0.1)

## 状态说明

### Agent状态
- **草稿中**: 有未发布的配置更改
- **已发布**: 当前版本已发布并运行
- **未激活**: Agent未激活，不可使用

### 配置状态
- **has_changes**: 是否有未发布的更改
- **is_ready_to_publish**: 是否准备好发布（配置有效）
- **validation_errors**: 配置验证错误列表

## 错误处理

### 常见错误码
- `400`: 配置验证失败
- `404`: Agent或版本不存在
- `403`: 无权限访问
- `409`: 名称冲突

### 错误响应格式
```json
{
  "detail": "配置验证失败: 必须选择LLM模型"
}
```

## 最佳实践

1. **使用模板**: 选择合适的模板快速创建Agent
2. **频繁保存**: 利用自动保存功能，避免配置丢失
3. **验证配置**: 发布前验证配置完整性
4. **版本说明**: 添加清晰的发布说明便于追踪
5. **测试验证**: 发布前在草稿状态充分测试

