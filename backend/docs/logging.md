# 日志配置指南

本项目使用集中式日志配置系统，可以通过环境变量或代码方式灵活配置全局和模块级别的日志级别。

## 日志级别

日志级别从低到高依次为：

- DEBUG: 详细的调试信息
- INFO: 一般信息
- WARNING: 警告信息
- ERROR: 错误信息
- CRITICAL: 严重错误信息

## 通过环境变量配置

### 全局日志级别

设置全局默认日志级别：

```bash
# 设置全局日志级别为DEBUG
export LOG_LEVEL=debug

# 设置全局日志级别为INFO（默认）
export LOG_LEVEL=info

# 设置全局日志级别为WARNING
export LOG_LEVEL=warning
```

### 模块特定日志级别

可以为特定模块设置不同的日志级别，覆盖全局设置：

```bash
# 设置app.core.rag.extor模块的日志级别为DEBUG
export LOG_LEVEL_APP_CORE_RAG_EXTOR=debug

# 设置app.services.knowledge模块的日志级别为WARNING
export LOG_LEVEL_APP_SERVICES_KNOWLEDGE=warning
```

> 注意：环境变量名称中的点(.)替换为下划线(_)，并且全部大写。

### 自定义日志格式

可以自定义日志输出格式：

```bash
export LOG_FORMAT="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
```

## 通过代码配置

### 在应用启动时配置

可以在应用启动时配置全局日志设置：

```python
from app.core.logging_config import logging_config

# 配置全局日志级别和格式
logging_config.configure_logging(
    default_level="debug",
    format_str="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)

# 配置特定模块的日志级别
logging_config.set_module_level("app.core.rag.extor", "debug")
logging_config.set_module_level("app.services.knowledge", "warning")
```

### 获取已配置的日志记录器

在代码中使用已配置的日志记录器：

```python
from app.core.logging_config import logging_config

# 获取当前模块的日志记录器
logger = logging_config.get_logger(__name__)

# 使用日志记录器
logger.debug("这是一条调试信息")
logger.info("这是一条信息")
logger.warning("这是一条警告")
logger.error("这是一条错误信息")
```

## 最佳实践

1. 在生产环境中使用INFO或更高级别，减少日志量
2. 在开发和测试环境中可使用DEBUG级别获取更多信息
3. 对问题排查的关键模块可单独设置更低的日志级别
4. 敏感信息不要记录到日志中（如密码、令牌等）
5. 使用结构化的日志信息，便于后续分析

## 示例：启动应用时设置不同环境的日志级别

```bash
# 开发环境
export LOG_LEVEL=debug
export LOG_LEVEL_APP_CORE_RAG_EXTOR=debug
uvicorn app.main:app --reload

# 测试环境
export LOG_LEVEL=info
export LOG_LEVEL_APP_CORE_RAG_EXTOR=debug
uvicorn app.main:app

# 生产环境
export LOG_LEVEL=warning
uvicorn app.main:app --workers 4
``` 