# Agent模板重新设计

## 概述

本次重新设计主要解决了Agent模板与创建请求字段不一致的问题，并改进了可用资源的返回格式，提升了前端开发体验。

## 主要改进

### 1. 字段一致性

**问题**：之前模板的配置字段与`AgentCreateRequest`字段不一致，导致前端需要做额外的字段映射。

**解决方案**：
- 模板的`default_config`字段现在与`AgentCreateRequest`完全一致
- 包含所有必需字段：`model_id`、`prompt`、`knowledge_base_ids`、`tool_ids`、`mcp_server_ids`、`llm_model_config`、`knowledge_base_config`

**新模板结构**：
```json
{
  "template_id": "general_assistant",
  "name": "通用助手",
  "agent_type": "general",
  "description": "适用于日常对话和问答的通用AI助手",
  "default_config": {
    "model_id": null,
    "prompt": "你是一个友善、专业的AI助手...",
    "knowledge_base_ids": [],
    "tool_ids": [],
    "mcp_server_ids": [],
    "llm_model_config": {
      "temperature": 0.7,
      "max_tokens": 2000,
      "top_p": 0.5,
      "max_retries": 3,
      "timeout": 10
    },
    "knowledge_base_config": {
      "max_results": 5,
      "min_score": 0.2
    }
  }
}
```

### 2. 资源ID格式

**问题**：`available_resources`中的模型信息返回的是`key`而不是`id`，前端需要额外映射。

**解决方案**：
- `available_resources`现在返回完整的资源信息，包含`id`、`name`、`key`等
- 模板推荐使用实际的资源ID列表：`suggested_model_ids`、`suggested_tool_ids`、`suggested_knowledge_base_ids`
- 前端可以直接使用ID，无需映射

**新资源格式**：
```json
{
  "available_resources": {
    "models": [
      {
        "id": "model-uuid-123",
        "name": "GPT-4",
        "key": "gpt-4",
        "description": "最新的GPT-4模型",
        "provider_name": "OpenAI",
        "model_type": "chat",
        "context_window": 8192,
        "max_output_tokens": 4096,
        "is_multimodal": false
      }
    ],
    "tools": [...],
    "knowledge_bases": [...],
    "mcp_servers": [...]
  }
}
```

### 3. 智能推荐系统

**新增功能**：根据模板类型智能推荐合适的资源

**推荐策略**：
- **通用助手**：优先推荐GPT-4、Claude、Qwen等通用模型
- **知识问答**：优先推荐高精度模型（GPT-4、Claude-3、Qwen-Max）
- **客服助手**：优先推荐稳定性好的模型（GPT-3.5-turbo、Qwen-turbo）
- **监督者Agent**：优先推荐推理能力强的模型（GPT-4、Claude-3-Opus）

**工具推荐**：根据关键词匹配推荐相关工具
- 知识问答：search、summarize、knowledge
- 客服助手：query、ticket、order、knowledge  
- 监督者：agent_call、workflow、task

### 4. 增强的配置向导

**改进**：向导步骤包含更详细的元数据

**新向导格式**：
```json
{
  "step": 1,
  "title": "选择模型",
  "description": "选择适合的语言模型",
  "field": "model_id",
  "required": true,
  "field_type": "select",
  "options_source": "available_models",
  "default_value": null
}
```

**字段类型支持**：
- `select`：单选下拉框
- `multi_select`：多选框
- `textarea`：文本域
- `object`：复杂对象配置

### 5. 新增模板类型

**监督者Agent模板**：
- 专门用于创建可以调度其他Agent的监督者
- 包含合适的默认提示词和配置
- 推荐高推理能力的模型

## API变更

### 模板获取接口

```
GET /tenants/{tenant_id}/agents/templates
```

**响应变更**：
- 移除：`suggested_models`、`suggested_tools`、`default_prompt`
- 新增：`suggested_model_ids`、`suggested_tool_ids`、`suggested_knowledge_base_ids`
- 改进：`available_resources`包含完整的资源信息

### 前端使用示例

```javascript
// 获取模板和资源
const response = await api.get(`/tenants/${tenantId}/agents/templates`);
const { templates, available_resources } = response.data;

// 选择模板
const template = templates.find(t => t.template_id === 'knowledge_qa');

// 使用推荐的第一个模型
const modelId = template.suggested_model_ids[0];

// 创建Agent请求
const createRequest = {
  name: "我的知识问答助手",
  description: "基于公司知识库的专业问答",
  template_id: template.template_id,
  agent_type: template.agent_type,
  model_id: modelId,
  prompt: template.default_config.prompt,
  knowledge_base_ids: [selectedKnowledgeBaseId],
  llm_model_config: template.default_config.llm_model_config
};
```

## 向后兼容性

为保持向后兼容：
- 保留了原有的API端点
- 新字段为可选，不影响现有前端代码
- 逐步迁移建议：
  1. 更新前端以使用新的资源ID格式
  2. 利用智能推荐提升用户体验
  3. 使用增强的配置向导

## 测试验证

运行测试文件验证所有功能：

```bash
cd backend
python demo/agent_template_test.py
```

测试覆盖：
- 字段一致性验证
- 资源ID格式检查
- 智能推荐功能
- 配置向导完整性
- 基于模板创建Agent

## 总结

这次重新设计显著提升了Agent模板系统的可用性：

1. **简化前端开发**：字段一致性减少了映射工作
2. **提升用户体验**：智能推荐和增强向导
3. **改善数据格式**：资源信息更完整和标准化
4. **增强扩展性**：支持更多Agent类型和配置方式

新设计为构建更智能、更易用的Agent创建流程打下了坚实基础。 