# 邀请功能API使用文档

## 概览

邀请功能允许组织管理员邀请用户加入组织，包含完整的邀请链接生成、邀请详情获取和接受邀请流程。

## 设计架构

### 核心组件

1. **InvitationService**: 邀请链接服务，负责生成邀请链接和处理邀请信息
2. **TenantService**: 租户服务，包含邀请相关的业务逻辑

### 前端配置

在 `backend/app/core/config.py` 中配置前端URL：

```python
FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:3000")
```

可通过环境变量 `FRONTEND_URL` 进行配置。

## API接口

### 1. 邀请用户加入组织

**POST** `/api/v1/auth/tenants/{tenant_id}/invite`

**权限要求**: 需要组织拥有者或管理员权限

**请求体**:
```json
{
  "email": "<EMAIL>",
  "role_key": "member"
}
```

**响应体**:
```json
{
  "message": "邀请已发送",
  "invitation": {
    "id": "invite_xxx",
    "token": "invitation_token_xxx",
    "email": "<EMAIL>",
    "role_key": "member",
    "expires_at": "2024-01-15T10:30:00Z",
    "invitation_link": "http://localhost:3000/invitations/accept?token=xxx&tenant_id=xxx&tenant_name=xxx",
    "tenant_info": {
      "id": "tenant_xxx",
      "name": "示例组织"
    },
    "inviter_name": "张三",
    "status": "pending"
  }
}
```

### 2. 获取邀请详情

**GET** `/api/v1/auth/invitations/{invitation_token}`

**权限要求**: 无需认证（公开接口）

**响应体**:
```json
{
  "invitation": {
    "id": "invite_xxx",
    "token": "invitation_token_xxx",
    "email": "<EMAIL>",
    "role_key": "member",
    "expires_at": "2024-01-15T10:30:00Z",
    "invitation_link": "http://localhost:3000/invitations/accept?token=xxx",
    "tenant_info": {
      "id": "tenant_xxx",
      "name": "示例组织"
    },
    "inviter_name": "张三",
    "status": "pending"
  },
  "tenant_name": "示例组织",
  "tenant_description": "这是一个示例组织",
  "inviter_name": "张三",
  "is_expired": false
}
```

### 3. 接受邀请

**POST** `/api/v1/auth/accept-invitation`

**权限要求**: 需要用户认证

**请求体**:
```json
{
  "invitation_token": "invitation_token_xxx"
}
```

**响应体**:
```json
{
  "success": true,
  "message": "已成功加入组织",
  "tenant_id": "tenant_xxx"
}
```

## 邀请链接格式

生成的邀请链接格式为：
```
{FRONTEND_URL}/invitations/accept?token={invitation_token}&tenant_id={tenant_id}&tenant_name={tenant_name}
```

参数说明：
- `token`: 邀请令牌（必需）
- `tenant_id`: 租户ID（可选，用于前端显示）
- `tenant_name`: 租户名称（可选，用于前端显示）

## 使用流程

### 1. 邀请流程

1. 组织管理员调用邀请API
2. 系统生成邀请记录和令牌
3. 返回包含邀请链接的完整信息
4. 发送邀请邮件（可选，需要实现邮件服务）

### 2. 接受流程

1. 用户收到邀请链接
2. 点击链接访问前端页面
3. 前端调用获取邀请详情API显示邀请信息
4. 用户登录并调用接受邀请API
5. 成功加入组织

## 前端集成建议

### 1. 邀请页面 (/invitations/accept)

```typescript
// 获取URL参数
const token = searchParams.get('token')
const tenantId = searchParams.get('tenant_id')
const tenantName = searchParams.get('tenant_name')

// 获取邀请详情
const { data } = await apiClient.GET('/api/v1/auth/invitations/{invitation_token}', {
  params: { path: { invitation_token: token } }
})

// 接受邀请
const { data } = await apiClient.POST('/api/v1/auth/accept-invitation', {
  body: { invitation_token: token }
})
```

### 2. 错误处理

- 401: 需要用户登录
- 403: 邀请不属于当前用户
- 404: 邀请不存在
- 400: 邀请已过期或已被接受

## 安全考虑

1. **令牌唯一性**: 邀请令牌全局唯一
2. **有效期控制**: 邀请默认7天有效期
3. **权限验证**: 接受邀请时验证邮箱匹配
4. **状态管理**: 防止重复接受邀请
5. **邮箱验证**: 确保只有对应邮箱用户能接受邀请

## 扩展功能

### 1. 邮件模板数据

`InvitationService.generate_email_template_data()` 方法提供了邮件模板所需的所有数据：

```python
{
    "invitee_email": "<EMAIL>",
    "inviter_name": "张三",
    "tenant_name": "示例组织",
    "tenant_description": "组织描述",
    "role_key": "member",
    "invitation_link": "完整的邀请链接",
    "expires_at": "过期时间",
    "platform_name": "云知问",
    "platform_url": "前端URL"
}
```

### 2. 批量邀请

可以基于现有API实现批量邀请功能，只需在前端循环调用邀请API。

### 3. 邀请管理

后续可以添加邀请列表、撤销邀请等管理功能。 