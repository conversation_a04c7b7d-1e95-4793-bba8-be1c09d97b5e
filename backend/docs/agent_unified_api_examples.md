# Agent 统一创建接口使用示例

## 概述

新的统一创建接口 `/tenants/{tenant_id}/agents` 采用草稿模式，支持模板和自定义两种创建方式。所有Agent创建后都处于草稿状态，需要进一步配置和完善后才能发布使用。

## 接口地址

```
POST /api/v1/tenants/{tenant_id}/agents
```

## 核心功能

1. **模板创建**：基于预定义模板快速创建Agent草稿
2. **自定义创建**：完全自定义Agent草稿配置
3. **草稿模式**：统一的草稿 → 配置 → 发布流程
4. **灵活配置**：支持创建时预配置或后续完善

## 使用示例

### 1. 基于模板创建（推荐）

#### 最简单的模板创建
```json
{
  "name": "客服助手",
  "template_id": "customer-service"
}
```

#### 模板创建并预配置
```json
{
  "name": "智能客服",
  "template_id": "customer-service",
  "description": "专业的智能客服系统",
  "model_id": "gpt-4",
  "knowledge_base_ids": ["kb_001", "kb_002"],
  "llm_model_config": {
    "temperature": 0.7,
    "max_tokens": 2000
  }
}
```

### 2. 完全自定义创建

#### 最小自定义配置
```json
{
  "name": "自定义助手",
  "agent_type": "general",
  "description": "自定义业务助手",
  "model_id": "gpt-4",
  "prompt": "你是一个专业的业务助手..."
}
```

#### 完整自定义配置
```json
{
  "name": "专业顾问",
  "agent_type": "general",
  "description": "专业的业务顾问和分析师",
  "model_id": "gpt-4",
  "prompt": "你是一个专业的业务顾问...",
  "knowledge_base_ids": ["kb_business", "kb_legal"],
  "tool_ids": ["tool_calculator", "tool_email"],
  "llm_model_config": {
    "temperature": 0.3,
    "max_tokens": 4000,
    "top_p": 0.9
  }
}
```

## 创建响应

### 成功响应示例
```json
{
  "agent": {
    "id": "agent_123",
    "name": "智能客服",
    "agent_type": "general",
    "description": "专业的智能客服系统",
    "tenant_id": "tenant_001",
    "is_active": true,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  },
  "draft_config": {
    "model_id": "gpt-4",
    "prompt": "你是一个专业的客服助手...",
    "knowledge_base_ids": ["kb_001", "kb_002"],
    "has_changes": true,
    "is_ready_to_publish": true,
    "validation_errors": [],
    "last_saved_at": "2024-01-01T12:00:00Z"
  },
  "next_steps": [
    "完善提示词：PUT /agents/{agent_id}/draft",
    "验证配置：POST /agents/{agent_id}/validate", 
    "发布Agent：POST /agents/{agent_id}/publish"
  ]
}
```

## 后续操作流程

### 1. 基本信息管理（立即生效）

```javascript
// 更新基本信息 - 立即生效，无需发布
const updateBasicInfo = async (agentId, data) => {
  return await fetch(`/api/v1/tenants/${tenantId}/agents/${agentId}/basic-info`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: data.name,           // Agent名称
      description: data.description,  // Agent描述
      is_active: data.is_active       // 是否激活
    })
  });
};
```

### 2. 配置信息管理（草稿模式）

```javascript
// 获取草稿配置
const getDraftConfig = async (agentId) => {
  return await fetch(`/api/v1/tenants/${tenantId}/agents/${agentId}/draft`);
};

// 更新草稿配置
const updateDraftConfig = async (agentId, config) => {
  return await fetch(`/api/v1/tenants/${tenantId}/agents/${agentId}/draft`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      model_id: config.model_id,
      prompt: config.prompt,
      knowledge_base_ids: config.knowledge_base_ids,
      tool_ids: config.tool_ids,
      
      // 使用结构化配置字段
      llm_model_config: {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 0.9
      },
      knowledge_base_config: {
        max_results: 5,
        min_score: 0.3
      }
    })
  });
};

// 验证配置
const validateConfig = async (agentId) => {
  return await fetch(`/api/v1/tenants/${tenantId}/agents/${agentId}/validate`, {
    method: 'POST'
  });
};

// 发布配置
const publishConfig = async (agentId, releaseNotes) => {
  return await fetch(`/api/v1/tenants/${tenantId}/agents/${agentId}/publish`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      release_notes: releaseNotes
    })
  });
};
```

## 📋 前端开发指南

### 🎯 信息分类理解

为了让前端开发更清晰，我们将 Agent 信息分为两类：

#### **基本信息**（立即生效）
```javascript
// 这些信息修改后立即生效，无需发布
const basicInfo = {
  name: "Agent名称",        // ✅ 用户看到的显示名称
  description: "Agent描述", // ✅ 用户看到的描述信息
  is_active: true,          // ✅ 是否启用Agent
  icon: "图标文件"          // ✅ Agent头像/图标
};

// API调用：立即保存，立即生效
PUT /agents/{id}/basic-info
```

#### **配置信息**（草稿模式）
```javascript  
// 这些信息影响Agent行为，需要通过草稿→发布流程
const configInfo = {
  model_id: "gpt-4",                 // 🔄 LLM模型选择
  prompt: "你是一个专业助手...",      // 🔄 Agent行为定义
  knowledge_base_ids: ["kb_001"],    // 🔄 知识库关联
  tool_ids: ["tool_001"],            // 🔄 工具能力
  
  // 结构化配置字段 - 后端自动处理JSON存储
  llm_model_config: {                // 🔄 LLM模型参数
    temperature: 0.7,
    max_tokens: 2000,
    top_p: 0.9
  },
  knowledge_base_config: {           // 🔄 知识库检索配置
    max_results: 5,
    min_score: 0.3
  }
};

// API调用：草稿保存 → 验证 → 发布生效
PUT /agents/{id}/draft → POST /agents/{id}/publish
```

### 💾 数据结构说明

#### **前端只需关心的结构化字段**
```javascript
// 前端使用的配置结构 - 清晰简单
const agentConfig = {
  // 基本配置
  model_id: "gpt-4",
  prompt: "你是一个专业助手...",
  knowledge_base_ids: ["kb_001", "kb_002"],
  tool_ids: ["tool_001"],
  
  // LLM模型配置
  llm_model_config: {
    temperature: 0.7,        // 创意程度：0.1(保守) - 0.9(创意)
    max_tokens: 2000,        // 最大回复长度
    top_p: 0.9,             // 词汇选择范围
    max_retries: 3,         // 重试次数
    timeout: 10             // 超时时间(秒)
  },
  
  // 知识库检索配置
  knowledge_base_config: {
    max_results: 5,         // 最大检索结果数
    min_score: 0.3         // 最小相关度分数
  }
};
```

#### **后端处理说明**
- 前端传入结构化字段
- 后端自动合并到数据库的 `config` JSON 字段
- 前端无需了解底层存储结构
- 专注于业务逻辑，不用处理复杂的 JSON

### 🎨 前端界面设计建议

```

## 💡 最佳实践

### 🎯 使用结构化配置字段

```javascript
// ✅ 推荐：使用结构化字段
const configUpdate = {
  model_id: "gpt-4",
  prompt: "你是专业助手...",
  llm_model_config: {
    temperature: 0.7,
    max_tokens: 2000
  },
  knowledge_base_config: {
    max_results: 5,
    min_score: 0.3
  }
};

// ❌ 不推荐：直接操作复杂JSON（前端已隐藏）
// const configUpdate = {
//   config: { /* 复杂的JSON结构 */ }
// };
```

### 🔧 配置参数建议

#### **LLM模型配置**
```javascript
const llmConfig = {
  temperature: 0.7,     // 创意程度：0.1(保守) - 0.9(创意)
  max_tokens: 2000,     // 回复长度：1-4000
  top_p: 0.9,          // 词汇选择：0.1(精确) - 1.0(多样)
  max_retries: 3,      // 重试次数：1-5
  timeout: 10          // 超时时间：5-30秒
};
```

#### **知识库配置**
```javascript
const kbConfig = {
  max_results: 5,      // 检索结果数：1-20
  min_score: 0.3      // 相关度阈值：0.1-0.9
};
```

### 📊 界面设计原则

1. **分区明确**：基本信息和配置信息分开展示
2. **状态清晰**：显示是否有未发布的配置更改
3. **操作简单**：使用结构化表单而非JSON编辑器
4. **反馈及时**：基本信息立即生效，配置信息显示草稿状态

## 常见问题

### Q: 为什么不直接暴露 config JSON 字段？
A: 为了简化前端开发，避免复杂的JSON操作，使用结构化字段更清晰、更安全。

### Q: 如果需要自定义配置怎么办？
A: 可以通过扩展 llm_model_config 或 knowledge_base_config 的字段来支持新配置。

### Q: 结构化字段是如何存储的？
A: 后端自动将这些字段合并到数据库的 config JSON 字段中，前端无需关心存储细节。

### Q: 配置优先级是什么？
A: 用户明确指定的配置 > 模板默认配置 > 系统默认配置。