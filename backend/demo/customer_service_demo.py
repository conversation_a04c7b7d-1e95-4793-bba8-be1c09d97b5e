import os
from typing import Dict, List, Any, TypedDict, Annotated, Optional
from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
import asyncio
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain.chat_models import init_chat_model
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.vectorstores import VectorStore
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import TextLoader
from langchain_community.embeddings import FastEmbedEmbeddings
from langchain_community.vectorstores import FAISS

from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.tools import tool

# 配置LLM
api_key = "sk-zfhpiwxpncrzqlmtomnochjllwctdajoilqsbzetjsuhgmua"
base_url = "https://api.siliconflow.cn/v1"

# 初始化LLM
llm = init_chat_model("Qwen/Qwen2.5-72B-Instruct", model_provider="openai", api_key=api_key, base_url=base_url)

# 定义状态类型，使用TypedDict确保类型安全
class AgentState(TypedDict):
    messages: Annotated[List, add_messages]  # 使用add_messages注解确保消息可合并
    user_info: Dict[str, Any]  # 用户上下文信息
    knowledge_base_id: Optional[str]  # 知识库ID

# 知识库工具
@tool
def search_knowledge_base(query: str) -> str:
    """搜索知识库，查询相关信息
    
    Args:
        query: 查询内容
    """
    # 这里实现知识库搜索逻辑
    # 实际项目中应连接到向量数据库
    return f"从知识库中找到的关于'{query}'的信息: 这是一个模拟的知识库搜索结果。"

# 用户信息获取工具
@tool
def get_user_context(user_id: str) -> Dict[str, Any]:
    """获取用户上下文信息
    
    Args:
        user_id: 用户ID
    """
    # 模拟从数据库获取用户信息
    # 实际项目中应连接到用户数据库
    return {
        "user_id": user_id,
        "name": "张三",
        "vip_level": "高级会员",
        "last_purchase": "智能手表",
        "preferred_contact": "电话"
    }

# 定义智能客服节点
def customer_service_node(state: AgentState):
    """主客服节点，处理用户请求并返回回复"""
    messages = state["messages"]
    user_info = state.get("user_info", {})
    
    # 构建系统提示，包含用户信息上下文
    system_prompt = f"""你是一位专业的客服助手。
用户信息: {user_info if user_info else '暂无用户信息'}
请根据用户的问题提供准确、有帮助的回答。
如果需要查询更多信息，请使用可用的工具。
回答要简洁、专业、友好。"""
    
    # 创建带工具的Agent
    agent = create_react_agent(
        llm,
        tools=[search_knowledge_base, get_user_context],
        system_prompt=system_prompt
    )
    
    # 执行Agent
    response = agent.invoke({"messages": messages})
    
    # 返回AI回复
    return {"messages": response["messages"]}

# 构建智能客服图
def build_customer_service_graph():
    """构建智能客服的图结构"""
    # 创建图
    workflow = StateGraph(AgentState)
    
    # 添加节点
    workflow.add_node("customer_service", customer_service_node)
    
    # 设置入口和出口
    workflow.set_entry_point(START)
    workflow.add_edge(START, "customer_service")
    workflow.add_edge("customer_service", END)
    
    # 编译图
    return workflow.compile()

# 为每个会话创建独立的内存存储
session_memory = {}

# 创建或获取会话内存
def get_session_memory(session_id: str):
    if session_id not in session_memory:
        session_memory[session_id] = MemorySaver()
    return session_memory[session_id]

# API模型
class ChatRequest(BaseModel):
    session_id: str = Field(..., description="会话ID")
    message: str = Field(..., description="用户消息")
    user_id: Optional[str] = Field(None, description="用户ID，用于获取用户信息")

class ChatResponse(BaseModel):
    session_id: str
    response: str
    
# 创建FastAPI应用
app = FastAPI(title="智能客服系统")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 构建智能客服图
customer_service_graph = build_customer_service_graph()

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    # 获取会话记忆
    memory = get_session_memory(request.session_id)
    
    # 准备输入状态
    messages = [HumanMessage(content=request.message)]
    
    # 获取用户信息(如果提供了用户ID)
    user_info = {}
    if request.user_id:
        user_info = get_user_context(request.user_id)
    
    input_state = {
        "messages": messages,
        "user_info": user_info,
    }
    
    # 使用图执行
    result = customer_service_graph.invoke(
        input_state,
        config={"configurable": {"thread_id": request.session_id}},
        checkpointer=memory
    )
    
    # 获取最后一条AI消息作为响应
    ai_messages = [msg for msg in result["messages"] if isinstance(msg, AIMessage)]
    response = ai_messages[-1].content if ai_messages else "抱歉，无法处理您的请求。"
    
    return ChatResponse(
        session_id=request.session_id,
        response=response
    )

# 模拟知识库构建函数(实际项目应单独处理)
def build_knowledge_base(docs_path: str, kb_name: str):
    """构建示例知识库"""
    # 加载文档
    loader = TextLoader(docs_path)
    documents = loader.load()
    
    # 分割文档
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200
    )
    chunks = text_splitter.split_documents(documents)
    
    # 创建向量存储
    embeddings = FastEmbedEmbeddings()
    vector_store = FAISS.from_documents(chunks, embeddings)
    
    # 保存向量存储
    vector_store.save_local(f"knowledge_base/{kb_name}")
    
    return f"知识库 {kb_name} 已创建"

if __name__ == "__main__":
    # 启动服务器
    uvicorn.run("customer_service_demo:app", host="0.0.0.0", port=8000, reload=True) 