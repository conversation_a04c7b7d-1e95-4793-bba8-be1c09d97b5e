

from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langchain.chat_models import init_chat_model
from llm_test import llm_deepseek_v3, llm_qwen3_32b, llm_qwen2_5_72b_instruct
import asyncio



async def get_tools():
    async with MultiServerMCPClient(
            {
                "math": {
                    "command": "python3",
                    "args": ["math_server.py"],
                    "transport": "stdio",
                },
                "weather": {
                    # Ensure your start your weather server on port 8000
                    "url": "http://localhost:8000/sse",
                    "transport": "sse",
                }
            }
    ) as client:
        tools = client.get_tools()
        return tools
    
async def run_agent():
    tools = await get_tools()
    print(f"Available tools: {tools}")
    llm = llm_qwen3_32b
    agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt="You are a helpful assistant that can answer questions and help with tasks. you can use the tools to get the information you need."
    )
    math_response = await agent.ainvoke(
        {"messages": [{"role": "user", "content": "what's (3 + 5) x 12?"}]}
    )
    weather_response = await agent.ainvoke(
        {"messages": [{"role": "user", "content": "what is the weather in nyc?"}]}
    )
    print(math_response)
    print(weather_response)

if __name__ == "__main__":
    asyncio.run(run_agent())





# print(math_response.content)

# weather_response = agent.invoke(
#     {"messages": [{"role": "user", "content": "what is the weather in nyc?"}]}
# )

# print(weather_response.content)