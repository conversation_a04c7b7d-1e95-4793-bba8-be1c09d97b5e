from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.store.memory import InMemoryStore
from langchain.chat_models import init_chat_model
from llm_test import llm_deepseek_v3, llm_qwen3_32b, llm_qwen2_5_72b_instruct
from langgraph.checkpoint.mongodb import MongoDBSaver


from typing import Optional
import os
import asyncio

class AgentService:
    """Agent 服务单例类，用于管理 agent 的创建和重用"""
    
    _instance: Optional['AgentService'] = None
    _agent = None
    _checkpointer = None
    _store = None
    _mongodb_context = None  # 添加 MongoDB 上下文管理器
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._agent is None:
            self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化 agent（只执行一次）"""
        print("Initializing agent...")
        
        # 获取工具
        tools = get_tools()
        print(f"Available tools: {tools}")
        
        # 创建 InMemoryStore 作为存储后端
        self._store = InMemoryStore()
        
        # 创建 MongoDBSaver 用于对话记忆持久化
        try:
            # MongoDB 连接配置
            mongodb_url = os.getenv("MONGODB_URL", "**************************************")
            database_name = os.getenv("MONGODB_DATABASE", "langgraph_checkpoints")
            
            # 使用上下文管理器创建 MongoDBSaver
            from langgraph.checkpoint.mongodb import MongoDBSaver
            
            # 正确的方式：保存上下文管理器并进入
            self._mongodb_context = MongoDBSaver.from_conn_string(
                mongodb_url, 
                database_name=database_name
            )
            self._checkpointer = self._mongodb_context.__enter__()
            
            # MongoDB 不需要 setup() 方法，会自动创建索引
            
            print(f"✅ 使用 MongoDBSaver，连接到: {mongodb_url}/{database_name}")
        except Exception as e:
            print(f"⚠️ MongoDB 连接失败，回退到 InMemorySaver: {str(e)}")
            # 如果 MongoDB 不可用，回退到 InMemorySaver
            self._checkpointer = InMemorySaver()
            self._mongodb_context = None
        
        # 创建 agent
        llm = llm_qwen3_32b
        self._agent = create_react_agent(
            model=llm,
            tools=tools,
            checkpointer=self._checkpointer,
            store=self._store,
        )
        print("Agent initialized successfully!")
    
    def get_agent(self):
        """获取 agent 实例"""
        return self._agent
    
    def get_checkpointer(self):
        """获取 checkpointer 实例"""
        return self._checkpointer
    
    def get_store(self):
        """获取 store 实例"""
        return self._store
    
    def cleanup(self):
        """清理资源"""
        if self._mongodb_context:
            try:
                self._mongodb_context.__exit__(None, None, None)
                print("🧹 MongoDB 连接已清理")
            except Exception as e:
                print(f"清理 MongoDB 连接时出错: {e}")
        elif hasattr(self._checkpointer, '__exit__'):
            try:
                self._checkpointer.__exit__(None, None, None)
            except Exception as e:
                print(f"清理 checkpointer 时出错: {e}")

def get_tools():
    """获取 MCP 工具"""
    # 初始化 MCP 客户端
    client = MultiServerMCPClient(
        {
            "math": {
                "command": "python3",
                "args": ["math_server.py"],
                "transport": "stdio",
            },
            "weather": {
                "url": "http://localhost:8000/sse",
                "transport": "sse",
            }
        }
    )
    tools = client.get_tools()
    return tools

# 全局 agent 服务实例
agent_service = AgentService()

def chat_with_agent(message: str, thread_id: str = "default") -> dict:
    """
    与 agent 进行对话
    
    Args:
        message: 用户消息
        thread_id: 会话ID，用于区分不同用户或会话
    
    Returns:
        包含响应内容的字典
    """
    agent = agent_service.get_agent()
    config = {"configurable": {"thread_id": thread_id}}
    
    try:
        response = agent.invoke(
            {"messages": [{"role": "user", "content": message}]},
            config
        )
        
        # 提取最后一条消息作为响应
        last_message = response["messages"][-1]
        return {
            "success": True,
            "response": last_message.content,
            "thread_id": thread_id
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "thread_id": thread_id
        }

# 测试代码（可以在开发时使用）
if __name__ == "__main__":
    # 测试多轮对话
    print("=== 测试对话 ===")
    
    # 第一轮对话
    result1 = chat_with_agent("what's (3 + 5) x 12?", "user_1")
    print(f"Math result: {result1}")
    
    # 第二轮对话（同一用户）
    result2 = chat_with_agent("what is the weather in nyc?", "user_1")
    print(f"Weather result: {result2}")
    
    # 第三轮对话（同一用户，测试记忆）
    result3 = chat_with_agent("Can you explain the math calculation again?", "user_1")
    print(f"Follow-up result: {result3}")
    
    # 不同用户的对话
    result4 = chat_with_agent("Hello, this is a new conversation", "user_2")
    print(f"New user result: {result4}")