from langchain.chat_models import init_chat_model
from langchain.embeddings import init_embeddings

api_key = "sk-zfhpiwxpncrzqlmtomnochjllwctdajoilqsbzetjsuhgmua"
base_url = "https://api.siliconflow.cn/v1"

llm_qwen3_32b = init_chat_model("Qwen/Qwen3-32B", model_provider="openai", api_key=api_key, base_url=base_url)
llm_qwen2_5_72b_instruct = init_chat_model("Qwen/Qwen2.5-72B-Instruct", model_provider="openai", api_key=api_key, base_url=base_url)
llm_deepseek_v3 = init_chat_model("deepseek-ai/DeepSeek-V3", model_provider="openai", api_key=api_key, base_url=base_url)


baai_bge_m3 = init_embeddings("openai:BAAI/bge-m3", api_key=api_key, base_url=base_url)
baai_bge_m3_pro = init_embeddings("openai:Pro/BAAI/bge-m3", api_key=api_key, base_url=base_url)
bce_embedding_base_v1 = init_embeddings("openai:netease-youdao/bce-embedding-base_v1", api_key=api_key, base_url=base_url)
bge_large_en_v1_5 = init_embeddings("openai:BAAI/bge-large-en-v1.5", api_key=api_key, base_url=base_url)
