"""
多租户Agent服务使用示例

这个脚本演示了如何使用多租户Agent服务：
1. 创建租户
2. 为租户创建不同类型的Agent
3. 与Agent进行对话
4. 管理会话和历史记录
"""

import requests
import json
from typing import Dict, Any
import time

class MultiTenantAgentClient:
    """多租户Agent服务客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def create_tenant(self, tenant_id: str, name: str, max_agents: int = 10, 
                     allowed_agent_types: list = None) -> Dict[str, Any]:
        """创建租户"""
        data = {
            "tenant_id": tenant_id,
            "name": name,
            "max_agents": max_agents,
            "allowed_agent_types": allowed_agent_types or []
        }
        
        response = self.session.post(f"{self.base_url}/tenants", json=data)
        return response.json()
    
    def list_tenants(self) -> Dict[str, Any]:
        """列出所有租户"""
        response = self.session.get(f"{self.base_url}/tenants")
        return response.json()
    
    def create_agent(self, tenant_id: str, agent_type: str, agent_id: str = None,
                    name: str = None, description: str = None, 
                    custom_prompt: str = None) -> Dict[str, Any]:
        """为租户创建Agent"""
        data = {
            "agent_type": agent_type,
            "agent_id": agent_id,
            "name": name,
            "description": description,
            "custom_prompt": custom_prompt
        }
        # 移除None值
        data = {k: v for k, v in data.items() if v is not None}
        
        response = self.session.post(f"{self.base_url}/tenants/{tenant_id}/agents", json=data)
        return response.json()
    
    def list_agents(self, tenant_id: str) -> Dict[str, Any]:
        """列出租户的所有Agent"""
        response = self.session.get(f"{self.base_url}/tenants/{tenant_id}/agents")
        return response.json()
    
    def chat_with_agent(self, tenant_id: str, agent_id: str, message: str, 
                       thread_id: str = "default") -> Dict[str, Any]:
        """与Agent对话"""
        data = {
            "message": message,
            "thread_id": thread_id
        }
        
        response = self.session.post(
            f"{self.base_url}/tenants/{tenant_id}/agents/{agent_id}/chat", 
            json=data
        )
        return response.json()
    
    def chat_with_agent_stream(self, tenant_id: str, agent_id: str, message: str, 
                             thread_id: str = "default"):
        """与Agent进行流式对话（SSE格式）"""
        data = {
            "message": message,
            "thread_id": thread_id
        }
        
        with self.session.post(
            f"{self.base_url}/tenants/{tenant_id}/agents/{agent_id}/chat/stream",
            json=data,
            stream=True,
            headers={"Accept": "text/event-stream"}
        ) as response:
            if response.status_code == 200:
                for line in response.iter_lines(decode_unicode=True):
                    if line.startswith("data: "):
                        try:
                            data_str = line[6:]  # 移除 "data: " 前缀
                            if data_str.strip():
                                yield json.loads(data_str)
                        except json.JSONDecodeError:
                            continue
            else:
                yield {"type": "error", "data": f"HTTP {response.status_code}: {response.text}"}
    
    def chat_with_agent_stream_json(self, tenant_id: str, agent_id: str, message: str, 
                                   thread_id: str = "default"):
        """与Agent进行流式对话（JSON流格式）"""
        data = {
            "message": message,
            "thread_id": thread_id
        }
        
        with self.session.post(
            f"{self.base_url}/tenants/{tenant_id}/agents/{agent_id}/chat/stream-json",
            json=data,
            stream=True,
            headers={"Accept": "application/x-ndjson"}
        ) as response:
            if response.status_code == 200:
                for line in response.iter_lines(decode_unicode=True):
                    if line.strip():
                        try:
                            yield json.loads(line)
                        except json.JSONDecodeError:
                            continue
            else:
                yield {"type": "error", "data": f"HTTP {response.status_code}: {response.text}"}
    
    def get_session_history(self, tenant_id: str, agent_id: str, 
                           thread_id: str) -> Dict[str, Any]:
        """获取会话历史"""
        response = self.session.get(
            f"{self.base_url}/tenants/{tenant_id}/agents/{agent_id}/sessions/{thread_id}/history"
        )
        return response.json()
    
    def clear_session(self, tenant_id: str, agent_id: str, 
                     thread_id: str) -> Dict[str, Any]:
        """清除会话历史"""
        response = self.session.delete(
            f"{self.base_url}/tenants/{tenant_id}/agents/{agent_id}/sessions/{thread_id}"
        )
        return response.json()
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        response = self.session.get(f"{self.base_url}/system/info")
        return response.json()
    
    def get_agent_types(self) -> Dict[str, Any]:
        """获取可用的Agent类型"""
        response = self.session.get(f"{self.base_url}/system/agent-types")
        return response.json()

    # ==================== 用户管理 ====================
    
    def create_user(self, user_id: str, user_name: str, tenant_id: str, 
                   email: str = None, role: str = "user") -> Dict[str, Any]:
        """创建用户"""
        data = {
            "user_id": user_id,
            "user_name": user_name,
            "tenant_id": tenant_id,
            "email": email,
            "role": role
        }
        # 移除None值
        data = {k: v for k, v in data.items() if v is not None}
        
        response = self.session.post(f"{self.base_url}/users", json=data)
        return response.json()
    
    def list_users(self, tenant_id: str = None) -> Dict[str, Any]:
        """列出用户"""
        params = {}
        if tenant_id:
            params["tenant_id"] = tenant_id
            
        response = self.session.get(f"{self.base_url}/users", params=params)
        return response.json()
    
    def get_user(self, user_id: str) -> Dict[str, Any]:
        """获取用户信息"""
        response = self.session.get(f"{self.base_url}/users/{user_id}")
        return response.json()
    
    def delete_user(self, user_id: str) -> Dict[str, Any]:
        """删除用户"""
        response = self.session.delete(f"{self.base_url}/users/{user_id}")
        return response.json()
    
    # ==================== 会话管理 ====================
    
    def list_sessions(self, tenant_id: str = None, agent_id: str = None, 
                     user_id: str = None, active_only: bool = False, 
                     limit: int = 50) -> Dict[str, Any]:
        """列出会话"""
        params = {
            "active_only": active_only,
            "limit": limit
        }
        if tenant_id:
            params["tenant_id"] = tenant_id
        if agent_id:
            params["agent_id"] = agent_id
        if user_id:
            params["user_id"] = user_id
        
        response = self.session.get(f"{self.base_url}/sessions", params=params)
        return response.json()
    
    def list_user_sessions(self, user_id: str, active_only: bool = False, 
                          limit: int = 50) -> Dict[str, Any]:
        """列出指定用户的会话"""
        params = {
            "active_only": active_only,
            "limit": limit
        }
        response = self.session.get(f"{self.base_url}/users/{user_id}/sessions", params=params)
        return response.json()
    
    def list_tenant_sessions(self, tenant_id: str, active_only: bool = False, 
                           limit: int = 50) -> Dict[str, Any]:
        """列出指定租户的会话"""
        params = {
            "active_only": active_only,
            "limit": limit
        }
        response = self.session.get(f"{self.base_url}/tenants/{tenant_id}/sessions", params=params)
        return response.json()
    
    def cleanup_expired_sessions(self, expiry_hours: int = 24) -> Dict[str, Any]:
        """清理过期会话"""
        params = {"expiry_hours": expiry_hours}
        response = self.session.delete(f"{self.base_url}/sessions/cleanup", params=params)
        return response.json()

def print_json(data: Dict[str, Any], title: str = ""):
    """格式化打印JSON数据"""
    if title:
        print(f"\n=== {title} ===")
    print(json.dumps(data, indent=2, ensure_ascii=False))

def demo_multi_tenant_agents():
    """演示多租户Agent服务的完整流程"""
    
    client = MultiTenantAgentClient()
    
    print("🚀 多租户Agent服务演示开始")
    
    # 1. 获取系统信息
    print("\n📊 获取系统信息...")
    system_info = client.get_system_info()
    print_json(system_info, "系统信息")
    
    # 2. 获取可用的Agent类型
    print("\n🤖 获取可用的Agent类型...")
    agent_types = client.get_agent_types()
    print_json(agent_types, "Agent类型")
    
    # 3. 创建租户
    print("\n🏢 创建租户...")
    
    # 创建第一个租户（小公司）
    tenant1_result = client.create_tenant(
        tenant_id="small_company",
        name="小型科技公司",
        max_agents=5,
        allowed_agent_types=["general", "math", "weather"]
    )
    print_json(tenant1_result, "创建租户1")
    
    # 创建第二个租户（大企业）
    tenant2_result = client.create_tenant(
        tenant_id="enterprise",
        name="大型企业集团",
        max_agents=20,
        allowed_agent_types=["general", "math", "weather", "code", "analysis"]
    )
    print_json(tenant2_result, "创建租户2")
    
    # 4. 列出所有租户
    print("\n📋 列出所有租户...")
    tenants = client.list_tenants()
    print_json(tenants, "租户列表")
    
    # 5. 为租户创建Agent
    print("\n🤖 为租户创建Agent...")
    
    # 为小公司创建Agent
    agent1_result = client.create_agent(
        tenant_id="small_company",
        agent_type="general",
        agent_id="assistant",
        name="小公司助手",
        description="专为小公司定制的通用助手",
        custom_prompt="你是小型科技公司的AI助手，专注于帮助员工提高工作效率。"
    )
    print_json(agent1_result, "小公司 - 通用助手")
    
    agent2_result = client.create_agent(
        tenant_id="small_company",
        agent_type="math",
        agent_id="calculator"
    )
    print_json(agent2_result, "小公司 - 数学计算器")
    
    # 为大企业创建Agent
    agent3_result = client.create_agent(
        tenant_id="enterprise",
        agent_type="general",
        agent_id="enterprise_assistant"
    )
    print_json(agent3_result, "大企业 - 通用助手")
    
    agent4_result = client.create_agent(
        tenant_id="enterprise",
        agent_type="code",
        agent_id="code_reviewer",
        name="代码审查助手",
        description="专业的代码审查和优化助手",
        custom_prompt="你是一个资深的代码审查专家，擅长发现代码问题并提供优化建议。"
    )
    print_json(agent4_result, "大企业 - 代码助手")
    
    agent5_result = client.create_agent(
        tenant_id="enterprise",
        agent_type="analysis",
        agent_id="data_scientist"
    )
    print_json(agent5_result, "大企业 - 数据分析师")
    
    # 5.5. 创建用户
    print("\n👥 创建用户...")
    
    # 为小公司创建用户
    user1_result = client.create_user(
        user_id="alice_smith",
        user_name="Alice Smith",
        tenant_id="small_company",
        email="<EMAIL>",
        role="admin"
    )
    print_json(user1_result, "小公司用户 - Alice")
    
    user2_result = client.create_user(
        user_id="bob_jones",
        user_name="Bob Jones",
        tenant_id="small_company",
        email="<EMAIL>",
        role="user"
    )
    print_json(user2_result, "小公司用户 - Bob")
    
    # 为大企业创建用户
    user3_result = client.create_user(
        user_id="charlie_wilson",
        user_name="Charlie Wilson",
        tenant_id="enterprise",
        email="<EMAIL>",
        role="admin"
    )
    print_json(user3_result, "大企业用户 - Charlie")
    
    user4_result = client.create_user(
        user_id="diana_brown",
        user_name="Diana Brown",
        tenant_id="enterprise",
        email="<EMAIL>",
        role="developer"
    )
    print_json(user4_result, "大企业用户 - Diana")

    # 6. 列出各租户的Agent
    print("\n📋 列出各租户的Agent...")
    
    small_company_agents = client.list_agents("small_company")
    print_json(small_company_agents, "小公司的Agent列表")
    
    enterprise_agents = client.list_agents("enterprise")
    print_json(enterprise_agents, "大企业的Agent列表")
    
    # 6.5. 列出用户
    print("\n👥 列出用户...")
    
    all_users = client.list_users()
    print_json(all_users, "所有用户列表")
    
    small_company_users = client.list_users("small_company")
    print_json(small_company_users, "小公司用户列表")

    # 7. 与不同Agent进行对话
    print("\n💬 与不同Agent进行对话...")
    
    # Alice与小公司的通用助手对话
    chat1 = client.chat_with_agent(
        tenant_id="small_company",
        agent_id="assistant",
        message="你好，我是Alice，请介绍一下你的功能。",
        thread_id="alice_session_1"
    )
    # 添加用户信息到请求中
    chat1_with_user = {
        "message": "你好，我是Alice，请介绍一下你的功能。",
        "thread_id": "alice_session_1",
        "user_id": "alice_smith",
        "user_name": "Alice Smith"
    }
    response1 = client.session.post(
        f"{client.base_url}/tenants/small_company/agents/assistant/chat",
        json=chat1_with_user
    )
    print_json(response1.json(), "Alice与小公司助手对话")
    
    # Bob与数学计算器对话
    chat2_with_user = {
        "message": "请计算 (25 + 75) × 12 ÷ 4 的结果",
        "thread_id": "bob_math_session",
        "user_id": "bob_jones",
        "user_name": "Bob Jones"
    }
    response2 = client.session.post(
        f"{client.base_url}/tenants/small_company/agents/calculator/chat",
        json=chat2_with_user
    )
    print_json(response2.json(), "Bob与数学计算器对话")
    
    # Charlie与代码助手对话
    chat3_with_user = {
        "message": "请帮我审查这段Python代码：\n\ndef calculate(x, y):\n    return x + y * 2",
        "thread_id": "charlie_code_review",
        "user_id": "charlie_wilson",
        "user_name": "Charlie Wilson"
    }
    response3 = client.session.post(
        f"{client.base_url}/tenants/enterprise/agents/code_reviewer/chat",
        json=chat3_with_user
    )
    print_json(response3.json(), "Charlie与代码助手对话")

    # 8. 演示流式对话
    print("\n🌊 演示流式对话...")
    
    print("Diana与数据分析助手的流式对话:")
    stream_chunks = []
    for chunk in client.chat_with_agent_stream_json(
        tenant_id="enterprise",
        agent_id="data_scientist",
        message="请介绍一下数据分析的基本流程",
        thread_id="diana_analysis_session"
    ):
        stream_chunks.append(chunk)
        if chunk.get("type") == "content":
            print(f"  📨 {chunk.get('data', '')}")
        elif chunk.get("type") == "done":
            print("  ✅ 对话完成")
            break
        elif chunk.get("type") == "error":
            print(f"  ❌ 错误: {chunk.get('data', '')}")
            break
    
    print_json(stream_chunks[-3:], "流式对话最后几个chunk")

    # 9. 会话管理演示
    print("\n📋 会话管理演示...")
    
    # 列出所有会话
    all_sessions = client.list_sessions(limit=10)
    print_json(all_sessions, "所有会话列表")
    
    # 列出Alice的会话
    alice_sessions = client.list_user_sessions("alice_smith")
    print_json(alice_sessions, "Alice的会话列表")
    
    # 列出小公司的会话
    small_company_sessions = client.list_tenant_sessions("small_company")
    print_json(small_company_sessions, "小公司的会话列表")
    
    # 列出活跃会话
    active_sessions = client.list_sessions(active_only=True)
    print_json(active_sessions, "活跃会话列表")

    # 10. 多轮对话测试
    print("\n🔄 多轮对话测试...")
    
    # Alice继续对话
    chat4_with_user = {
        "message": "我刚才问了什么问题？",
        "thread_id": "alice_session_1",
        "user_id": "alice_smith",
        "user_name": "Alice Smith"
    }
    response4 = client.session.post(
        f"{client.base_url}/tenants/small_company/agents/assistant/chat",
        json=chat4_with_user
    )
    print_json(response4.json(), "Alice的第二轮对话")

    # 11. 获取会话历史
    print("\n📚 获取会话历史...")
    
    history1 = client.get_session_history(
        tenant_id="small_company",
        agent_id="assistant",
        thread_id="alice_session_1"
    )
    print_json(history1, "Alice的会话历史")
    
    # 12. 演示不同租户间的隔离
    print("\n🔒 演示租户隔离...")
    
    # 尝试让小公司访问大企业的Agent（应该失败）
    try:
        chat_fail = client.chat_with_agent(
            tenant_id="small_company",
            agent_id="code_reviewer",  # 这是大企业的Agent
            message="Hello",
            thread_id="test"
        )
        print_json(chat_fail, "跨租户访问测试")
    except Exception as e:
        print(f"❌ 跨租户访问被正确阻止: {e}")
    
    # 13. 清理演示
    print("\n🧹 清理演示数据...")
    
    # 清除会话
    clear_result = client.clear_session(
        tenant_id="small_company",
        agent_id="assistant",
        thread_id="alice_session_1"
    )
    print_json(clear_result, "清除会话结果")
    
    # 清理过期会话
    cleanup_result = client.cleanup_expired_sessions(expiry_hours=0)  # 立即清理所有会话
    print_json(cleanup_result, "清理过期会话结果")
    
    print("\n✅ 多租户Agent服务演示完成！")

def performance_test():
    """性能测试"""
    print("\n⚡ 开始性能测试...")
    
    client = MultiTenantAgentClient()
    
    # 测试并发对话
    import concurrent.futures
    import threading
    
    def chat_test(thread_num: int):
        """单个对话测试"""
        start_time = time.time()
        result = client.chat_with_agent(
            tenant_id="small_company",
            agent_id="assistant",
            message=f"这是第{thread_num}个测试消息",
            thread_id=f"perf_test_{thread_num}"
        )
        end_time = time.time()
        return {
            "thread": thread_num,
            "duration": end_time - start_time,
            "success": result.get("success", False)
        }
    
    # 并发测试
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(chat_test, i) for i in range(10)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    # 统计结果
    successful = sum(1 for r in results if r["success"])
    avg_duration = sum(r["duration"] for r in results) / len(results)
    
    print(f"📊 性能测试结果:")
    print(f"   总请求数: {len(results)}")
    print(f"   成功请求: {successful}")
    print(f"   平均响应时间: {avg_duration:.2f}秒")
    print(f"   成功率: {successful/len(results)*100:.1f}%")

if __name__ == "__main__":
    try:
        # 主演示
        demo_multi_tenant_agents()
        
        # 性能测试
        performance_test()
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到多租户Agent服务")
        print("请确保服务正在运行: python multi_tenant_agent_api.py")
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc() 