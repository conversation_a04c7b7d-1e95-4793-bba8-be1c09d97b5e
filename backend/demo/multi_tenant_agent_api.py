from fastapi import FastAPI, HTTPException, Depends, Header
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
import uvicorn
import logging
from datetime import datetime
import json
import asyncio

from multi_tenant_agent_service import (
    multi_tenant_agent_service,
    AgentType,
    TenantConfig,
    AgentConfig,
    UserInfo,
    SessionInfo
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="多租户 LangGraph Agent API",
    description="支持多租户、每个租户下多个Agent的智能对话服务",
    version="2.0.0"
)

# ==================== 请求/响应模型 ====================

class TenantCreateRequest(BaseModel):
    tenant_id: str = Field(..., description="租户ID")
    name: str = Field(..., description="租户名称")
    max_agents: int = Field(10, description="最大Agent数量")
    max_sessions_per_agent: int = Field(100, description="每个Agent最大会话数")
    mongodb_database: Optional[str] = Field(None, description="专用MongoDB数据库名")
    allowed_agent_types: List[str] = Field(default_factory=list, description="允许的Agent类型")

class AgentCreateRequest(BaseModel):
    agent_type: str = Field(..., description="Agent类型")
    agent_id: Optional[str] = Field(None, description="自定义Agent ID")
    name: Optional[str] = Field(None, description="自定义Agent名称")
    description: Optional[str] = Field(None, description="Agent描述")
    custom_prompt: Optional[str] = Field(None, description="自定义提示词")

class ChatRequest(BaseModel):
    message: str = Field(..., description="用户消息")
    thread_id: Optional[str] = Field("default", description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    user_name: Optional[str] = Field(None, description="用户名称")

class ChatResponse(BaseModel):
    success: bool
    response: Optional[str] = None
    error: Optional[str] = None
    tenant_id: str
    agent_id: str
    thread_id: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None

class TenantResponse(BaseModel):
    tenant_id: str
    name: str
    agent_count: int
    max_agents: int
    created_at: str

class AgentResponse(BaseModel):
    agent_id: str
    agent_type: str
    name: str
    description: str
    tools_count: int

class UserCreateRequest(BaseModel):
    user_id: str = Field(..., description="用户ID")
    user_name: str = Field(..., description="用户名称")
    tenant_id: str = Field(..., description="所属租户ID")
    email: Optional[str] = Field(None, description="邮箱")
    role: str = Field("user", description="角色")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")

class UserResponse(BaseModel):
    user_id: str
    user_name: str
    tenant_id: str
    email: Optional[str]
    role: str
    created_at: str
    last_login: Optional[str]

class SessionResponse(BaseModel):
    session_id: str
    tenant_id: str
    agent_id: str
    thread_id: str
    user_id: Optional[str]
    user_name: Optional[str]
    created_at: str
    last_active: str
    message_count: int
    metadata: Dict[str, Any]

# ==================== 依赖注入 ====================

def get_tenant_id(x_tenant_id: str = Header(..., description="租户ID")):
    """从请求头获取租户ID"""
    return x_tenant_id

def get_agent_id(x_agent_id: str = Header(..., description="Agent ID")):
    """从请求头获取Agent ID"""
    return x_agent_id

# ==================== 健康检查 ====================

@app.get("/health")
async def health_check():
    """健康检查端点"""
    tenants = multi_tenant_agent_service.list_tenants()
    return {
        "status": "healthy",
        "service": "multi_tenant_agent_api",
        "tenants_count": len(tenants),
        "timestamp": datetime.now().isoformat()
    }

# ==================== 租户管理 ====================

@app.post("/tenants", response_model=Dict[str, Any])
async def create_tenant(request: TenantCreateRequest):
    """创建新租户"""
    try:
        # 转换允许的Agent类型
        allowed_types = []
        for type_str in request.allowed_agent_types:
            try:
                allowed_types.append(AgentType(type_str))
            except ValueError:
                raise HTTPException(
                    status_code=400, 
                    detail=f"无效的Agent类型: {type_str}. 可用类型: {[t.value for t in AgentType]}"
                )
        
        # 如果没有指定允许的类型，默认允许所有类型
        if not allowed_types:
            allowed_types = list(AgentType)
        
        tenant_config = TenantConfig(
            tenant_id=request.tenant_id,
            name=request.name,
            max_agents=request.max_agents,
            max_sessions_per_agent=request.max_sessions_per_agent,
            mongodb_database=request.mongodb_database,
            allowed_agent_types=allowed_types
        )
        
        success = multi_tenant_agent_service.create_tenant(tenant_config)
        
        if success:
            return {
                "success": True,
                "message": f"租户 {request.tenant_id} 创建成功",
                "tenant_id": request.tenant_id
            }
        else:
            raise HTTPException(status_code=400, detail="租户创建失败")
            
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建租户出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

@app.get("/tenants", response_model=List[TenantResponse])
async def list_tenants():
    """列出所有租户"""
    try:
        tenants = multi_tenant_agent_service.list_tenants()
        return [TenantResponse(**tenant) for tenant in tenants]
    except Exception as e:
        logger.error(f"获取租户列表出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取租户列表失败: {str(e)}")

@app.delete("/tenants/{tenant_id}")
async def delete_tenant(tenant_id: str):
    """删除租户"""
    try:
        success = multi_tenant_agent_service.delete_tenant(tenant_id)
        
        if success:
            return {
                "success": True,
                "message": f"租户 {tenant_id} 删除成功",
                "tenant_id": tenant_id
            }
        else:
            raise HTTPException(status_code=404, detail=f"租户 {tenant_id} 不存在")
            
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"删除租户出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除租户失败: {str(e)}")

# ==================== Agent管理 ====================

@app.post("/tenants/{tenant_id}/agents")
async def create_agent(tenant_id: str, request: AgentCreateRequest):
    """为指定租户创建Agent"""
    try:
        # 验证Agent类型
        try:
            agent_type = AgentType(request.agent_type)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"无效的Agent类型: {request.agent_type}. 可用类型: {[t.value for t in AgentType]}"
            )
        
        # 创建自定义配置（如果提供）
        custom_config = None
        if any([request.name, request.description, request.custom_prompt]):
            # 获取默认模板
            template = multi_tenant_agent_service._agent_templates[agent_type]
            custom_config = AgentConfig(
                agent_type=agent_type,
                name=request.name or template.name,
                description=request.description or template.description,
                llm_model=template.llm_model,
                tools=template.tools,
                custom_prompt=request.custom_prompt or template.custom_prompt
            )
        
        agent_id = multi_tenant_agent_service.create_agent(
            tenant_id=tenant_id,
            agent_type=agent_type,
            agent_id=request.agent_id,
            custom_config=custom_config
        )
        
        if agent_id:
            return {
                "success": True,
                "message": f"Agent {agent_id} 创建成功",
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "agent_type": agent_type.value
            }
        else:
            raise HTTPException(status_code=400, detail="Agent创建失败")
            
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建Agent出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建Agent失败: {str(e)}")

@app.get("/tenants/{tenant_id}/agents", response_model=List[AgentResponse])
async def list_agents(tenant_id: str):
    """列出指定租户的所有Agent"""
    try:
        agents = multi_tenant_agent_service.list_agents(tenant_id)
        return [AgentResponse(**agent) for agent in agents]
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取Agent列表出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取Agent列表失败: {str(e)}")

@app.delete("/tenants/{tenant_id}/agents/{agent_id}")
async def delete_agent(tenant_id: str, agent_id: str):
    """删除指定租户的指定Agent"""
    try:
        success = multi_tenant_agent_service.delete_agent(tenant_id, agent_id)
        
        if success:
            return {
                "success": True,
                "message": f"Agent {agent_id} 删除成功",
                "tenant_id": tenant_id,
                "agent_id": agent_id
            }
        else:
            raise HTTPException(status_code=404, detail=f"Agent {agent_id} 不存在")
            
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"删除Agent出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除Agent失败: {str(e)}")

# ==================== 对话功能 ====================

@app.post("/tenants/{tenant_id}/agents/{agent_id}/chat", response_model=ChatResponse)
async def chat_with_agent(tenant_id: str, agent_id: str, request: ChatRequest):
    """与指定租户的指定Agent进行对话"""
    try:
        logger.info(f"对话请求 - 租户: {tenant_id}, Agent: {agent_id}, 会话: {request.thread_id}, 用户: {request.user_id}")
        
        result = multi_tenant_agent_service.chat_with_agent(
            tenant_id=tenant_id,
            agent_id=agent_id,
            message=request.message,
            thread_id=request.thread_id,
            user_id=request.user_id,
            user_name=request.user_name
        )
        
        logger.info(f"对话完成 - 租户: {tenant_id}, Agent: {agent_id}, 成功: {result['success']}")
        
        return ChatResponse(**result)
        
    except Exception as e:
        logger.error(f"对话处理出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"对话处理失败: {str(e)}")

@app.post("/tenants/{tenant_id}/agents/{agent_id}/chat/stream")
async def chat_with_agent_stream(tenant_id: str, agent_id: str, request: ChatRequest):
    """与指定租户的指定Agent进行流式对话"""
    
    async def generate_stream():
        """生成流式响应"""
        try:
            logger.info(f"流式对话请求 - 租户: {tenant_id}, Agent: {agent_id}, 会话: {request.thread_id}")
            
            # 发送开始事件
            yield f"data: {json.dumps({'type': 'start', 'data': '开始对话', 'tenant_id': tenant_id, 'agent_id': agent_id, 'thread_id': request.thread_id}, ensure_ascii=False)}\n\n"
            
            # 使用异步流式方法
            async for chunk in multi_tenant_agent_service.chat_with_agent_stream_async(
                tenant_id=tenant_id,
                agent_id=agent_id,
                message=request.message,
                thread_id=request.thread_id,
                user_id=request.user_id,
                user_name=request.user_name
            ):
                # 将chunk转换为SSE格式
                sse_data = json.dumps(chunk, ensure_ascii=False)
                yield f"data: {sse_data}\n\n"
                
                # 如果是错误或完成，添加小延迟确保数据传输
                if chunk.get("type") in ["error", "done"]:
                    await asyncio.sleep(0.1)
            
            logger.info(f"流式对话完成 - 租户: {tenant_id}, Agent: {agent_id}")
            
        except Exception as e:
            logger.error(f"流式对话处理出错: {str(e)}")
            error_chunk = {
                "type": "error",
                "data": str(e),
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": request.thread_id
            }
            yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

@app.post("/tenants/{tenant_id}/agents/{agent_id}/chat/stream-json")
async def chat_with_agent_stream_json(tenant_id: str, agent_id: str, request: ChatRequest):
    """与指定租户的指定Agent进行流式对话（JSON流格式）"""
    
    async def generate_json_stream():
        """生成JSON流式响应"""
        try:
            logger.info(f"JSON流式对话请求 - 租户: {tenant_id}, Agent: {agent_id}, 会话: {request.thread_id}")
            
            # 使用异步流式方法
            async for chunk in multi_tenant_agent_service.chat_with_agent_stream_async(
                tenant_id=tenant_id,
                agent_id=agent_id,
                message=request.message,
                thread_id=request.thread_id,
                user_id=request.user_id,
                user_name=request.user_name
            ):
                # 每个chunk作为一行JSON
                yield json.dumps(chunk, ensure_ascii=False) + "\n"
                
                # 如果是错误或完成，添加小延迟
                if chunk.get("type") in ["error", "done"]:
                    await asyncio.sleep(0.1)
            
            logger.info(f"JSON流式对话完成 - 租户: {tenant_id}, Agent: {agent_id}")
            
        except Exception as e:
            logger.error(f"JSON流式对话处理出错: {str(e)}")
            error_chunk = {
                "type": "error",
                "data": str(e),
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": request.thread_id
            }
            yield json.dumps(error_chunk, ensure_ascii=False) + "\n"
    
    return StreamingResponse(
        generate_json_stream(),
        media_type="application/x-ndjson",  # Newline Delimited JSON
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

# ==================== 便捷端点（使用Header） ====================

@app.post("/chat", response_model=ChatResponse)
async def chat_with_header(
    request: ChatRequest,
    tenant_id: str = Depends(get_tenant_id),
    agent_id: str = Depends(get_agent_id)
):
    """使用Header指定租户和Agent进行对话（便捷接口）"""
    return await chat_with_agent(tenant_id, agent_id, request)

@app.post("/chat/stream")
async def chat_stream_with_header(
    request: ChatRequest,
    tenant_id: str = Depends(get_tenant_id),
    agent_id: str = Depends(get_agent_id)
):
    """使用Header指定租户和Agent进行流式对话（便捷接口）"""
    return await chat_with_agent_stream(tenant_id, agent_id, request)

@app.post("/chat/stream-json")
async def chat_stream_json_with_header(
    request: ChatRequest,
    tenant_id: str = Depends(get_tenant_id),
    agent_id: str = Depends(get_agent_id)
):
    """使用Header指定租户和Agent进行JSON流式对话（便捷接口）"""
    return await chat_with_agent_stream_json(tenant_id, agent_id, request)

@app.get("/agents", response_model=List[AgentResponse])
async def list_agents_with_header(tenant_id: str = Depends(get_tenant_id)):
    """使用Header指定租户获取Agent列表（便捷接口）"""
    return await list_agents(tenant_id)

# ==================== 会话管理 ====================

@app.post("/users")
async def create_user(request: UserCreateRequest):
    """创建用户"""
    try:
        user_info = UserInfo(
            user_id=request.user_id,
            user_name=request.user_name,
            tenant_id=request.tenant_id,
            email=request.email,
            role=request.role,
            metadata=request.metadata
        )
        
        success = multi_tenant_agent_service.create_user(user_info)
        
        if success:
            return {
                "success": True,
                "message": f"用户 {request.user_id} 创建成功",
                "user_id": request.user_id
            }
        else:
            raise HTTPException(status_code=400, detail="用户创建失败")
            
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建用户出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建用户失败: {str(e)}")

@app.get("/users", response_model=List[UserResponse])
async def list_users(tenant_id: Optional[str] = None):
    """列出用户（可按租户过滤）"""
    try:
        users = multi_tenant_agent_service.list_users(tenant_id)
        return [UserResponse(**user) for user in users]
    except Exception as e:
        logger.error(f"获取用户列表出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")

@app.get("/tenants/{tenant_id}/users", response_model=List[UserResponse])
async def list_tenant_users(tenant_id: str):
    """列出指定租户的用户"""
    return await list_users(tenant_id)

@app.get("/users/{user_id}", response_model=UserResponse)
async def get_user(user_id: str):
    """获取用户信息"""
    try:
        user_info = multi_tenant_agent_service.get_user(user_id)
        if user_info:
            return UserResponse(
                user_id=user_info.user_id,
                user_name=user_info.user_name,
                tenant_id=user_info.tenant_id,
                email=user_info.email,
                role=user_info.role,
                created_at=user_info.created_at.isoformat(),
                last_login=user_info.last_login.isoformat() if user_info.last_login else None
            )
        else:
            raise HTTPException(status_code=404, detail=f"用户 {user_id} 不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户信息失败: {str(e)}")

@app.delete("/users/{user_id}")
async def delete_user(user_id: str):
    """删除用户"""
    try:
        success = multi_tenant_agent_service.delete_user(user_id)
        
        if success:
            return {
                "success": True,
                "message": f"用户 {user_id} 删除成功",
                "user_id": user_id
            }
        else:
            raise HTTPException(status_code=404, detail=f"用户 {user_id} 不存在")
            
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"删除用户出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")

@app.get("/sessions", response_model=List[SessionResponse])
async def list_sessions(
    tenant_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    user_id: Optional[str] = None,
    active_only: bool = False,
    limit: int = 50
):
    """列出会话"""
    try:
        sessions = multi_tenant_agent_service.list_sessions(
            tenant_id=tenant_id,
            agent_id=agent_id,
            user_id=user_id,
            active_only=active_only,
            limit=limit
        )
        return [SessionResponse(**session) for session in sessions]
    except Exception as e:
        logger.error(f"获取会话列表出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

@app.get("/tenants/{tenant_id}/sessions", response_model=List[SessionResponse])
async def list_tenant_sessions(tenant_id: str, active_only: bool = False, limit: int = 50):
    """列出指定租户的会话"""
    return await list_sessions(tenant_id=tenant_id, active_only=active_only, limit=limit)

@app.get("/tenants/{tenant_id}/agents/{agent_id}/sessions", response_model=List[SessionResponse])
async def list_agent_sessions(tenant_id: str, agent_id: str, active_only: bool = False, limit: int = 50):
    """列出指定Agent的会话"""
    return await list_sessions(tenant_id=tenant_id, agent_id=agent_id, active_only=active_only, limit=limit)

@app.get("/users/{user_id}/sessions", response_model=List[SessionResponse])
async def list_user_sessions(user_id: str, active_only: bool = False, limit: int = 50):
    """列出指定用户的会话"""
    return await list_sessions(user_id=user_id, active_only=active_only, limit=limit)

@app.delete("/sessions/cleanup")
async def cleanup_expired_sessions(expiry_hours: int = 24):
    """清理过期会话"""
    try:
        cleaned_count = multi_tenant_agent_service.cleanup_expired_sessions(expiry_hours)
        return {
            "success": True,
            "message": f"清理了 {cleaned_count} 个过期会话",
            "cleaned_count": cleaned_count,
            "expiry_hours": expiry_hours
        }
    except Exception as e:
        logger.error(f"清理过期会话出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理过期会话失败: {str(e)}")

@app.get("/tenants/{tenant_id}/agents/{agent_id}/sessions/{thread_id}/history")
async def get_session_history(tenant_id: str, agent_id: str, thread_id: str):
    """获取指定会话的历史记录"""
    try:
        checkpointer = multi_tenant_agent_service.get_checkpointer(tenant_id, agent_id)
        config = {"configurable": {"thread_id": f"{tenant_id}_{agent_id}_{thread_id}"}}
        
        state = checkpointer.get(config)
        
        if state is None:
            return {
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "messages": [],
                "message": "会话不存在"
            }
        
        messages = []
        try:
            state_values = state['channel_values']
            if "messages" in state_values:
                for msg in state_values["messages"]:
                    messages.append({
                        "content": msg.content if hasattr(msg, 'content') else str(msg),
                        "type": msg.__class__.__name__
                    })
        except Exception as parse_error:
            logger.warning(f"解析会话状态时出错: {str(parse_error)}")
            return {
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "messages": [],
                "message": f"会话存在但解析失败: {str(parse_error)}"
            }
        
        return {
            "tenant_id": tenant_id,
            "agent_id": agent_id,
            "thread_id": thread_id,
            "messages": messages,
            "total_messages": len(messages)
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取会话历史出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话历史失败: {str(e)}")

@app.delete("/tenants/{tenant_id}/agents/{agent_id}/sessions/{thread_id}")
async def clear_session(tenant_id: str, agent_id: str, thread_id: str):
    """清除指定会话的历史记录"""
    try:
        checkpointer = multi_tenant_agent_service.get_checkpointer(tenant_id, agent_id)
        config = {"configurable": {"thread_id": f"{tenant_id}_{agent_id}_{thread_id}"}}
        
        if hasattr(checkpointer, 'delete'):
            checkpointer.delete(config)
            return {
                "success": True,
                "message": f"会话 {thread_id} 已成功清除",
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id
            }
        else:
            return {
                "success": False,
                "message": f"当前存储类型不支持删除操作",
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id
            }
            
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"清除会话出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清除会话失败: {str(e)}")

# ==================== 系统信息 ====================

@app.get("/system/info")
async def get_system_info():
    """获取系统信息"""
    tenants = multi_tenant_agent_service.list_tenants()
    total_agents = sum(tenant["agent_count"] for tenant in tenants)
    
    return {
        "service_name": "多租户Agent服务",
        "version": "2.0.0",
        "tenants_count": len(tenants),
        "total_agents": total_agents,
        "available_agent_types": [t.value for t in AgentType],
        "timestamp": datetime.now().isoformat()
    }

@app.get("/system/agent-types")
async def get_agent_types():
    """获取可用的Agent类型"""
    return {
        "agent_types": [
            {
                "type": agent_type.value,
                "name": config.name,
                "description": config.description
            }
            for agent_type, config in multi_tenant_agent_service._agent_templates.items()
        ]
    }

# ==================== 启动和关闭事件 ====================

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info("多租户Agent API服务启动中...")
    logger.info("多租户Agent服务初始化完成，服务就绪")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("多租户Agent API服务正在关闭...")
    multi_tenant_agent_service.cleanup()
    logger.info("资源清理完成")

if __name__ == "__main__":
    # 启动服务
    uvicorn.run(
        "multi_tenant_agent_api:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    ) 