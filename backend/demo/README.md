# LangGraph Agent HTTP 服务

这是一个基于 LangGraph 的智能对话 Agent HTTP 服务，支持多用户会话管理和对话记忆功能。

## 功能特性

- ✅ **Agent 单例管理** - Agent 只创建一次，多次重用，提升性能
- ✅ **多用户会话** - 支持多个用户同时对话，每个用户有独立的会话记忆
- ✅ **对话记忆** - 支持多轮对话，Agent 能记住之前的对话内容
- ✅ **并发处理** - 支持多个用户同时发送请求
- ✅ **会话管理** - 支持查看、清除会话历史
- ✅ **健康检查** - 提供服务状态检查端点
- ✅ **完整的 REST API** - 标准的 HTTP 接口
- ✅ **错误处理** - 完善的异常处理和用户友好的错误信息

## 项目结构

```
backend/demo/
├── mcp_test2.py          # Agent 服务核心逻辑（需要MCP依赖）
├── simple_agent_demo.py  # 简化版Agent演示（无需外部依赖）
├── agent_api.py          # 完整版FastAPI HTTP服务
├── simple_agent_api.py   # 简化版FastAPI HTTP服务
├── test_agent_api.py     # 完整API测试脚本
├── simple_test.py        # 简化测试脚本
├── requirements.txt      # 依赖包列表
└── README.md            # 项目说明
```

## 快速开始

### 方式1: 使用简化版（推荐用于学习和演示）

```bash
# 1. 安装基础依赖
pip install fastapi uvicorn requests

# 2. 启动简化版服务
python simple_agent_api.py

# 3. 在另一个终端测试
python simple_test.py
```

### 方式2: 使用完整版（需要更多依赖）

```bash
# 1. 安装所有依赖
pip install -r requirements.txt

# 2. 启动完整版服务
python agent_api.py

# 3. 运行完整测试
python test_agent_api.py
```

## 核心问题解答

### Q: LangGraph create_react_agent 用户每次对话都要重新创建agent吗？

**答案：不需要！** 这是一个常见的误解。正确的做法是：

#### ❌ 错误做法（每次重新创建）
```python
def chat(message):
    # 每次都创建新的agent - 性能很差！
    agent = create_react_agent(model=llm, tools=tools)
    return agent.invoke({"messages": [{"role": "user", "content": message}]})
```

#### ✅ 正确做法（单例模式）
```python
class AgentService:
    _instance = None
    _agent = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._agent is None:
            # 只创建一次
            self._agent = create_react_agent(
                model=llm, 
                tools=tools,
                checkpointer=InMemorySaver()  # 用于会话记忆
            )
    
    def get_agent(self):
        return self._agent

# 全局单例
agent_service = AgentService()

def chat(message, thread_id="default"):
    # 重复使用同一个agent
    agent = agent_service.get_agent()
    config = {"configurable": {"thread_id": thread_id}}
    return agent.invoke(
        {"messages": [{"role": "user", "content": message}]},
        config
    )
```

### 关键优势

1. **性能提升** - Agent只初始化一次，避免重复创建开销
2. **内存效率** - 共享同一个Agent实例，节省内存
3. **会话管理** - 通过`thread_id`区分不同用户/会话
4. **并发支持** - 同一个Agent可以同时处理多个用户请求

## API 端点

### 1. 健康检查
```http
GET /health
```

### 2. 对话接口
```http
POST /chat
Content-Type: application/json

{
    "message": "你好，请帮我计算 (3+5)*12",
    "thread_id": "user_123"  // 可选，默认为 "default"
}
```

### 3. 获取会话历史
```http
GET /sessions/{thread_id}/history
```

### 4. 清除会话
```http
DELETE /sessions/{thread_id}
```

### 5. 获取会话列表
```http
GET /sessions
```

## 使用示例

### Python 客户端示例

```python
import requests

# 发送对话请求
response = requests.post("http://localhost:8001/chat", json={
    "message": "计算 (3+5)*12",
    "thread_id": "user_123"
})

result = response.json()
print(f"Agent 响应: {result['response']}")

# 继续对话（Agent会记住之前的内容）
response2 = requests.post("http://localhost:8001/chat", json={
    "message": "刚才的计算结果是多少？",
    "thread_id": "user_123"  # 同一个thread_id
})
```

### cURL 示例

```bash
# 发送对话请求
curl -X POST "http://localhost:8001/chat" \
     -H "Content-Type: application/json" \
     -d '{"message": "你好", "thread_id": "user_123"}'

# 健康检查
curl -X GET "http://localhost:8001/health"
```

## 错误处理和修复

### 常见错误1: "argument of type 'builtin_function_or_method' is not iterable"

**原因**: 访问`state.values`时，可能是方法而不是属性

**解决方案**: 
```python
# 修复前
if "messages" in state.values:  # 错误：state.values可能是方法

# 修复后
if hasattr(state, 'values') and state.values:
    state_values = state.values
elif hasattr(state, 'channel_values') and state.channel_values:
    state_values = state.channel_values
```

### 常见错误2: InMemorySaver没有delete方法

**解决方案**:
```python
try:
    if hasattr(checkpointer, 'delete'):
        checkpointer.delete(config)
    else:
        return {"message": "当前checkpointer不支持删除操作"}
except AttributeError:
    return {"message": "删除功能不可用"}
```

## 生产环境建议

### 1. 使用持久化 Checkpointer

```python
from langgraph.checkpoint.postgres import PostgresSaver

# 使用 PostgreSQL 作为持久化存储
checkpointer = PostgresSaver(connection_string="postgresql://...")
```

### 2. 添加认证和授权

```python
from fastapi import Depends, HTTPException
from fastapi.security import HTTPBearer

security = HTTPBearer()

@app.post("/chat")
async def chat(request: ChatRequest, token: str = Depends(security)):
    if not verify_token(token):
        raise HTTPException(status_code=401, detail="Invalid token")
```

### 3. 添加限流

```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@app.post("/chat")
@limiter.limit("10/minute")
async def chat(request: Request, chat_request: ChatRequest):
    # 限制每分钟最多 10 次请求
```

### 4. 监控和日志

- 添加 Prometheus 指标
- 集成 ELK 日志系统
- 添加分布式追踪

### 5. 部署配置

```yaml
# docker-compose.yml
version: '3.8'
services:
  agent-api:
    build: .
    ports:
      - "8001:8001"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: agent_sessions
      POSTGRES_USER: agent
      POSTGRES_PASSWORD: password
```

## 常见问题

### Q: Agent 初始化很慢怎么办？
A: 可以考虑：
1. 使用更轻量的模型
2. 预热 Agent（在服务启动时完成初始化）
3. 使用缓存机制

### Q: 如何处理大量并发请求？
A: 建议：
1. 使用异步处理
2. 添加请求队列
3. 水平扩展多个服务实例
4. 使用负载均衡

### Q: 会话数据会占用太多内存吗？
A: 可以：
1. 设置会话过期时间
2. 限制每个会话的消息数量
3. 使用外部存储（Redis、PostgreSQL）
4. 定期清理无用会话

### Q: 如何在HTTP服务中重复利用已创建的agent？
A: 核心要点：
1. **使用单例模式** - 确保Agent只创建一次
2. **全局共享** - 所有请求使用同一个Agent实例
3. **会话隔离** - 通过`thread_id`区分不同用户
4. **状态管理** - 使用checkpointer保存会话状态

## 许可证

MIT License 

# LangGraph Agent 重用和持久化演示

## 概述

这个项目演示了 LangGraph 中 Agent 的正确使用模式，特别是关于 **Agent 重用** 和 **持久化存储** 的最佳实践。

## 核心问题解答

### ❓ 用户每次对话都要重新创建 agent 吗？

**答案：不需要！** 正确的做法是：

1. **Agent 单例模式** - Agent 只需创建一次，可以重复使用
2. **会话管理** - 通过 `thread_id` 区分不同用户/会话
3. **持久化存储** - 使用 checkpointer 保存会话历史

## 持久化存储选项

### 1. InMemorySaver（开发测试）
- ✅ 简单易用，无需外部依赖
- ❌ 重启应用后数据丢失
- 🎯 适用于开发和测试环境

### 2. RedisSaver（推荐生产环境）
- ✅ 持久化存储，重启后数据不丢失
- ✅ 高性能，内存数据库
- ✅ 支持集群和分片
- ✅ 多实例共享数据
- 🎯 适用于生产环境

### 3. PostgresSaver（企业级）
- ✅ 关系型数据库，ACID 特性
- ✅ 复杂查询和分析
- ✅ 企业级可靠性
- 🎯 适用于企业级应用

## 文件说明

### 核心实现文件

| 文件 | 说明 | 存储方式 |
|------|------|----------|
| `mcp_test2.py` | 完整版 Agent 服务（MCP 工具） | RedisSaver |
| `simple_agent_demo.py` | 简化版 Agent 演示 | InMemoryStore + RedisSaver |
| `redis_agent_demo.py` | Redis Agent 完整演示 | RedisSaver |
| `simple_redis_demo.py` | Redis 基础功能演示 | 原生 Redis |

### API 服务文件

| 文件 | 说明 | 依赖 |
|------|------|------|
| `agent_api.py` | 完整版 FastAPI 服务 | MCP + LangGraph |
| `simple_agent_api.py` | 简化版 FastAPI 服务 | 仅 LangGraph |

### 测试文件

| 文件 | 说明 |
|------|------|
| `test_agent_api.py` | 完整 API 测试 |
| `simple_test.py` | 简化测试 |
| `test_redis_connection.py` | Redis 连接测试 |

### 配置和文档

| 文件 | 说明 |
|------|------|
| `docker-compose.yml` | Redis 服务配置 |
| `redis_setup.md` | Redis 安装配置指南 |
| `inmemory_store_demo.py` | InMemoryStore 演示 |

## 快速开始

### 1. 使用 InMemorySaver（简单开始）

```python
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import InMemorySaver

# 创建 checkpointer
checkpointer = InMemorySaver()

# 创建 agent（只需一次）
agent = create_react_agent(
    model=llm,
    tools=tools,
    checkpointer=checkpointer
)

# 多次使用，不同用户
config1 = {"configurable": {"thread_id": "user_1"}}
config2 = {"configurable": {"thread_id": "user_2"}}

response1 = agent.invoke({"messages": [{"role": "user", "content": "Hello"}]}, config1)
response2 = agent.invoke({"messages": [{"role": "user", "content": "Hi"}]}, config2)
```

### 2. 使用 RedisSaver（生产环境）

#### 安装依赖

```bash
# 安装 Redis Python 客户端
pip install redis

# 安装 LangGraph Redis 支持
pip install langgraph-checkpoint-redis
```

#### 启动 Redis 服务

```bash
# 方式1: 使用 Docker
docker run -d -p 6379:6379 redis:7-alpine

# 方式2: 使用 Docker Compose
docker-compose up -d

# 方式3: 本地安装（macOS）
brew install redis
brew services start redis
```

#### 代码实现

```python
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.redis import RedisSaver
from langgraph.store.memory import InMemoryStore

# 创建 Redis checkpointer
redis_url = "redis://127.0.0.1:6379/0"
with RedisSaver.from_conn_string(redis_url) as checkpointer:
    # 设置 Redis 索引（只需执行一次）
    checkpointer.setup()
    
    # 创建存储
    store = InMemoryStore()
    
    # 创建 agent（只需一次）
    agent = create_react_agent(
        model=llm,
        tools=tools,
        checkpointer=checkpointer,
        store=store
    )
    
    # 使用 agent
    config = {"configurable": {"thread_id": "user_123"}}
    response = agent.invoke(
        {"messages": [{"role": "user", "content": "Hello"}]}, 
        config
    )
```

### 3. Agent 服务单例模式

```python
class AgentService:
    _instance = None
    _agent = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._agent is None:
            self._initialize_agent()
    
    def _initialize_agent(self):
        # 创建 checkpointer
        with RedisSaver.from_conn_string("redis://127.0.0.1:6379/0") as checkpointer:
            checkpointer.setup()
            
            # 创建 agent（只执行一次）
            self._agent = create_react_agent(
                model=llm,
                tools=tools,
                checkpointer=checkpointer
            )
    
    def get_agent(self):
        return self._agent

# 全局实例
agent_service = AgentService()

def chat_with_agent(message: str, thread_id: str):
    agent = agent_service.get_agent()
    config = {"configurable": {"thread_id": thread_id}}
    return agent.invoke({"messages": [{"role": "user", "content": message}]}, config)
```

## 错误处理和最佳实践

### 常见错误

1. **`'_GeneratorContextManager' object has no attribute 'get_next_version'`**
   - 原因：错误使用 `RedisSaver.from_conn_string()`
   - 解决：使用上下文管理器 `with RedisSaver.from_conn_string(url) as checkpointer:`

2. **Redis 连接失败**
   - 检查 Redis 服务是否启动
   - 验证连接 URL 格式
   - 实现回退机制到 InMemorySaver

### 最佳实践

1. **错误处理和回退**
```python
def create_checkpointer():
    try:
        redis_url = os.getenv("REDIS_URL", "redis://127.0.0.1:6379/0")
        with RedisSaver.from_conn_string(redis_url) as checkpointer:
            checkpointer.setup()
            return checkpointer
    except Exception as e:
        print(f"Redis 不可用，使用 InMemorySaver: {e}")
        return InMemorySaver()
```

2. **环境配置**
```bash
# 设置环境变量
export REDIS_URL="redis://127.0.0.1:6379/0"

# 或在 .env 文件中
REDIS_URL=redis://127.0.0.1:6379/0
```

3. **资源清理**
```python
class AgentService:
    def cleanup(self):
        if hasattr(self._checkpointer, '__exit__'):
            self._checkpointer.__exit__(None, None, None)
```

## 性能对比

| 特性 | InMemorySaver | RedisSaver | PostgresSaver |
|------|---------------|------------|---------------|
| 启动速度 | ⚡ 极快 | ⚡ 快 | 🐌 中等 |
| 内存使用 | 📈 高 | 📊 中等 | 📉 低 |
| 持久化 | ❌ 无 | ✅ 是 | ✅ 是 |
| 扩展性 | ❌ 单机 | ✅ 集群 | ✅ 集群 |
| 查询能力 | 🔍 基础 | 🔍 中等 | 🔍 强大 |
| 运维复杂度 | 🟢 简单 | 🟡 中等 | 🔴 复杂 |

## 生产环境建议

### Redis 配置优化

1. **内存管理**
```conf
maxmemory 2gb
maxmemory-policy allkeys-lru
```

2. **持久化配置**
```conf
save 900 1
save 300 10
save 60 10000
```

3. **安全配置**
```conf
requirepass your_strong_password
bind 127.0.0.1
```

### 监控和日志

```bash
# 查看 Redis 状态
redis-cli info

# 监控命令
redis-cli monitor

# 查看内存使用
redis-cli info memory
```

### 集群部署

```python
# Redis Cluster 配置
redis_url = "redis://node1:6379,node2:6379,node3:6379/0?cluster=true"
```

## 故障排除

### Redis 连接问题

1. **检查服务状态**
```bash
redis-cli ping
# 应该返回 PONG
```

2. **检查端口占用**
```bash
lsof -i :6379
```

3. **查看日志**
```bash
docker logs redis-container
```

### 性能问题

1. **监控慢查询**
```bash
redis-cli slowlog get 10
```

2. **内存分析**
```bash
redis-cli --bigkeys
```

## 云服务选项

### Redis Cloud
- 托管 Redis 服务
- 自动备份和恢复
- 全球分布式部署

### AWS ElastiCache
```python
redis_url = "redis://your-cluster.cache.amazonaws.com:6379/0"
```

### Azure Cache for Redis
```python
redis_url = "redis://:<EMAIL>:6380/0?ssl=true"
```

## 总结

使用 `RedisSaver` 可以显著提升 LangGraph 应用的可靠性和性能：

- 🔄 **持久化** - 数据不会因重启而丢失
- ⚡ **高性能** - 内存数据库，访问速度快
- 🔧 **易于管理** - 丰富的管理工具和监控选项
- 📈 **可扩展** - 支持集群和分片
- 👥 **多用户** - 每个用户独立的对话记忆

建议在开发环境使用 `InMemorySaver`，在生产环境使用 `RedisSaver` 或 `PostgresSaver`。 