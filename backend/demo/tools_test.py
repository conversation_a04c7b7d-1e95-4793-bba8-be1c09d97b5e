
from langgraph.prebuilt import create_react_agent

from langgraph.checkpoint.memory import InMemorySaver
from llm_test import llm_deepseek_v3


def get_weather(city: str) -> str:  
    """Get weather for a given city."""
    return f"It's always sunny in {city}!"




agent = create_react_agent(
    model=llm_deepseek_v3,  
    tools=[get_weather],  
    prompt="You are a helpful assistant"  
)

# Run the agent 
events = agent.stream(
    {"messages": [{"role": "user", "content": "what is the weather in sf"}]},
    stream_mode="values",
)
for event in events:
    if "messages" in event:
        event["messages"][-1].pretty_print()



