import os
from typing import Dict, List, Any, TypedDict, Annotated, Optional, Literal, Union
from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
import asyncio
import json
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain.chat_models import init_chat_model
from langchain_core.tools import tool

from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, create_react_agent
from langgraph.checkpoint.memory import MemorySaver

# 配置LLM
api_key = "sk-zfhpiwxpncrzqlmtomnochjllwctdajoilqsbzetjsuhgmua"
base_url = "https://api.siliconflow.cn/v1"

# 初始化LLM
llm = init_chat_model("Qwen/Qwen2.5-72B-Instruct", model_provider="openai", api_key=api_key, base_url=base_url)

# 定义状态类型
class AgentState(TypedDict):
    messages: Annotated[List, add_messages]  # 使用add_messages注解确保消息可合并
    user_info: Dict[str, Any]  # 用户上下文信息
    next: Optional[str]  # 下一个要执行的节点

# 知识库工具
@tool
def search_knowledge_base(query: str) -> str:
    """搜索知识库，查询相关信息
    
    Args:
        query: 查询内容
    """
    # 这里实现知识库搜索逻辑
    return f"从知识库中找到的关于'{query}'的信息: 这是一个模拟的知识库搜索结果。"

# 用户信息获取工具
@tool
def get_user_context(user_id: str) -> Dict[str, Any]:
    """获取用户上下文信息
    
    Args:
        user_id: 用户ID
    """
    # 模拟从数据库获取用户信息
    return {
        "user_id": user_id,
        "name": "张三",
        "vip_level": "高级会员",
        "last_purchase": "智能手表",
        "preferred_contact": "电话"
    }

# 创建产品专家Agent
def create_product_expert():
    system_prompt = """你是一位产品专家客服。
你的职责是回答有关产品功能、规格、使用方法和故障排除的问题。
只回答产品相关的问题，如果问题超出你的专业范围，请说明你需要咨询其他专家。
回答要详细、准确、易于理解。"""

    return create_react_agent(
        llm,
        tools=[search_knowledge_base],
        system_prompt=system_prompt
    )

# 创建订单专家Agent
def create_order_expert():
    system_prompt = """你是一位订单专家客服。
你的职责是处理订单查询、修改、取消等相关问题，以及解决物流和配送问题。
只回答订单和物流相关的问题，如果问题超出你的专业范围，请说明你需要咨询其他专家。
回答要简洁、明确、有帮助。"""

    return create_react_agent(
        llm,
        tools=[search_knowledge_base],
        system_prompt=system_prompt
    )

# 创建会员专家Agent
def create_membership_expert():
    system_prompt = """你是一位会员服务专家客服。
你的职责是处理会员政策、积分、优惠券、账户问题等相关咨询。
只回答会员相关的问题，如果问题超出你的专业范围，请说明你需要咨询其他专家。
回答要热情、周到、体现会员价值。"""

    return create_react_agent(
        llm,
        tools=[search_knowledge_base, get_user_context],
        system_prompt=system_prompt
    )

# 创建通用客服Agent
def create_general_expert():
    system_prompt = """你是一位通用客服助手。
你的职责是处理一般性咨询和问题，提供友好的交流和基本指导。
如果遇到需要专业知识的问题，请表明你会转接到相关专家。
回答要亲切、简洁、有帮助。"""

    return create_react_agent(
        llm,
        tools=[search_knowledge_base],
        system_prompt=system_prompt
    )

# 创建Supervisor Agent
def create_supervisor():
    system_prompt = """你是客服团队的主管。
你的职责是分析用户的问题，决定由哪位专家来处理：
1. 产品专家 - 处理产品功能、规格、使用方法和故障排除问题
2. 订单专家 - 处理订单查询、修改、取消及物流配送问题
3. 会员专家 - 处理会员政策、积分、优惠券、账户问题
4. 通用客服 - 处理一般性咨询和简单问题

请先思考用户问题的类型，然后决定应该由哪个专家处理。
如果是简单问题或问候语，可以直接由通用客服处理。
"""

    return create_react_agent(
        llm,
        tools=[],
        system_prompt=system_prompt
    )

# 产品专家节点
def product_expert_node(state: AgentState):
    """产品专家节点"""
    # 获取消息和用户信息
    messages = state["messages"]
    user_info = state.get("user_info", {})
    
    # 创建产品专家Agent
    agent = create_product_expert()
    
    # 执行Agent
    response = agent.invoke({"messages": messages})
    
    # 返回结果
    return {"messages": response["messages"], "next": None}

# 订单专家节点
def order_expert_node(state: AgentState):
    """订单专家节点"""
    # 获取消息和用户信息
    messages = state["messages"]
    user_info = state.get("user_info", {})
    
    # 创建订单专家Agent
    agent = create_order_expert()
    
    # 执行Agent
    response = agent.invoke({"messages": messages})
    
    # 返回结果
    return {"messages": response["messages"], "next": None}

# 会员专家节点
def membership_expert_node(state: AgentState):
    """会员专家节点"""
    # 获取消息和用户信息
    messages = state["messages"]
    user_info = state.get("user_info", {})
    
    # 创建会员专家Agent
    agent = create_membership_expert()
    
    # 执行Agent
    response = agent.invoke({"messages": messages})
    
    # 返回结果
    return {"messages": response["messages"], "next": None}

# 通用客服节点
def general_expert_node(state: AgentState):
    """通用客服节点"""
    # 获取消息和用户信息
    messages = state["messages"]
    user_info = state.get("user_info", {})
    
    # 创建通用客服Agent
    agent = create_general_expert()
    
    # 执行Agent
    response = agent.invoke({"messages": messages})
    
    # 返回结果
    return {"messages": response["messages"], "next": None}

# Supervisor节点
def supervisor_node(state: AgentState):
    """Supervisor节点，决定由哪个专家处理用户问题"""
    # 获取消息和用户信息
    messages = state["messages"]
    user_info = state.get("user_info", {})
    
    # 只考虑最后一条用户消息
    latest_messages = []
    for i, msg in enumerate(messages):
        if i > 0 and isinstance(messages[i-1], AIMessage) and isinstance(msg, HumanMessage):
            # 这是新一轮的对话
            latest_messages = []
        latest_messages.append(msg)
    
    # 为Supervisor添加思考提示
    prompt = f"""
# 用户问题分析
分析以下用户消息，确定应该由哪位专家处理：

用户消息: {latest_messages[-1].content if isinstance(latest_messages[-1], HumanMessage) else "无用户消息"}

请从以下选择一个最合适的专家:
- product_expert: 产品功能、规格、使用方法和故障排除
- order_expert: 订单查询、修改、取消及物流配送
- membership_expert: 会员政策、积分、优惠券、账户问题
- general_expert: 一般性咨询和简单问题

输出格式:
```json
{{"expert": "专家名称"}}
```
仅输出JSON格式的结果，不要有任何其他文字。
"""
    
    # 创建Supervisor Agent
    agent = create_supervisor()
    
    # 构造系统消息
    system_message = SystemMessage(content=prompt)
    agent_messages = [system_message] + latest_messages
    
    # 执行Agent
    response = agent.invoke({"messages": agent_messages})
    
    # 获取最后一条AI消息
    last_message = response["messages"][-1]
    
    # 解析决策结果
    try:
        content = last_message.content
        # 从内容中提取JSON部分
        if "```json" in content and "```" in content:
            json_str = content.split("```json")[1].split("```")[0].strip()
        else:
            json_str = content.strip()
            
        decision = json.loads(json_str)
        expert = decision.get("expert", "general_expert")
    except:
        # 如果解析失败，默认使用通用客服
        expert = "general_expert"
    
    # 返回下一个要执行的节点
    return {"next": expert}

# 路由函数
def router(state: AgentState):
    """路由到下一个节点"""
    return state.get("next", "supervisor")

# 构建智能客服图
def build_supervisor_graph():
    """构建基于Supervisor的多Agent智能客服系统图"""
    # 创建图
    workflow = StateGraph(AgentState)
    
    # 添加节点
    workflow.add_node("supervisor", supervisor_node)
    workflow.add_node("product_expert", product_expert_node)
    workflow.add_node("order_expert", order_expert_node)
    workflow.add_node("membership_expert", membership_expert_node)
    workflow.add_node("general_expert", general_expert_node)
    
    # 设置入口
    workflow.set_entry_point(START)
    workflow.add_edge(START, "supervisor")
    
    # 设置路由
    workflow.add_conditional_edges(
        "supervisor",
        router,
        {
            "product_expert": "product_expert",
            "order_expert": "order_expert",
            "membership_expert": "membership_expert",
            "general_expert": "general_expert"
        }
    )
    
    # 设置出口
    workflow.add_edge("product_expert", END)
    workflow.add_edge("order_expert", END)
    workflow.add_edge("membership_expert", END)
    workflow.add_edge("general_expert", END)
    
    # 编译图
    return workflow.compile()

# 为每个会话创建独立的内存存储
session_memory = {}

# 创建或获取会话内存
def get_session_memory(session_id: str):
    if session_id not in session_memory:
        session_memory[session_id] = MemorySaver()
    return session_memory[session_id]

# API模型
class ChatRequest(BaseModel):
    session_id: str = Field(..., description="会话ID")
    message: str = Field(..., description="用户消息")
    user_id: Optional[str] = Field(None, description="用户ID，用于获取用户信息")

class ChatResponse(BaseModel):
    session_id: str
    response: str
    expert_type: str
    
# 创建FastAPI应用
app = FastAPI(title="多Agent智能客服系统")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 构建智能客服图
supervisor_graph = build_supervisor_graph()

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    # 获取会话记忆
    memory = get_session_memory(request.session_id)
    
    # 准备输入状态
    messages = [HumanMessage(content=request.message)]
    
    # 获取用户信息(如果提供了用户ID)
    user_info = {}
    if request.user_id:
        user_info = get_user_context(request.user_id)
    
    # 获取之前的消息历史
    config = {"configurable": {"thread_id": request.session_id}}
    try:
        state = memory.get(config=config)
        if state:
            # 添加新消息到历史消息中
            messages = state["messages"] + messages
    except:
        # 如果没有历史记录，使用当前消息
        pass
    
    input_state = {
        "messages": messages,
        "user_info": user_info,
        "next": None
    }
    
    # 使用图执行
    result = supervisor_graph.invoke(
        input_state,
        config=config,
        checkpointer=memory
    )
    
    # 获取最后一条AI消息作为响应
    ai_messages = [msg for msg in result["messages"] if isinstance(msg, AIMessage)]
    response = ai_messages[-1].content if ai_messages else "抱歉，无法处理您的请求。"
    
    # 获取处理此消息的专家类型
    # 检查消息来源，尝试从metadata获取expert_type
    expert_type = "unknown"
    if ai_messages and hasattr(ai_messages[-1], "metadata"):
        expert_type = ai_messages[-1].metadata.get("expert_type", "unknown")
    else:
        # 尝试通过上下文推断专家类型
        if result.get("next"):
            expert_type = result.get("next")
    
    return ChatResponse(
        session_id=request.session_id,
        response=response,
        expert_type=expert_type
    )

if __name__ == "__main__":
    # 启动服务器
    uvicorn.run("supervisor_cs_demo:app", host="0.0.0.0", port=8001, reload=True) 