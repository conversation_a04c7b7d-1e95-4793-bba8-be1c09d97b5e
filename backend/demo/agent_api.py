from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Optional
import uvicorn
from mcp_test2 import chat_with_agent, agent_service
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="LangGraph Agent API",
    description="基于 LangGraph 的智能对话 Agent HTTP 服务",
    version="1.0.0"
)

# 请求模型
class ChatRequest(BaseModel):
    message: str
    thread_id: Optional[str] = "default"
    
class ChatResponse(BaseModel):
    success: bool
    response: Optional[str] = None
    error: Optional[str] = None
    thread_id: str

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "agent_initialized": agent_service.get_agent() is not None
    }

# 主要对话端点
@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    与 Agent 进行对话
    
    Args:
        request: 包含消息和会话ID的请求
        
    Returns:
        Agent 的响应
    """
    try:
        logger.info(f"收到对话请求 - Thread ID: {request.thread_id}, Message: {request.message}")
        
        # 调用 agent 进行对话
        result = chat_with_agent(request.message, request.thread_id)
        
        logger.info(f"对话完成 - Thread ID: {request.thread_id}, Success: {result['success']}")
        
        return ChatResponse(**result)
        
    except Exception as e:
        logger.error(f"对话处理出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# 获取会话历史（可选功能）
@app.get("/sessions/{thread_id}/history")
async def get_session_history(thread_id: str):
    """
    获取指定会话的历史记录
    
    Args:
        thread_id: 会话ID
        
    Returns:
        会话历史记录
    """
    try:
        checkpointer = agent_service.get_checkpointer()
        config = {"configurable": {"thread_id": thread_id}}
        
        # 获取会话状态
        state = checkpointer.get(config)
        
        if state is None:
            return {"thread_id": thread_id, "messages": [], "message": "会话不存在"}
        
        # 提取消息历史
        messages = []
        
        # 处理不同的状态结构
        try:
            # 尝试获取状态值
            state_values = state['channel_values']
            print(state_values)
            # print(state['channel_values']['messages'])
            # 查找消息
            if "messages" in state_values:
                for msg in state_values["messages"]:
                    messages.append(msg)
                    # 确定消息类型
                    # msg_type = "user" if "Human" in msg.__class__.__name__ else "assistant"
                    
            
        except Exception as parse_error:
            logger.warning(f"解析会话状态时出错: {str(parse_error)}")
            return {
                "thread_id": thread_id,
                "messages": [],
                "message": f"会话存在但解析失败: {str(parse_error)}",
                "state_type": str(type(state))
            }
        
        return {
            "thread_id": thread_id,
            "messages": messages,
            "total_messages": len(messages)
        }
        
    except Exception as e:
        logger.error(f"获取会话历史出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话历史失败: {str(e)}")

# 清除会话历史
@app.delete("/sessions/{thread_id}")
async def clear_session(thread_id: str):
    """
    清除指定会话的历史记录
    
    Args:
        thread_id: 会话ID
        
    Returns:
        操作结果
    """
    try:
        checkpointer = agent_service.get_checkpointer()
        config = {"configurable": {"thread_id": thread_id}}
        
        # 尝试清除会话状态
        try:
            if hasattr(checkpointer, 'delete'):
                checkpointer.delete(config)
                logger.info(f"会话已清除 - Thread ID: {thread_id}")
                return {
                    "success": True,
                    "message": f"会话 {thread_id} 已成功清除",
                    "thread_id": thread_id
                }
            else:
                # InMemorySaver 可能没有 delete 方法
                logger.warning(f"Checkpointer 不支持删除操作 - Thread ID: {thread_id}")
                return {
                    "success": False,
                    "message": f"当前 checkpointer ({type(checkpointer).__name__}) 不支持删除操作",
                    "note": "使用 InMemorySaver 时，需要重启服务才能清除所有会话",
                    "thread_id": thread_id
                }
        except AttributeError as attr_error:
            logger.warning(f"Checkpointer 删除方法不可用: {str(attr_error)}")
            return {
                "success": False,
                "message": f"删除功能不可用: {str(attr_error)}",
                "thread_id": thread_id
            }
        except Exception as delete_error:
            logger.error(f"删除会话时出错: {str(delete_error)}")
            return {
                "success": False,
                "message": f"删除会话失败: {str(delete_error)}",
                "thread_id": thread_id
            }
        
    except Exception as e:
        logger.error(f"清除会话出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清除会话失败: {str(e)}")

# 获取所有活跃会话
@app.get("/sessions")
async def list_sessions():
    """
    获取所有活跃会话列表
    
    Returns:
        活跃会话列表
    """
    try:
        checkpointer = agent_service.get_checkpointer()
        
        # 这里需要根据具体的 checkpointer 实现来获取所有会话
        # InMemorySaver 可能没有直接的方法获取所有会话，这是一个简化的实现
        return {
            "message": "会话列表功能需要根据具体的 checkpointer 实现",
            "note": "当前使用 InMemorySaver，建议在生产环境中使用持久化的 checkpointer"
        }
        
    except Exception as e:
        logger.error(f"获取会话列表出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info("Agent API 服务启动中...")
    logger.info("Agent 初始化完成，服务就绪")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("Agent API 服务正在关闭...")

if __name__ == "__main__":
    # 启动服务
    uvicorn.run(
        "agent_api:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    ) 