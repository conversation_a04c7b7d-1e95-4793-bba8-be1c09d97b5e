import os
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import asyncio
import uuid
import json
from datetime import datetime
import numpy as np

from langchain_openai import OpenAIEmbeddings
from langchain_community.embeddings import FastEmbedEmbeddings
from langchain_community.vectorstores import FAISS, Chroma, Milvus
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import (
    TextLoader, 
    DirectoryLoader, 
    PyPDFLoader,
    CSVLoader,
    JSONLoader
)

# 配置API密钥
api_key = "sk-zfhpiwxpncrzqlmtomnochjllwctdajoilqsbzetjsuhgmua"
base_url = "https://api.siliconflow.cn/v1"

# 数据模型
class Document(BaseModel):
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)

class SearchQuery(BaseModel):
    query: str
    kb_id: str
    top_k: int = 5
    filter_metadata: Optional[Dict[str, Any]] = None

class CreateKBRequest(BaseModel):
    kb_name: str
    description: Optional[str] = None
    embedding_model: str = "fastembedembeddings"  # 默认使用本地嵌入模型
    
class UploadDocsRequest(BaseModel):
    kb_id: str
    documents: List[Document]

class KBResponse(BaseModel):
    kb_id: str
    kb_name: str
    status: str
    doc_count: int
    created_at: str
    
class SearchResponse(BaseModel):
    results: List[Document]
    kb_id: str
    query: str

# 知识库管理类
class KnowledgeBaseManager:
    def __init__(self, storage_path: str = "kb_storage"):
        self.storage_path = storage_path
        self.kb_index_path = os.path.join(storage_path, "kb_index.json")
        self.kb_index = self._load_index()
        
        # 创建存储目录
        os.makedirs(storage_path, exist_ok=True)
        
    def _load_index(self) -> Dict:
        """加载知识库索引"""
        if os.path.exists(self.kb_index_path):
            with open(self.kb_index_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def _save_index(self):
        """保存知识库索引"""
        with open(self.kb_index_path, 'w', encoding='utf-8') as f:
            json.dump(self.kb_index, f, ensure_ascii=False, indent=2)
    
    def get_embeddings(self, model_name: str):
        """获取嵌入模型"""
        if model_name == "openai":
            return OpenAIEmbeddings(openai_api_key=api_key, base_url=base_url)
        else:  # 默认使用本地模型
            return FastEmbedEmbeddings()
    
    def create_kb(self, kb_name: str, description: str = None, embedding_model: str = "fastembedembeddings") -> str:
        """创建新的知识库"""
        kb_id = f"kb_{uuid.uuid4().hex[:8]}"
        kb_dir = os.path.join(self.storage_path, kb_id)
        
        # 创建知识库目录
        os.makedirs(kb_dir, exist_ok=True)
        
        # 记录知识库信息
        self.kb_index[kb_id] = {
            "kb_id": kb_id,
            "kb_name": kb_name,
            "description": description,
            "created_at": datetime.now().isoformat(),
            "doc_count": 0,
            "embedding_model": embedding_model,
            "status": "ready"
        }
        
        # 保存索引
        self._save_index()
        
        return kb_id
    
    def get_kb(self, kb_id: str) -> Dict:
        """获取知识库信息"""
        if kb_id not in self.kb_index:
            raise ValueError(f"知识库 {kb_id} 不存在")
        return self.kb_index[kb_id]
    
    def list_kbs(self) -> List[Dict]:
        """列出所有知识库"""
        return list(self.kb_index.values())
    
    def delete_kb(self, kb_id: str) -> bool:
        """删除知识库"""
        if kb_id not in self.kb_index:
            raise ValueError(f"知识库 {kb_id} 不存在")
        
        kb_dir = os.path.join(self.storage_path, kb_id)
        
        # 删除知识库目录
        import shutil
        if os.path.exists(kb_dir):
            shutil.rmtree(kb_dir)
        
        # 从索引中删除
        del self.kb_index[kb_id]
        
        # 保存索引
        self._save_index()
        
        return True
    
    def add_documents(self, kb_id: str, documents: List[Document]) -> int:
        """添加文档到知识库"""
        if kb_id not in self.kb_index:
            raise ValueError(f"知识库 {kb_id} 不存在")
        
        kb_info = self.kb_index[kb_id]
        kb_dir = os.path.join(self.storage_path, kb_id)
        
        # 获取嵌入模型
        embeddings = self.get_embeddings(kb_info["embedding_model"])
        
        # 转换文档格式
        langchain_docs = []
        for doc in documents:
            langchain_docs.append({
                "page_content": doc.content,
                "metadata": doc.metadata
            })
        
        # 检查向量库是否已存在
        vector_store_path = os.path.join(kb_dir, "vector_store")
        if os.path.exists(vector_store_path):
            # 加载现有向量库
            vector_store = FAISS.load_local(vector_store_path, embeddings)
            
            # 添加新文档
            vector_store.add_texts(
                texts=[doc.content for doc in documents],
                metadatas=[doc.metadata for doc in documents]
            )
        else:
            # 创建新向量库
            vector_store = FAISS.from_texts(
                texts=[doc.content for doc in documents],
                embedding=embeddings,
                metadatas=[doc.metadata for doc in documents]
            )
        
        # 保存向量库
        vector_store.save_local(vector_store_path)
        
        # 更新文档计数
        kb_info["doc_count"] += len(documents)
        self._save_index()
        
        return len(documents)
    
    def search(self, kb_id: str, query: str, top_k: int = 5, filter_metadata: Optional[Dict] = None) -> List[Document]:
        """搜索知识库"""
        if kb_id not in self.kb_index:
            raise ValueError(f"知识库 {kb_id} 不存在")
        
        kb_info = self.kb_index[kb_id]
        kb_dir = os.path.join(self.storage_path, kb_id)
        vector_store_path = os.path.join(kb_dir, "vector_store")
        
        if not os.path.exists(vector_store_path):
            raise ValueError(f"知识库 {kb_id} 的向量存储不存在")
        
        # 获取嵌入模型
        embeddings = self.get_embeddings(kb_info["embedding_model"])
        
        # 加载向量库
        vector_store = FAISS.load_local(vector_store_path, embeddings)
        
        # 执行搜索
        search_kwargs = {}
        if filter_metadata:
            search_kwargs["filter"] = filter_metadata
            
        search_results = vector_store.similarity_search(
            query=query,
            k=top_k,
            **search_kwargs
        )
        
        # 转换结果格式
        results = []
        for doc in search_results:
            results.append(Document(
                content=doc.page_content,
                metadata=doc.metadata
            ))
            
        return results
    
    def process_file(self, file_path: str, kb_id: str) -> int:
        """处理文件并添加到知识库"""
        if kb_id not in self.kb_index:
            raise ValueError(f"知识库 {kb_id} 不存在")
        
        # 根据文件类型选择加载器
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == ".txt":
            loader = TextLoader(file_path, encoding="utf-8")
        elif file_ext == ".pdf":
            loader = PyPDFLoader(file_path)
        elif file_ext == ".csv":
            loader = CSVLoader(file_path)
        elif file_ext == ".json":
            loader = JSONLoader(file_path=file_path, jq_schema=".")
        else:
            raise ValueError(f"不支持的文件类型: {file_ext}")
        
        # 加载文档
        raw_documents = loader.load()
        
        # 文本分割
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200
        )
        documents = text_splitter.split_documents(raw_documents)
        
        # 转换为API文档格式
        api_docs = []
        for doc in documents:
            api_docs.append(Document(
                content=doc.page_content,
                metadata=doc.metadata
            ))
        
        # 添加到知识库
        return self.add_documents(kb_id, api_docs)

# 初始化知识库管理器
kb_manager = KnowledgeBaseManager()

# 创建FastAPI应用
app = FastAPI(title="知识库检索服务")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API 端点
@app.post("/kb/create", response_model=KBResponse)
async def create_knowledge_base(request: CreateKBRequest):
    """创建新知识库"""
    try:
        kb_id = kb_manager.create_kb(
            kb_name=request.kb_name,
            description=request.description,
            embedding_model=request.embedding_model
        )
        kb_info = kb_manager.get_kb(kb_id)
        return KBResponse(
            kb_id=kb_info["kb_id"],
            kb_name=kb_info["kb_name"],
            status=kb_info["status"],
            doc_count=kb_info["doc_count"],
            created_at=kb_info["created_at"]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/kb/list", response_model=List[KBResponse])
async def list_knowledge_bases():
    """列出所有知识库"""
    try:
        kb_list = kb_manager.list_kbs()
        return [
            KBResponse(
                kb_id=kb["kb_id"],
                kb_name=kb["kb_name"],
                status=kb["status"],
                doc_count=kb["doc_count"],
                created_at=kb["created_at"]
            )
            for kb in kb_list
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/kb/{kb_id}", response_model=KBResponse)
async def get_knowledge_base(kb_id: str):
    """获取知识库信息"""
    try:
        kb_info = kb_manager.get_kb(kb_id)
        return KBResponse(
            kb_id=kb_info["kb_id"],
            kb_name=kb_info["kb_name"],
            status=kb_info["status"],
            doc_count=kb_info["doc_count"],
            created_at=kb_info["created_at"]
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/kb/{kb_id}")
async def delete_knowledge_base(kb_id: str):
    """删除知识库"""
    try:
        result = kb_manager.delete_kb(kb_id)
        return {"success": result, "kb_id": kb_id}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/kb/upload")
async def upload_documents(request: UploadDocsRequest):
    """上传文档到知识库"""
    try:
        doc_count = kb_manager.add_documents(request.kb_id, request.documents)
        return {"success": True, "kb_id": request.kb_id, "doc_count": doc_count}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/kb/search", response_model=SearchResponse)
async def search_knowledge_base(query: SearchQuery):
    """搜索知识库"""
    try:
        results = kb_manager.search(
            kb_id=query.kb_id,
            query=query.query,
            top_k=query.top_k,
            filter_metadata=query.filter_metadata
        )
        return SearchResponse(
            results=results,
            kb_id=query.kb_id,
            query=query.query
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 启动服务器
if __name__ == "__main__":
    uvicorn.run("kb_rag_service:app", host="0.0.0.0", port=8002, reload=True) 