# 多租户 Agent 服务架构设计

## 概述

这是一个支持多租户、每个租户下多个Agent的智能对话服务架构。该架构解决了在SaaS环境中需要为不同客户（租户）提供隔离的AI Agent服务的需求。

## 🏗️ 架构特点

### 1. 多租户隔离
- **数据隔离**: 每个租户的对话历史存储在独立的MongoDB数据库中
- **资源隔离**: 每个租户有独立的Agent实例和配置
- **权限隔离**: 租户只能访问自己的Agent，无法跨租户访问

### 2. 多Agent支持
- **类型多样**: 支持通用、数学、天气、代码、数据分析等多种Agent类型
- **自定义配置**: 每个Agent可以有自定义的名称、描述和提示词
- **工具集成**: 不同类型的Agent集成不同的工具集

### 3. 灵活配置
- **租户级配置**: 可限制租户的最大Agent数量和允许的Agent类型
- **Agent级配置**: 支持自定义LLM模型、工具集和提示词
- **会话管理**: 支持多会话并发，会话历史持久化

## 📁 文件结构

```
backend/demo/
├── multi_tenant_agent_service.py    # 核心多租户Agent服务
├── multi_tenant_agent_api.py        # FastAPI HTTP接口
├── multi_tenant_example.py          # 使用示例和测试
├── mcp_test2.py                     # 原始单租户实现（对比参考）
├── agent_api.py                     # 原始单租户API（对比参考）
└── README_MultiTenant.md            # 本文档
```

## 🚀 快速开始

### 1. 启动服务

```bash
# 启动多租户Agent API服务
python multi_tenant_agent_api.py
```

服务将在 `http://localhost:8002` 启动

### 2. 运行演示

```bash
# 运行完整的演示和测试
python multi_tenant_example.py
```

### 3. API文档

访问 `http://localhost:8002/docs` 查看自动生成的API文档

## 🏢 核心概念

### 租户 (Tenant)
```python
@dataclass
class TenantConfig:
    tenant_id: str                    # 租户唯一标识
    name: str                         # 租户名称
    max_agents: int = 10              # 最大Agent数量限制
    max_sessions_per_agent: int = 100 # 每个Agent最大会话数
    mongodb_database: Optional[str]   # 专用MongoDB数据库
    allowed_agent_types: List[AgentType]  # 允许的Agent类型
    created_at: datetime              # 创建时间
```

### Agent配置
```python
@dataclass
class AgentConfig:
    agent_type: AgentType             # Agent类型
    name: str                         # Agent名称
    description: str                  # Agent描述
    llm_model: Any                    # LLM模型实例
    tools: List[Any]                  # 工具集合
    max_sessions: int = 100           # 最大会话数限制
    session_timeout: int = 3600       # 会话超时时间
    custom_prompt: Optional[str]      # 自定义提示词
```

### Agent类型
```python
class AgentType(Enum):
    GENERAL = "general"     # 通用对话agent
    MATH = "math"          # 数学计算agent
    WEATHER = "weather"    # 天气查询agent
    CODE = "code"          # 代码助手agent
    ANALYSIS = "analysis"  # 数据分析agent
```

## 🔧 API接口

### 租户管理

#### 创建租户
```http
POST /tenants
Content-Type: application/json

{
  "tenant_id": "company_a",
  "name": "公司A",
  "max_agents": 10,
  "allowed_agent_types": ["general", "math", "code"]
}
```

#### 列出租户
```http
GET /tenants
```

#### 删除租户
```http
DELETE /tenants/{tenant_id}
```

### Agent管理

#### 创建Agent
```http
POST /tenants/{tenant_id}/agents
Content-Type: application/json

{
  "agent_type": "general",
  "agent_id": "assistant_1",
  "name": "通用助手",
  "description": "专业的AI助手",
  "custom_prompt": "你是一个友好的AI助手..."
}
```

#### 列出Agent
```http
GET /tenants/{tenant_id}/agents
```

#### 删除Agent
```http
DELETE /tenants/{tenant_id}/agents/{agent_id}
```

### 对话功能

#### 与Agent对话
```http
POST /tenants/{tenant_id}/agents/{agent_id}/chat
Content-Type: application/json

{
  "message": "你好，请介绍一下你的功能",
  "thread_id": "user_session_1"
}
```

#### 便捷对话接口（使用Header）
```http
POST /chat
X-Tenant-Id: company_a
X-Agent-Id: assistant_1
Content-Type: application/json

{
  "message": "Hello",
  "thread_id": "session_1"
}
```

### 会话管理

#### 获取会话历史
```http
GET /tenants/{tenant_id}/agents/{agent_id}/sessions/{thread_id}/history
```

#### 清除会话
```http
DELETE /tenants/{tenant_id}/agents/{agent_id}/sessions/{thread_id}
```

### 系统信息

#### 获取系统信息
```http
GET /system/info
```

#### 获取Agent类型
```http
GET /system/agent-types
```

## 💾 数据存储

### MongoDB结构
```
MongoDB实例
├── langgraph_company_a/          # 租户A的数据库
│   ├── checkpoints               # Agent检查点集合
│   └── writes                    # 写入记录集合
├── langgraph_company_b/          # 租户B的数据库
│   ├── checkpoints
│   └── writes
└── langgraph_checkpoints/        # 默认数据库（如果未指定）
    ├── checkpoints
    └── writes
```

### 会话ID格式
```
{tenant_id}_{agent_id}_{thread_id}
例如: company_a_assistant_1_user_session_1
```

## 🔒 安全和隔离

### 1. 数据隔离
- 每个租户使用独立的MongoDB数据库
- 会话ID包含租户和Agent信息，确保数据不会混淆
- Agent实例完全隔离，不共享状态

### 2. 权限控制
- API层验证租户和Agent的存在性
- 阻止跨租户访问
- 支持租户级别的Agent类型限制

### 3. 资源限制
- 租户级别的Agent数量限制
- Agent级别的会话数量限制
- 会话超时机制

## 🎯 使用场景

### 1. SaaS平台
```python
# 为不同客户创建隔离的AI服务
tenant_config = TenantConfig(
    tenant_id="customer_123",
    name="客户公司",
    max_agents=5,
    allowed_agent_types=[AgentType.GENERAL, AgentType.CODE]
)
```

### 2. 企业内部部门
```python
# 为不同部门创建专门的Agent
hr_agent = create_agent("hr_dept", AgentType.GENERAL, "hr_assistant")
dev_agent = create_agent("dev_dept", AgentType.CODE, "code_reviewer")
```

### 3. 多产品线
```python
# 为不同产品线提供专门的AI支持
product_a_agent = create_agent("product_a", AgentType.ANALYSIS, "data_analyst")
product_b_agent = create_agent("product_b", AgentType.GENERAL, "customer_service")
```

## 📊 性能优化

### 1. 连接池管理
- MongoDB连接复用
- Agent实例缓存
- 工具客户端复用

### 2. 异步处理
- FastAPI异步端点
- 并发会话支持
- 非阻塞I/O操作

### 3. 资源清理
- 自动清理过期会话
- MongoDB连接管理
- 内存使用优化

## 🔧 扩展和定制

### 1. 添加新的Agent类型
```python
class AgentType(Enum):
    # 现有类型...
    TRANSLATION = "translation"  # 新增翻译Agent
    
# 在_initialize_agent_templates中添加配置
AgentType.TRANSLATION: AgentConfig(
    agent_type=AgentType.TRANSLATION,
    name="翻译助手",
    description="多语言翻译服务",
    llm_model=llm_translation_model,
    tools=translation_tools,
    custom_prompt="你是一个专业的翻译助手..."
)
```

### 2. 自定义工具集成
```python
def _get_custom_tools(self) -> List[Any]:
    """获取自定义工具"""
    client = CustomToolClient({
        "custom_service": {
            "url": "http://custom-service:8000",
            "transport": "http",
        }
    })
    return client.get_tools()
```

### 3. 高级配置
```python
# 支持更复杂的租户配置
@dataclass
class AdvancedTenantConfig(TenantConfig):
    rate_limit: int = 100           # 请求频率限制
    storage_quota: int = 1024       # 存储配额(MB)
    custom_models: Dict[str, Any]   # 自定义模型配置
    webhook_url: Optional[str]      # 事件回调URL
```

## 🚨 监控和日志

### 1. 日志记录
- 租户和Agent操作日志
- 对话请求和响应日志
- 错误和异常日志
- 性能指标日志

### 2. 监控指标
- 租户数量和Agent数量
- 对话请求频率和响应时间
- 资源使用情况
- 错误率统计

### 3. 告警机制
- 资源使用超限告警
- 错误率异常告警
- 性能下降告警

## 🔄 与原有架构对比

| 特性 | 原有单租户架构 | 新多租户架构 |
|------|---------------|-------------|
| 租户支持 | 单一租户 | 多租户隔离 |
| Agent数量 | 单个Agent | 每租户多个Agent |
| 数据隔离 | 无隔离需求 | 完全隔离 |
| 配置灵活性 | 固定配置 | 租户级+Agent级配置 |
| 扩展性 | 有限 | 高度可扩展 |
| 资源管理 | 简单 | 精细化管理 |
| API复杂度 | 简单 | 结构化分层 |

## 📝 最佳实践

### 1. 租户设计
- 使用有意义的租户ID（如公司域名）
- 合理设置资源限制
- 定期清理无用租户

### 2. Agent配置
- 为不同用途创建专门的Agent
- 使用描述性的Agent ID和名称
- 定制化提示词以提高效果

### 3. 会话管理
- 使用有意义的thread_id
- 定期清理历史会话
- 监控会话数量和存储使用

### 4. 运维管理
- 定期备份MongoDB数据
- 监控系统资源使用
- 建立日志分析和告警机制

## 🔮 未来扩展

1. **微服务架构**: 将租户管理、Agent管理、对话服务拆分为独立微服务
2. **分布式部署**: 支持多节点部署和负载均衡
3. **高级权限**: 基于角色的访问控制(RBAC)
4. **实时通信**: WebSocket支持实时对话
5. **AI编排**: 支持多Agent协作和工作流
6. **数据分析**: 租户使用情况分析和优化建议

这个多租户架构为构建企业级AI Agent服务提供了坚实的基础，具有良好的扩展性和可维护性。 