from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.store.memory import InMemoryStore
from langgraph.checkpoint.mongodb import MongoDBSaver
from llm_test import llm_deepseek_v3, llm_qwen3_32b, llm_qwen2_5_72b_instruct

from typing import Dict, Optional, List, Any
from dataclasses import dataclass, field
from enum import Enum
import os
import asyncio
import logging
from datetime import datetime, timedelta
from collections import defaultdict

logger = logging.getLogger(__name__)

class AgentType(Enum):
    """Agent 类型枚举"""
    GENERAL = "general"  # 通用对话agent
    MATH = "math"        # 数学计算agent
    WEATHER = "weather"  # 天气查询agent
    CODE = "code"        # 代码助手agent
    ANALYSIS = "analysis" # 数据分析agent

@dataclass
class AgentConfig:
    """Agent 配置类"""
    agent_type: AgentType
    name: str
    description: str
    llm_model: Any  # LLM 模型实例
    tools: List[Any] = field(default_factory=list)
    max_sessions: int = 100  # 最大会话数限制
    session_timeout: int = 3600  # 会话超时时间（秒）
    custom_prompt: Optional[str] = None
    
@dataclass
class TenantConfig:
    """租户配置类"""
    tenant_id: str
    name: str
    max_agents: int = 10  # 最大agent数量
    max_sessions_per_agent: int = 100
    mongodb_database: Optional[str] = None  # 租户专用数据库
    allowed_agent_types: List[AgentType] = field(default_factory=lambda: list(AgentType))
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class SessionInfo:
    """会话信息类"""
    session_id: str                    # 会话ID格式: {tenant_id}_{agent_id}_{thread_id}
    tenant_id: str                     # 租户ID
    agent_id: str                      # Agent ID
    thread_id: str                     # 线程ID
    user_id: Optional[str] = None      # 用户ID
    user_name: Optional[str] = None    # 用户名称
    created_at: datetime = field(default_factory=datetime.now)
    last_active: datetime = field(default_factory=datetime.now)
    message_count: int = 0             # 消息数量
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据

@dataclass 
class UserInfo:
    """用户信息类"""
    user_id: str                       # 用户ID
    user_name: str                     # 用户名称
    tenant_id: str                     # 所属租户ID
    email: Optional[str] = None        # 邮箱
    role: str = "user"                 # 角色（user, admin等）
    created_at: datetime = field(default_factory=datetime.now)
    last_login: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

class MultiTenantAgentService:
    """多租户 Agent 服务管理器"""
    
    def __init__(self):
        self._tenants: Dict[str, TenantConfig] = {}
        self._agents: Dict[str, Dict[str, Any]] = {}  # {tenant_id: {agent_id: agent_instance}}
        self._checkpointers: Dict[str, Dict[str, Any]] = {}  # {tenant_id: {agent_id: checkpointer}}
        self._stores: Dict[str, Dict[str, Any]] = {}  # {tenant_id: {agent_id: store}}
        self._mongodb_contexts: Dict[str, Dict[str, Any]] = {}  # MongoDB 上下文管理器
        self._agent_configs: Dict[str, Dict[str, AgentConfig]] = {}  # {tenant_id: {agent_id: config}}
        
        # 会话和用户管理
        self._sessions: Dict[str, SessionInfo] = {}  # {session_id: SessionInfo}
        self._users: Dict[str, UserInfo] = {}  # {user_id: UserInfo}
        self._tenant_sessions: Dict[str, List[str]] = defaultdict(list)  # {tenant_id: [session_ids]}
        self._user_sessions: Dict[str, List[str]] = defaultdict(list)  # {user_id: [session_ids]}
        
        # 预定义的 Agent 配置模板
        self._agent_templates = self._initialize_agent_templates()
    
    def _initialize_agent_templates(self) -> Dict[AgentType, AgentConfig]:
        """初始化 Agent 配置模板"""
        return {
            AgentType.GENERAL: AgentConfig(
                agent_type=AgentType.GENERAL,
                name="通用助手",
                description="通用对话和问答助手",
                llm_model=llm_qwen3_32b,
                tools=self._get_general_tools(),
                custom_prompt="你是一个友好的AI助手，可以帮助用户解答各种问题。"
            ),
            AgentType.MATH: AgentConfig(
                agent_type=AgentType.MATH,
                name="数学计算助手",
                description="专门处理数学计算和公式推导",
                llm_model=llm_qwen2_5_72b_instruct,
                tools=self._get_math_tools(),
                custom_prompt="你是一个数学专家，擅长各种数学计算、公式推导和数学问题解答。"
            ),
            AgentType.WEATHER: AgentConfig(
                agent_type=AgentType.WEATHER,
                name="天气查询助手",
                description="提供天气信息查询服务",
                llm_model=llm_qwen3_32b,
                tools=self._get_weather_tools(),
                custom_prompt="你是一个天气助手，专门提供准确的天气信息和预报。"
            ),
            AgentType.CODE: AgentConfig(
                agent_type=AgentType.CODE,
                name="代码助手",
                description="代码编写、调试和优化助手",
                llm_model=llm_deepseek_v3,
                tools=self._get_code_tools(),
                custom_prompt="你是一个专业的编程助手，擅长代码编写、调试、优化和技术问题解答。"
            ),
            AgentType.ANALYSIS: AgentConfig(
                agent_type=AgentType.ANALYSIS,
                name="数据分析助手",
                description="数据分析和可视化助手",
                llm_model=llm_qwen2_5_72b_instruct,
                tools=self._get_analysis_tools(),
                custom_prompt="你是一个数据分析专家，擅长数据处理、统计分析和可视化。"
            )
        }
    
    def _get_general_tools(self) -> List[Any]:
        """获取通用工具"""
        try:
            client = MultiServerMCPClient({
                "math": {
                    "command": "python3",
                    "args": ["math_server.py"],
                    "transport": "stdio",
                },
                "weather": {
                    "url": "http://localhost:8000/sse",
                    "transport": "sse",
                }
            })
            return client.get_tools()
        except Exception as e:
            logger.warning(f"获取通用工具失败: {e}")
            return []
    
    def _get_math_tools(self) -> List[Any]:
        """获取数学工具"""
        try:
            client = MultiServerMCPClient({
                "math": {
                    "command": "python3",
                    "args": ["math_server.py"],
                    "transport": "stdio",
                }
            })
            return client.get_tools()
        except Exception as e:
            logger.warning(f"获取数学工具失败: {e}")
            return []
    
    def _get_weather_tools(self) -> List[Any]:
        """获取天气工具"""
        try:
            client = MultiServerMCPClient({
                "weather": {
                    "url": "http://localhost:8000/sse",
                    "transport": "sse",
                }
            })
            return client.get_tools()
        except Exception as e:
            logger.warning(f"获取天气工具失败: {e}")
            return []
    
    def _get_code_tools(self) -> List[Any]:
        """获取代码工具"""
        # 这里可以添加代码相关的工具
        return []
    
    def _get_analysis_tools(self) -> List[Any]:
        """获取数据分析工具"""
        # 这里可以添加数据分析相关的工具
        return []
    
    def create_tenant(self, tenant_config: TenantConfig) -> bool:
        """创建租户"""
        try:
            if tenant_config.tenant_id in self._tenants:
                raise ValueError(f"租户 {tenant_config.tenant_id} 已存在")
            
            self._tenants[tenant_config.tenant_id] = tenant_config
            self._agents[tenant_config.tenant_id] = {}
            self._checkpointers[tenant_config.tenant_id] = {}
            self._stores[tenant_config.tenant_id] = {}
            self._mongodb_contexts[tenant_config.tenant_id] = {}
            self._agent_configs[tenant_config.tenant_id] = {}
            
            logger.info(f"✅ 租户 {tenant_config.tenant_id} 创建成功")
            return True
            
        except Exception as e:
            logger.error(f"创建租户失败: {e}")
            return False
    
    def create_agent(self, tenant_id: str, agent_type: AgentType, agent_id: Optional[str] = None, custom_config: Optional[AgentConfig] = None) -> Optional[str]:
        """为租户创建 Agent"""
        try:
            # 验证租户存在
            if tenant_id not in self._tenants:
                raise ValueError(f"租户 {tenant_id} 不存在")
            
            tenant_config = self._tenants[tenant_id]
            
            # 检查agent类型是否被允许
            if agent_type not in tenant_config.allowed_agent_types:
                raise ValueError(f"租户 {tenant_id} 不允许创建 {agent_type.value} 类型的agent")
            
            # 检查agent数量限制
            if len(self._agents[tenant_id]) >= tenant_config.max_agents:
                raise ValueError(f"租户 {tenant_id} 已达到最大agent数量限制 ({tenant_config.max_agents})")
            
            # 生成agent_id
            if agent_id is None:
                agent_id = f"{agent_type.value}_{len(self._agents[tenant_id]) + 1}"
            
            if agent_id in self._agents[tenant_id]:
                raise ValueError(f"Agent {agent_id} 在租户 {tenant_id} 中已存在")
            
            # 使用自定义配置或模板配置
            config = custom_config or self._agent_templates[agent_type]
            
            # 创建存储和检查点
            store = InMemoryStore()
            checkpointer = self._create_checkpointer(tenant_id, agent_id)
            
            # 创建agent
            agent = create_react_agent(
                model=config.llm_model,
                tools=config.tools,
                checkpointer=checkpointer,
                store=store,
            )
            
            # 保存实例
            self._agents[tenant_id][agent_id] = agent
            self._checkpointers[tenant_id][agent_id] = checkpointer
            self._stores[tenant_id][agent_id] = store
            self._agent_configs[tenant_id][agent_id] = config
            
            logger.info(f"✅ 为租户 {tenant_id} 创建 Agent {agent_id} ({agent_type.value}) 成功")
            return agent_id
            
        except Exception as e:
            logger.error(f"创建Agent失败: {e}")
            return None
    
    def _create_checkpointer(self, tenant_id: str, agent_id: str):
        """为特定租户和agent创建检查点存储"""
        try:
            tenant_config = self._tenants[tenant_id]
            mongodb_url = os.getenv("MONGODB_URL", "**************************************")
            
            # 使用租户专用数据库或默认数据库
            database_name = tenant_config.mongodb_database or f"langgraph_{tenant_id}"
            
            # 创建 MongoDBSaver
            mongodb_context = MongoDBSaver.from_conn_string(
                mongodb_url,
                database_name=database_name
            )
            checkpointer = mongodb_context.__enter__()
            
            # 保存上下文管理器
            if tenant_id not in self._mongodb_contexts:
                self._mongodb_contexts[tenant_id] = {}
            self._mongodb_contexts[tenant_id][agent_id] = mongodb_context
            
            logger.info(f"✅ 为租户 {tenant_id} Agent {agent_id} 创建 MongoDB 检查点: {database_name}")
            return checkpointer
            
        except Exception as e:
            logger.warning(f"MongoDB 连接失败，使用内存存储: {e}")
            return InMemorySaver()
    
    def get_agent(self, tenant_id: str, agent_id: str):
        """获取指定租户的指定agent"""
        if tenant_id not in self._agents:
            raise ValueError(f"租户 {tenant_id} 不存在")
        
        if agent_id not in self._agents[tenant_id]:
            raise ValueError(f"Agent {agent_id} 在租户 {tenant_id} 中不存在")
        
        return self._agents[tenant_id][agent_id]
    
    def get_checkpointer(self, tenant_id: str, agent_id: str):
        """获取指定租户的指定agent的检查点存储"""
        if tenant_id not in self._checkpointers:
            raise ValueError(f"租户 {tenant_id} 不存在")
        
        if agent_id not in self._checkpointers[tenant_id]:
            raise ValueError(f"Agent {agent_id} 在租户 {tenant_id} 中不存在")
        
        return self._checkpointers[tenant_id][agent_id]
    
    def get_store(self, tenant_id: str, agent_id: str):
        """获取指定租户的指定agent的存储"""
        if tenant_id not in self._stores:
            raise ValueError(f"租户 {tenant_id} 不存在")
        
        if agent_id not in self._stores[tenant_id]:
            raise ValueError(f"Agent {agent_id} 在租户 {tenant_id} 中不存在")
        
        return self._stores[tenant_id][agent_id]
    
    def list_tenants(self) -> List[Dict[str, Any]]:
        """列出所有租户"""
        return [
            {
                "tenant_id": tenant_id,
                "name": config.name,
                "agent_count": len(self._agents.get(tenant_id, {})),
                "max_agents": config.max_agents,
                "created_at": config.created_at.isoformat()
            }
            for tenant_id, config in self._tenants.items()
        ]
    
    def list_agents(self, tenant_id: str) -> List[Dict[str, Any]]:
        """列出指定租户的所有agent"""
        if tenant_id not in self._tenants:
            raise ValueError(f"租户 {tenant_id} 不存在")
        
        agents = []
        for agent_id, config in self._agent_configs.get(tenant_id, {}).items():
            agents.append({
                "agent_id": agent_id,
                "agent_type": config.agent_type.value,
                "name": config.name,
                "description": config.description,
                "tools_count": len(config.tools)
            })
        
        return agents
    
    def delete_agent(self, tenant_id: str, agent_id: str) -> bool:
        """删除指定租户的指定agent"""
        try:
            if tenant_id not in self._agents:
                raise ValueError(f"租户 {tenant_id} 不存在")
            
            if agent_id not in self._agents[tenant_id]:
                raise ValueError(f"Agent {agent_id} 在租户 {tenant_id} 中不存在")
            
            # 清理MongoDB上下文
            if (tenant_id in self._mongodb_contexts and 
                agent_id in self._mongodb_contexts[tenant_id]):
                try:
                    self._mongodb_contexts[tenant_id][agent_id].__exit__(None, None, None)
                except Exception as e:
                    logger.warning(f"清理MongoDB上下文失败: {e}")
                del self._mongodb_contexts[tenant_id][agent_id]
            
            # 删除相关实例
            del self._agents[tenant_id][agent_id]
            del self._checkpointers[tenant_id][agent_id]
            del self._stores[tenant_id][agent_id]
            del self._agent_configs[tenant_id][agent_id]
            
            logger.info(f"✅ 删除租户 {tenant_id} 的 Agent {agent_id} 成功")
            return True
            
        except Exception as e:
            logger.error(f"删除Agent失败: {e}")
            return False
    
    def delete_tenant(self, tenant_id: str) -> bool:
        """删除租户及其所有agent"""
        try:
            if tenant_id not in self._tenants:
                raise ValueError(f"租户 {tenant_id} 不存在")
            
            # 删除所有agent
            agent_ids = list(self._agents.get(tenant_id, {}).keys())
            for agent_id in agent_ids:
                self.delete_agent(tenant_id, agent_id)
            
            # 删除租户
            del self._tenants[tenant_id]
            if tenant_id in self._agents:
                del self._agents[tenant_id]
            if tenant_id in self._checkpointers:
                del self._checkpointers[tenant_id]
            if tenant_id in self._stores:
                del self._stores[tenant_id]
            if tenant_id in self._mongodb_contexts:
                del self._mongodb_contexts[tenant_id]
            if tenant_id in self._agent_configs:
                del self._agent_configs[tenant_id]
            
            logger.info(f"✅ 删除租户 {tenant_id} 成功")
            return True
            
        except Exception as e:
            logger.error(f"删除租户失败: {e}")
            return False
    
    def chat_with_agent(self, tenant_id: str, agent_id: str, message: str, thread_id: str = "default",
                       user_id: Optional[str] = None, user_name: Optional[str] = None) -> Dict[str, Any]:
        """与指定租户的指定agent进行对话"""
        try:
            agent = self.get_agent(tenant_id, agent_id)
            config = {"configurable": {"thread_id": f"{tenant_id}_{agent_id}_{thread_id}"}}
            
            # 创建或更新会话
            session_id = f"{tenant_id}_{agent_id}_{thread_id}"
            if session_id not in self._sessions:
                self.create_session(tenant_id, agent_id, thread_id, user_id, user_name)
            else:
                # 更新会话活跃时间
                self.update_session_activity(session_id)
            
            response = agent.invoke(
                {"messages": [{"role": "user", "content": message}]},
                config
            )
            
            # 再次更新会话活跃时间（响应完成后）
            self.update_session_activity(session_id)
            
            last_message = response["messages"][-1]
            return {
                "success": True,
                "response": last_message.content,
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id,
                "session_id": session_id
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id
            }
    
    def chat_with_agent_stream(self, tenant_id: str, agent_id: str, message: str, thread_id: str = "default",
                              user_id: Optional[str] = None, user_name: Optional[str] = None):
        """与指定租户的指定agent进行流式对话"""
        try:
            agent = self.get_agent(tenant_id, agent_id)
            config = {"configurable": {"thread_id": f"{tenant_id}_{agent_id}_{thread_id}"}}
            
            # 创建或更新会话
            session_id = f"{tenant_id}_{agent_id}_{thread_id}"
            if session_id not in self._sessions:
                self.create_session(tenant_id, agent_id, thread_id, user_id, user_name)
            else:
                self.update_session_activity(session_id)
            
            # 使用stream方法进行流式调用
            for chunk in agent.stream(
                {"messages": [{"role": "user", "content": message}]},
                config
            ):
                # 处理不同类型的chunk
                if "messages" in chunk:
                    # 获取最新的消息
                    messages = chunk["messages"]
                    if messages:
                        last_message = messages[-1]
                        # 检查是否是AI消息且有内容
                        if hasattr(last_message, 'content') and last_message.content:
                            yield {
                                "type": "content",
                                "data": last_message.content,
                                "tenant_id": tenant_id,
                                "agent_id": agent_id,
                                "thread_id": thread_id,
                                "user_id": user_id,
                                "session_id": session_id,
                                "message_type": last_message.__class__.__name__
                            }
                
                # 处理工具调用
                if "tools" in chunk:
                    yield {
                        "type": "tool_call",
                        "data": chunk["tools"],
                        "tenant_id": tenant_id,
                        "agent_id": agent_id,
                        "thread_id": thread_id,
                        "user_id": user_id,
                        "session_id": session_id
                    }
                
                # 处理agent步骤
                if "agent" in chunk:
                    yield {
                        "type": "agent_step",
                        "data": "Agent正在思考...",
                        "tenant_id": tenant_id,
                        "agent_id": agent_id,
                        "thread_id": thread_id,
                        "user_id": user_id,
                        "session_id": session_id
                    }
            
            # 更新会话活跃时间
            self.update_session_activity(session_id)
            
            # 发送完成信号
            yield {
                "type": "done",
                "data": "对话完成",
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id,
                "session_id": session_id
            }
            
        except Exception as e:
            yield {
                "type": "error",
                "data": str(e),
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id
            }
    
    async def chat_with_agent_stream_async(self, tenant_id: str, agent_id: str, message: str, thread_id: str = "default",
                                          user_id: Optional[str] = None, user_name: Optional[str] = None):
        """与指定租户的指定agent进行异步流式对话"""
        try:
            agent = self.get_agent(tenant_id, agent_id)
            config = {"configurable": {"thread_id": f"{tenant_id}_{agent_id}_{thread_id}"}}
            
            # 创建或更新会话
            session_id = f"{tenant_id}_{agent_id}_{thread_id}"
            if session_id not in self._sessions:
                self.create_session(tenant_id, agent_id, thread_id, user_id, user_name)
            else:
                self.update_session_activity(session_id)
            
            # 使用astream方法进行异步流式调用
            async for chunk in agent.astream(
                {"messages": [{"role": "user", "content": message}]},
                config
            ):
                # 处理不同类型的chunk
                if "messages" in chunk:
                    messages = chunk["messages"]
                    if messages:
                        last_message = messages[-1]
                        if hasattr(last_message, 'content') and last_message.content:
                            yield {
                                "type": "content",
                                "data": last_message.content,
                                "tenant_id": tenant_id,
                                "agent_id": agent_id,
                                "thread_id": thread_id,
                                "user_id": user_id,
                                "session_id": session_id,
                                "message_type": last_message.__class__.__name__
                            }
                
                if "tools" in chunk:
                    yield {
                        "type": "tool_call",
                        "data": str(chunk["tools"]),
                        "tenant_id": tenant_id,
                        "agent_id": agent_id,
                        "thread_id": thread_id,
                        "user_id": user_id,
                        "session_id": session_id
                    }
                
                if "agent" in chunk:
                    yield {
                        "type": "agent_step",
                        "data": "Agent正在思考...",
                        "tenant_id": tenant_id,
                        "agent_id": agent_id,
                        "thread_id": thread_id,
                        "user_id": user_id,
                        "session_id": session_id
                    }
            
            # 更新会话活跃时间
            self.update_session_activity(session_id)
            
            # 发送完成信号
            yield {
                "type": "done",
                "data": "对话完成",
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id,
                "session_id": session_id
            }
            
        except Exception as e:
            yield {
                "type": "error",
                "data": str(e),
                "tenant_id": tenant_id,
                "agent_id": agent_id,
                "thread_id": thread_id,
                "user_id": user_id
            }
    
    def cleanup(self):
        """清理所有资源"""
        logger.info("开始清理多租户Agent服务资源...")
        
        for tenant_id in list(self._tenants.keys()):
            self.delete_tenant(tenant_id)
        
        logger.info("✅ 多租户Agent服务资源清理完成")
    
    # ==================== 会话管理方法 ====================
    
    def create_session(self, tenant_id: str, agent_id: str, thread_id: str, 
                      user_id: Optional[str] = None, user_name: Optional[str] = None) -> str:
        """创建新会话"""
        session_id = f"{tenant_id}_{agent_id}_{thread_id}"
        
        if session_id in self._sessions:
            logger.warning(f"会话 {session_id} 已存在，将更新活跃时间")
            self.update_session_activity(session_id)
            return session_id
        
        session_info = SessionInfo(
            session_id=session_id,
            tenant_id=tenant_id,
            agent_id=agent_id,
            thread_id=thread_id,
            user_id=user_id,
            user_name=user_name
        )
        
        # 保存会话信息
        self._sessions[session_id] = session_info
        self._tenant_sessions[tenant_id].append(session_id)
        
        if user_id:
            self._user_sessions[user_id].append(session_id)
        
        logger.info(f"✅ 创建会话 {session_id}")
        return session_id
    
    def update_session_activity(self, session_id: str) -> bool:
        """更新会话活跃时间"""
        if session_id in self._sessions:
            self._sessions[session_id].last_active = datetime.now()
            self._sessions[session_id].message_count += 1
            return True
        return False
    
    def list_sessions(self, tenant_id: Optional[str] = None, agent_id: Optional[str] = None, 
                     user_id: Optional[str] = None, active_only: bool = False, 
                     limit: int = 50) -> List[Dict[str, Any]]:
        """列出会话"""
        sessions = []
        
        # 筛选会话
        filtered_sessions = []
        for session in self._sessions.values():
            # 按租户筛选
            if tenant_id and session.tenant_id != tenant_id:
                continue
            # 按Agent筛选
            if agent_id and session.agent_id != agent_id:
                continue
            # 按用户筛选
            if user_id and session.user_id != user_id:
                continue
            # 按活跃状态筛选
            if active_only:
                # 如果最后活跃时间在1小时内，认为是活跃会话
                if (datetime.now() - session.last_active).total_seconds() > 3600:
                    continue
            
            filtered_sessions.append(session)
        
        # 按最后活跃时间降序排序
        filtered_sessions.sort(key=lambda x: x.last_active, reverse=True)
        
        # 限制数量
        for session in filtered_sessions[:limit]:
            sessions.append({
                "session_id": session.session_id,
                "tenant_id": session.tenant_id,
                "agent_id": session.agent_id,
                "thread_id": session.thread_id,
                "user_id": session.user_id,
                "user_name": session.user_name,
                "created_at": session.created_at.isoformat(),
                "last_active": session.last_active.isoformat(),
                "message_count": session.message_count,
                "metadata": session.metadata
            })
        
        return sessions
    
    def cleanup_expired_sessions(self, expiry_hours: int = 24) -> int:
        """清理过期会话"""
        cutoff_time = datetime.now() - timedelta(hours=expiry_hours)
        expired_sessions = []
        
        for session_id, session in self._sessions.items():
            if session.last_active < cutoff_time:
                expired_sessions.append(session_id)
        
        # 删除过期会话
        for session_id in expired_sessions:
            session = self._sessions[session_id]
            
            # 从租户会话列表中移除
            if session_id in self._tenant_sessions[session.tenant_id]:
                self._tenant_sessions[session.tenant_id].remove(session_id)
            
            # 从用户会话列表中移除
            if session.user_id and session_id in self._user_sessions[session.user_id]:
                self._user_sessions[session.user_id].remove(session_id)
            
            # 从主会话字典中移除
            del self._sessions[session_id]
        
        logger.info(f"✅ 清理了 {len(expired_sessions)} 个过期会话")
        return len(expired_sessions)
    
    # ==================== 用户管理方法 ====================
    
    def create_user(self, user_info: UserInfo) -> bool:
        """创建用户"""
        try:
            if user_info.user_id in self._users:
                raise ValueError(f"用户 {user_info.user_id} 已存在")
            
            # 验证租户存在
            if user_info.tenant_id not in self._tenants:
                raise ValueError(f"租户 {user_info.tenant_id} 不存在")
            
            # 设置登录时间
            user_info.last_login = datetime.now()
            
            self._users[user_info.user_id] = user_info
            logger.info(f"✅ 创建用户 {user_info.user_id} 成功")
            return True
            
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            return False
    
    def get_user(self, user_id: str) -> Optional[UserInfo]:
        """获取用户信息"""
        return self._users.get(user_id)
    
    def list_users(self, tenant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出用户"""
        users = []
        
        for user in self._users.values():
            # 按租户筛选
            if tenant_id and user.tenant_id != tenant_id:
                continue
            
            users.append({
                "user_id": user.user_id,
                "user_name": user.user_name,
                "tenant_id": user.tenant_id,
                "email": user.email,
                "role": user.role,
                "created_at": user.created_at.isoformat(),
                "last_login": user.last_login.isoformat() if user.last_login else None
            })
        
        return users
    
    def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        try:
            if user_id not in self._users:
                raise ValueError(f"用户 {user_id} 不存在")
            
            # 删除用户的所有会话
            user_session_ids = list(self._user_sessions.get(user_id, []))
            for session_id in user_session_ids:
                if session_id in self._sessions:
                    session = self._sessions[session_id]
                    # 从租户会话列表中移除
                    if session_id in self._tenant_sessions[session.tenant_id]:
                        self._tenant_sessions[session.tenant_id].remove(session_id)
                    # 删除会话
                    del self._sessions[session_id]
            
            # 清理用户会话列表
            if user_id in self._user_sessions:
                del self._user_sessions[user_id]
            
            # 删除用户
            del self._users[user_id]
            
            logger.info(f"✅ 删除用户 {user_id} 成功")
            return True
            
        except Exception as e:
            logger.error(f"删除用户失败: {e}")
            return False

# 全局多租户agent服务实例
multi_tenant_agent_service = MultiTenantAgentService()

def initialize_default_tenants():
    """初始化默认租户和agent（用于演示）"""
    # 创建演示租户
    demo_tenant = TenantConfig(
        tenant_id="demo_company",
        name="演示公司",
        max_agents=5,
        allowed_agent_types=[AgentType.GENERAL, AgentType.MATH, AgentType.WEATHER]
    )
    
    enterprise_tenant = TenantConfig(
        tenant_id="enterprise_corp",
        name="企业公司",
        max_agents=20,
        allowed_agent_types=list(AgentType)  # 允许所有类型
    )
    
    # 创建租户
    multi_tenant_agent_service.create_tenant(demo_tenant)
    multi_tenant_agent_service.create_tenant(enterprise_tenant)
    
    # 为演示租户创建agent
    multi_tenant_agent_service.create_agent("demo_company", AgentType.GENERAL, "assistant")
    multi_tenant_agent_service.create_agent("demo_company", AgentType.MATH, "calculator")
    
    # 为企业租户创建agent
    multi_tenant_agent_service.create_agent("enterprise_corp", AgentType.GENERAL, "general_assistant")
    multi_tenant_agent_service.create_agent("enterprise_corp", AgentType.CODE, "code_helper")
    multi_tenant_agent_service.create_agent("enterprise_corp", AgentType.ANALYSIS, "data_analyst")
    
    logger.info("✅ 默认租户和Agent初始化完成")

# 在模块加载时初始化默认租户
if __name__ != "__main__":
    initialize_default_tenants() 