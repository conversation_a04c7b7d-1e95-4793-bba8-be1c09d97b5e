import asyncio
import time
import logging
from typing import Dict, List, Any, Optional, Callable, Awaitable
from fastapi import FastAPI, Depends, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from redis import asyncio as aioredis
from contextlib import asynccontextmanager
import uuid
import json
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 会话状态定义
class SessionState(BaseModel):
    session_id: str
    user_id: Optional[str] = None
    created_at: str
    last_active: str
    metadata: Dict[str, Any] = {}

# 高并发管理器
class ConcurrencyManager:
    def __init__(
        self, 
        redis_url: str = "redis://localhost:6379/0",
        max_concurrent_requests: int = 100,
        rate_limit_per_minute: int = 60,
        session_ttl: int = 3600  # 会话有效期，单位秒
    ):
        self.redis_url = redis_url
        self.max_concurrent_requests = max_concurrent_requests
        self.rate_limit_per_minute = rate_limit_per_minute
        self.session_ttl = session_ttl
        self.redis = None
        self.active_requests = 0
        self.semaphore = asyncio.Semaphore(max_concurrent_requests)
        
    async def setup(self):
        """初始化并连接Redis"""
        self.redis = await aioredis.from_url(self.redis_url)
        logger.info(f"Connected to Redis at {self.redis_url}")
        
    async def cleanup(self):
        """关闭Redis连接"""
        if self.redis:
            await self.redis.close()
            logger.info("Redis connection closed")

    async def create_session(self, user_id: Optional[str] = None, metadata: Dict[str, Any] = {}) -> str:
        """创建新会话"""
        if not self.redis:
            raise RuntimeError("Redis连接未初始化")
            
        session_id = str(uuid.uuid4())
        now = datetime.now().isoformat()
        
        session_state = SessionState(
            session_id=session_id,
            user_id=user_id,
            created_at=now,
            last_active=now,
            metadata=metadata
        )
        
        # 存储会话状态
        await self.redis.set(
            f"session:{session_id}", 
            session_state.model_dump_json(),
            ex=self.session_ttl
        )
        
        return session_id
    
    async def get_session(self, session_id: str) -> Optional[SessionState]:
        """获取会话状态"""
        if not self.redis:
            raise RuntimeError("Redis连接未初始化")
            
        session_data = await self.redis.get(f"session:{session_id}")
        if not session_data:
            return None
            
        # 更新会话活跃时间
        session_state = SessionState.model_validate_json(session_data)
        session_state.last_active = datetime.now().isoformat()
        
        # 重新存储并延长有效期
        await self.redis.set(
            f"session:{session_id}", 
            session_state.model_dump_json(),
            ex=self.session_ttl
        )
        
        return session_state
    
    async def update_session(self, session_id: str, metadata: Dict[str, Any]) -> bool:
        """更新会话元数据"""
        if not self.redis:
            raise RuntimeError("Redis连接未初始化")
            
        session_state = await self.get_session(session_id)
        if not session_state:
            return False
            
        # 更新元数据
        session_state.metadata.update(metadata)
        session_state.last_active = datetime.now().isoformat()
        
        # 重新存储
        await self.redis.set(
            f"session:{session_id}", 
            session_state.model_dump_json(),
            ex=self.session_ttl
        )
        
        return True
    
    async def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        if not self.redis:
            raise RuntimeError("Redis连接未初始化")
            
        result = await self.redis.delete(f"session:{session_id}")
        return result > 0
    
    async def check_rate_limit(self, key: str) -> bool:
        """检查速率限制"""
        if not self.redis:
            raise RuntimeError("Redis连接未初始化")
            
        # 使用滑动窗口实现速率限制
        now = int(time.time())
        pipeline = self.redis.pipeline()
        
        # 清理过期的请求记录
        pipeline.zremrangebyscore(
            f"rate_limit:{key}",
            0,
            now - 60  # 移除60秒前的记录
        )
        
        # 添加当前请求
        pipeline.zadd(f"rate_limit:{key}", {now: now})
        
        # 获取当前窗口内的请求数
        pipeline.zcount(f"rate_limit:{key}", now - 60, now)
        
        # 设置过期时间
        pipeline.expire(f"rate_limit:{key}", 120)  # 2分钟后自动过期
        
        # 执行pipeline
        _, _, count, _ = await pipeline.execute()
        
        # 检查是否超过限制
        return count <= self.rate_limit_per_minute
    
    @asynccontextmanager
    async def request_context(self, key: str):
        """请求上下文管理器，处理并发和速率限制"""
        # 检查速率限制
        rate_limited = await self.check_rate_limit(key)
        if not rate_limited:
            raise HTTPException(
                status_code=429, 
                detail="请求过于频繁，请稍后再试"
            )
        
        # 并发控制
        async with self.semaphore:
            self.active_requests += 1
            try:
                yield
            finally:
                self.active_requests -= 1

# 工厂函数，创建依赖项
def create_concurrency_manager(
    redis_url: str = "redis://localhost:6379/0",
    max_concurrent_requests: int = 100,
    rate_limit_per_minute: int = 60,
    session_ttl: int = 3600
):
    manager = ConcurrencyManager(
        redis_url=redis_url,
        max_concurrent_requests=max_concurrent_requests,
        rate_limit_per_minute=rate_limit_per_minute,
        session_ttl=session_ttl
    )
    
    async def lifespan(app: FastAPI):
        # 启动时
        await manager.setup()
        yield
        # 关闭时
        await manager.cleanup()
    
    return manager, lifespan

# 限流中间件
async def rate_limiter(
    request: Request,
    concurrency_manager: ConcurrencyManager = None
):
    """限流中间件"""
    if not concurrency_manager:
        return
        
    # 获取客户端IP
    client_ip = request.client.host
    
    # 尝试从请求头获取会话ID
    session_id = request.headers.get("X-Session-ID")
    
    # 使用IP或会话ID作为限流键
    rate_limit_key = session_id or client_ip
    
    # 检查速率限制
    if not await concurrency_manager.check_rate_limit(rate_limit_key):
        raise HTTPException(
            status_code=429, 
            detail="请求过于频繁，请稍后再试"
        )

# 会话中间件
async def session_middleware(
    request: Request,
    concurrency_manager: ConcurrencyManager
):
    """会话中间件，确保会话有效并刷新会话"""
    # 尝试从请求头获取会话ID
    session_id = request.headers.get("X-Session-ID")
    
    if session_id:
        # 获取并刷新会话
        session = await concurrency_manager.get_session(session_id)
        if not session:
            # 会话无效，创建新会话
            new_session_id = await concurrency_manager.create_session()
            request.state.session_id = new_session_id
            request.state.new_session = True
        else:
            request.state.session_id = session_id
            request.state.new_session = False
            request.state.session = session
    else:
        # 没有会话ID，创建新会话
        new_session_id = await concurrency_manager.create_session()
        request.state.session_id = new_session_id
        request.state.new_session = True

# 演示FastAPI应用程序
def create_app():
    # 创建并发管理器和生命周期管理器
    concurrency_manager, lifespan = create_concurrency_manager(
        redis_url="redis://localhost:6379/0",
        max_concurrent_requests=100,
        rate_limit_per_minute=60
    )
    
    # 创建FastAPI应用
    app = FastAPI(
        title="高并发示例API",
        lifespan=lifespan
    )
    
    # 添加中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 依赖项
    async def get_concurrency_manager():
        return concurrency_manager
        
    # 会话API示例
    @app.post("/sessions")
    async def create_new_session(
        manager: ConcurrencyManager = Depends(get_concurrency_manager)
    ):
        """创建新会话"""
        session_id = await manager.create_session()
        return {"session_id": session_id}
    
    @app.get("/sessions/{session_id}")
    async def get_session_info(
        session_id: str,
        manager: ConcurrencyManager = Depends(get_concurrency_manager)
    ):
        """获取会话信息"""
        async with manager.request_context(session_id):
            session = await manager.get_session(session_id)
            if not session:
                raise HTTPException(status_code=404, detail="会话不存在")
            return session
    
    @app.delete("/sessions/{session_id}")
    async def delete_session(
        session_id: str,
        manager: ConcurrencyManager = Depends(get_concurrency_manager)
    ):
        """删除会话"""
        result = await manager.delete_session(session_id)
        if not result:
            raise HTTPException(status_code=404, detail="会话不存在")
        return {"success": True}
    
    # 高并发测试接口
    @app.get("/test_concurrency")
    async def test_concurrency(
        request: Request,
        manager: ConcurrencyManager = Depends(get_concurrency_manager)
    ):
        """测试并发控制"""
        client_ip = request.client.host
        
        async with manager.request_context(client_ip):
            # 模拟处理时间
            await asyncio.sleep(1)
            
            return {
                "success": True,
                "active_requests": manager.active_requests,
                "timestamp": datetime.now().isoformat()
            }
    
    return app

# 主函数
if __name__ == "__main__":
    # 创建应用
    app = create_app()
    
    # 启动服务器
    uvicorn.run(app, host="0.0.0.0", port=8003) 