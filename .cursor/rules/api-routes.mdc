---
description:
globs:
alwaysApply: false
---
# 云知问平台API路由指南

## API版本与结构
所有API路由都在`/api/v1/`前缀下，如[backend/app/main.py](mdc:backend/app/main.py)中所示。

## 主要API模块：

### 认证相关API
- 路径：`/api/v1/auth/`
- 文件：[backend/app/api/v1/auth.py](mdc:backend/app/api/v1/auth.py)
- 功能：用户登录、登出、刷新令牌等

### 租户管理API
- 路径：`/api/v1/tenants/`
- 文件：[backend/app/api/v1/tenant.py](mdc:backend/app/api/v1/tenant.py)
- 功能：创建、查询、更新、删除租户

### 用户管理API
- 路径：`/api/v1/users/`
- 文件：[backend/app/api/v1/user.py](mdc:backend/app/api/v1/user.py)
- 功能：用户CRUD操作

### 角色管理API
- 路径：`/api/v1/roles/`
- 文件：[backend/app/api/v1/role.py](mdc:backend/app/api/v1/role.py)
- 功能：角色与权限管理

### Agent管理API
- 路径：`/api/v1/agents/`
- 文件：[backend/app/api/v1/agent.py](mdc:backend/app/api/v1/agent.py)
- 功能：Agent创建、配置、查询等

### 工具管理API
- 路径：`/api/v1/tools/`
- 文件：[backend/app/api/v1/tool.py](mdc:backend/app/api/v1/tool.py)
- 功能：Agent工具的CRUD操作

### 模型管理API
- 路径：`/api/v1/models/`
- 文件：[backend/app/api/v1/model.py](mdc:backend/app/api/v1/model.py)
- 功能：LLM模型管理
