---
description: 
globs: 
alwaysApply: false
---
# 云知问平台开发指南

## 后端开发

### 启动服务
后端服务的入口点是[backend/app/main.py](mdc:backend/app/main.py)，可通过以下方式启动：
```python
# 直接运行main.py
cd backend  && source venv/bin/activate && python3 -m app.main

# 或使用uvicorn
cd backend && source venv/bin/activate &&  uvicorn app.main:app --reload
```

### 开发流程
1. **添加新API**：
   - 在[backend/app/api/v1/](mdc:backend/app/api/v1)目录下创建或修改路由文件
   - 在[backend/app/main.py](mdc:backend/app/main.py)中注册路由

2. **添加新数据模型**：
   - 在[backend/app/models/](mdc:backend/app/models)中创建模型类
   - 在[backend/app/db/base.py](mdc:backend/app/db/base.py)中导入模型

3. **添加新请求/响应模式**：
   - 在[backend/app/schemas/](mdc:backend/app/schemas)目录下创建Pydantic模型

4. **添加业务逻辑**：
   - 在[backend/app/services/](mdc:backend/app/services)中实现服务层逻辑

### API文档
- API文档在启动服务后可通过`/docs`访问
- OpenAPI规范可通过`/openapi.json`访问

### 配置与环境变量
主要配置位于[backend/app/core/config.py](mdc:backend/app/core/config.py)，支持通过环境变量覆盖默认配置。
