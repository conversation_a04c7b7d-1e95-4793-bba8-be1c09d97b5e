---
description: 
globs: 
alwaysApply: false
---
# 云知问平台数据库架构

## 数据库配置
数据库连接和配置在[backend/app/db/session.py](mdc:backend/app/db/session.py)中定义，使用SQLAlchemy作为ORM工具。

## 数据模型结构
所有数据库模型都定义在[backend/app/models/](mdc:backend/app/models)目录中。

### 核心表结构：

#### 租户表
- 文件：[backend/app/models/tenant.py](mdc:backend/app/models/tenant.py)
- 功能：存储多租户信息

#### 用户表
- 文件：[backend/app/models/user.py](mdc:backend/app/models/user.py)
- 功能：用户信息与认证数据

#### 角色表
- 文件：[backend/app/models/role.py](mdc:backend/app/models/role.py)
- 功能：用户角色与权限

#### Agent表
- 文件：[backend/app/models/agent.py](mdc:backend/app/models/agent.py)
- 功能：Agent配置与元数据

#### 工具表
- 文件：[backend/app/models/tool.py](mdc:backend/app/models/tool.py)
- 功能：Agent可用工具信息

#### 模型表
- 文件：[backend/app/models/model.py](mdc:backend/app/models/model.py)
- 功能：LLM模型配置

## 数据库迁移
实际生产环境建议使用Alembic进行数据库迁移管理。
