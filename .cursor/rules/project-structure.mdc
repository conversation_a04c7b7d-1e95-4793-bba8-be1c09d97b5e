---
description: 
globs: 
alwaysApply: false
---
# 云知问智能Agent平台项目结构

## 项目概述
云知问是一个多租户智能Agent平台，包含前端和后端两部分。

## 后端结构
后端使用FastAPI构建，主要入口文件是[backend/app/main.py](mdc:backend/app/main.py)。

### 核心模块：
- **API路由**：[backend/app/api/v1/](mdc:backend/app/api/v1)目录包含所有API端点
- **数据库**：使用SQLAlchemy进行ORM，主要配置在[backend/app/db/](mdc:backend/app/db)
- **配置**：核心配置项在[backend/app/core/](mdc:backend/app/core)目录
- **数据模型**：SQLAlchemy模型定义在[backend/app/models/](mdc:backend/app/models)
- **请求/响应模式**：Pydantic模型定义在[backend/app/schemas/](mdc:backend/app/schemas)
- **业务逻辑**：服务层代码位于[backend/app/services/](mdc:backend/app/services)
- **工具函数**：通用工具函数在[backend/app/utils/](mdc:backend/app/utils)

### 主要功能模块：
- 租户管理
- 用户管理
- 角色管理
- Agent管理
- 工具管理
- 模型管理
- 认证与授权
