apiVersion: apps/v1
kind: Deployment
metadata:
  name: yunzhiwen
  namespace: default
spec:
  progressDeadlineSeconds: 30
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      workload.user.cattle.io/workloadselector: deployment-default-yunzhiwen
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        workload.user.cattle.io/workloadselector: deployment-default-yunzhiwen
    spec:
      containers:        
        - name: yunzhiwen
          image: registry.hn.com/hnczb/yunzhiwen:YCPRTAG
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 2
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 2
          ports:
            - containerPort: 80
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 2
            successThreshold: 2
            tcpSocket:
              port: 80
            timeoutSeconds: 2
          resources: {}
          securityContext:
            allowPrivilegeEscalation: false
            capabilities: {}
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
          volumeMounts:
            - mountPath: /etc/localtime
              name: volume-localtime
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: harbor
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: volume-localtime
