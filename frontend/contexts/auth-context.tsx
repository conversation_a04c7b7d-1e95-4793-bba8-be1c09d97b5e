"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { apiClient } from "@/lib/api/api"
import { components } from "@/lib/api/strapi"
import { Loader2 } from "lucide-react"

type Token = components["schemas"]["Token"]
type UserDetail = components["schemas"]["UserDetail"]
type UserTenantRole = components["schemas"]["UserTenantRole"]

interface AuthState {
  user: UserDetail | undefined
  token: Token | undefined
  isLoading: boolean
  isAuthenticated: boolean
  currentTenant: UserTenantRole | undefined
}
interface AuthContextType extends AuthState {
  login: (username: string, password: string) => Promise<boolean>
  logout: () => void
  getUser: () => void
  setCurrentTenant: (tenant?: UserTenantRole) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    user: undefined,
    token: undefined,
    isLoading: true,
    isAuthenticated: false,
    currentTenant: undefined
  })
  const router = useRouter()
  const { toast } = useToast()

  // 初始化时从localStorage加载认证状态
  useEffect(() => {
    const loadAuthState = () => {
      try {
        const storedToken = localStorage.getItem("ls-token")
        const storedUser = localStorage.getItem("ls-user")
        const savedTenant = localStorage.getItem('ls-current-tenant')
        const user = storedUser ? JSON.parse(storedUser) : undefined
        const token = storedToken ? JSON.parse(storedToken) : undefined
        const tenant = savedTenant ? JSON.parse(savedTenant) : undefined

        setAuthState({
          ...authState,
          user: user ?? authState.user,
          token: token ?? authState.token,
          currentTenant: tenant ?? authState.currentTenant ?? user?.tenants?.find(Boolean),
          isAuthenticated: user && token,
          isLoading: false
        })
      } catch (error) {
        console.error("Failed to load auth state:", error)
      } finally {
        setAuthState((prev) => ({ ...prev, isLoading: false }))
      }
    }

    loadAuthState()
  }, [])

  const login = async (username: string, password: string): Promise<boolean> => {
    try {

      const { data, error } = await apiClient.POST("/api/v1/auth/login", {
        body: {
          grant_type: "password",
          username: username,
          password: password,
          scope: "",
          client_id: "",
          client_secret: "",
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          accept: "application/json",
        },
      });
      

      if (error) {
        toast.error("登录失败！")
        return false
      } else if (data) {
        // 提取用户信息和令牌
        localStorage.setItem("ls-token", JSON.stringify(data))

        // 更新状态
        setAuthState({
          ...authState,
          token: data,
          isLoading: false,
          isAuthenticated: true,
        })
        // await getUser(data)
        toast.success(`登录成功,欢迎回来，${data.username}！`)
      }

      return true
    } catch (error) {
      toast.error("登录失败！")
      return false
    }
  }

  useEffect(() => {
    getUser(authState.token)
  }, [authState.isAuthenticated])

  const getUser = async (token?: Token) => {
    if (token || authState.token) {
      const { data, error } = await apiClient.GET("/api/v1/users/me/detail");
      if (error) {
        toast.error("获取用户信息失败！")
        return
      } else if (data) {
        localStorage.setItem("ls-user", JSON.stringify(data))
        setAuthState({
          ...authState,
          user: data
        })
        const info = localStorage.getItem('ls-current-tenant')
        !info && setCurrentTenant(data.tenants?.[0])
      }
    }
  }
  const setCurrentTenant = (tenant?: UserTenantRole) => {
    setAuthState(prevState => {
      const newState = { ...prevState, currentTenant: tenant }
      // 将新状态保存到 localStorage
      localStorage.setItem('ls-current-tenant', JSON.stringify(newState.currentTenant))
      return newState
    })
  }
  const logout = () => {
    
    // 清除本地存储
    localStorage.removeItem("ls-token")
    localStorage.removeItem("ls-user")
    localStorage.removeItem("ls-current-tenant")

    // 重置状态
    setAuthState({
      user: undefined,
      token: undefined,
      isLoading: false,
      isAuthenticated: false,
      currentTenant: undefined
    })

    // 重定向到登录页
    router.push("/login")
    toast.message(`已退出登录`)
  }

  if (authState.isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        login,
        logout,
        getUser,
        setCurrentTenant,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
