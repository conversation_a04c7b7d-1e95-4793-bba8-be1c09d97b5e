"use client"

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

interface JsonFormProps {
  schema: Record<string, any>;
  onSubmit: (data: any) => void;
}

const JsonSchemaForm: React.FC<JsonFormProps> = ({ schema, onSubmit }) => {
  const generateZodSchema = (jsonSchema: Record<string, any>): z.ZodObject<any> => {
    const shape: Record<string, z.ZodTypeAny> = {};
    if (jsonSchema.properties) {
      for (const key in jsonSchema.properties) {
        const prop = jsonSchema.properties[key];
        let zodType: z.ZodTypeAny;

        switch (prop.type) {
          case 'string':
            zodType = z.string();
            if (prop.minLength) {
              if (zodType instanceof z.ZodString) {
                zodType = zodType.min(prop.minLength, `最少${prop.minLength}个字符`);
              }
            }
            if (prop.maxLength) {
              if (zodType instanceof z.ZodString) {
                zodType = zodType.max(prop.maxLength, `最多${prop.maxLength}个字符`);
              }
            }
            if (prop.format === 'email' && zodType instanceof z.ZodString) {
              zodType = zodType.email('无效的邮箱格式');
            }
            if (prop.format === 'uri' && zodType instanceof z.ZodString) {
              zodType = zodType.url('无效的URL格式');
            }
            if (prop.pattern && zodType instanceof z.ZodString) {
              zodType = zodType.regex(new RegExp(prop.pattern), '格式不匹配');
            }
            break;
          case 'integer':
          case 'number':
            zodType = z.number();
            // 由于类型检查问题，需要确保 zodType 是 ZodNumber 类型
            if (prop.minimum && zodType instanceof z.ZodNumber) {
              zodType = zodType.min(prop.minimum, `最小值${prop.minimum}`);
            }
            // 由于类型检查问题，需要确保 zodType 是 ZodNumber 类型
            if (prop.maximum && zodType instanceof z.ZodNumber) {
              zodType = zodType.max(prop.maximum, `最大值${prop.maximum}`);
            }
            break;
          case 'boolean':
            zodType = z.boolean();
            break;
          case 'array':
            zodType = z.array(z.any());
            break;
          case 'object':
            // 未验证输入
            // zodType = z.object({
            //   [key]: z.any()
            // }).transform((value) => JSON.stringify(value));
            zodType = z.any({
              [key]: z.any()
            }).transform((value) => {
              return value;
            })
            break;
          default:
            zodType = z.any();
        }

        if (jsonSchema.required && jsonSchema.required.includes(key)) {
          // 由于 ZodTypeAny 没有 nonempty 方法，根据不同类型使用不同的必填校验方法
          if (zodType instanceof z.ZodString) {
            zodType = zodType.min(1, `${prop.title || key}不能为空`);
          } else if (zodType instanceof z.ZodArray) {
            zodType = zodType.min(1, `${prop.title || key}不能为空`);
          } else {
            zodType = zodType.refine(value => value !== undefined && value !== null, `${prop.title || key}不能为空`);
          }
        } else {
          zodType = zodType.optional();
        }
        shape[key] = zodType;
      }
    }
    return z.object(shape);
  };

  const formSchema = generateZodSchema(schema);
  type FormData = z.infer<typeof formSchema>;

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: Object.keys(schema.properties || {}).reduce((acc, key) => {
      const prop = schema.properties[key];
      if (prop.default !== undefined) {
        acc[key] = prop.default;
      }
      return acc;
    }, {} as Record<string, any>),
  });

  const renderFormField = (key: string, prop: Record<string, any>) => {
    const commonProps = {
      name: key,
      control: form.control,
      render: ({ field }: { field: any }) => (
        <FormItem>
          <FormLabel>{prop.title || key}</FormLabel>
          <FormControl>
            {(() => {
              switch (prop.type) {
                case 'string':
                  if (prop.format === 'password') {
                    return <Input type="password" placeholder={prop.description} {...field} />;
                  } else if (prop.format === 'textarea') {
                    return <Textarea placeholder={prop.description} {...field} />;
                  } else if (prop.enum) {
                    return (
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder={prop.description} />
                        </SelectTrigger>
                        <SelectContent>
                          {prop.enum.map((option: string) => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    );
                  } else {
                    return <Input placeholder={prop.description} {...field} />;
                  }
                case 'integer':
                case 'number':
                  return <Input 
                    type="number" 
                    placeholder={prop.description} 
                    {...field} 
                    value={field.value ?? ''}
                    onChange={e => field.onChange(Number(e.target.value))} 
                  />;
                case 'boolean':
                  return (
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                      <Label htmlFor={key}>{prop.description}</Label>
                    </div>
                  );
                case 'array':
                  return <Input placeholder={prop.description || `Enter comma-separated ${key}`} {...field} value={field.value ? field.value.join(', ') : ''} onChange={e => field.onChange(e.target.value.split(',').map((s: string) => s.trim()))} />;
                case 'object':
                  return (
                    <FormItem>
                      <FormControl>
                        <Textarea
                          placeholder={prop.description || `输入有效的JSON格式数据`}
                          {...field}
                          value={typeof field.value === 'string' ? field.value : JSON.stringify(field.value)}
                          onChange={e => {
                            try {
                              const parsed = JSON.parse(e.target.value)
                              field.onChange(parsed);
                            } catch (err) {
                              // Keep invalid JSON as string to allow editing
                              field.onChange(e.target.value);
                            }
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  );
                default:
                  return <Input placeholder={prop.description} {...field} />;
              }
            })()}
          </FormControl>
          {prop.description && <FormDescription>{prop.description}</FormDescription>}
          <FormMessage />
        </FormItem>
      ),
    };

    return <FormField {...commonProps} key={key} />;
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {schema.properties &&
          Object.keys(schema.properties).map((key) =>
            renderFormField(key, schema.properties[key])
          )}
        <Button type="submit" className='float-right'>提交</Button>
      </form>
    </Form>
  );
};

export default JsonSchemaForm;