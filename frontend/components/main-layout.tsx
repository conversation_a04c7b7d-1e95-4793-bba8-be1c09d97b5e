"use client"

import type React from "react"
import { usePathname, useRouter } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { ModeToggle } from "@/components/mode-toggle"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useMobile } from "@/hooks/use-mobile"
import {
  Home,
  Bot,
  Database,
  Layers,
  Users,
  UserPlus,
  Settings,
  Menu,
  Bell,
  ChevronDown,
  LogOut,
  User,
  Building,
  CircleUser,
  Server,
  Wrench
} from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { ProtectedRoute } from "@/components/protected-route"
import { components } from "@/lib/api/strapi"

type UserTenantRole = components["schemas"]["UserTenantRole"]

interface MainLayoutProps {
  children: React.ReactNode
}
export function MainLayout({ children }: MainLayoutProps) {

  const pathname = usePathname()
  const isMobile = useMobile()
  const router = useRouter()
  const { user, logout, currentTenant, setCurrentTenant } = useAuth()
  const navigation = [
    { name: "首页", href: "/dashboard", icon: Home },
    { name: "Agent管理", href: "/agents", icon: Bot },
    { name: "知识库管理", href: "/knowledge-bases", icon: Database },
    { name: "模型管理", href: "/models", icon: Layers },
    { name: "模型提供商", href: "/providers", icon: Server },
    { name: "工具管理", href: "/tools", icon: Wrench },
    { name: "用户与租户", href: "/users", icon: Users },
    { name: "邀请与角色", href: "/invitations", icon: UserPlus },
    { name: "系统设置", href: "/settings", icon: Settings },
  ]

  const handleTenantChange = (tenant?: UserTenantRole) => {
    setCurrentTenant(tenant)
  }

  const handleUserCenter = () => {
    router.push("/settings")
  }
  const handleLogout = () => {
    logout()
  }

  return (
    <ProtectedRoute>
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar for desktop */}
        {!isMobile && (
          <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 z-80">
            <div className="flex flex-col grow border-r border-border bg-card">
              <div className="flex items-center h-16 shrink-0 px-4 border-b border-border">
                <Link href="/dashboard" className="flex items-center space-x-2">
                  <div className="w-8 h-8 rounded-md bg-primary flex items-center justify-center text-primary-foreground font-bold text-lg">
                    云
                  </div>
                  <span className="text-xl font-semibold">云知问</span>
                </Link>
              </div>
              <div className="flex-1 flex flex-col overflow-y-auto">
                <nav className="flex-1 px-2 py-4 space-y-1">
                  {navigation.map((item) => {
                    const isActive = pathname.startsWith(item.href)
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                          "flex items-center px-3 py-2 text-sm font-medium rounded-md",
                          isActive ? "bg-primary/10 text-primary" : "text-muted-foreground hover:bg-muted",
                        )}
                      >
                        <item.icon className={cn("mr-3 h-5 w-5", isActive ? "text-primary" : "text-muted-foreground")} />
                        {item.name}
                      </Link>
                    )
                  })}
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* Main content */}
        <div className={cn("flex flex-col flex-1", !isMobile && "md:pl-64")}>
          {/* Top navigation */}
          <div className="sticky top-0 z-10 shrink-0 h-16 bg-background border-b border-border">
            <div className="flex items-center justify-between px-4 h-full">
              <div className="flex items-center">
                {isMobile && (
                  <Sheet>
                    <SheetTrigger asChild>
                      <Button variant="ghost" size="icon" className="mr-2">
                        <Menu className="h-5 w-5" />
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="left" className="p-0">
                      <div className="flex items-center h-16 shrink-0 px-4 border-b border-border">
                        <Link href="/dashboard" className="flex items-center space-x-2">
                          <div className="w-8 h-8 rounded-md bg-primary flex items-center justify-center text-primary-foreground font-bold text-lg">
                            云
                          </div>
                          <span className="text-xl font-semibold">云知问</span>
                        </Link>
                      </div>
                      <ScrollArea className="h-[calc(100vh-4rem)]">
                        <nav className="flex-1 px-2 py-4 space-y-1">
                          {navigation.map((item) => {
                            const isActive = pathname.startsWith(item.href)
                            return (
                              <Link
                                key={item.name}
                                href={item.href}
                                className={cn(
                                  "flex items-center px-3 py-2 text-sm font-medium rounded-md",
                                  isActive ? "bg-primary/10 text-primary" : "text-muted-foreground hover:bg-muted",
                                )}
                              >
                                <item.icon
                                  className={cn("mr-3 h-5 w-5", isActive ? "text-primary" : "text-muted-foreground")}
                                />
                                {item.name}
                              </Link>
                            )
                          })}
                        </nav>
                      </ScrollArea>
                    </SheetContent>
                  </Sheet>
                )}

                {isMobile && (
                  <Link href="/dashboard" className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-md bg-primary flex items-center justify-center text-primary-foreground font-bold text-lg">
                      云
                    </div>
                    <span className="text-xl font-semibold">云知问</span>
                  </Link>
                )}

                {/* <div className="ml-4 flex items-center space-x-4">
                  <form role="search" autoComplete="off" onSubmit={(e) => e.preventDefault()} className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input type="search" name="global_search" autoComplete="off" placeholder="全局搜索..." className="pl-8 w-full md:w-[200px] lg:w-[300px]" />
                  </form>
                </div> */}
              </div>

              <div className="flex items-center space-x-4">
                {/* <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="flex items-center space-x-1">
                      <Languages className="h-4 w-4 mr-1" />
                      <span>中文</span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>中文</DropdownMenuItem>
                    <DropdownMenuItem>English</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu> */}

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="relative">
                      <Bell className="h-5 w-5" />
                      {/* <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-destructive"></span> */}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-80">
                    <DropdownMenuLabel>通知</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <div className="max-h-[300px] overflow-y-auto">
                      <div className="p-3 hover:bg-muted rounded-md cursor-pointer">
                        <div className="font-medium">暂无消息</div>
                      </div>
                      {/* <div className="p-3 hover:bg-muted rounded-md cursor-pointer">
                        <div className="font-medium">Agent部署完成</div>
                        <div className="text-sm text-muted-foreground">客服Agent v1.2 已成功部署</div>
                        <div className="text-xs text-muted-foreground mt-1">1小时前</div>
                      </div>
                      <div className="p-3 hover:bg-muted rounded-md cursor-pointer">
                        <div className="font-medium">知识库更新</div>
                        <div className="text-sm text-muted-foreground">产品手册知识库已完成索引构建</div>
                        <div className="text-xs text-muted-foreground mt-1">昨天</div>
                      </div> */}
                    </div>
                    {/* <DropdownMenuSeparator />
                    <div className="p-2 text-center">
                      <Button variant="ghost" size="sm" className="w-full">
                        查看全部通知
                      </Button>
                    </div> */}
                  </DropdownMenuContent>
                </DropdownMenu>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center space-x-2">
                      <div className="flex items-center">
                        <Avatar className="h-8 w-8">
                          <AvatarImage />
                          <AvatarFallback>
                          <CircleUser className="h-8 w-8" />
                          </AvatarFallback>
                        </Avatar>
                        {!isMobile && (
                          <div className="ml-2 text-left">
                            <p className="text-sm font-medium">{user?.display_name ?? '--'}</p>
                            <p className="text-xs text-muted-foreground"></p>
                          </div>
                        )}
                      </div>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>我的账户</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleUserCenter()}>
                      <User className="mr-2 h-4 w-4" />
                      <span>个人资料</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => router.push('/users?tabActive=tenants')}>
                      <Building className="mr-2 h-4 w-4" />
                      <span>租户管理</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => router.push('/settings?tabActive=security')}>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>安全设置</span>
                    </DropdownMenuItem>
                    {/* <DropdownMenuItem>
                      <HelpCircle className="mr-2 h-4 w-4" />
                      <span>帮助中心</span>
                    </DropdownMenuItem> */}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleLogout()} >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>退出登录</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <ModeToggle />

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="flex items-center space-x-1">
                      <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                      <span className="max-w-[150px] truncate">{currentTenant?.tenant?.name}</span>
                      <ChevronDown className="h-4 w-4 ml-1" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>切换租户</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {user?.tenants?.map((item) => (
                      <DropdownMenuItem
                        key={item?.tenant?.id}
                        onClick={() => handleTenantChange(item)}

                        className="flex justify-between"
                      >
                        <span className="truncate">{item?.tenant?.name}</span>
                        <Badge variant="outline" className="ml-2">
                          {item?.role?.key}
                        </Badge>
                      </DropdownMenuItem>
                    ))}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => router.push('/users/create-tenant')}>
                      <span className="w-full text-center">创建新租户</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>

          {/* Main content area */}
          <main className="flex-1 overflow-y-auto bg-muted/30">{children}</main>
        </div>
      </div>
    </ProtectedRoute>
  )
}
