# JsonSchemaForm 使用示例

`JsonSchemaForm` 组件现在支持传入初始值，可以同时用于新增和修改功能。

## API 接口

```typescript
interface JsonFormProps {
  schema: Record<string, any>;
  onSubmit: (data: any) => void;
  initialValues?: Record<string, any>; // 新增的可选属性
}
```

## 使用示例

### 1. 新增模式（无初始值）

```tsx
<JsonSchemaForm 
  schema={schema} 
  onSubmit={handleSubmit} 
/>
```

### 2. 编辑模式（带初始值）

```tsx
const initialValues = {
  api_key: "existing-api-key",
  base_url: "https://existing-url.com",
  model: "gpt-4"
};

<JsonSchemaForm 
  schema={schema} 
  onSubmit={handleSubmit} 
  initialValues={initialValues}
/>
```

### 3. 完整的新增/编辑页面示例

```tsx
export default function ProviderPage() {
  const searchParams = useSearchParams();
  const isEditMode = searchParams.get('mode') === 'edit';
  const [initialFormValues, setInitialFormValues] = useState();
  
  useEffect(() => {
    if (isEditMode) {
      // 加载现有数据
      loadExistingData().then(data => {
        setInitialFormValues(data.credentials);
      });
    }
  }, [isEditMode]);

  return (
    <JsonSchemaForm 
      schema={schema} 
      onSubmit={handleSubmit} 
      initialValues={initialFormValues}
    />
  );
}
```

## 优先级规则

1. **initialValues** - 最高优先级，用于编辑模式
2. **schema.properties[key].default** - 次优先级，用于 schema 定义的默认值
3. **undefined** - 如果以上都没有，字段为空

## URL 参数约定

- 新增模式: `/providers/set`
- 编辑模式: `/providers/set?mode=edit&providerId=123`

这样可以通过 URL 参数来区分新增和编辑模式，并传递相应的数据 ID。
