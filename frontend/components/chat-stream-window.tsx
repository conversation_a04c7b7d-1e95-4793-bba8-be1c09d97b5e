"use client"

import { useState, useEffect, useRef } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { chatStreamService } from "@/lib/api/chat-stream"
import {
  Send,
  Bot,
  User,
  Settings,
  Loader2,
  Copy,
  Trash2,
  MessageSquare,
  Clock,
  Zap,
  CheckCircle,
  XCircle,
} from "lucide-react"
import { components } from "@/lib/api/strapi"
import { useAuth } from "@/contexts/auth-context"
import { apiClient } from "@/lib/api/api"
import ReactMarkdown from "react-markdown"
// @ts-ignore
import remarkMath from 'remark-math'
// @ts-ignore
import rehypeKatex from 'rehype-katex'
import 'katex/dist/katex.min.css'
import { Form, FormField, FormControl, FormMessage } from "@/components/ui/form"
import dayjs from "dayjs"

// 表单验证
const messageSchema = z.object({
  message: z.string().min(1, "消息不能为空").max(1000, "消息不能超过1000个字符"),
})

type MessageFormData = z.infer<typeof messageSchema>

// 组件Props
interface ChatStreamWindowProps {
  agentId: string
  threadId?: string
  className?: string
}

// 聊天消息类型
interface ChatMessage {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: string
  isStreaming?: boolean
  metadata?: any
}

// Agent信息类型
type Agent = components["schemas"]["Agent"]
export function ChatStreamWindow({ agentId, threadId = "default", className }: ChatStreamWindowProps) {
  const { toast } = useToast()
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [agent, setAgent] = useState<Agent | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)
  const [isStreaming, setIsStreaming] = useState(false)
  const [_, setCurrentStreamingId] = useState<string | null>(null)
  const { currentTenant } = useAuth()
  const form = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      message: ''
    }
  })

  const getAgent = async () => {
    try {
      const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents/{agent_id}`, {
        params: {
          path: {
            tenant_id: currentTenant?.tenant_id ?? '',
            agent_id: agentId,
          }
        }
      })

      if (error) {
        console.log(error)
      } else if (data) {
        setAgent(data)
        setMessages([
          {
            id: "welcome",
            role: "assistant",
            content: `你好！我是 ${data.name}。${data.description || "很高兴为您服务！"}`,
            timestamp: new Date().toISOString(),
          },
        ])
      }
    } catch (err) {
      //
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (currentTenant?.tenant_id && agentId) {
      getAgent()
    }
  }, [currentTenant?.tenant_id, agentId])

  // 自动滚动到底部
  useEffect(() => {
    // messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  // 发送消息
  const onSubmit = async (data: MessageFormData) => {
    if (!agent || isSending || isStreaming) return

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      role: "user",
      content: data.message,
      timestamp: new Date().toISOString(),
    }

    setMessages((prev) => [...prev, userMessage])
    form.reset()
    setIsSending(true)
    setIsStreaming(true)

    // 创建助手消息占位符
    const assistantMessageId = `assistant_${Date.now()}`
    setCurrentStreamingId(assistantMessageId)

    const assistantMessage: ChatMessage = {
      id: assistantMessageId,
      role: "assistant",
      content: "",
      timestamp: new Date().toISOString(),
      isStreaming: true,
    }

    setMessages((prev) => [...prev, assistantMessage])

    try {
      let accumulatedContent = ""

      await chatStreamService.sendMessage(
        {
          message: data.message,
          thread_id: threadId,
          tenant_id: currentTenant?.tenant_id ?? '',
          agent_id: agentId,
        },
        // onMessage
        (sseMessage) => {
          if (sseMessage.type === "content") {
            // 累积内容
            accumulatedContent += sseMessage.data

            // 更新消息内容
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === assistantMessageId
                  ? {
                    ...msg,
                    content: accumulatedContent,
                    metadata: sseMessage.metadata,
                  }
                  : msg,
              ),
            )
          }
        },
        // onError
        (_error) => {
          toast("发送消息失败,请稍后重试")

          // 移除失败的消息
          setMessages((prev) => prev.filter((msg) => msg.id !== assistantMessageId))
        },
        // onComplete
        () => {
          // 标记流式传输完成
          setMessages((prev) =>
            prev.map((msg) => (msg.id === assistantMessageId ? { ...msg, isStreaming: false } : msg)),
          )
        },
      )
    } catch (error) {
      toast("发送消息失败,请稍后重试")

      // 移除失败的消息
      setMessages((prev) => prev.filter((msg) => msg.id !== assistantMessageId))
    } finally {
      setIsSending(false)
      setIsStreaming(false)
      setCurrentStreamingId(null)
    }
  }

  // 复制消息
  const copyMessage = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      toast("消息内容已复制到剪贴板")
    } catch (error) {
      toast("无法复制到剪贴板")
    }
  }

  // 清空对话
  const clearChat = () => {
    if (agent) {
      setMessages([
        {
          id: "welcome",
          role: "assistant",
          content: `你好！我是 ${agent.name}。${agent.description || "很高兴为您服务！"}`,
          timestamp: new Date().toISOString(),
        },
      ])
    }
  }

  // 获取状态徽章
  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? "default" : "secondary"} className={isActive ? "bg-green-100 text-green-800" : ""}>
        {isActive ? (
          <>
            <CheckCircle className="w-3 h-3 mr-1" />
            活跃
          </>
        ) : (
          <>
            <XCircle className="w-3 h-3 mr-1" />
            非活跃
          </>
        )}
      </Badge>
    )
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>加载Agent信息中...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!agent) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Agent 不可用</h3>
            <p className="text-muted-foreground">无法加载Agent信息，请检查配置</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)] ${className}`}>
      {/* 聊天区域 */}
      <div className="lg:col-span-3 flex flex-col">
        <Card className="flex-1 flex flex-col">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={agent.icon_access_url || undefined} alt={agent.name} />
                <AvatarFallback>
                  <Bot className="h-5 w-5" />
                </AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="flex items-center gap-2">
                  与 {agent.name} 对话
                  {agent.is_supervisor && <Badge variant="outline">监督者</Badge>}
                </CardTitle>
                <CardDescription className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  已对话 {messages.length - 1} 轮 • 线程: {threadId}
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={clearChat} disabled={isStreaming}>
                <Trash2 className="mr-2 h-4 w-4" />
                清空
              </Button>
              {getStatusBadge(agent.is_active)}
            </div>
          </CardHeader>

          <CardContent className="flex-1 flex flex-col p-0">
            {/* 消息列表 */}
            <ScrollArea className="flex-1 px-6">
              <div className="space-y-4 py-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${message.role === "user" ? "justify-end" : "justify-start"}`}
                  >
                    {message.role === "assistant" && (
                      <div className="flex-shrink-0">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={agent.icon_access_url || undefined} alt={agent.name} />
                          <AvatarFallback>
                            <Bot className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                      </div>
                    )}

                    <div
                      className={`max-w-[80%] rounded-lg px-4 py-3 ${message.role === "user" ? "bg-primary text-primary-foreground" : "bg-muted border border-border"
                        }`}
                    >
                      <div className="break-words whitespace-pre-line">
                        {message.role === "user" ? (
                          message.content
                        ) : (
                          <ReactMarkdown
                            remarkPlugins={[remarkMath]}
                            rehypePlugins={[rehypeKatex]}
                            components={{
                              math: ({node, ...props}: {node?: any; children?: React.ReactNode}) => (
                                <div className="math-formula block bg-gray-100 p-2 my-1 rounded">
                                  {props.children}
                                </div>
                              ),
                              inlineMath: ({node, ...props}: {node?: any; children?: React.ReactNode}) => (
                                <span className="math-formula inline-block bg-gray-100 px-1 rounded">
                                  {props.children}
                                </span>
                              )
                            } as any}
                          >
                            {message.content}
                          </ReactMarkdown>
                        )}
                        {message.isStreaming && <span className="inline-block w-2 h-5 bg-current animate-pulse ml-1" />}
                      </div>

                      <div className="flex items-center justify-between mt-2 text-xs opacity-70">
                        <div className="flex items-center gap-2">
                          <Clock className="h-3 w-3" />
                          <span>{dayjs(message.timestamp).format('YYYY-MM-DD HH:mm:ss')}</span>
                          {message.metadata && (
                            <>
                              <Separator orientation="vertical" className="h-3" />
                              <Zap className="h-3 w-3" />
                              <span>{message.metadata.model}</span>
                            </>
                          )}
                        </div>
                        {message.role === "assistant" && !message.isStreaming && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-auto p-1 opacity-50 hover:opacity-100"
                            onClick={() => copyMessage(message.content)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>

                    {message.role === "user" && (
                      <div className="flex-shrink-0">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            <User className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                      </div>
                    )}
                  </div>
                ))}

                {isSending && !isStreaming && (
                  <div className="flex gap-3 justify-start">
                    <div className="flex-shrink-0">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={agent.icon_access_url || undefined} alt={agent.name} />
                        <AvatarFallback>
                          <Bot className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                    </div>
                    <div className="bg-muted border border-border rounded-lg px-4 py-3">
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>正在思考...</span>
                      </div>
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* 输入区域 */}
            <div className="border-t p-4">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="flex gap-2">
                  <FormField
                    control={form.control}
                    name="message"
                    render={({ field }) => (
                      <>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="输入您的消息..."
                            disabled={isStreaming || isSending}
                            className="flex-1"
                            autoComplete="off"
                          />
                        </FormControl>
                        <FormMessage />
                      </>
                    )}
                  />
                  <Button type="submit" disabled={isStreaming || isSending}>
                    {isStreaming || isSending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>

                </form>
              </Form>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Agent信息侧边栏 */}
      <div className="space-y-6">
        {/* Agent基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Agent 信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={agent.icon_access_url || undefined} alt={agent.name} />
                <AvatarFallback>
                  <Bot className="h-6 w-6" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="font-semibold">{agent.name}</h3>
                <p className="text-sm text-muted-foreground">{agent.agent_type || "通用助手"}</p>
              </div>
            </div>

            {agent.description && (
              <div>
                <label className="text-sm font-medium">描述</label>
                <p className="text-sm text-muted-foreground mt-1">{agent.description}</p>
              </div>
            )}

            <Separator />

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">状态</span>
                {getStatusBadge(agent.is_active)}
              </div>

              <div className="flex justify-between">
                <span className="text-sm w-20 flex-none">Agent ID</span>
                <span className="text-sm font-mono text-muted-foreground break-all">{agent.id}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-sm w-20 flex-none">Open ID</span>
                <span className="text-sm font-mono text-muted-foreground break-all">{agent.open_id}</span>
              </div>

              {agent.model_id && (
                <div className="flex justify-between">
                  <span className="text-sm">模型</span>
                  <Badge variant="outline" className="text-xs">
                    {agent.model_id}
                  </Badge>
                </div>
              )}

              <div className="flex justify-between">
                <span className="text-sm">创建时间</span>
                <span className="text-sm text-muted-foreground">
                  {dayjs(agent.created_at).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-sm">更新时间</span>
                <span className="text-sm text-muted-foreground">
                  {dayjs(agent.updated_at).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 配置信息 */}
        {agent.config && Object.keys(agent.config).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                配置信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(agent.config).map(([key, value]) => (
                  <div key={key} className="justify-between">
                    <div className="text-sm">{key}</div>
                    <span className="text-sm text-muted-foreground break-all">
                      {typeof value === "object" ? JSON.stringify(value) : String(value)}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 系统提示 */}
        {agent.prompt && (
          <Card>
            <CardHeader>
              <CardTitle>系统提示</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">{agent.prompt}</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
