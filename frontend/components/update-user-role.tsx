"use client"
import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Save } from "lucide-react"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { apiClient } from "@/lib/api/api"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useRouter } from 'next/navigation'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Edit } from "lucide-react"
import { components } from "@/lib/api/strapi"
import { userR<PERSON>K<PERSON> } from "@/lib/options"
import { useAuth } from "@/contexts/auth-context"

type IUser =  components["schemas"]["User"]

export function UpdateUserRole({ userId, getUserData }: { userId: string; getUserData: Function }) {
  const router = useRouter()
  const { currentTenant } = useAuth()
  const [roleKey, setRoleKey] = useState<string>('member')
  const [userInfo, setUserInfo] = useState<IUser>()
  const [_, setTenantId] = useState<string>(currentTenant?.tenant_id ?? '')
  const formSchema = z.object({
    role_key: z.string().min(2, {
      message: "请选择角色",
    }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      role_key: roleKey,
    },
  })

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    postData(values)
  }
  const postData = async (values: { role_key: string; }) => {
    const { error } = await apiClient.PUT(`/api/v1/tenants/{tenant_id}/users/{user_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          user_id: userId,
        }
      },
      body: {
        role_key: values.role_key,
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      getUserData()
      router.push('/users')
      useToast().toast.success("提交成功", {
        description: `修改用户角色成功`
      })
    }
  }
  const getUserDetail = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/users/{user_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          user_id: userId,
        }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      setRoleKey(data.role_key)
      setUserInfo(data)
    }
  }
  useEffect(() => {
    console.log('userId', userId)
    setTenantId(currentTenant?.tenant_id?? '')
    getUserDetail()
  }, [currentTenant, userId])
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="w-full">
          <Edit className="mr-2 h-4 w-4" />
          <span>编辑</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>更新用户角色</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <Card>
          <CardContent className="px-5">
            <div className="grid grid-cols-1 gap-4 text-sm mb-5">
              <div>
                <p className="text-muted-foreground">用户名</p>
                <p>{ userInfo?.username }</p>
              </div>
              <div>
                <p className="text-muted-foreground">邮箱</p>
                <p>{ userInfo?.email }</p>
              </div>
            </div>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                <FormField
                  control={form.control}
                  name="role_key"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>角色信息</FormLabel>
                      <FormControl>
                        <Select {...field} value={roleKey} onValueChange={(value: string) => {
                          field.onChange(value)
                          setRoleKey(value)
                          }}>
                          <SelectTrigger id="role_key">
                            <SelectValue placeholder="选择类型" />
                          </SelectTrigger>
                          <SelectContent>
                            { Object.entries(userRoleKey).map(([key, value]) => (
                              <SelectItem value={key} key={key}>{value}</SelectItem>
                            )) }
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </CardContent>
        </Card>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="secondary">
              关闭
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button type="submit" onClick={() => onSubmit(form.getValues())}>
              <Save className="mr-2 h-4 w-4" />
              提交
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
  </Dialog>)
}
