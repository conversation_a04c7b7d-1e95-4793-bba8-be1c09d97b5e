"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"

import {
    Form,
    FormField,
    FormItem,
    FormLabel,
    FormControl,
    FormMessage,
} from "@/components/ui/form"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/hooks/use-toast"
import {
    Eye,
    EyeOff,
    Loader2,
    CheckCircle,
    XCircle,
    User,
    Mail,
    Lock,
    UserCheck,
    ArrowLeft,
    Shield,
} from "lucide-react"
import Link from "next/link"
import { RegisterFormData, registerSchema } from "@/lib/validations/auth"
import { apiClient } from "@/lib/api/api"

// 密码强度计算
const calculatePasswordStrength = (password: string): number => {
    let strength = 0
    if (password.length >= 8) strength += 25
    if (/[a-z]/.test(password)) strength += 25
    if (/[A-Z]/.test(password)) strength += 25
    if (/\d/.test(password)) strength += 25
    return strength
}

// 密码强度颜色
const getPasswordStrengthColor = (strength: number): string => {
    if (strength < 25) return "bg-red-500"
    if (strength < 50) return "bg-orange-500"
    if (strength < 75) return "bg-yellow-500"
    return "bg-green-500"
}

// 密码强度文本
const getPasswordStrengthText = (strength: number): string => {
    if (strength < 25) return "弱"
    if (strength < 50) return "一般"
    if (strength < 75) return "良好"
    return "强"
}

export default function RegisterClientPage() {
    const router = useRouter()
    const { toast } = useToast()
    const [loading, setLoading] = useState(false)
    const [showPassword, setShowPassword] = useState(false)
    const [showConfirmPassword, setShowConfirmPassword] = useState(false)
    const [usernameChecking, setUsernameChecking] = useState(false)
    const [emailChecking, setEmailChecking] = useState(false)
    const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(null)
    const [emailAvailable, setEmailAvailable] = useState<boolean | null>(null)

    const form = useForm<RegisterFormData>({
        resolver: zodResolver(registerSchema),
        mode: "onChange",
        defaultValues: {
            agree_to_terms: false,
            display_name: "",
            email: "",
            password: "",
            username: "",  
            confirm_password: "",
            invitation_token: ""   
        },
    })

    const { handleSubmit, watch, formState: { errors, isValid } } = form

    const watchedValues = watch()
    const passwordStrength = calculatePasswordStrength(watchedValues.password || "")

    // 检查用户名可用性
    const checkUsername = async (username: string) => {
        if (username.length < 3) {
            setUsernameAvailable(null)
            return
        }
        setUsernameChecking(true)
    }

    // 检查邮箱可用性
    const checkEmail = async (email: string) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(email)) {
            setEmailAvailable(null)
            return
        }
        setEmailChecking(true)
    }

    // 提交注册
    const onSubmit = async (registerData: RegisterFormData) => {
        try {
            setLoading(true)
            const { data, error } = await apiClient.POST("/api/v1/auth/register", {
                body: {
                    username: registerData.username,
                    email: registerData.email,
                    password: registerData.password,
                    display_name: registerData.display_name,
                    invitation_token: registerData.invitation_token,
                }
            });
            if (error) {
                toast("注册失败")
            }
            else if (data) {
                toast("注册成功")
                router.push("/login?registered=true")
            }
        } catch (error) {
            toast("注册失败")
        } finally {
            setLoading(false)
        }
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">

                {/* 注册表单 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-center">注册信息</CardTitle>
                        <CardDescription className="text-center">请填写以下信息创建您的账户</CardDescription>
                    </CardHeader>

                    <Form {...form}>
                        <form onSubmit={handleSubmit(onSubmit)}>
                            <CardContent className="space-y-6">
                                {/* 用户名 */}
                                <FormField
                                    control={form.control}
                                    name="username"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <User className="h-4 w-4" />
                                                用户名 *
                                            </FormLabel>
                                            <FormControl>
                                                <div className="relative">
                                                    <Input
                                                        {...field}
                                                        id="username"
                                                        autoComplete="username"
                                                        placeholder="请输入用户名"
                                                        className={errors.username ? "border-red-500" : ""}
                                                        onChange={e => {
                                                            field.onChange(e)
                                                            checkUsername(e.target.value)
                                                        }}
                                                    />
                                                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                                        {usernameChecking ? (
                                                            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                                                        ) : usernameAvailable === true ? (
                                                            <CheckCircle className="h-4 w-4 text-green-500" />
                                                        ) : usernameAvailable === false ? (
                                                            <XCircle className="h-4 w-4 text-red-500" />
                                                        ) : null}
                                                    </div>
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                            {usernameAvailable === false && <p className="text-sm text-red-500">该用户名已被使用</p>}
                                            {usernameAvailable === true && <p className="text-sm text-green-600">用户名可用</p>}
                                        </FormItem>
                                    )}
                                />

                                {/* 邮箱 */}
                                <FormField
                                    control={form.control}
                                    name="email"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <Mail className="h-4 w-4" />
                                                邮箱地址 *
                                            </FormLabel>
                                            <FormControl>
                                                <div className="relative">
                                                    <Input
                                                        {...field}
                                                        id="email"
                                                        autoComplete="email"
                                                        type="email"
                                                        placeholder="请输入邮箱地址"
                                                        className={errors.email ? "border-red-500" : ""}
                                                        onChange={e => {
                                                            field.onChange(e)
                                                            checkEmail(e.target.value)
                                                        }}
                                                    />
                                                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                                        {emailChecking ? (
                                                            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                                                        ) : emailAvailable === true ? (
                                                            <CheckCircle className="h-4 w-4 text-green-500" />
                                                        ) : emailAvailable === false ? (
                                                            <XCircle className="h-4 w-4 text-red-500" />
                                                        ) : null}
                                                    </div>
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                            {emailAvailable === false && <p className="text-sm text-red-500">该邮箱已被注册</p>}
                                            {emailAvailable === true && <p className="text-sm text-green-600">邮箱可用</p>}
                                        </FormItem>
                                    )}
                                />

                                {/* 显示名称 */}
                                <FormField
                                    control={form.control}
                                    name="display_name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <UserCheck className="h-4 w-4" />
                                                显示名称
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    id="display_name"
                                                    placeholder="请输入显示名称"
                                                    className={errors.display_name ? "border-red-500" : ""}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* 密码 */}
                                <FormField
                                    control={form.control}
                                    name="password"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <Lock className="h-4 w-4" />
                                                密码 *
                                            </FormLabel>
                                            <FormControl>
                                                <div className="relative">
                                                    <Input
                                                        {...field}
                                                        id="password"
                                                        autoComplete="new-password"
                                                        type={showPassword ? "text" : "password"}
                                                        placeholder="请输入密码"
                                                        className={errors.password ? "border-red-500" : ""}
                                                    />
                                                    <Button
                                                        type="button"
                                                        variant="ghost"
                                                        size="icon"
                                                        className="absolute right-0 top-0 h-full px-3"
                                                        onClick={() => setShowPassword(!showPassword)}
                                                    >
                                                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                                    </Button>
                                                </div>
                                            </FormControl>
                                            {/* 密码强度指示器 */}
                                            {watchedValues.password && (
                                                <div className="space-y-2">
                                                    <div className="flex items-center justify-between text-sm">
                                                        <span>密码强度</span>
                                                        <span
                                                            className={`font-medium ${passwordStrength >= 75 ? "text-green-600" : passwordStrength >= 50 ? "text-yellow-600" : "text-red-600"}`}
                                                        >
                                                            {getPasswordStrengthText(passwordStrength)}
                                                        </span>
                                                    </div>
                                                    <Progress value={passwordStrength} className="h-2">
                                                        <div
                                                            className={`h-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength)}`}
                                                            style={{ width: `${passwordStrength}%` }}
                                                        />
                                                    </Progress>
                                                </div>
                                            )}
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* 确认密码 */}
                                <FormField
                                    control={form.control}
                                    name="confirm_password"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <Shield className="h-4 w-4" />
                                                确认密码 *
                                            </FormLabel>
                                            <FormControl>
                                                <div className="relative">
                                                    <Input
                                                        {...field}
                                                        id="confirm_password"
                                                        autoComplete="off"
                                                        type={showConfirmPassword ? "text" : "password"}
                                                        placeholder="请再次输入密码"
                                                        className={errors.confirm_password ? "border-red-500" : ""}
                                                    />
                                                    <Button
                                                        type="button"
                                                        variant="ghost"
                                                        size="icon"
                                                        className="absolute right-0 top-0 h-full px-3"
                                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                                    >
                                                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                                    </Button>
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* 服务条款 */}
                                <FormField
                                    control={form.control}
                                    name="agree_to_terms"
                                    render={({ field }) => (
                                        <FormItem>
                                            <div className="flex items-start space-x-3">
                                                <FormControl>
                                                    <Checkbox
                                                        checked={field.value}
                                                        onCheckedChange={field.onChange}
                                                        className={errors.agree_to_terms ? "border-red-500" : ""}
                                                    />
                                                </FormControl>
                                                <div className="grid gap-1.5 leading-none">
                                                    <FormLabel className="text-sm font-normal cursor-pointer">
                                                        我已阅读并同意{" "}
                                                        <Link href="/terms" className="text-primary hover:underline">
                                                            服务条款
                                                        </Link>{" "}
                                                        和{" "}
                                                        <Link href="/privacy" className="text-primary hover:underline">
                                                            隐私政策
                                                        </Link>
                                                    </FormLabel>
                                                </div>
                                            </div>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </CardContent>

                            <CardFooter className="flex flex-col space-y-4 py-4">
                                <Button
                                    type="submit"
                                    className="w-full"
                                    disabled={loading || !isValid || usernameAvailable === false || emailAvailable === false}
                                >
                                    {loading ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            注册中...
                                        </>
                                    ) : (
                                        <>
                                            <UserCheck className="mr-2 h-4 w-4" />
                                            创建账户
                                        </>
                                    )}
                                </Button>

                                <div className="text-center text-sm text-gray-600">
                                    已有账户？{" "}
                                    <Link href="/login" className="text-primary hover:underline font-medium">
                                        立即登录
                                    </Link>
                                </div>
                            </CardFooter>
                        </form>
                    </Form>
                </Card>

                {/* 返回首页 */}
                <div className="text-center">
                    <Link
                        href="/"
                        className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 transition-colors"
                    >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        返回首页
                    </Link>
                </div>
            </div>
        </div>
    )
}
