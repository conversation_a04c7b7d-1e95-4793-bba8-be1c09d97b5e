import type { Metada<PERSON> } from "next"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Workflow, ArrowLeft, Save, Play, Plus, Bot, Database, Lightbulb, Search, MessageSquare } from "lucide-react"

export const metadata: Metadata = {
  title: "Agent工作流 | 云知问",
  description: "云知问多租户智能Agent平台Agent工作流",
}

export default function WorkflowPage() {
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href="/agents">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Agent工作流</h1>
            <p className="text-muted-foreground">设计多Agent协作流程</p>
          </div>
          <div className="ml-auto flex space-x-2">
            <Button variant="outline">
              <Save className="mr-2 h-4 w-4" />
              保存
            </Button>
            <Button>
              <Play className="mr-2 h-4 w-4" />
              测试
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-[300px_1fr]">
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Agent组件</CardTitle>
                <CardDescription>拖拽组件到工作流画布</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="p-3 border rounded-md cursor-move hover:bg-muted transition-colors">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Lightbulb className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">意图分析Agent</p>
                      <p className="text-xs text-muted-foreground">分析用户意图</p>
                    </div>
                  </div>
                </div>
                <div className="p-3 border rounded-md cursor-move hover:bg-muted transition-colors">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Bot className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">客服Agent</p>
                      <p className="text-xs text-muted-foreground">处理客户咨询</p>
                    </div>
                  </div>
                </div>
                <div className="p-3 border rounded-md cursor-move hover:bg-muted transition-colors">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Database className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">知识库Agent</p>
                      <p className="text-xs text-muted-foreground">检索知识库</p>
                    </div>
                  </div>
                </div>
                <div className="p-3 border rounded-md cursor-move hover:bg-muted transition-colors">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Search className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">检索Agent</p>
                      <p className="text-xs text-muted-foreground">高级文档检索</p>
                    </div>
                  </div>
                </div>
                <div className="p-3 border rounded-md cursor-move hover:bg-muted transition-colors">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <MessageSquare className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">对话Agent</p>
                      <p className="text-xs text-muted-foreground">生成回复</p>
                    </div>
                  </div>
                </div>
                <Button variant="outline" className="w-full">
                  <Plus className="mr-2 h-4 w-4" />
                  更多组件
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">工作流属性</CardTitle>
                <CardDescription>配置工作流参数</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-sm space-y-4">
                  <div>
                    <p className="font-medium">名称</p>
                    <p className="text-muted-foreground">客服工作流</p>
                  </div>
                  <div>
                    <p className="font-medium">描述</p>
                    <p className="text-muted-foreground">客服Agent工作流，包含意图分析和知识库检索</p>
                  </div>
                  <div>
                    <p className="font-medium">节点数</p>
                    <p className="text-muted-foreground">5个</p>
                  </div>
                  <div>
                    <p className="font-medium">创建时间</p>
                    <p className="text-muted-foreground">2023-05-15</p>
                  </div>
                  <div>
                    <p className="font-medium">最后修改</p>
                    <p className="text-muted-foreground">2023-05-20</p>
                  </div>
                  <Button variant="outline" size="sm" className="w-full">
                    编辑属性
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardContent className="p-6">
              <div className="h-[600px] border-2 border-dashed rounded-md flex items-center justify-center bg-muted/30">
                <div className="text-center">
                  <Workflow className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-lg font-semibold">Agent工作流设计器</h3>
                  <p className="text-sm text-muted-foreground max-w-md mx-auto mt-1">
                    这里将显示Agent工作流设计器，您可以通过拖拽组件创建多Agent协作流程，设置节点间的连接和数据流转。
                  </p>
                  <Button className="mt-4">
                    <Plus className="mr-2 h-4 w-4" />
                    添加起始节点
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
