"use client"
import { useEffect, useState } from "react"
import { use<PERSON><PERSON>, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { User, <PERSON>, Save, <PERSON>ader2, <PERSON><PERSON><PERSON> } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/hooks/use-toast"
import { apiClient } from "@/lib/api/api"
import { useSearchParams } from 'next/navigation'
import { profileSchema, ProfileSchema } from "@/lib/validations/auth"


export default function SettingsPage() {
  const urlParams = useSearchParams();
  const active = urlParams.get('tabActive');
  useEffect(() => {
    document.title = "系统设置 | 云知问"
    setTabActive(active || "profile")
  }, [])
  const { user, getUser } = useAuth()
  const [ tabActive, setTabActive] = useState("profile")
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [profileIsSubmitting, setProfileIsSubmitting] = useState(false)

  const handleChangePassword = async () => {
    if (newPassword !== confirmPassword) {
      useToast().toast.info('新密码和确认密码不一致');
      return;
    }

    setIsSubmitting(true);
    try {
      const { error } = await apiClient.POST(`/api/v1/users/me/change-password`, {
        body: {
          current_password: currentPassword,
          new_password: newPassword,
        }
      });
      if (error) {
        console.log('error', error)
      } else {
          setCurrentPassword("");
          setNewPassword("");
          setConfirmPassword("");
          useToast().toast.success('密码已成功更新');
      } 
    } catch (error) {
      console.log(error)
      useToast().toast.error('密码更新失败');
    } finally {
      setIsSubmitting(false);
    }
  }

  // 初始化表单
  const profileForm = useForm<ProfileSchema>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      display_name: user?.display_name || user?.username || "",
      email: user?.email || "",
      // title: "",
      // phone: "",
      // bio: "",
    },
  })

  // 个人资料提交处理
  const onProfileSubmit = async (data: ProfileSchema) => {
    setProfileIsSubmitting(true)
    try {
      const { error } = await apiClient.PUT(`/api/v1/users/me`, {
        body: {
          username: user?.username,
          display_name: data.display_name,
          is_active: true,
          email: data.email
        }
      })
      if(!error){
        useToast().toast.success('个人信息已成功更新')
        getUser()
      }
    } catch (error) {
      console.log(error)
    } finally {
      setProfileIsSubmitting(false)
    }
  }
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">系统设置</h1>
          <p className="text-muted-foreground">管理系统配置和个人偏好</p>
        </div>

        <Tabs value={tabActive} onValueChange={e => setTabActive(e)} className="space-y-4">
          <div className="flex overflow-x-auto pb-2">
            <TabsList className="h-auto p-0 bg-transparent space-x-2">
              <TabsTrigger
                value="profile"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <User className="h-4 w-4 mr-2" />
                个人资料
              </TabsTrigger>
              {/* <TabsTrigger
                value="account"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <Building className="h-4 w-4 mr-2" />
                账户设置
              </TabsTrigger>
              <TabsTrigger
                value="api"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <Key className="h-4 w-4 mr-2" />
                API密钥
              </TabsTrigger>
              <TabsTrigger
                value="notifications"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <Bell className="h-4 w-4 mr-2" />
                通知设置
              </TabsTrigger>
              <TabsTrigger
                value="language"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <Languages className="h-4 w-4 mr-2" />
                语言与区域
              </TabsTrigger> */}
              <TabsTrigger
                value="security"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <Shield className="h-4 w-4 mr-2" />
                安全设置
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="profile" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>个人资料</CardTitle>
                <CardDescription>管理您的个人信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormProvider {...profileForm}>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>头像</Label>
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-20 w-20">
                        <AvatarImage/>
                        <AvatarFallback>
                        <CircleUser className="h-8 w-8"/>
                        </AvatarFallback>
                      </Avatar>
                      {/* <Button variant="outline">
                        <Upload className="mr-2 h-4 w-4" />
                        上传新头像
                      </Button> */}
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <FormField
                      control={profileForm.control}
                      name="display_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>姓名</FormLabel>
                          <FormControl>
                            <Input placeholder="请输入您的姓名" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="space-y-2">
                      <Label htmlFor="email">邮箱</Label>
                      <FormField
                        control={profileForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input id="email" type="email" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* <FormField
                      control={profileForm.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>职位</FormLabel>
                          <FormControl>
                            <Input placeholder="您的职位" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    /> */}

                    {/* <FormField
                      control={profileForm.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>电话</FormLabel>
                          <FormControl>
                            <Input placeholder="您的电话号码" type="tel" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    /> */}
                  </div>

                  {/* <FormField
                    control={profileForm.control}
                    name="bio"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>个人简介</FormLabel>
                        <FormControl>
                          <Input placeholder="您的个人简介" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  /> */}
                </div>

                <Separator />

                <div className="flex justify-end">
                  <Button
                    onClick={profileForm.handleSubmit(onProfileSubmit)}
                    disabled={profileIsSubmitting}
                  >
                    {profileIsSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        保存中...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        保存更改
                      </>
                    )}
                  </Button>
                </div>
                </FormProvider>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="account" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>账户设置</CardTitle>
                <CardDescription>管理您的账户偏好设置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>默认租户</Label>
                    <Select defaultValue={user?.tenants?.[0]?.tenant?.id as string}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择默认租户" />
                      </SelectTrigger>
                      <SelectContent>
                        {user?.tenants?.map((item) => (
                          <SelectItem key={item?.tenant?.id} value={item?.tenant?.id as string}>
                            {item?.tenant?.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">登录后默认进入的租户空间</p>
                  </div>

                  <div className="space-y-2">
                    <Label>默认页面</Label>
                    <Select defaultValue="dashboard">
                      <SelectTrigger>
                        <SelectValue placeholder="选择默认页面" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="dashboard">仪表盘</SelectItem>
                        <SelectItem value="agents">Agent管理</SelectItem>
                        <SelectItem value="knowledge-bases">知识库管理</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">登录后默认显示的页面</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="dark-mode">深色模式</Label>
                      <p className="text-sm text-muted-foreground">启用深色模式界面</p>
                    </div>
                    <Switch id="dark-mode" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="compact-view">紧凑视图</Label>
                      <p className="text-sm text-muted-foreground">使用更紧凑的界面布局</p>
                    </div>
                    <Switch id="compact-view" />
                  </div>
                </div>

                <Separator />

                <div className="flex justify-end">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存更改
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="api" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>API密钥</CardTitle>
                <CardDescription>管理您的API密钥，用于访问云知问API</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>API密钥</Label>
                    <div className="flex space-x-2">
                      <Input value="••••••••••••••••••••••••••••••" readOnly />
                      <Button variant="outline">复制</Button>
                      <Button variant="outline">重新生成</Button>
                    </div>
                    <p className="text-xs text-muted-foreground">此密钥用于API访问，请妥善保管</p>
                  </div>

                  <div className="space-y-2">
                    <Label>API访问权限</Label>
                    <Select defaultValue="read-write">
                      <SelectTrigger>
                        <SelectValue placeholder="选择访问权限" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="read-only">只读</SelectItem>
                        <SelectItem value="read-write">读写</SelectItem>
                        <SelectItem value="full-access">完全访问</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>API使用限制</Label>
                    <Select defaultValue="no-limit">
                      <SelectTrigger>
                        <SelectValue placeholder="选择使用限制" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="no-limit">无限制</SelectItem>
                        <SelectItem value="1000-per-day">每天1000次</SelectItem>
                        <SelectItem value="5000-per-day">每天5000次</SelectItem>
                        <SelectItem value="10000-per-day">每天10000次</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="api-logging">API调用日志</Label>
                      <p className="text-sm text-muted-foreground">记录所有API调用</p>
                    </div>
                    <Switch id="api-logging" defaultChecked />
                  </div>
                </div>

                <Separator />

                <div className="flex justify-end">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存更改
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>通知设置</CardTitle>
                <CardDescription>管理您的通知偏好</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="email-notifications">邮件通知</Label>
                      <p className="text-sm text-muted-foreground">接收重要更新的邮件通知</p>
                    </div>
                    <Switch id="email-notifications" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="browser-notifications">浏览器通知</Label>
                      <p className="text-sm text-muted-foreground">在浏览器中接收实时通知</p>
                    </div>
                    <Switch id="browser-notifications" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="agent-updates">Agent更新通知</Label>
                      <p className="text-sm text-muted-foreground">当Agent被更新或发布时通知我</p>
                    </div>
                    <Switch id="agent-updates" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="kb-updates">知识库更新通知</Label>
                      <p className="text-sm text-muted-foreground">当知识库被更新时通知我</p>
                    </div>
                    <Switch id="kb-updates" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="user-invites">用户邀请通知</Label>
                      <p className="text-sm text-muted-foreground">当新用户被邀请或加入时通知我</p>
                    </div>
                    <Switch id="user-invites" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="marketing-emails">营销邮件</Label>
                      <p className="text-sm text-muted-foreground">接收产品更新和营销信息</p>
                    </div>
                    <Switch id="marketing-emails" />
                  </div>
                </div>

                <Separator />

                <div className="flex justify-end">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存更改
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="language" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>语言与区域</CardTitle>
                <CardDescription>设置您的语言和区域偏好</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>界面语言</Label>
                    <Select defaultValue="zh-CN">
                      <SelectTrigger>
                        <SelectValue placeholder="选择语言" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="zh-CN">中文（简体）</SelectItem>
                        <SelectItem value="en-US">English (US)</SelectItem>
                        <SelectItem value="ja-JP">日本語</SelectItem>
                        <SelectItem value="ko-KR">한국어</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>时区</Label>
                    <Select defaultValue="Asia/Shanghai">
                      <SelectTrigger>
                        <SelectValue placeholder="选择时区" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Asia/Shanghai">中国标准时间 (UTC+8)</SelectItem>
                        <SelectItem value="America/New_York">东部标准时间 (UTC-5)</SelectItem>
                        <SelectItem value="Europe/London">格林威治标准时间 (UTC+0)</SelectItem>
                        <SelectItem value="Asia/Tokyo">日本标准时间 (UTC+9)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>日期格式</Label>
                    <Select defaultValue="yyyy-MM-dd">
                      <SelectTrigger>
                        <SelectValue placeholder="选择日期格式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="yyyy-MM-dd">YYYY-MM-DD</SelectItem>
                        <SelectItem value="dd/MM/yyyy">DD/MM/YYYY</SelectItem>
                        <SelectItem value="MM/dd/yyyy">MM/DD/YYYY</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>时间格式</Label>
                    <Select defaultValue="24h">
                      <SelectTrigger>
                        <SelectValue placeholder="选择时间格式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="24h">24小时制</SelectItem>
                        <SelectItem value="12h">12小时制</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Separator />

                <div className="flex justify-end">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存更改
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>安全设置</CardTitle>
                <CardDescription>管理您的账户安全选项</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>修改密码</Label>
                    <div className="space-y-2">
                      <Input 
                        type="password" 
                        placeholder="当前密码" autoComplete="current-password" 
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                        disabled={isSubmitting}
                      />
                      <Input 
                        type="password" 
                        placeholder="新密码" autoComplete="new-password" 
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        disabled={isSubmitting}
                      />
                      <Input 
                        type="password" 
                        placeholder="确认新密码" autoComplete="new-password" 
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        disabled={isSubmitting}
                      />
                    </div>
                    <Button 
                      className="mt-2" 
                      onClick={handleChangePassword} 
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          提交中...
                        </>
                      ) : (
                        "更新密码"
                      )}
                    </Button>
                  </div>

                  {/* <Separator /> */}

                  {/* <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="two-factor">两步验证</Label>
                      <p className="text-sm text-muted-foreground">使用两步验证增强账户安全</p>
                    </div>
                    <Switch id="two-factor" />
                  </div> */}

                  {/* <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="session-timeout">会话超时</Label>
                      <p className="text-sm text-muted-foreground">长时间不活动后自动登出</p>
                    </div>
                    <Switch id="session-timeout" defaultChecked />
                  </div> */}

                  {/* <div className="space-y-2">
                    <Label>会话超时时间</Label>
                    <Select defaultValue="30">
                      <SelectTrigger>
                        <SelectValue placeholder="选择超时时间" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="15">15分钟</SelectItem>
                        <SelectItem value="30">30分钟</SelectItem>
                        <SelectItem value="60">1小时</SelectItem>
                        <SelectItem value="120">2小时</SelectItem>
                        <SelectItem value="240">4小时</SelectItem>
                      </SelectContent>
                    </Select>
                  </div> */}

                  {/* <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="login-notification">登录通知</Label>
                      <p className="text-sm text-muted-foreground">新设备登录时发送邮件通知</p>
                    </div>
                    <Switch id="login-notification" defaultChecked />
                  </div> */}
                </div>

                {/* <Separator /> */}

                {/* <div className="flex justify-end">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    保存更改
                  </Button>
                </div> */}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
