"use client"

import { MainLayout } from "@/components/main-layout"
import ModelEdit from "@/app/models/_components/model-edit"
import React from "react";
export default function EditModelPage({
    params,
}: {
    params: Promise<{ id: string }>
}) {
    const routeParams = React.use(params);
    return (
        <MainLayout>
            <div className="container py-6 space-y-6">
                <ModelEdit modelId={routeParams.id}/>
            </div>
        </MainLayout>
    )
}
