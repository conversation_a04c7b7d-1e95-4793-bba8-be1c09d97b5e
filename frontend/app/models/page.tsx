"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Search, Plus, MoreHorizontal, Edit, Trash2, MessageSquare } from "lucide-react"
import { apiClient } from "@/lib/api/api"
import { components } from "@/lib/api/strapi"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from 'next/navigation'
import { available } from "@/lib/utils"

type Model = components["schemas"]["Model"]
export default function ModelsPage() {
  useEffect(() => {
    document.title = "模型管理 | 云知问"
  }, [])
  const [models, setModels] = useState<Model[] | undefined>()
  const [filterModelType, setFilterModelType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [itemsPerPage] = useState(9);
  const [_, setIsLoading] = useState(false);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const totalPages = Math.ceil(total / itemsPerPage);
  const { currentTenant } = useAuth()
  const router = useRouter()
  const fetchModels = async () => {
    const { data } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/models`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        },
        query: {
          skip: startIndex,
          limit: itemsPerPage,
          model_type: filterModelType === "all" ? undefined : filterModelType,
          name: searchTerm ? searchTerm : undefined,
          // is_active: true, 
          // provider_id: undefined,
        }
      }
    });
    setModels(data?.items)
    setTotal(data?.total ?? 0)
  }

  useEffect(() => {
    const timer = setTimeout(async () => {
      setIsLoading(true);
      fetchModels()
      setIsLoading(false);
    }, 300); 
    return () => clearTimeout(timer);
  }, [currentPage, filterModelType, searchTerm, currentTenant])

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // 搜索时回到第一页
  };
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const toEdit = (id: number | string) => {
    router.push(`/models/${id}/edit`)
  }

  const toDelete = (id: number | string) => {
    deleteModel(`${id}`)
  }

  const deleteModel = async (id: string) => {
    const { error } = await apiClient.DELETE(`/api/v1/tenants/{tenant_id}/models/{model_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          model_id: id,
        }
      }
    });
    if(error){
      //
    } else {
      fetchModels()
    }
  }

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">模型管理</h1>
            <p className="text-muted-foreground">管理大语言模型和Embedding模型</p>
          </div>
          <div>
            {available(currentTenant) && <Button asChild>
              <Link href="/models/create">
                <Plus className="mr-2 h-4 w-4" />
                添加模型
              </Link>
            </Button>}
          </div>
        </div>

        <Tabs defaultValue={filterModelType} onValueChange={setFilterModelType} className="space-y-4">
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="llm">对话模型</TabsTrigger>
              <TabsTrigger value="embedding">Embedding模型</TabsTrigger>
            </TabsList>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" onChange={(e) => handleSearch(e.target.value)} placeholder="搜索模型..." className="pl-8 w-[200px] md:w-[300px]" />
              </div>
            </div>
          </div>

          <TabsContent value="all" className="space-y-4" >
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* 模型卡片1 */}
              {models?.map((model) => (
                <Card key={model.id}>
                  <CardContent className="p-0">
                    <div className="p-6 border-b">
                      <div className="flex justify-between items-start">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold text-lg">{model.name}</h3>
                            {model.model_type == 'llm' && <Badge
                              variant="outline"
                              className="bg-green-50 text-green-700 dark:bg-green-900 dark:text-green-100 border-green-200 dark:border-green-800"
                            >
                              {model.model_type}
                            </Badge>}

                            {model.model_type == 'embedding' && <Badge
                              variant="outline"
                              className="bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-800"
                            >
                              {model.model_type}
                            </Badge>
                            }
                          </div>
                          <p className="text-sm text-muted-foreground">{model.description}</p>
                        </div>
                        {available(currentTenant) && <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => toEdit(model.id)}>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>编辑</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive" onClick={() => toDelete(model.id)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>删除</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>}
                      </div>
                    </div>
                    <div className="p-6 space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">服务商</p>
                          <p className="break-all">{model?.provider_id}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">模型ID</p>
                          <p className="break-all">{model?.id}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">最大Token</p>
                          <p>--</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">关联Agent</p>
                          <p>-</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        {available(currentTenant) && <Button variant="outline" size="sm" className="w-full" onClick={() => toEdit(model.id)}>
                          <Edit className="mr-2 h-3 w-3" />
                          编辑
                        </Button>}
                        {/* <Button size="sm" className="w-full">
                          <MessageSquare className="mr-2 h-3 w-3" />
                          测试
                        </Button> */}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="llm">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* 对话模型卡片 */}
              {models?.filter(it => it.model_type === 'llm')?.map((model) => (
                <Card key={model.id}>
                  <CardContent className="p-0">
                    <div className="p-6 border-b">
                      <div className="flex justify-between items-start">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold text-lg">{model.name}</h3>
                            <Badge
                              variant="outline"
                              className="bg-green-50 text-green-700 dark:bg-green-900 dark:text-green-100 border-green-200 dark:border-green-800"
                            >
                              {model.model_type}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{model.description}</p>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>编辑</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>删除</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    <div className="p-6 space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">服务商</p>
                          <p className="break-all">{model?.provider_id}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">模型ID</p>
                          <p className="break-all">{model?.key}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">最大Token</p>
                          <p>--</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">关联Agent</p>
                          <p>-</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        {available(currentTenant) && <Button variant="outline" size="sm" className="w-full" onClick={() => toEdit(model.id)}>
                          <Edit className="mr-2 h-3 w-3" />
                          编辑
                        </Button>}
                        {/* <Button size="sm" className="w-full">
                          <MessageSquare className="mr-2 h-3 w-3" />
                          测试
                        </Button> */}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="embedding">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* Embedding模型卡片 */}
              {models?.filter(it => it.model_type === 'embedding')?.map((model) => (
                <Card key={model.id}>
                  <CardContent className="p-0">
                    <div className="p-6 border-b">
                      <div className="flex justify-between items-start">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold text-lg">{model.name}</h3>
                            <Badge
                              variant="outline"
                              className="bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-800"
                            >
                              {model.model_type}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{model.description}</p>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>编辑</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>删除</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    <div className="p-6 space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">服务商</p>
                          <p className="break-all">{model?.provider_id}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">模型ID</p>
                          <p className="break-all">{model?.key}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">最大Token</p>
                          <p>--</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">关联Agent</p>
                          <p>-</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                      {available(currentTenant) && <Button variant="outline" size="sm" className="w-full" onClick={() => toEdit(model.id)}>
                          <Edit className="mr-2 h-3 w-3" />
                          编辑
                        </Button>}
                        {/* <Button size="sm" className="w-full">
                          <MessageSquare className="mr-2 h-3 w-3" />
                          测试
                        </Button> */}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* 分页组件 */}
        {totalPages > 1 && (
          <div className="flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                    className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>

                {/* 页码显示逻辑 */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNumber;
                  if (totalPages <= 5) {
                    pageNumber = i + 1;
                  } else if (currentPage <= 3) {
                    pageNumber = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNumber = totalPages - 4 + i;
                  } else {
                    pageNumber = currentPage - 2 + i;
                  }

                  return (
                    <PaginationItem key={pageNumber}>
                      <PaginationLink
                        onClick={() => handlePageChange(pageNumber)}
                        isActive={currentPage === pageNumber}
                        className="cursor-pointer"
                      >
                        {pageNumber}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
                    className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    </MainLayout >
  )
}
