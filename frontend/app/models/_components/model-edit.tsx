"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form"
import { useToast } from "@/hooks/use-toast"
import {
    createModelSchema,
    type CreateModelFormData,
} from "@/lib/validations/model"

import {
    ArrowLeft,
    Save,
    Loader2,
    Database,
    Key,
    AlertCircle,
    CheckCircle,
} from "lucide-react"
import Link from "next/link"
import { apiClient } from "@/lib/api/api"
import { useAuth } from "@/contexts/auth-context"
import { components } from "@/lib/api/strapi"

import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from '@/components/ui/command';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';

type EnabledProviderItem = components["schemas"]["EnabledProviderItem"]
type ModelType = components["schemas"]["ModelType"]
type Model = components["schemas"]["Model"]

export default function ModelEdit({
    modelId,
}: {
    modelId?: string
}) {
    const router = useRouter()
    const { toast } = useToast()
    const [loading, setLoading] = useState(false)
    const [enabledProviders, setEnabledProviders] = useState<EnabledProviderItem[] | undefined>()
    const [modelTypes, setModelTypes] = useState<ModelType[] | undefined>()
    const [providerModelKeys, setProviderModelKeys] = useState<string[]>([])
    const [_, setEditingModel] = useState<Model | undefined>()
    const [openModelNameSelect, setOpenModelNameSelect] = useState(false);
    const { currentTenant } = useAuth()
    
    const form = useForm<CreateModelFormData>({
        resolver: zodResolver(createModelSchema),
        mode: "onChange",
        defaultValues: {
            name: '',
            key: '',
            model_type: '',
            provider_access_credential_id: '',
        },
    })

    const watchedValues = form.watch()

    // 获取提供商列表
    useEffect(() => {
        const fetchModelTypes = async () => {
            try {
                setLoading(true)
                const { data } = await apiClient.GET(`/api/v1/model-types`);
                setModelTypes(data?.items)
            } catch (error) {
                toast("获取提供商列表失败")
            } finally {
                setLoading(false)
            }
        }

        fetchModelTypes()
    }, [])

    // 获取已启用提供商列表
    useEffect(() => {
        const fetchProviders = async () => {
            if (!currentTenant) {
                return
            }
            try {
                setLoading(true)
                const { data } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/enabled-providers`, {
                    params: {
                        path: {
                            tenant_id: currentTenant.tenant_id,
                        }
                    }
                });
                setEnabledProviders(data?.items)
            } catch (error) {
                toast("获取已启用提供商列表失败")
            } finally {
                setLoading(false)
            }
        }

        fetchProviders()
    }, [currentTenant])

    useEffect(() => {
        const fetchModels = async () => {
            if (!currentTenant) {
                return
            }
            try {
                const { data } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/provider-models/{credential_id}`, {
                    params: {
                        path: {
                            tenant_id: currentTenant?.tenant_id ?? '',
                            credential_id: watchedValues.provider_access_credential_id
                        }
                    }
                });
                setProviderModelKeys(data?.models.map(model => model.key) ?? [])
            } catch (error) {
                toast("获取已启用提供商模型列表失败")
            }
        }
        if (watchedValues.provider_access_credential_id) {
            fetchModels()
        }
    }, [currentTenant, watchedValues.provider_access_credential_id])

    useEffect(() => {
        const fetchModelDetail = async () => {
            if (currentTenant?.tenant_id && modelId && enabledProviders && modelTypes) {
                try {
                    const { data } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/models/{model_id}`, {
                        params: {
                            path: {
                                tenant_id: currentTenant.tenant_id,
                                model_id: modelId
                            }
                        }
                    });
                    setEditingModel(data)
                    data?.provider_access_credential_id && form.setValue("provider_access_credential_id", data.provider_access_credential_id, { shouldValidate: true })
                    data?.key && form.setValue("key", data.key, { shouldValidate: true })
                    data?.name && form.setValue("name", data.name, { shouldValidate: true })
                    data?.model_type && form.setValue("model_type", data.model_type, { shouldValidate: true })
                } catch (error) {
                    //
                }
            }
        }
        fetchModelDetail()
    }, [currentTenant, modelId, enabledProviders, modelTypes])
    // 生成模型标识
    const generateKey = () => {
        const name = watchedValues.name
        if (name) {
            const key = name
                .toLowerCase()
                .replace(/[\u4e00-\u9fa5]/g, "") // 移除中文字符
                .replace(/[^a-z0-9]/g, "_") // 替换非字母数字为下划线
                .replace(/_+/g, "_") // 合并多个下划线
                .replace(/^_|_$/g, "") // 移除首尾下划线
            form.setValue("key", key, { shouldValidate: true })
        }
    }

    // 提交表单
    const onSubmit = async (modelData: CreateModelFormData) => {
        if (modelId) {
            toEditModel(modelData)
        } else {
            toCreateModel(modelData)
        }
    }

    const toCreateModel = async (modelData: CreateModelFormData) => {
        if (!currentTenant) {
            return
        }
        try {
            setLoading(true)

            // 构建额外配置

            const { data } = await apiClient.POST(`/api/v1/tenants/{tenant_id}/models`, {
                params: {
                    path: {
                        tenant_id: currentTenant.tenant_id,
                    }
                },
                body: modelData
            });

            toast(`模型 "${data?.name}" 已成功创建`)
            router.push('/models')
        } catch (error) {
            toast("创建失败,请稍后重试")
        } finally {
            setLoading(false)
        }
    }

    const toEditModel = async (modelData: CreateModelFormData) => {
        if (!currentTenant || !modelId) {
            return
        }
        try {
            setLoading(true)

            // 构建额外配置

            const { data } = await apiClient.PUT(`/api/v1/tenants/{tenant_id}/models/{model_id}`, {
                params: {
                    path: {
                        tenant_id: currentTenant.tenant_id,
                        model_id: modelId
                    }
                },
                body: modelData
            });

            toast(`模型 "${data?.name}" 已成功修改`)
            router.push('/models')
        } catch (error) {
            toast("修改失败,请稍后重试")
        } finally {
            setLoading(false)
        }
    }

    return (

        <div className="flex flex-col gap-6">
            {/* 页面头部 */}
            <div className="flex items-center gap-4">
                <Button variant="outline" size="icon" asChild>
                    <Link href="/models">
                        <ArrowLeft className="h-4 w-4" />
                    </Link>
                </Button>
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">{modelId ? '编辑模型' : '创建模型'}</h1>
                    <p className="text-muted-foreground mt-2">配置新的AI模型</p>
                </div>
            </div>

            <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-1">
                    {/* 基本信息 */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* 提供商配置 */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Key className="h-5 w-5" />
                                    提供商配置
                                </CardTitle>
                                <CardDescription>选择模型提供商</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {loading ? (
                                    <div className="flex items-center gap-2">
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                        <span>加载提供商列表...</span>
                                    </div>
                                ) : (
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="provider_access_credential_id">访问提供商 *</Label>
                                            <FormField
                                                control={form.control}
                                                name="provider_access_credential_id"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            {loading ? (
                                                                <div className="flex items-center gap-2 p-2 border rounded">
                                                                    <Loader2 className="h-4 w-4 animate-spin" />
                                                                    <span>加载提供商列表...</span>
                                                                </div>
                                                            ) : (
                                                                <Select
                                                                    onValueChange={field.onChange}
                                                                    value={field.value}
                                                                >
                                                                    <SelectTrigger>
                                                                        <SelectValue placeholder="选择提供商" />
                                                                    </SelectTrigger>
                                                                    <SelectContent>
                                                                        {enabledProviders?.map((item) => (
                                                                            <SelectItem key={item.credential_id} value={item.credential_id}>
                                                                                <div className="flex items-center gap-2">
                                                                                    <span>{item.provider.name}</span>
                                                                                    {item.credential_id ? (
                                                                                        <CheckCircle className="h-3 w-3 text-green-500" />
                                                                                    ) : (
                                                                                        <AlertCircle className="h-3 w-3 text-red-500" />
                                                                                    )}
                                                                                </div>
                                                                            </SelectItem>
                                                                        ))}
                                                                    </SelectContent>
                                                                </Select>
                                                            )}
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Database className="h-5 w-5" />
                                    基本信息
                                </CardTitle>
                                <CardDescription>设置模型的基本信息和标识</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>模型名称 *</FormLabel>
                                            <FormControl>
                                                <Popover open={openModelNameSelect} onOpenChange={setOpenModelNameSelect}>
                                                    <PopoverTrigger asChild>
                                                        <Button
                                                            variant="outline"
                                                            role="combobox"
                                                            aria-expanded={openModelNameSelect}
                                                            className="w-full justify-between"
                                                        >
                                                            {watchedValues.name || "选择或输入模型名称..."}
                                                        </Button>
                                                    </PopoverTrigger>
                                                    <PopoverContent className="w-full p-0">
                                                        <Command>
                                                            <CommandInput
                                                                placeholder="搜索模型名称..."
                                                                value={field.value}
                                                                onValueChange={(value) => form.setValue("name", value)}
                                                            />
                                                            <CommandList>
                                                                <CommandEmpty>未找到匹配的模型</CommandEmpty>
                                                                <CommandGroup>
                                                                    {providerModelKeys
                                                                        .filter(it =>
                                                                            it.toLowerCase().includes(watchedValues.name.toLowerCase())
                                                                        )
                                                                        .map((it) => (
                                                                            <CommandItem
                                                                                key={it}
                                                                                value={it}
                                                                                onSelect={() => {
                                                                                    form.setValue("name", it);
                                                                                    setOpenModelNameSelect(false);
                                                                                }}
                                                                            >
                                                                                {it}
                                                                            </CommandItem>
                                                                        ))}
                                                                </CommandGroup>
                                                            </CommandList>
                                                        </Command>
                                                    </PopoverContent>
                                                </Popover>
                                            </FormControl>
                                            <FormDescription>用于API调用的唯一标识符</FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="key"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                模型标识 *
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={generateKey}
                                                    className="h-auto p-1 text-xs"
                                                >
                                                    自动生成
                                                </Button>
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="请输入模型标识"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="model_type"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>模型类型 *</FormLabel>
                                            <FormControl>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="选择模型类型" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {modelTypes?.map((type) => (
                                                            <SelectItem key={type.key} value={type.key}>
                                                                <div>
                                                                    <div className="font-medium">{type.name}</div>
                                                                </div>
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </CardContent>
                        </Card>
                        {/* 操作按钮 */}
                        <Card>
                            <CardHeader>
                                <CardTitle>操作</CardTitle>
                            </CardHeader>
                            <CardContent className="flex space-x-2">
                                <Button variant="outline" type="button"  className="w-full" asChild>
                                    <Link href="/models">取消</Link>
                                </Button>
                                <Button type="submit" className="w-full" disabled={loading || !form.formState.isValid}>
                                    {loading ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            创建中...
                                        </>
                                    ) : (
                                        <>
                                            <Save className="mr-2 h-4 w-4" />
                                            {modelId ? "更新模型" : "创建模型"}
                                        </>
                                    )}
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </form>
            </Form>
        </div>
    )
}
