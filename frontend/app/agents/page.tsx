"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  Plus,
  Filter,
  MoreHorizontal,
  ExternalLink,
  Edit,
  Trash2,
  CheckCircle2,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

import { apiClient } from "@/lib/api/api"
import { components } from "@/lib/api/strapi"
import { useRouter } from 'next/navigation'
import { agentTypeOptions } from "@/lib/options"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/hooks/use-toast"
import { available } from "@/lib/utils"
type Agent = components["schemas"]["Agent"]

export default function AgentsPage() {
  useEffect(() => {
    document.title = "Agent管理 | 云知问"
  }, [])

  const { currentTenant } = useAuth()
  const [agents, setAgents] = useState<Agent[] | undefined>([])
  const [searchName, setSearchName] = useState<string | undefined>()
  const [agentType, setAgentType] = useState<string | undefined>()
  const [agentStatus, setAgentStatus] = useState<string | undefined>('all')
  const getData = async () => {
    const { data } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        },
        query: {
          skip: 0,
          limit: 10,
          name: searchName,
          agent_type: agentType === 'all' ? undefined : agentType,
          is_active: agentStatus == 'all' ? undefined : true,
        }
      }
    });
    setAgents(data?.items)

  }
  const router = useRouter()
  const toEdit = (id: number | string) => {
    router.push(`/agents/create/wizard?id=${id}`)
  }
  const toDetail = (id: number | string) => {
    router.push(`/agents/${id}`)
  }

  const toDelete = (id: number | string) => {
    deleteAgent(`${id}`)
    getData()
  }

  const deleteAgent = async (id: string) => {
    const { error, data } = await apiClient.DELETE(`/api/v1/tenants/{tenant_id}/agents/{agent_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          agent_id: id,
        }
      }
    })
    if(error){
      console.log(error)
    }else{
      useToast().toast.success('Agent已删除')
    }
  }

  useEffect(() => {
    getData()
  }, [searchName, agentType, agentStatus, currentTenant])

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Agent管理</h1>
            <p className="text-muted-foreground">创建、配置和管理您的智能Agent</p>
          </div>
          <div>
            {available(currentTenant) && <Button asChild>
              <Link href="/agents/create/wizard">
                <Plus className="mr-2 h-4 w-4" />
                创建Agent
              </Link>
            </Button>}
          </div>
        </div>

        <Tabs defaultValue="all" onValueChange={(e) => setAgentStatus(e)} className="space-y-4">
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="published">已激活</TabsTrigger>
              {/* <TabsTrigger value="draft">草稿</TabsTrigger>
              <TabsTrigger value="testing">测试中</TabsTrigger> */}
            </TabsList>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" onChange={(e) => setSearchName(e.target.value)} placeholder="搜索Agent..." className="pl-8 w-[200px] md:w-[300px]" />
              </div>
              <Select defaultValue="all" onValueChange={(e) => setAgentType(e)}>
                <SelectTrigger className="w-[180px]">
                  <div className="flex items-center">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="筛选类型" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  {agentTypeOptions.map(item => <SelectItem value={item.value} key={item.value}>{item.label}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {agents?.map(item => {
              const { agent_type, description, name, } = item
              return (<Card key={item.id}>
                <CardContent className="p-0">
                  <div className="p-6 border-b">
                    <div className="flex justify-between items-start">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-semibold text-lg">{name}</h3>
                          {item.is_active && <Badge
                            variant="outline"
                            className="bg-green-50 text-green-700 dark:bg-green-900 dark:text-green-100 border-green-200 dark:border-green-800"
                          >
                            <CheckCircle2 className="mr-1 h-3 w-3" />
                            已激活
                          </Badge>}
                          {/* {agentStatus == 'draft' && <Badge
                            variant="outline"
                            className="bg-yellow-50 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-100 border-yellow-200 dark:border-yellow-800"
                          >
                            <Clock className="mr-1 h-3 w-3" />
                            草稿
                          </Badge>}
                          {agentStatus == 'testing' && <Badge
                            variant="outline"
                            className="bg-amber-50 text-amber-700 dark:bg-amber-900 dark:text-amber-100 border-amber-200 dark:border-amber-800"
                          >
                            <Clock className="mr-1 h-3 w-3" />
                            测试中
                          </Badge>}
                          {agentStatus == 'error' && <Badge
                            variant="outline"
                            className="bg-red-50 text-red-700 dark:bg-red-900 dark:text-red-100 border-red-200 dark:border-red-800"
                          >
                            <AlertCircle className="mr-1 h-3 w-3" />
                            错误
                          </Badge>} */}
                        </div>
                        <p className="text-sm text-muted-foreground line-clamp-1">{description}</p>
                      </div>
                      {available(currentTenant) && <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => toEdit(item.id)}>
                            <Edit className="mr-2 h-4 w-4" />
                            <span>编辑</span>
                          </DropdownMenuItem>
                          {/* <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" />
                            <span>复制</span>
                          </DropdownMenuItem> */}
                          <DropdownMenuItem onClick={() => toDetail(item.id)}>
                            <ExternalLink className="mr-2 h-4 w-4" />
                            <span>访问</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-destructive" onClick={() => toDelete(item.id)}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            <span>删除</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>}
                    </div>
                  </div>
                  <div className="p-6 space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">类型</p>
                        <p>{agent_type}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">版本</p>
                        <p>{ '--' }</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">关联知识库</p>
                        <p>{ '--' } 个</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">调用次数</p>
                        <p>{ '--' } 次</p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      {available(currentTenant) && <Button variant="outline" onClick={() => toEdit(item.id)} size="sm" className="w-full">
                        <Edit className="mr-2 h-3 w-3" />
                        编辑
                      </Button>}
                      <Button size="sm" onClick={() => toDetail(item.id)} className="w-full">
                        <ExternalLink className="mr-2 h-3 w-3" />
                        访问
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>)
            })}
          </div>
        </Tabs>
      </div>
    </MainLayout>
  )
}
