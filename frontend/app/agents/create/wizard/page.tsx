"use client"
import { useEffect, useState } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, ChevronRight, Bot, Save, Trash2 } from "lucide-react"
import { agentTypeOptions } from "@/lib/options"
import { apiClient } from "@/lib/api/api"
import { components } from "@/lib/api/strapi"
import { useForm, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { createAgentSchema, type CreateAgentFormData } from "@/lib/validations/agent"
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
type Model = components["schemas"]["Model"]
import { useRouter, useSearchParams } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
export default function CreateAgentWizardPage() {
  useEffect(() => {
    document.title = "创建Agent向导 | 云知问"
  }, [])
  const [tabStep, setTabStep] = useState('basic')
  const { currentTenant } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [progress, setProgress] = useState(25)
  const [versionInfo, setVersionInfo] = useState<any>()
  const [versionIndex, setVersionIndex] = useState('0')
  const [iconUrl, setIconUrl] = useState('')
  const [models, setModels] = useState<Model[] | undefined>()
  const [knowledge, setKnowledge] = useState<any[] | undefined>()
  const [toolInfo, setToolInfo] = useState<any[] | undefined>()
  const [mcpInfo, setMcpInfo] = useState<any[] | undefined>()
  const [templatesInfo, setTemplatesInfo] = useState<any[]>([])
  const router = useRouter()
  const { toast } = useToast()
  const urlParams = useSearchParams();
  const id = urlParams.get('id');

  const form = useForm<CreateAgentFormData>({
    resolver: zodResolver(createAgentSchema),
    defaultValues: {
      tenant_id: currentTenant?.tenant_id || '',
      name: '',
      description: '',
      agent_type: 'general',
      model_id: '',
      prompt: '',
      is_supervisor: false,
      knowledge_base_ids: [],
      knowledge_max_results: 5,
      knowledge_min_score: 0.7,
      tool_ids: [],
      mcp_server_ids: [],
      is_active: false,
      temperature: 1,
      top_p: 1,
      max_tokens: 1024,
      max_retries: 3,
      timeout: 30,
      release_notes: '',
    },
    mode: "onChange",
  })

  const { watch, formState: { isValid, errors }, setValue, control, handleSubmit } = form
  const watchedValues = watch()

  // 添加调试日志
  // useEffect(() => {
  //   console.log('Form validation state:', { isValid, errors })
  //   console.log('Current form values:', watchedValues)
  // }, [isValid, errors, watchedValues])

  // 更新表单进度
  useEffect(() => {
    if (tabStep === 'basic') setProgress(25)
    else if (tabStep === 'type') setProgress(50)
    else if (tabStep === 'advanced') setProgress(75)
    else if (tabStep === 'preview') setProgress(100)
  }, [tabStep])

  // 当租户变化时更新表单值
  useEffect(() => {
    if (currentTenant?.tenant_id) {
      setValue('tenant_id', currentTenant.tenant_id)
    }
  }, [currentTenant, setValue])

  // 获取已启用提供商列表
  useEffect(() => {
    const fetchModels = async () => {
      const { data } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/models`, {
        params: {
          path: {
            tenant_id: currentTenant?.tenant_id ?? '',
          }
        }
      });
      setModels(data?.items)
    }

    fetchModels()
  }, [currentTenant, toast])

  const onSubmit = async (data: CreateAgentFormData, is_published?: boolean) => {
    console.log('Form submitted with data:', data)
    if (!currentTenant) {
      toast.error('未选择租户，无法创建Agent')
      return
    }
    const controller = new AbortController()
    if (id) {
      putData(data, is_published)
    } else {
      try {
        setIsSubmitting(true)
        const { error } = await apiClient.POST(`/api/v1/tenants/{tenant_id}/agents`, {
          params: {
            path: {
              tenant_id: currentTenant.tenant_id,
            },
          },
          body: {
            name: data.name,
            agent_type: data.agent_type,
            description: data.description,
            prompt: data.prompt,
            template_id: data.template_id || undefined,
            model_id: data.model_id,
            knowledge_base_ids: data.knowledge_base_ids,
            tool_ids: data.tool_ids,
            mcp_server_ids: data.mcp_server_ids,
            llm_model_config: {
              temperature: data.temperature,
              top_p: data.top_p,
              max_tokens: data.max_tokens,
              max_retries: data.max_retries,
              timeout: data.timeout
            },
            knowledge_base_config: {
              max_results: data.knowledge_max_results,
              min_score: data.knowledge_min_score
            },
          },
          signal: controller.signal,
        })

        if (error) {
          throw new Error('创建Agent失败')
        }

        toast.success('Agent创建成功')
        router.push('/agents')
      } catch (error) {
        console.error('创建Agent失败:', error)
      } finally {
        setIsSubmitting(false)
      }
    }
    return () => {
      // 清理函数，在组件卸载时取消请求
      controller.abort()
    }
  }

  const getTemplatesData = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents/templates`, {
      params: {
      path: {
        tenant_id: currentTenant?.tenant_id ?? '',
      }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      setTemplatesInfo(data.templates)
    }
  }

  // 获取配置信息
  useEffect(() => {
    const fetchData = async () => {
      // 获取知识库
      const { data } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents/available-knowledge-bases`, {
        params: {
          path: {
            tenant_id: currentTenant?.tenant_id ?? '',
          }
        }
      })
      setKnowledge(data as any[])
      // 获取工具
      const { data: toolData } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents/available-tools`, {
        params: {
          path: {
            tenant_id: currentTenant?.tenant_id ?? '',
          }
        }
      });
      setToolInfo(toolData as any[])
      // 获取MCP
      const { data: mcpData } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents/available-mcp-servers`, {
        params: {
          path: {
            tenant_id: currentTenant?.tenant_id ?? '',
          },
        }
      });
      setMcpInfo(mcpData as any[])
    }

    fetchData()
    !id && getTemplatesData()
  }, [currentTenant])

  // 获取发布历史
  // useEffect(() => {
  //   const fetchKnowledge = async () => {
  //     const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents/{agent_id}/history`, {
  //       params: {
  //         path: {
  //           tenant_id: currentTenant?.tenant_id ?? '',
  //           agent_id: id ?? '',
  //         },
  //       }
  //     })
  //     setVersionInfo(data?.versions)
  //   }

  //   fetchKnowledge()
  // }, [currentTenant])

  const getData = async (id: string) => {
    const { data, error }: any = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents/{agent_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          agent_id: id,
        }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      setValue('name', data.name)
      setValue('description', data.description)
      setValue('agent_type', data.agent_type)
      setValue('is_active', data.is_active)
      setIconUrl(data.icon_access_url)
      setFormVersionData(data?.versions[0])
      setVersionInfo(data?.versions)
    }
  }

  useEffect(() => {
    id && getData(id.toString())
  }, [id, currentTenant])

  const setFormVersionData = (data: any) => {
    // setValue('name', data.name)
    // setValue('description', data.description)
    // setValue('agent_type', data.agent_type)
    
    setValue('release_notes', data?.description || '')
    setValue('model_id', data.model_id)
    setValue('prompt', data.prompt)
    setValue('knowledge_base_ids', data.knowledge_base_ids)
    setValue('knowledge_max_results', data.knowledge_base_config.max_results)
    setValue('knowledge_min_score', data.knowledge_base_config.min_score)
    setValue('tool_ids', data.tool_ids)
    setValue('mcp_server_ids', data.mcp_server_ids)
    setValue('temperature', data.llm_model_config.temperature)
    setValue('top_p', data.llm_model_config.top_p)
    setValue('max_tokens', data.llm_model_config.max_tokens)
    setValue('max_retries', data.llm_model_config.max_retries)
    setValue('timeout', data.llm_model_config.timeout)
  }
  const setFormTemplateData = (data: any) => {
    setValue('name', data.name)
    setValue('description', data.description)
    setValue('agent_type', data.agent_type)
    setValue('template_id', data.template_id)
    setValue('prompt', data.default_config.prompt)
    setValue('knowledge_base_ids', data.suggested_knowledge_base_ids)
    setValue('knowledge_max_results', data.default_config.knowledge_base_config.max_results)
    setValue('knowledge_min_score', data.default_config.knowledge_base_config.min_score)
    setValue('tool_ids', data.suggested_tool_ids)
    setValue('mcp_server_ids', data?.suggested_mcp_server_ids ?? [])
    setValue('temperature', data.default_config.llm_model_config.temperature)
    setValue('top_p', data.default_config.llm_model_config.top_p)
    setValue('max_tokens', data.default_config.llm_model_config.max_tokens)
    setValue('max_retries', data.default_config.llm_model_config.max_retries)
    setValue('timeout', data.default_config.llm_model_config.timeout)
  }
  const putData = async (values: any, is_published?: boolean) => {
    setIsSubmitting(true)
    // 修改基础信息
    const { error } = await apiClient.PUT(`/api/v1/tenants/{tenant_id}/agents/{agent_id}/basic-info`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          agent_id: id as string,
        }
      },
      body: {
        // ...values,
        name: values.name,
        agent_type: values.agent_type,
        description: values.description,
        is_active: values.is_active,
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      // toast.success('Agent修改成功')
      // router.push('/agents')
      putDraft(values, is_published)
    }
    setIsSubmitting(false)
  }
  const putDraft = async (values: any, is_published?: boolean) => {
    // 更新Agent配置信息 - 草稿保存
    setIsSubmitting(true)
    const { error } = await apiClient.PUT(`/api/v1/tenants/{tenant_id}/agents/{agent_id}/draft`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          agent_id: id as string,
        }
      },
      body: {
        // ...values,
        prompt: values.prompt,
        model_id: values.model_id,
        knowledge_base_ids: values.knowledge_base_ids,
        tool_ids: values.tool_ids,
        mcp_server_ids: values.mcp_server_ids,
        llm_model_config: {
          temperature: values.temperature,
          top_p: values.top_p,
          max_tokens: values.max_tokens,
          max_retries: values.max_retries,
          timeout: values.timeout
        },
        knowledge_base_config: {
          max_results: values.knowledge_max_results,
          min_score: values.knowledge_min_score
        },
      }
    });
    setIsSubmitting(false)
    if (error) {
      console.log('error', error)
    } else {
      toast.success('Agent版本修改成功')
      router.push('/agents')
      if (is_published) {
        // 发布Agent
        publishAgent(values.release_notes)
      }
    }
  }
  const publishAgent = async (release_notes: string) => {
    const { error } = await apiClient.POST(`/api/v1/tenants/{tenant_id}/agents/{agent_id}/publish`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          agent_id: id as string,
        }
      },
      body: {
        release_notes,
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      toast.success('Agent发布成功')
    }
  }
  const getIcon = async (id: string) => {
    const { data, error }: any = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents/{agent_id}/icon-url`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          agent_id: id,
        }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      setIconUrl(data.icon_url)
    }
  }

  const updateFile = async (e: any) => {
    const file = e.target.files[0];

    if (!file) {
      return;
    }

    const formData = new FormData();
    formData.append('icon', file);

    const { error } = await apiClient.POST(`/api/v1/tenants/{tenant_id}/agents/{agent_id}/icon`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          agent_id: id ?? '',
        }
      },
      body: formData as any,
    });
    if (error) {
      console.log('error', error)
    } else {
      getIcon(id as string)
    }
  }

  const delIcon = async () => {
    const { data, error } = await apiClient.DELETE(`/api/v1/tenants/{tenant_id}/agents/{agent_id}/icon`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          agent_id: id ?? '',
        }
      },
    });
  }

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href="/agents">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{id ? '修改' : '创建'}Agent</h1>
            <p className="text-muted-foreground">按照向导步骤配置您的Agent</p>
          </div>
        </div>
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(async (values) => onSubmit(values))} className="flex flex-col md:flex-row gap-6">
            <div className="md:w-64 space-y-4">
              <Card>
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className={`flex items-center space-x-2 py-2 px-3 ${tabStep === 'basic' ? 'bg-primary/10 text-primary' : ''} rounded-md`}>
                      <div className={`w-6 h-6 rounded-full ${tabStep === 'basic' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'} flex items-center justify-center text-xs font-medium`}>
                        1
                      </div>
                      <span className={tabStep === 'basic' ? 'font-medium' : 'text-muted-foreground'}>基础信息</span>
                    </div>
                    <div className={`flex items-center space-x-2 py-2 px-3 ${tabStep === 'type' ? 'bg-primary/10 text-primary' : ''} rounded-md`}>
                      <div className={`w-6 h-6 rounded-full ${tabStep === 'type' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'} flex items-center justify-center text-xs font-medium`}>
                        2
                      </div>
                      <span className={tabStep === 'type' ? 'font-medium' : 'text-muted-foreground'}>类型配置</span>
                    </div>
                    <div className={`flex items-center space-x-2 py-2 px-3 ${tabStep === 'advanced' ? 'bg-primary/10 text-primary' : ''} rounded-md`}>
                      <div className={`w-6 h-6 rounded-full ${tabStep === 'advanced' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'} flex items-center justify-center text-xs font-medium`}>
                        3
                      </div>
                      <span className={tabStep === 'advanced' ? 'font-medium' : 'text-muted-foreground'}>高级配置</span>
                    </div>
                    <div className={`flex items-center space-x-2 py-2 px-3 ${tabStep === 'preview' ? 'bg-primary/10 text-primary' : ''} rounded-md`}>
                      <div className={`w-6 h-6 rounded-full ${tabStep === 'preview' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'} flex items-center justify-center text-xs font-medium`}>
                        4
                      </div>
                      <span className={tabStep === 'preview' ? 'font-medium' : 'text-muted-foreground'}>预览与测试</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">配置进度</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="bg-primary h-2 rounded-full" style={{ width: `${progress}%` }}></div>
                    </div>
                    <div className="text-sm text-muted-foreground">完成度: {progress}%</div>
                  </div>
                </CardContent>
              </Card>

              {id ? <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">选择回滚版本</CardTitle>
                  <CardDescription>只回滚agent配置信息，基础信息不可回滚。</CardDescription>
                </CardHeader>
                <CardContent>
                  <Select value={versionIndex} onValueChange={(e) => { setVersionIndex(e), setFormVersionData(versionInfo[Number(e)]) }}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="请选择" />
                    </SelectTrigger>
                    <SelectContent>
                      {versionInfo?.map((item: any, index: number) => (
                        <SelectItem key={index} value={index.toString()}>
                          <div className="flex items-center gap-2">
                            <span>{item.version_number} {item.is_current && '（当前版）'}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card> : <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">选择模板</CardTitle>
                  <CardDescription>选择一个模板，快速配置Agent。</CardDescription>
                </CardHeader>
                <CardContent>
                  <Select onValueChange={(e) => { setFormTemplateData(templatesInfo[Number(e)]) }}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="请选择模版" />
                    </SelectTrigger>
                    <SelectContent>
                      {templatesInfo?.map((item: any, index: number) => (
                        <SelectItem key={index} value={index.toString()}>{item.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>}
            </div>

            <div className="flex-1 space-y-6">
              <Tabs value={tabStep} className="space-y-6" onValueChange={(e) => setTabStep(e)}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic">基础信息</TabsTrigger>
                  <TabsTrigger value="type">关联配置</TabsTrigger>
                  <TabsTrigger value="advanced">高级配置</TabsTrigger>
                  <TabsTrigger value="preview">预览与测试</TabsTrigger>
                </TabsList>

                <TabsContent value="basic">
                  <Card>
                    <CardHeader>
                      <CardTitle>基础信息</CardTitle>
                      <CardDescription>设置Agent的基本信息</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <FormField
                        control={control}
                        name="name"
                        render={({ field }: { field: any }) => (
                          <FormItem>
                            <FormLabel>Agent名称</FormLabel>
                            <FormControl>
                              <Input placeholder="输入Agent名称，如：产品客服助手" {...field} />
                            </FormControl>
                            <FormDescription>名称将显示在Agent列表和对话界面中</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Agent描述</FormLabel>
                            <FormControl>
                              <Textarea placeholder="简要描述Agent的功能和用途" rows={3} {...field} />
                            </FormControl>
                            <FormDescription>描述将帮助团队成员了解此Agent的用途</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={control}
                        name="agent_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Agent类型</FormLabel>
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="选择Agent类型" />
                                </SelectTrigger>
                                <SelectContent>
                                  {agentTypeOptions.map(item =>
                                    <SelectItem value={item.value} key={item.value}>
                                      {item.label}
                                    </SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormDescription>Agent类型决定了其基本功能和行为</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {id && <FormField
                        control={control}
                        name="release_notes"
                        render={({ field }: { field: any }) => (
                          <FormItem>
                            <FormLabel>发布说明</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="输入发布说明"
                                rows={4}
                                value={field.value}
                                onChange={field.onChange}
                              />
                            </FormControl>
                            <FormDescription>发布说明将帮助团队成员了解此Agent的版本更新</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />}

                      {id && <div className="space-y-2">
                        <Label htmlFor="icon">Agent图标</Label>
                        <div className="flex items-center space-x-4">
                          <div className="w-16 h-16 rounded-md bg-muted flex items-center justify-center">
                            {iconUrl ? <img src={iconUrl} className="w-16 h-16 rounded-md" alt="" /> :
                              <Bot className="h-8 w-8 text-muted-foreground" />}
                          </div>
                          <Button variant="outline" className="relative">上传图标
                            <Input id="picture" onChange={e => updateFile(e)} className="absolute left-0 right-0 z-10 opacity-0" type="file" accept=".png, .jpg, .svg, .webp" />
                          </Button>
                          <a onClick={() => delIcon()} className="relative flex text-red-400 cursor-pointer">
                            <Trash2 className="mr-2 h-4 w-4 mt-1" />
                            删除图标
                          </a>
                        </div>
                        <p className="text-sm text-muted-foreground">推荐尺寸：256x256像素，支持PNG、JPG格式</p>
                      </div>}

                      {id && <FormField
                        control={form.control}
                        name="is_active"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <div className="flex items-center justify-between">
                              <FormLabel htmlFor="is_active">是否激活</FormLabel>
                              <FormControl>
                                <Switch
                                  id="is_active"
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />}
                    </CardContent>
                  </Card>

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" asChild>
                      <Link href="/agents/create">取消</Link>
                    </Button>
                    <Button onClick={() => setTabStep('type')}>
                      下一步：类型配置
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="type">
                  <Card>
                    <CardHeader>
                      <CardTitle>类型配置</CardTitle>
                      <CardDescription>配置Agent的可选工具</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">

                      <FormField
                        control={control}
                        name="model_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>大语言模型</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="选择模型" />
                                </SelectTrigger>
                                <SelectContent>
                                  {models?.map((item) => (
                                    <SelectItem key={item.id} value={item.id}>
                                      <div className="flex items-center gap-2">
                                        <span>{item.name}</span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                            <FormDescription>选择用于此Agent的大语言模型</FormDescription>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={control}
                        name="prompt"
                        render={({ field }: { field: any }) => (
                          <FormItem>
                            <FormLabel>系统提示词</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="输入系统提示词"
                                rows={4}
                                value={field.value}
                                onChange={field.onChange}
                              />
                            </FormControl>
                            <FormDescription>定义Agent的核心行为和响应风格</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Separator />
                      <FormField
                        control={form.control}
                        name="knowledge_base_ids"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel>关联知识库</FormLabel>
                            <FormControl>
                              <div className="border rounded-md p-4 space-y-4">
                                {knowledge && knowledge.length > 0 ? (
                                  knowledge.map((kb) => (
                                    <div key={kb.id} className="flex items-center space-x-2">
                                      <Checkbox
                                        id={kb.id}
                                        value={kb.id}
                                        checked={field.value?.includes(kb.id)}
                                        onCheckedChange={(checked) => {
                                          const newValue = checked
                                            ? [...(field.value || []), kb.id]
                                            : field.value?.filter((id: string) => id !== kb.id) || [];
                                          field.onChange(newValue);
                                        }}
                                      />
                                      <Label htmlFor={kb.id}>{kb.name}</Label>
                                    </div>
                                  ))
                                ) : (
                                  <div className="text-sm text-muted-foreground">暂无可用知识库</div>
                                )}
                                <Button variant="outline" size="sm" type="button" asChild>
                                  <Link href="/knowledge-bases/create">添加知识库</Link>
                                </Button>
                              </div>
                            </FormControl>
                            <FormDescription className="text-sm text-muted-foreground">选择Agent可以访问的知识库</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="knowledge_max_results"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>知识库最大返回结果数</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="知识库最大返回结果数"
                                type="number"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>控制知识库查询返回的结果数量</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="knowledge_min_score"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>知识库最小得分</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="知识库最小得分"
                                type="number"
                                step="0.1"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>设置知识库匹配的最小相关性分数</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Separator />

                      <FormField
                        control={form.control}
                        name="tool_ids"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel>系统工具配置</FormLabel>
                            <FormControl>
                              <div className="border rounded-md p-4 space-y-4">
                                {toolInfo && toolInfo.length > 0 ? (
                                  toolInfo.map((kb) => (
                                    <div key={kb.id} className="flex items-center space-x-2">
                                      <Checkbox
                                        id={kb.id}
                                        value={kb.id}
                                        checked={field.value?.includes(kb.id)}
                                        onCheckedChange={(checked) => {
                                          const newValue = checked
                                            ? [...(field.value || []), kb.id]
                                            : field.value?.filter((id: string) => id !== kb.id) || [];
                                          field.onChange(newValue);
                                        }}
                                      />
                                      <Label htmlFor={kb.id}>{kb.name}</Label>
                                    </div>
                                  ))
                                ) : (
                                  <div className="text-sm text-muted-foreground">暂无可用系统工具</div>
                                )}
                                <Button variant="outline" size="sm" type="button" asChild>
                                  <Link href="/tools/system/create">添加系统工具</Link>
                                </Button>
                              </div>
                            </FormControl>
                            <FormDescription className="text-sm text-muted-foreground">选择Agent可以访问的系统工具</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <Separator />
                      <FormField
                        control={form.control}
                        name="mcp_server_ids"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel>MCP服务</FormLabel>
                            <FormControl>
                              <div className="border rounded-md p-4 space-y-4">
                                {mcpInfo && mcpInfo.length > 0 ? (
                                  mcpInfo.map((kb) => (
                                    <div key={kb.id} className="flex items-center space-x-2">
                                      <Checkbox
                                        id={kb.id}
                                        value={kb.id}
                                        checked={field.value?.includes(kb.id)}
                                        onCheckedChange={(checked) => {
                                          const newValue = checked
                                            ? [...(field.value || []), kb.id]
                                            : field.value?.filter((id: string) => id !== kb.id) || [];
                                          field.onChange(newValue);
                                        }}
                                      />
                                      <Label htmlFor={kb.id}>{kb.name}</Label>
                                    </div>
                                  ))
                                ) : (
                                  <div className="text-sm text-muted-foreground">暂无可用MCP</div>
                                )}
                                <Button variant="outline" size="sm" type="button" asChild>
                                  <Link href="/tools/mcp/create">添加MCP</Link>
                                </Button>
                              </div>
                            </FormControl>
                            <FormDescription className="text-sm text-muted-foreground">选择Agent可以访问的MCP服务</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setTabStep('basic')}>上一步：基础信息</Button>
                    <Button onClick={() => setTabStep('advanced')}>
                      下一步：高级配置
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="advanced">
                  <Card>
                    <CardHeader>
                      <CardTitle>高级配置</CardTitle>
                      <CardDescription>配置Agent的高级参数和行为</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <FormField
                        control={control}
                        name="temperature"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>温度</FormLabel>
                            <FormControl>
                              <div className="flex items-center space-x-4">
                                <Input
                                  type="range"
                                  min="0"
                                  max="1"
                                  step="0.1"
                                  value={field.value}
                                  onChange={(e) => {
                                    field.onChange(Number(e.target.value));
                                  }}
                                />
                                <span className="w-10 text-center">{field.value}</span>
                              </div>
                            </FormControl>
                            <FormDescription>控制回答的随机性，较低的值使回答更确定，较高的值使回答更多样化</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={control}
                        name="max_tokens"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>最大Token数</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="最大Token数"
                                type="number"
                                value={field.value}
                                onChange={(e) => {
                                  field.onChange(Number(e.target.value));
                                }}
                              />
                            </FormControl>
                            <FormDescription>限制模型回答的最大长度</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={control}
                        name="top_p"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Top P</FormLabel>
                            <FormControl>
                              <div className="flex items-center space-x-4">
                                <Input
                                  type="range"
                                  min="0"
                                  max="1"
                                  step="0.1"
                                  value={field.value}
                                  onChange={(e) => {
                                    field.onChange(Number(e.target.value));
                                  }}
                                />
                                <span className="w-10 text-center">{field.value}</span>
                              </div>
                            </FormControl>
                            <FormDescription>控制模型考虑的词汇范围，较低的值使回答更聚焦</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={control}
                        name="max_retries"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>最大重试次数</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="最大重试次数"
                                type="number"
                                value={field.value}
                                onChange={(e) => {
                                  field.onChange(Number(e.target.value));
                                }}
                              />
                            </FormControl>
                            <FormDescription>限制模型最大重试次数</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={control}
                        name="timeout"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>会话超时时间</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="会话超时时间"
                                type="number"
                                value={field.value}
                                onChange={(e) => {
                                  field.onChange(Number(e.target.value));
                                }}
                              />
                            </FormControl>
                            <FormDescription>限制模型回答的会话超时时间</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* <div className="space-y-2">
                      <Label>高级选项</Label>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="streaming">流式响应</Label>
                            <p className="text-sm text-muted-foreground">启用流式响应，实时显示模型输出</p>
                          </div>
                          <Switch id="streaming" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="log_conversations">记录对话</Label>
                            <p className="text-sm text-muted-foreground">保存用户与Agent的对话历史</p>
                          </div>
                          <Switch id="log_conversations" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="use_tools">启用工具调用</Label>
                            <p className="text-sm text-muted-foreground">允许Agent调用外部工具和API</p>
                          </div>
                          <Switch id="use_tools" />
                        </div>
                      </div>
                    </div> */}
                    </CardContent>
                  </Card>

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setTabStep('type')}>上一步：类型配置</Button>
                    <Button onClick={() => setTabStep('preview')}>
                      下一步：预览与测试
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="preview">
                  <Card>
                    <CardHeader>
                      <CardTitle>预览与测试</CardTitle>
                      <CardDescription>预览Agent配置并进行测试</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div className="border rounded-md p-6 space-y-4">
                          <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 rounded-md bg-primary/10 flex items-center justify-center">
                              {iconUrl ? <img src={iconUrl} className="w-16 h-16 rounded-md" alt="" /> :
                                <Bot className="h-8 w-8 text-muted-foreground" />}
                            </div>
                            <div>
                              <h3 className="font-semibold text-lg">{watchedValues.name}</h3>
                              <p className="text-sm text-muted-foreground">{agentTypeOptions.find(item => item.value === watchedValues.agent_type)?.label || '客服Agent'}</p>
                            </div>
                          </div>

                          <Separator />

                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-muted-foreground">模型:</p>
                              <p>{models?.find(item => item.id === watchedValues.model_id)?.name || '未选择'}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">知识库:</p>
                              {knowledge && knowledge.length > 0 ? (
                                  knowledge.map((kb) => (
                                    watchedValues.knowledge_base_ids?.includes(kb.id) && <p>{kb.name}</p>
                                  ))
                                ) : (
                                  <div className="text-sm text-muted-foreground">暂无可用知识库</div>
                                )}
                            </div>
                            <div>
                              <p className="text-muted-foreground">可见范围:</p>
                              <p>租户内可见</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">描述:</p>
                              <p>{watchedValues.description || '未填写描述'}</p>
                            </div>
                          </div>
                        </div>

                        {/* <div className="border rounded-md p-6">
                          <h3 className="font-semibold mb-4">测试对话</h3>
                          <div className="space-y-4 mb-4">
                            <div className="bg-muted p-3 rounded-lg max-w-[80%] ml-auto">
                              <p>你好，我想了解一下你们的产品退货政策。</p>
                            </div>
                            <div className="bg-primary/10 p-3 rounded-lg max-w-[80%]">
                              <p>
                                您好！高兴为您提供帮助。根据我们的退货政策，您在收到商品后的14天内可以申请无理由退货。退货商品需保持原包装和未使用状态。如果是因为商品质量问题，我们将承担退货运费；如果是因为个人原因退货，运费需要您自行承担。
                              </p>
                              <p className="text-xs text-muted-foreground mt-2">来源: 产品手册知识库</p>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Input placeholder="输入测试问题..." className="flex-1" />
                            <Button type="button">发送</Button>
                          </div>
                        </div> */}
                      </div>
                    </CardContent>
                  </Card>

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setTabStep('advanced')}>上一步：高级配置</Button>
                    <div>
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        onClick={(e) => {
                          e.preventDefault();
                          console.log('Submit button clicked');
                          console.log('Form validation state:', { isValid, errors });
                          console.log('Current form values:', watchedValues);
                          handleSubmit((data) => {
                            console.log('Form submitted with data:', data);
                            onSubmit(data);
                          }, (errors) => {
                            console.log('Form validation failed:', errors);
                            toast.error('表单验证失败，请检查填写的内容');
                            // 如果在预览标签页，但表单有错误，跳转到有错误的标签页
                            if (errors.name || errors.description) {
                              setTabStep('basic');
                            } else if (errors.agent_type || errors.model_id || errors.prompt || errors.knowledge_base_ids) {
                              setTabStep('type');
                            }
                          })();
                        }}
                      >
                        {isSubmitting ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            创建中...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" />
                            完成{id ? '修改' : '创建'}
                          </>
                        )}
                      </Button>
                      {id && <Button
                        type="submit"
                        className="ml-2"
                        disabled={isSubmitting}
                        onClick={(e) => {
                          e.preventDefault();
                          handleSubmit((data) => {
                            onSubmit(data, true);
                          }, (errors) => {
                            console.log('Form validation failed:', errors);
                            toast.error('表单验证失败，请检查填写的内容');
                            // 如果在预览标签页，但表单有错误，跳转到有错误的标签页
                            if (errors.name || errors.description) {
                              setTabStep('basic');
                            } else if (errors.agent_type || errors.model_id || errors.prompt || errors.knowledge_base_ids) {
                              setTabStep('type');
                            }
                          })();
                        }}
                      >
                        {isSubmitting ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            创建中...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" />
                            修改并发布
                          </>
                        )}
                      </Button>}
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </form>
        </FormProvider>
      </div>
    </MainLayout>
  )
}
