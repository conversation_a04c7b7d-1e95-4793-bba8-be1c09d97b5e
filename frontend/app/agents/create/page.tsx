"use client"

import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Bot, Database, Lightbulb, Search, PenToolIcon as Tool, ArrowLeft, ChevronRight } from "lucide-react"
import { useRouter } from 'next/navigation'

// export const metadata: Metadata = {
//   title: "创建Agent | 云知问",
//   description: "云知问多租户智能Agent平台创建Agent",
// }

export default function CreateAgentPage() {
  const router = useRouter()
  const toCreate = () => {
    router.push('/agents/create/wizard')
  }
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href="/agents">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">创建Agent</h1>
            <p className="text-muted-foreground">选择Agent类型，开始创建您的智能Agent</p>
          </div>
        </div>

        <Tabs defaultValue="templates" className="space-y-4">
          <TabsList>
            <TabsTrigger value="templates">选择模板</TabsTrigger>
            <TabsTrigger value="custom">自定义创建</TabsTrigger>
          </TabsList>
          <TabsContent value="templates" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card className="cursor-pointer hover:border-primary/50 transition-colors">
                <CardHeader className="pb-2">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                    <Bot className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">客服Agent</CardTitle>
                  <CardDescription>智能客服，处理用户咨询，提供产品支持</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">适用场景:</span>
                      <span>客户支持、FAQ、产品咨询</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">推荐模型:</span>
                      <span>GPT-4、智谱GLM-4</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">推荐知识库:</span>
                      <span>产品手册、FAQ文档</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button className="w-full" onClick={toCreate}>
                      选择此模板
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:border-primary/50 transition-colors">
                <CardHeader className="pb-2">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                    <Database className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">知识库Agent</CardTitle>
                  <CardDescription>基于知识库的智能问答系统</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">适用场景:</span>
                      <span>文档问答、知识检索、学习辅助</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">推荐模型:</span>
                      <span>GPT-4、智谱GLM-4</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">推荐知识库:</span>
                      <span>技术文档、研究报告</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button className="w-full" onClick={toCreate}>
                      选择此模板
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:border-primary/50 transition-colors">
                <CardHeader className="pb-2">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                    <Lightbulb className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">意图分析Agent</CardTitle>
                  <CardDescription>分析用户意图，智能分类与路由</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">适用场景:</span>
                      <span>客服分流、需求分类、情感分析</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">推荐模型:</span>
                      <span>GPT-4、智谱GLM-4</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">推荐知识库:</span>
                      <span>意图分类库、业务规则库</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button className="w-full" onClick={toCreate}>
                      选择此模板
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:border-primary/50 transition-colors">
                <CardHeader className="pb-2">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                    <Search className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">检索Agent</CardTitle>
                  <CardDescription>高级文档检索与精准问答</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">适用场景:</span>
                      <span>文档搜索、精准检索、研究辅助</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">推荐模型:</span>
                      <span>GPT-4、智谱GLM-4</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">推荐知识库:</span>
                      <span>研究论文、法律文档</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button className="w-full" onClick={toCreate}>
                      选择此模板
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:border-primary/50 transition-colors">
                <CardHeader className="pb-2">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                    <Tool className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">工具Agent</CardTitle>
                  <CardDescription>API调用与工具集成</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">适用场景:</span>
                      <span>API集成、数据处理、自动化</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">推荐模型:</span>
                      <span>GPT-4、智谱GLM-4</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">推荐知识库:</span>
                      <span>API文档、工具使用手册</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button className="w-full" onClick={toCreate}>
                      选择此模板
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="custom" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>自定义Agent</CardTitle>
                <CardDescription>从零开始创建自定义Agent，完全按照您的需求配置</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <Card className="cursor-pointer hover:border-primary/50 transition-colors">
                      <CardHeader className="pb-2">
                        <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                          <Bot className="h-6 w-6 text-primary" />
                        </div>
                        <CardTitle className="text-lg">空白Agent</CardTitle>
                        <CardDescription>从零开始，完全自定义配置</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="text-sm space-y-2">
                          <p className="text-muted-foreground">
                            完全自定义的Agent，您可以自由配置所有参数，包括模型、知识库、Prompt等。
                          </p>
                        </div>
                        <div className="mt-4">
                          <Button className="w-full" onClick={toCreate}>
                            创建空白Agent
                            <ChevronRight className="ml-2 h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:border-primary/50 transition-colors">
                      <CardHeader className="pb-2">
                        <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                          <Database className="h-6 w-6 text-primary" />
                        </div>
                        <CardTitle className="text-lg">多Agent协作</CardTitle>
                        <CardDescription>创建多Agent协作系统</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="text-sm space-y-2">
                          <p className="text-muted-foreground">
                            创建多个Agent协作的系统，可以设计复杂的工作流，实现更强大的功能。
                          </p>
                        </div>
                        <div className="mt-4">
                          <Button className="w-full" onClick={toCreate}>
                            创建多Agent系统
                            <ChevronRight className="ml-2 h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
