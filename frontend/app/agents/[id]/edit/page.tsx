"use client"

import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Save, Bot } from "lucide-react"
import { use, useEffect } from "react"

export default function EditAgentPage({ params }: { params: Promise<{ id: string }> }) {

  useEffect(() => {
    document.title = "编辑Agent | 云知问"
  }, [])

  const agentId = use(params)?.id

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href={`/agents/${agentId}`}>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">编辑Agent</h1>
            <p className="text-muted-foreground">修改Agent配置和设置</p>
          </div>
        </div>

        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基础信息</TabsTrigger>
            <TabsTrigger value="type">类型配置</TabsTrigger>
            <TabsTrigger value="advanced">高级配置</TabsTrigger>
            <TabsTrigger value="preview">预览与测试</TabsTrigger>
          </TabsList>

          <TabsContent value="basic">
            <Card>
              <CardHeader>
                <CardTitle>基础信息</CardTitle>
                <CardDescription>修改Agent的基本信息和可见范围</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Agent名称</Label>
                  <Input id="name" defaultValue="客服Agent" />
                  <p className="text-sm text-muted-foreground">名称将显示在Agent列表和对话界面中</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Agent描述</Label>
                  <Textarea id="description" defaultValue="智能客服，处理用户咨询，提供产品支持" rows={3} />
                  <p className="text-sm text-muted-foreground">描述将帮助团队成员了解此Agent的用途</p>
                </div>

                <div className="space-y-2">
                  <Label>可见范围</Label>
                  <RadioGroup defaultValue="tenant">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="tenant" id="tenant" />
                      <Label htmlFor="tenant">租户内可见</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="public" id="public" />
                      <Label htmlFor="public">公开可见（可通过链接访问）</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="private" id="private" />
                      <Label htmlFor="private">仅创建者可见</Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="space-y-2">
                  <Label>标签</Label>
                  <div className="flex flex-wrap gap-2">
                    <div className="flex items-center space-x-2 border rounded-md px-3 py-1">
                      <Checkbox id="tag1" defaultChecked />
                      <Label htmlFor="tag1" className="text-sm">
                        客服
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 border rounded-md px-3 py-1">
                      <Checkbox id="tag2" defaultChecked />
                      <Label htmlFor="tag2" className="text-sm">
                        产品
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 border rounded-md px-3 py-1">
                      <Checkbox id="tag3" defaultChecked />
                      <Label htmlFor="tag3" className="text-sm">
                        FAQ
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 border rounded-md px-3 py-1">
                      <Checkbox id="tag4" />
                      <Label htmlFor="tag4" className="text-sm">
                        自动回复
                      </Label>
                    </div>
                    <Button variant="outline" size="sm">
                      添加标签
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="icon">Agent图标</Label>
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 rounded-md bg-primary/10 flex items-center justify-center">
                      <Bot className="h-8 w-8 text-primary" />
                    </div>
                    <Button variant="outline">更换图标</Button>
                  </div>
                  <p className="text-sm text-muted-foreground">推荐尺寸：256x256像素，支持PNG、JPG格式</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="type">
            <Card>
              <CardHeader>
                <CardTitle>类型配置</CardTitle>
                <CardDescription>修改Agent的类型和模型</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="agent_type">Agent类型</Label>
                  <Select defaultValue="customer_service">
                    <SelectTrigger id="agent_type">
                      <SelectValue placeholder="选择Agent类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="customer_service">客服Agent</SelectItem>
                      <SelectItem value="knowledge_base">知识库Agent</SelectItem>
                      <SelectItem value="intent_analysis">意图分析Agent</SelectItem>
                      <SelectItem value="retrieval">检索Agent</SelectItem>
                      <SelectItem value="tool">工具Agent</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">Agent类型决定了其基本功能和行为</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">大语言模型</Label>
                  <Select defaultValue="gpt-4o">
                    <SelectTrigger id="model">
                      <SelectValue placeholder="选择大语言模型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gpt-4o">OpenAI GPT-4o</SelectItem>
                      <SelectItem value="gpt-4">OpenAI GPT-4</SelectItem>
                      <SelectItem value="gpt-3.5-turbo">OpenAI GPT-3.5 Turbo</SelectItem>
                      <SelectItem value="glm-4">智谱 GLM-4</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">选择用于此Agent的大语言模型</p>
                </div>

                <div className="space-y-2">
                  <Label>关联知识库</Label>
                  <div className="border rounded-md p-4 space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="kb1" defaultChecked />
                      <Label htmlFor="kb1">产品手册知识库</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="kb2" />
                      <Label htmlFor="kb2">技术文档知识库</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="kb3" defaultChecked />
                      <Label htmlFor="kb3">FAQ知识库</Label>
                    </div>
                    <Button variant="outline" size="sm">
                      添加知识库
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">选择Agent可以访问的知识库</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="system_prompt">系统提示词</Label>
                  <Textarea
                    id="system_prompt"
                    rows={5}
                    defaultValue="你是一个专业的客服助手，负责回答用户关于我们产品的问题。请保持礼貌和专业，如果不确定答案，请告知用户你会转接给人工客服。"
                  />
                  <p className="text-sm text-muted-foreground">系统提示词用于指导模型的行为和回答方式</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced">
            <Card>
              <CardHeader>
                <CardTitle>高级配置</CardTitle>
                <CardDescription>修改Agent的高级参数和行为</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="temperature">温度</Label>
                  <div className="flex items-center space-x-4">
                    <Input
                      id="temperature"
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      defaultValue="0.7"
                      className="w-full"
                    />
                    <span className="w-10 text-center">0.7</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    控制回答的随机性，较低的值使回答更确定，较高的值使回答更多样化
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max_tokens">最大Token数</Label>
                  <Input id="max_tokens" type="number" defaultValue="2000" />
                  <p className="text-sm text-muted-foreground">限制模型回答的最大长度</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="top_p">Top P</Label>
                  <div className="flex items-center space-x-4">
                    <Input id="top_p" type="range" min="0" max="1" step="0.05" defaultValue="0.95" className="w-full" />
                    <span className="w-10 text-center">0.95</span>
                  </div>
                  <p className="text-sm text-muted-foreground">控制模型考虑的词汇范围，较低的值使回答更聚焦</p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>知识库检索设置</Label>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="retrieval_top_k">检索结果数量</Label>
                      <Input id="retrieval_top_k" type="number" defaultValue="5" />
                      <p className="text-sm text-muted-foreground">从知识库中检索的最大结果数量</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="similarity_threshold">相似度阈值</Label>
                      <div className="flex items-center space-x-4">
                        <Input
                          id="similarity_threshold"
                          type="range"
                          min="0"
                          max="1"
                          step="0.05"
                          defaultValue="0.7"
                          className="w-full"
                        />
                        <span className="w-10 text-center">0.7</span>
                      </div>
                      <p className="text-sm text-muted-foreground">检索结果的最低相似度要求</p>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>高级选项</Label>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="streaming">流式响应</Label>
                        <p className="text-sm text-muted-foreground">启用流式响应，实时显示模型输出</p>
                      </div>
                      <Switch id="streaming" defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="log_conversations">记录对话</Label>
                        <p className="text-sm text-muted-foreground">保存用户与Agent的对话历史</p>
                      </div>
                      <Switch id="log_conversations" defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="use_tools">启用工具调用</Label>
                        <p className="text-sm text-muted-foreground">允许Agent调用外部工具和API</p>
                      </div>
                      <Switch id="use_tools" defaultChecked />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preview">
            <Card>
              <CardHeader>
                <CardTitle>预览与测试</CardTitle>
                <CardDescription>预览Agent配置并进行测试</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="border rounded-md p-6 space-y-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 rounded-md bg-primary/10 flex items-center justify-center">
                        <Bot className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">客服Agent</h3>
                        <p className="text-sm text-muted-foreground">客服Agent</p>
                      </div>
                    </div>

                    <Separator />

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">模型:</p>
                        <p>OpenAI GPT-4o</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">知识库:</p>
                        <p>产品手册知识库, FAQ知识库</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">可见范围:</p>
                        <p>租户内可见</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">标签:</p>
                        <p>客服, 产品, FAQ</p>
                      </div>
                    </div>
                  </div>

                  <div className="border rounded-md p-6">
                    <h3 className="font-semibold mb-4">测试对话</h3>
                    <div className="space-y-4 mb-4">
                      <div className="bg-muted p-3 rounded-lg max-w-[80%] ml-auto">
                        <p>你好，我想了解一下你们的产品退货政策。</p>
                      </div>
                      <div className="bg-primary/10 p-3 rounded-lg max-w-[80%]">
                        <p>
                          您好！很高兴为您提供帮助。根据我们的退货政策，您在收到商品后的14天内可以申请无理由退货。退货商品需保持原包装和未使用状态。如果是因为商品质量问题，我们将承担退货运费；如果是因为个人原因退货，运费需要您自行承担。
                        </p>
                        <p className="text-xs text-muted-foreground mt-2">来源: 产品手册知识库</p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Input placeholder="输入测试问题..." className="flex-1" />
                      <Button>发送</Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" asChild>
            <Link href={`/agents/${agentId}`}>取消</Link>
          </Button>
          <Button>
            <Save className="mr-2 h-4 w-4" />
            保存更改
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
