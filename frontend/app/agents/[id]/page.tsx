"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  MessageSquare,
  Edit,
  Play,
  Pause,
  BarChart,
  Database,
  ArrowRight,
} from "lucide-react"
import { apiClient } from "@/lib/api/api"
import { components } from "@/lib/api/strapi"
import { useAuth } from "@/contexts/auth-context"
import { MainLayout } from "@/components/main-layout"
import dayjs from "dayjs"
type Agent = components["schemas"]["Agent"]
type AgentVersion = components["schemas"]["AgentVersion"]

// 定义统计数据接口
interface AgentStats {
  totalChats: number
  totalMessages: number
  avgResponseTime: string
  satisfactionRate: string
}

// 定义最近对话接口
interface RecentChat {
  id: string
  user: string
  preview: string
  time: string
}

// 定义知识库接口
interface KnowledgeBase {
  id: string
  name: string
  // documentCount: number
}

export default function AgentDetailPage() {
  const router = useRouter()
  const params = useParams()
  const [agentId, setAgentId] = useState<string>("")
  const { currentTenant } = useAuth()
  const [agent, setAgent] = useState<Agent | null>(null)
  const [agentCurrent, setAgentCurrent] = useState<AgentVersion | null>(null)
  const [isActive, setIsActive] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // 模拟统计数据
  const [stats, setStats] = useState<AgentStats>({
    totalChats: 0,
    totalMessages: 0,
    avgResponseTime: "0s",
    satisfactionRate: "0%"
  })

  // 模拟最近对话数据
  const [recentChats, setRecentChats] = useState<RecentChat[]>([])

  // 模拟知识库数据
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([])

  // 切换激活状态
  const toggleStatus = async () => {
    try {
      const { data, error } = await apiClient.PUT(`/api/v1/tenants/{tenant_id}/agents/{agent_id}`, {
        params: {
          path: {
            tenant_id: currentTenant?.tenant_id ?? '',
            agent_id: agentId,
          }
        },
        body: {
          name: agent?.name ?? '',
          is_active: !isActive
        }
      })

      if (error) {
        console.error("Error updating agent status:", error)
        return
      }

      if (data) {
        setIsActive(data.is_active)
        setAgent(data)
      }
    } catch (err) {
      console.error("Failed to update agent status:", err)
    }
  }

  const getAgent = async () => {
    setIsLoading(true)
    try {
      const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents/{agent_id}`, {
        params: {
          path: {
            tenant_id: currentTenant?.tenant_id ?? '',
            agent_id: agentId,
          }
        }
      })

      if (error) {
        console.error("Error fetching agent:", error)
        setIsLoading(false)
        return
      }

      if (data) {
        setAgent(data)
        setIsActive(data.is_active)
        const info = data.versions.length > 1 ? data.versions.filter(item => item.is_current)[0] : data.versions[0]
        setAgentCurrent(info)
        // 模拟设置统计数据
        setStats({
          totalChats: Math.floor(Math.random() * 100),
          totalMessages: Math.floor(Math.random() * 1000),
          avgResponseTime: `${(Math.random() * 2).toFixed(1)}s`,
          satisfactionRate: `${Math.floor(Math.random() * 30) + 70}%`
        })

        // 模拟最近对话
        setRecentChats([
          // { id: '1', user: '用户A', preview: '你好，我想了解一下这个产品', time: '10分钟前' },
          // { id: '2', user: '用户B', preview: '如何使用这个功能？', time: '30分钟前' },
          // { id: '3', user: '用户C', preview: '谢谢你的帮助', time: '1小时前' },
        ])

        // 知识库数据
        const { data: kbData } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents/available-knowledge-bases`, {
          params: {
            path: {
              tenant_id: currentTenant?.tenant_id ?? '',
            }
          }
        })
        const list = info?.knowledge_base_ids || []
        setKnowledgeBases(
          list.map((item: string) => {
            const kb = Array.isArray(kbData) ? kbData.find((kb: any) => kb.id === item) : null;
            return {
              id: item,
              name: kb?.name || `知识库${item}`,
            };
          })
        )
      }
    } catch (err) {
      console.error("Failed to fetch agent:", err)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (params?.id) {
      setAgentId(params.id as string)
    }
  }, [params])

  // 组件加载时获取Agent数据
  useEffect(() => {
    if (currentTenant?.tenant_id && agentId) {
      getAgent()
    }
  }, [currentTenant, agentId])

  if (isLoading) {
    return <div className="container py-6 flex items-center justify-center">加载中...</div>
  }

  if (!agent) {
    return <div className="container py-6">未找到Agent信息</div>
  }

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={agent.icon_access_url || "/avatar.svg"} alt={agent.name} />
              <AvatarFallback>{agent.name.substring(0, 2)}</AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-2xl font-bold">{agent.name}</h1>
              <p className="text-muted-foreground">{agent.description}</p>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant={isActive ? "default" : "secondary"}>{isActive ? "已激活" : "未激活"}</Badge>
                <Badge variant="outline">{agent.model_id || "默认模型"}</Badge>
                <span className="text-xs text-muted-foreground">
                  最后更新: {dayjs(agent.updated_at).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={toggleStatus}>
              {isActive ? <Pause className="mr-2 h-4 w-4" /> : <Play className="mr-2 h-4 w-4" />}
              {isActive ? "暂停" : "激活"}
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href={`/agents/create/wizard?id=${agent.id}`}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Link>
            </Button>
            <Button size="sm" asChild>
              <Link href={`/agents/${agent.id}/chat`}>
                <MessageSquare className="mr-2 h-4 w-4" />
                开始对话
              </Link>
            </Button>
            {/* <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Agent操作</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Copy className="mr-2 h-4 w-4" />
                  复制Agent
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  高级设置
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-destructive">
                  <Trash className="mr-2 h-4 w-4" />
                  删除Agent
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu> */}
          </div>
        </div>

        <Tabs defaultValue="overview">
          <TabsList>
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="knowledge">知识库</TabsTrigger>
            <TabsTrigger value="analytics">分析</TabsTrigger>
            <TabsTrigger value="settings">设置</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">总对话数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalChats}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">总消息数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalMessages}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">平均响应时间</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.avgResponseTime}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">满意度</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.satisfactionRate}</div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>最近对话</CardTitle>
                <CardDescription>用户与此Agent的最近交互</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentChats.map((chat) => (
                    <div key={chat.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>{chat.user.substring(0, 2)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{chat.user}</div>
                          <div className="text-sm text-muted-foreground truncate max-w-[300px]">{chat.preview}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-muted-foreground">{chat.time}</span>
                        <Button variant="ghost" size="icon" onClick={() => router.push(`/agents/${agent.id}/chat`)}>
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/agents/${agent.id}/chat`}>
                    <MessageSquare className="mr-2 h-4 w-4" />
                    开始新对话
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="knowledge" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>关联知识库</CardTitle>
                <CardDescription>此Agent使用的知识库资源</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {knowledgeBases.map((kb) => (
                    <div key={kb.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-md bg-primary/10 flex items-center justify-center">
                          <Database className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <div className="font-medium">{kb.name}</div>
                          {/* <div className="text-sm text-muted-foreground">{kb.documentCount} 个文档</div> */}
                        </div>
                      </div>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/knowledge-bases/${kb.id}`}>查看</Link>
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/agents/create/wizard?id=${agent.id}`}>
                    <Database className="mr-2 h-4 w-4" />
                    添加知识库
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>使用分析</CardTitle>
                <CardDescription>Agent的使用情况和性能分析</CardDescription>
              </CardHeader>
              <CardContent className="h-[400px] flex items-center justify-center">
                <div className="text-center">
                  <BarChart className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-medium">分析数据加载中</h3>
                  <p className="text-muted-foreground">详细的使用分析将在此显示</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Agent设置</CardTitle>
                <CardDescription>管理Agent的基本设置和配置</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium mb-2">基本信息</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">创建时间</span>
                          <span>{dayjs(agent.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">最后更新</span>
                          <span>{dayjs(agent.updated_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">使用模型</span>
                          <span>{agentCurrent?.model_id || "默认模型"}</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-2">高级设置</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">温度</span>
                          <span>{agentCurrent?.llm_model_config?.temperature || '--'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">最大响应长度</span>
                          <span>{agentCurrent?.llm_model_config?.max_tokens || '--'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">知识库检索数量</span>
                          <span>{agentCurrent?.llm_model_config?.top_p || '--'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" asChild>
                  <Link href={`/agents/create/wizard?id=${agent.id}`}>
                    <Edit className="mr-2 h-4 w-4" />
                    编辑设置
                  </Link>
                </Button>
                {/* <Button variant="default">
                  <Settings className="mr-2 h-4 w-4" />
                  高级配置
                </Button> */}
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
