import { Skeleton } from "@/components/ui/skeleton"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"

export default function AgentChatLoading() {
  return (
    <div className="flex flex-col h-[calc(100vh-4rem)]">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div>
            <Skeleton className="h-6 w-40 mb-1" />
            <div className="flex items-center space-x-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Skeleton className="h-8 w-8 rounded-md" />
          <Skeleton className="h-8 w-8 rounded-md" />
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Chat area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <div className="flex-1 p-4">
            <div className="max-w-3xl mx-auto">
              <div className="flex mb-6">
                <Avatar className="h-10 w-10 mt-1 mr-3">
                  <AvatarFallback>AI</AvatarFallback>
                </Avatar>
                <div className="max-w-[80%]">
                  <Skeleton className="h-32 w-full rounded-lg" />
                  <div className="flex items-center mt-1">
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Input area */}
          <div className="p-4 border-t">
            <div className="max-w-3xl mx-auto">
              <Skeleton className="h-[80px] w-full rounded-md mb-2" />
              <Skeleton className="h-4 w-48" />
            </div>
          </div>
        </div>

        {/* References sidebar */}
        <div className="w-80 border-l overflow-hidden flex flex-col">
          <div className="p-4 border-b flex items-center justify-between">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-8 w-8 rounded-md" />
          </div>
          <div className="p-4">
            <Skeleton className="h-8 w-full mb-4" />
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-32 w-full rounded-md" />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
