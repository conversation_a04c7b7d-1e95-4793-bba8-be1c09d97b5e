"use client"

import React, { useEffect, useState } from "react"
import { ChatStreamWindow } from "@/components/chat-stream-window"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { MessageSquare, Settings } from "lucide-react"
import { useParams } from "next/navigation"
import { MainLayout } from "@/components/main-layout"



export default function ChatPage() {
    const [agentId, setAgentId] = useState('')
    const params = useParams()
    const [config] = useState({
        threadId: "default",
    })


    useEffect(() => {
        if (params?.id) {
            setAgentId(params.id as string)
        }
    }, [params])

    return (
        <MainLayout>
            <div className="container py-6 space-y-6">
                <div className="flex flex-col gap-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
                                <MessageSquare className="h-8 w-8" />
                                AI 对话
                            </h1>
                            <p className="text-muted-foreground mt-2">与AI助手进行实时流式对话</p>
                        </div>
                    </div>

                    {/* 聊天窗口 */}
                    {agentId ? (
                        <ChatStreamWindow
                            agentId={agentId}
                            threadId={config.threadId}
                        />
                    ) : (
                        <Card>
                            <CardContent className="flex items-center justify-center h-96">
                                <div className="text-center">
                                    <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-semibold mb-2">需要配置</h3>
                                    <p className="text-muted-foreground mb-4">请先配置Agent连接参数</p>
                                    <Button>
                                        <Settings className="mr-2 h-4 w-4" />
                                        开始配置
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </MainLayout>
    )
}
