"use client"

import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { ArrowLeft, Save } from "lucide-react"
import { use, useEffect, useState } from "react"
import { components } from "@/lib/api/strapi"

type EnabledProviderItem = components["schemas"]["EnabledProviderItem"]

export default function EditProviderPage({ params }: { params: Promise<{ id: string }> }) {
  useEffect(() => {
    document.title = "编辑提供商 | 云知问"
  }, [])
  const providerId = use(params)?.id
  const [provider] = useState<EnabledProviderItem | undefined>()
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href={`/providers/${providerId}`}>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">编辑模型提供商</h1>
            <p className="text-muted-foreground">修改 {provider?.provider.name} 的信息</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
            <CardDescription>修改模型提供商的基本信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">提供商名称</Label>
              <Input id="name" defaultValue={provider?.provider.name} />
              <p className="text-xs text-muted-foreground">提供商名称必须唯一</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="provider_type">提供商类型</Label>
              <Select defaultValue={provider?.provider.provider_type}>
                <SelectTrigger id="provider_type">
                  <SelectValue placeholder="选择提供商类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="llm">LLM (大语言模型)</SelectItem>
                  <SelectItem value="embedding">Embedding (嵌入模型)</SelectItem>
                  <SelectItem value="llm_embedding">LLM/Embedding (混合)</SelectItem>
                  <SelectItem value="custom">自定义</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea id="description" defaultValue={provider?.provider.description ?? ''} rows={3} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="is_active">激活状态</Label>
                <p className="text-sm text-muted-foreground">设置提供商是否处于激活状态</p>
              </div>
              <Switch id="is_active" defaultChecked={true} />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" asChild>
            <Link href={`/providers/${providerId}`}>取消</Link>
          </Button>
          <Button>
            <Save className="mr-2 h-4 w-4" />
            保存更改
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
