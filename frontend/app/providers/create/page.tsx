"use client"
import { useEffect } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { ArrowLeft, Save, Plus, Trash2 } from "lucide-react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { apiClient } from "@/lib/api/api"
import { useSearchParams } from 'next/navigation'
import { useAuth } from "@/contexts/auth-context"

export default function CreateProviderPage() {
  useEffect(() => {
    document.title = "创建模型提供商 | 云知问"
  }, [])
  const urlParams = useSearchParams();
  const id = urlParams.get('id');
  const { currentTenant } = useAuth()
  const formSchema = z.object({
    name: z.string({
      message: "请输入内容",
    }).min(2, {
      message: "请输入名称",
    }),
    type: z.string({
      message: "请选择类型",
    }),
    description: z.string().min(2, {
      message: "请输入描述",
    }),
    is_active: z.boolean(),
    api_key: z.string().min(2, {
      message: "请输入API key",
    }),
    api_base_url: z.string().min(2, {
      message: "请输入url",
    }),
    advancedConfiguration: z.array(z.object({
      key: z.string().min(2, {
        message: "请输入配置key",
      }),
      value: z.string().min(2, {
        message: "请输入配置value",
      }),
    }))
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      is_active: false,
      api_key: "",
      api_base_url: "",
      advancedConfiguration: [],
    },
  })

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    if (id) {
      console.log('update', values)
    } else {
      console.log('create', values)
    }
  }
  const getData = async (id: string) => {
    const { error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          kb_id: id,
        }
      }
    })
    if (error) {
      console.log('error', error)
    } else {

    }
  }
  const addAdvancedConfiguration = () => {
    const info = form.getValues('advancedConfiguration')
    form.setValue('advancedConfiguration', [...info, { key: '', value: '' }])
  }
  const setKey = (index: number, value: string) => {
    const info = form.getValues('advancedConfiguration')
    info[index].key = value
    form.setValue('advancedConfiguration', info)
  }
  const setValue = (index: number, value: string) => {
    const info = form.getValues('advancedConfiguration')
    info[index].value = value
    form.setValue('advancedConfiguration', info)
  }
  const removeAdvancedConfiguration = (index: number) => {
    const info = form.getValues('advancedConfiguration')
    info.splice(index, 1)
    form.setValue('advancedConfiguration', info)
  }
  useEffect(() => {
    id && getData(id.toString())
  }, [id])
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href="/providers">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">创建模型提供商</h1>
            <p className="text-muted-foreground">添加新的模型提供商</p>
          </div>
        </div>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
                <CardDescription>设置模型提供商的基本信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>提供商名称</FormLabel>
                      <FormControl>
                        <Input placeholder="输入提供商名称，如：OpenAI" {...field} />
                      </FormControl>
                      <FormDescription>提供商名称必须唯一</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>提供商类型</FormLabel>
                      <FormControl>
                        <Select {...field} onValueChange={(value: string) => field.onChange(value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="选择提供商类型" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="llm">LLM (大语言模型)</SelectItem>
                            <SelectItem value="embedding">Embedding (嵌入模型)</SelectItem>
                            <SelectItem value="llm_embedding">LLM/Embedding (混合)</SelectItem>
                            <SelectItem value="custom">自定义</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>描述</FormLabel>
                      <FormControl>
                        <Textarea placeholder="简要描述此提供商的功能和特点" {...field} />
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>激活状态</FormLabel>
                      <FormControl>
                        <Switch checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormDescription>设置提供商是否处于激活状态</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>配置信息</CardTitle>
                <CardDescription>设置提供商的API配置信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="api_key"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API Key</FormLabel>
                      <FormControl>
                        <Input placeholder="输入API Key" {...field} />
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="api_base_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API Base URL</FormLabel>
                      <FormControl>
                        <Input placeholder="输入API基础URL，如：https://api.openai.com/v1" {...field} />
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="advancedConfiguration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>高级配置</FormLabel>
                      <FormControl>
                        <div className="border rounded-md p-4 space-y-4">
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <Label>配置项</Label>
                              <Button variant="outline" onClick={() => addAdvancedConfiguration()} size="sm">
                                <Plus className="h-3 w-3 mr-1" />
                                添加配置
                              </Button>
                            </div>

                            <div className="space-y-2">
                              {field.value.map((item, index) => <div className="flex items-center space-x-2" key={index}>
                                <Input placeholder="配置项名称" value={item.key} onChange={e => setKey(index, e.target.value)} className="flex-1" />
                                <Input placeholder="配置项值" value={item.value} onChange={e => setValue(index, e.target.value)} className="flex-1" />
                                <Button variant="ghost" onClick={() => removeAdvancedConfiguration(index)} size="icon">
                                  <Trash2 className="h-4 w-4 text-muted-foreground" />
                                </Button>
                              </div>)}
                            </div>
                          </div>
                        </div>
                      </FormControl>
                      <FormDescription>高级配置将以JSON格式存储</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" asChild>
                <Link href="/providers">取消</Link>
              </Button>
              <Button type="submit">
                <Save className="mr-2 h-4 w-4" />
                {
                  id ? '修改' : '创建'
                }提供商
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </MainLayout>
  )
}
