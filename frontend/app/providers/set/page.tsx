"use client"
import { useEffect, useState } from "react"
import { MainLayout } from "@/components/main-layout"
import JsonSchemaForm from '@/components/json-schema-from';
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from 'next/navigation'
import { useToast } from "@/hooks/use-toast"
import { apiClient } from "@/lib/api/api"
import { components } from "@/lib/api/strapi";

type AvailableProviderItem = components["schemas"]["AvailableProviderItem"]
export default function CreateProviderPage() {
  const router = useRouter()
  const { currentTenant } = useAuth()
  const [tableData, setTableData] = useState<AvailableProviderItem[] | undefined>([])
  const [schema, setSchema] = useState<Record<string, any>>()
  const [name, setName] = useState<string>('')
  const [provider, setProvider] = useState<any>({})
  const handleSubmit = (data: any) => {
    postData(data, provider.id as string)
  }
  const postData = async (values: any, providerId: string) => {
    const { error } = await apiClient.POST(`/api/v1/tenants/{tenant_id}/enable-provider/{provider_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          provider_id: providerId,
        }
      },
      body: {
        name: name,
        credentials: values
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      router.push('/providers')
      useToast().toast.success("提交成功", {
        description: `创建成功`
      })
    }
  }
  const getData = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/available-providers`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        }
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      setTableData(data.items)
    }
  }
  useEffect(() => {
    document.title = "创建模型提供商 | 云知问"
    getData()
  }, [currentTenant])
  return (
    <MainLayout>        
      <div className="container py-6 space-y-6"> 
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">模型提供商设置</h1>
            <p className="text-muted-foreground">模型提供商管理参数设置</p>
          </div>
        </div>
        <Card>
          <CardContent className="space-y-4 flex">
            <Select onValueChange={(value: string) => {
              setSchema(JSON.parse(value).config_schema);
              setProvider(JSON.parse(value))}}>
              <SelectTrigger>
                <SelectValue placeholder="请选择供应商" />
              </SelectTrigger>
              <SelectContent>
                {tableData?.map(item => <SelectItem key={item.key} value={JSON.stringify(item)}>{item.name}</SelectItem>)}
              </SelectContent>
            </Select>
            <Input className="ml-2" onChange={e => setName(e.target.value)} placeholder="请输入名称" />
          </CardContent>
        </Card>
        {!!schema && <Card>
          <CardContent className="space-y-4">
            <JsonSchemaForm schema={schema} onSubmit={handleSubmit} />
          </CardContent>
        </Card>}
      </div>
    </MainLayout>
  )
}
