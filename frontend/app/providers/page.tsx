"use client"
import { useEffect, useState } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, MoreHorizontal, Trash2,  Check } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { apiClient } from "@/lib/api/api"
import { useToast } from "@/hooks/use-toast"
import { components } from "@/lib/api/strapi"
import dayjs from 'dayjs';
import { available } from "@/lib/utils"
import { Avatar, AvatarImage } from "@/components/ui/avatar"
import { AspectRatio } from "@radix-ui/react-aspect-ratio"

type EnabledProviderItem = components["schemas"]["EnabledProviderItem"]
export default function ProvidersPage() {
  const { currentTenant } = useAuth()
  const [tableData, setTableData] = useState<EnabledProviderItem[] | undefined>([])
  const [active, setActive] = useState<string>('all')

  const getData = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/enabled-providers`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        }
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      setTableData(data.items)
    }
  }
  const toDelete = async (id: string) => {
    useToast().toast.info("", {
      description: `确认删除提供商？`,
      action: {
        label: "确认",
        onClick: async() => {
          const { error } = await apiClient.DELETE(`/api/v1/tenants/{tenant_id}/disable-provider/{credential_id}`, {
            params: {
              path: {
                tenant_id: currentTenant?.tenant_id ?? '',
                credential_id: id,
              }
            }
          })
          if (error) {
            console.log('error', error)
          } else {
            // 
            getData()
          }
        },
      }
    })
  }

  useEffect(() => {
    document.title = "模型提供商管理 | 云知问"
    getData()
  }, [active, currentTenant])

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">模型提供商管理</h1>
            <p className="text-muted-foreground">云知问多租户智能Agent平台模型提供商管理</p>
          </div>
          <div>
            {available(currentTenant) && <Button asChild>
              <Link href="/providers/set">
                <Plus className="mr-2 h-4 w-4" />
                添加提供商
              </Link>
            </Button>}
          </div>
        </div>

        <Tabs value={active} onValueChange={e => setActive(e)} className="space-y-4">
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              {/* <TabsTrigger value="active">已激活</TabsTrigger> */}
              {/* <TabsTrigger value="inactive">未激活</TabsTrigger> */}
            </TabsList>
            <div className="flex items-center space-x-2">
              {/* <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" placeholder="搜索提供商..." className="pl-8 w-[200px] md:w-[300px]" />
              </div> */}
            </div>
          </div>

          <Card className="px-4">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[250px]">提供商名称</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>模型数量</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tableData?.map((item) => <TableRow key={item.credential_id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <div className="w-8 h-8 rounded-md bg-primary/10 flex items-center justify-center">
                            {item.provider.icon ? <img src={item.provider.icon} className="w-8 h-auto" alt="icon" /> : 
                            <span className="text-primary font-semibold">{item.provider.name.substring(0, 1)}</span>}
                          </div>
                          <span>{item.provider.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{item.provider.provider_type}</Badge>
                      </TableCell>
                      <TableCell className="max-w-[300px] truncate">
                        {item?.provider?.description}
                      </TableCell>
                      <TableCell>{item.model_count}</TableCell>
                      <TableCell>
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                          <Check className="mr-1 h-3 w-3" />
                          已激活
                        </Badge>
                      </TableCell>
                      <TableCell>{dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')}</TableCell>
                      <TableCell className="text-right">
                        {available(currentTenant) && <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {/* <DropdownMenuItem asChild>
                              <Link href={`/providers/${item.credential_id}`}>
                                <ExternalLink className="mr-2 h-4 w-4" />
                                <span>查看详情</span>
                              </Link>
                            </DropdownMenuItem> */}
                            {/* <DropdownMenuItem asChild>
                              <Link href="/providers/1/edit">
                                <Edit className="mr-2 h-4 w-4" />
                                <span>编辑</span>
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Layers className="mr-2 h-4 w-4" />
                              <span>查看模型</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Settings className="mr-2 h-4 w-4" />
                              <span>配置</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator /> */}
                            <DropdownMenuItem className="text-destructive" onClick={() => toDelete(item?.credential_id as string)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>删除</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>}
                      </TableCell>
                    </TableRow>)}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
        </Tabs>
      </div>
    </MainLayout>
  )
}
