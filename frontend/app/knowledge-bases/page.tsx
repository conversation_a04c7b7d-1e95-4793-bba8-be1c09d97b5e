"use client"
import { useEffect, useState } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { PaginationBox } from "@/components/pagination"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Plus, MoreHorizontal, FileText, Edit, Trash2, FileChartColumn } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { apiClient } from "@/lib/api/api"
import { useRouter } from 'next/navigation'
import { Empty } from "@/components/empty"
import dayjs from 'dayjs';
import { useToast } from "@/hooks/use-toast"
import { available } from "@/lib/utils"


export default function KnowledgeBasesPage() {
  const router = useRouter()

  const { currentTenant } = useAuth()
  const [tableData, setTableData] = useState<any[] | undefined>([])
  const [active, setActive] = useState<string>('all')
  const [paginationInfo, setPaginationInfo] = useState({
    totalPages: 0,
    current: 1,
    skip: 0,
    limit: 10,
  })
  const changePage = (page: number) => {
    setPaginationInfo({
      ...paginationInfo,
      current: page,
      skip: (page - 1) * paginationInfo.limit,
    })
  }
  const getData = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/knowledge/bases`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        },
        query: {
          skip: paginationInfo.skip,
          limit: paginationInfo.limit,
          is_active: active == 'all' ? undefined : true,
        }
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      setTableData(data.items)
      setPaginationInfo({
        ...paginationInfo,
        totalPages: Math.ceil(data.total / paginationInfo.limit),
      })
    }
    return data
  }
  const toDelete = async (id: string) => {
    useToast().toast.info("", {
      description: `确认删除知识库？`,
      action: {
        label: "确认",
        onClick: async() => {
          const { error } = await apiClient.DELETE(`/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}`, {
            params: {
              path: {
                tenant_id: currentTenant?.tenant_id ?? '',
                kb_id: id,
              }
            }
          })
          if (error) {
            console.log('error', error)
          } else {
            getData()
          }
        },
      }
    })
  }
  useEffect(() => {
    document.title = "知识库管理 | 云知问"
    getData()
  }, [active, paginationInfo.current, currentTenant])
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">知识库管理</h1>
            <p className="text-muted-foreground">创建、配置和管理您的知识库</p>
          </div>
          <div>
            {available(currentTenant) && <Button asChild>
              <Link href="/knowledge-bases/create">
                <Plus className="mr-2 h-4 w-4" />
                创建知识库
              </Link>
            </Button>}
          </div>
        </div>

        <Tabs value={active} onValueChange={e => setActive(e)} className="space-y-4">
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="active">已激活</TabsTrigger>
              {/* <TabsTrigger value="building">构建中</TabsTrigger>
              <TabsTrigger value="error">错误</TabsTrigger> */}
            </TabsList>
            <div className="flex items-center space-x-2">
              {/* <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" placeholder="搜索知识库..." className="pl-8 w-[200px] md:w-[300px]" />
              </div> */}
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {tableData?.map(item => <Card key={item.id}>
                <CardContent className="p-0">
                  <div className="p-6 border-b">
                    <div className="flex justify-between items-start">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-semibold text-lg">{item.name}</h3>
                          {item?.is_active && <Badge
                            variant="outline"
                            className="bg-green-50 text-green-700 dark:bg-green-900 dark:text-green-100 border-green-200 dark:border-green-800"
                          >
                            已激活
                          </Badge>}
                        </div>
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      </div>
                      {available(currentTenant) && <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <Link href={`/knowledge-bases/create?id=${item?.id}`} className="w-full block">
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>编辑</span>
                            </DropdownMenuItem> 
                          </Link>
                          {/* <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" />
                            <span>复制</span>
                          </DropdownMenuItem> */}
                          {/* <DropdownMenuItem>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            <span>重建索引</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator /> */}
                          <DropdownMenuItem className="text-destructive" onClick={() => toDelete(item?.id as string)}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            <span>删除</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>}
                    </div>
                  </div>
                  <div className="p-6 space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">文档数量</p>
                        <p>--</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">关联Agent</p>
                        <p>--</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">创建时间</p>
                        <p>{dayjs(item?.created_at).format('YYYY-MM-DD HH:mm:ss')}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">更新时间</p>
                        <p>{dayjs(item?.updated_at).format('YYYY-MM-DD HH:mm:ss')}</p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                    
                      {/* <Link href={`/knowledge-bases/${item.id}/documents`} className="w-full block"> */}
                      {available(currentTenant) && <Button onClick={() => router.push(`/knowledge-bases/${item.id}/documents`)} variant="outline" size="sm" className="w-full">
                        <FileText className="mr-2 h-3 w-3" />
                        管理文档
                      </Button>}
                      {/* </Link> */}
                      <Button size="sm" onClick={() => router.push(`/knowledge-bases/${item.id}`)} className="w-full">
                        <FileChartColumn className="mr-2 h-3 w-3" />
                        {/* 关联Agent */}
                        详情
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>)}
              { !tableData?.length && <Empty /> }
            </div>
            <PaginationBox totalPages={paginationInfo.totalPages} current={paginationInfo.current} changePage={changePage} />
        </Tabs>
      </div>
    </MainLayout>
  )
}
