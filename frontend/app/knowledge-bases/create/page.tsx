"use client"
import { useEffect, useState } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { ArrowLeft, Save, Loader2 } from "lucide-react"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { apiClient } from "@/lib/api/api"
import { useRouter, useSearchParams } from 'next/navigation'
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"

export default function CreateKnowledgeBasePage() {
  useEffect(() => {
    document.title = "创建知识库 | 云知问"
  }, [])
  const router = useRouter()
  const urlParams = useSearchParams();
  const id = urlParams.get('id');
  const { currentTenant } = useAuth()

  const [embeddingInfo, setEmbeddingInfo] = useState<{ name: string, value: string }[]>([])
  const [indexType, setIndexType] = useState<string>('')


  const formSchema = z.object({
    name: z.string().min(2, {
      message: "请输入知识库名称",
    }),
    description: z.string().min(2, {
      message: "请输入知识库描述",
    }),
    // visibility: z.string().min(2, {
    //   message: "请选择可见范围",
    // }),
    // tag: z.string().min(0, {
    //   message: "请输入标签信息",
    // }),
    // vectorDatabaseType: z.string().min(0, {
    //   message: "请选择向量数据库类型",
    // }),
    // vectorDatabaseApiKey: z.string().min(0, {
    //   message: "请输入API Key",
    // }),
    // vectorDatabaseServiceUrl: z.string().min(0, {
    //   message: "请输入服务端点URL",
    // }),
    // vectorDatabaseIndexName: z.string().min(0, {
    //   message: "请输入索引名称",
    // }),
    embeddingModelsType: z.string().min(1, {
      message: "请选择类型",
    }),
    indexType: z.string().min(0, {
      message: "请选择类型",
    }),
    indexThreshold: z.number().min(0, {
      message: "请输入阈值",
    }),
    indexResults: z.number().int().min(0, {
      message: "请输入检索数量",
    }),
    indexWeight: z.number().min(0, {
      message: "请输入权重",
    }).transform((value) => {
      return Number(value.toFixed(1))
    }),
    // embeddingModelsApiKey: z.string().min(0, {
    //   message: "请输入API Key",
    // }),
    // embeddingModelsServiceUrl: z.string().min(0, {
    //   message: "请输入服务端点URL",
    // }),
    // embeddingModelsVectorDimension: z.number().int().min(0, {
    //   message: "请输入向量维度",
    // }),
    advancedSettingsForDocumentBlockSize: z.number().int().min(0, {
      message: "请输入块大小",
    }),
    advancedSettingsForDocumentChunkOverlap: z.number().int().min(0, {
      message: "请输入分块重叠大小",
    }),
    // advancedSettingsSimilarityThreshold: z.number().min(0, {
    //   message: "请输入相似度阈值",
    // }),
    // advancedSettingsNumberOfSearchResults: z.number().int().min(0, {
    //   message: "请输入检索结果数量",
    // }),
    // advancedSettingsAutoUpdate: z.boolean(),
    is_active: z.boolean(),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      // visibility: "tenant",
      // tag: "",
      // vectorDatabaseType: "",
      // vectorDatabaseApiKey: "",
      // vectorDatabaseServiceUrl: "",
      // vectorDatabaseIndexName: "",
      embeddingModelsType: "",
      indexType: "",
      indexThreshold: 0.7,
      indexResults: 5,
      indexWeight: 0.6,
      // embeddingModelsApiKey: "",
      // embeddingModelsServiceUrl: "",
      // embeddingModelsVectorDimension: 1536,
      advancedSettingsForDocumentBlockSize: 1000,
      advancedSettingsForDocumentChunkOverlap: 200,
      // advancedSettingsSimilarityThreshold: 0.7,
      // advancedSettingsNumberOfSearchResults: 5,
      // advancedSettingsAutoUpdate: true,
      is_active: false,
    },
  })

  const [isSubmitting, setIsSubmitting] = useState(false);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      if (id) {
        await putData(values)
      } else {
        await postData(values)
      }
    } finally {
      setIsSubmitting(false);
    }
  }
  const postData = async (values: any) => {
    const { error } = await apiClient.POST(`/api/v1/tenants/{tenant_id}/knowledge/bases`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        }
      },
      body: {
        tenant_id: currentTenant?.tenant_id ?? '',
        name: values.name,
        description: values.description,
        config: {
          embedding: {
            model_name: values.embeddingModelsType.split(',')[1],
            model_id: values.embeddingModelsType.split(',')[0]
          },
          retrieval: {
            default_search_type: values.indexType,
            vector_search: {
              top_k: 5,
              score_threshold: 0.5,
              rerank: {
                enabled: false,
                model_name: "",
                model_id: ""
              }
            },
            keyword_search: {
              top_k: 5,
              score_threshold: 0.5,
              rerank: {
                enabled: false,
                model_name: "",
                model_id: ""
              }
            },
            hybrid_search: {
              top_k: 5,
              score_threshold: 0.5,
              rerank: {
                enabled: false,
                model_name: "",
                model_id: ""
              },
              vector_weight: 0.7,
              keyword_weight: 0.3
            },
            [`${values.indexType}_search`]: {
              top_k: values.indexResults,
              score_threshold: values.indexThreshold,
              rerank: {
                enabled: true,
                model_name: values.embeddingModelsType.split(',')[1],
                model_id: values.embeddingModelsType.split(',')[0]
              },
              vector_weight: values.indexWeight,
              keyword_weight: 1 - values.indexWeight
            },
          },
          chunk_size: values.advancedSettingsForDocumentBlockSize,
          chunk_overlap: values.advancedSettingsForDocumentChunkOverlap
        }
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      router.push('/knowledge-bases')
      useToast().toast.success("提交成功", {
        description: `创建知识库成功`
      })
    }
  }
  const getData = async (id: string) => {
    const { data, error }: any = await apiClient.GET(`/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          kb_id: id,
        }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      form.setValue('name', data.name)
      form.setValue('description', data.description as string)
      // form.setValue('visibility', data?.config?.visibility as string)
      // form.setValue('tag', data?.config?.tag as string)

      // form.setValue('vectorDatabaseType', data?.config?.vectorDatabaseType as string)
      // form.setValue('vectorDatabaseApiKey', data?.config?.vectorDatabaseApiKey as string)
      // form.setValue('vectorDatabaseServiceUrl', data?.config?.vectorDatabaseServiceUrl as string)
      // form.setValue('vectorDatabaseIndexName', data?.config?.vectorDatabaseIndexName as string)

      form.setValue('embeddingModelsType', [data?.config?.embedding?.model_id, data?.config?.embedding?.model_name].toString())
      form.setValue('indexType', data?.config?.retrieval?.default_search_type as string)
      form.setValue('indexThreshold', data?.config?.retrieval?.[`${data?.config?.retrieval?.default_search_type}_search`].score_threshold as number)
      form.setValue('indexResults', data?.config?.retrieval?.[`${data?.config?.retrieval?.default_search_type}_search`].top_k as number)
      form.setValue('indexWeight', Number(data?.config?.retrieval?.[`${data?.config?.retrieval?.default_search_type}_search`].vector_weight) || 0.5 as number)
      // form.setValue('embeddingModelsApiKey', data?.config?.embeddingModelsApiKey as string)
      // form.setValue('embeddingModelsServiceUrl', data?.config?.embeddingModelsServiceUrl as string)
      // form.setValue('embeddingModelsVectorDimension', data?.config?.embeddingModelsVectorDimension as number)

      form.setValue('advancedSettingsForDocumentBlockSize', data?.config?.chunk_size as number)
      form.setValue('advancedSettingsForDocumentChunkOverlap', data?.config?.chunk_overlap as number)
      // form.setValue('advancedSettingsSimilarityThreshold', data?.config?.advancedSettingsSimilarityThreshold as number)
      // form.setValue('advancedSettingsNumberOfSearchResults', data?.config?.advancedSettingsNumberOfSearchResults as number)
      // form.setValue('advancedSettingsAutoUpdate', data?.config?.advancedSettingsAutoUpdate as boolean)
      form.setValue('is_active', data?.is_active as boolean)
      setIndexType(data?.config?.retrieval?.default_search_type)
    }
  }
  const putData = async (values: any) => {
    const { error } = await apiClient.PUT(`/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          kb_id: id as string,
        }
      },
      body: {
        name: values.name,
        description: values.description,
        config: {
          embedding: {
            model_name: values.embeddingModelsType.split(',')[1],
            model_id: values.embeddingModelsType.split(',')[0]
          },
          retrieval: {
            default_search_type: values.indexType,
            vector_search: {
              top_k: 5,
              score_threshold: 0.5,
              rerank: {
                enabled: false,
                model_name: "",
                model_id: ""
              }
            },
            keyword_search: {
              top_k: 5,
              score_threshold: 0.5,
              rerank: {
                enabled: false,
                model_name: "",
                model_id: ""
              }
            },
            hybrid_search: {
              top_k: 5,
              score_threshold: 0.5,
              rerank: {
                enabled: false,
                model_name: "",
                model_id: ""
              },
              vector_weight: 0.7,
              keyword_weight: 0.3
            },
            [`${values.indexType}_search`]: {
              top_k: values.indexResults,
              score_threshold: values.indexThreshold,
              rerank: {
                enabled: true,
                model_name: values.embeddingModelsType.split(',')[1],
                model_id: values.embeddingModelsType.split(',')[0]
              },
              vector_weight: values.indexWeight,
              keyword_weight: 1 - values.indexWeight
            },
          },
          chunk_size: values.advancedSettingsForDocumentBlockSize,
          chunk_overlap: values.advancedSettingsForDocumentChunkOverlap
        },
        is_active: values.is_active,
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      router.push('/knowledge-bases')
      useToast().toast.success("提交成功", {
        description: `修改成功`
      })
    }
  }
  const getModelsList = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/models`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        },
        query: {
          skip: 0,
          limit: 1000,
          model_type: "embedding",
        }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      setEmbeddingInfo(data.items.map((item: any) => ({ name: item.name, value: item.id })))
    }
  }
  useEffect(() => {
    getModelsList()
    id && getData(id.toString())
  }, [currentTenant])
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href="/knowledge-bases">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{id ? '编辑知识库' : '创建知识库'}</h1>
            <p className="text-muted-foreground">配置新的知识库</p>
          </div>
        </div>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(async (values) => {console.log('========', values); await onSubmit(values)})} className="space-y-8">
            <Tabs defaultValue="basic" className="space-y-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">基本信息</TabsTrigger>
                <TabsTrigger value="config">模型检索配置</TabsTrigger>
                <TabsTrigger value="advanced">高级设置</TabsTrigger>
                {/* <TabsTrigger value="documents">文档管理</TabsTrigger> */}
              </TabsList>
              <TabsContent value="basic">
                <Card>
                  <CardHeader>
                    <CardTitle>基本信息</CardTitle>
                    <CardDescription>设置知识库的基本信息</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>名称</FormLabel>
                          <FormControl>
                            <Input placeholder="请输入名称" {...field} />
                          </FormControl>
                          <FormDescription></FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>知识库描述</FormLabel>
                          <FormControl>
                            <Textarea placeholder="简要描述知识库的内容和用途" {...field} />
                          </FormControl>
                          <FormDescription></FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* <FormField
                        control={form.control}
                        name="visibility"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>可见范围</FormLabel>
                            <FormControl>
                              <RadioGroup {...field} onValueChange={(value: string) => field.onChange(value)}>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="tenant" id="tenant" />
                                  <Label htmlFor="tenant">租户内可见</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="private" id="private" />
                                  <Label htmlFor="private">仅创建者可见</Label>
                                </div>
                              </RadioGroup>
                            </FormControl>
                            <FormDescription></FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="tag"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>标签</FormLabel>
                            <FormControl>
                              <Input placeholder="请输入标签，并以“，”隔开" {...field} />
                            </FormControl>
                            <FormDescription></FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      /> */}
                    {/* <div className="space-y-2">
                      <div className="flex flex-wrap gap-2">
                        <div className="border rounded-md px-3 py-1">
                          <span className="text-sm">产品文档</span>
                        </div>
                      </div>
                    </div> */}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="config">
                <div className="grid gap-6 md:grid-cols-2">
                  {/* <Card>
                    <CardHeader>
                      <CardTitle>向量数据库配置</CardTitle>
                      <CardDescription>选择并配置向量数据库</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="vectorDatabaseType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>向量数据库类型</FormLabel>
                          <FormControl>
                            <Select {...field} onValueChange={(value: string) => field.onChange(value)}>
                              <SelectTrigger>
                                <SelectValue placeholder="选择向量数据库" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="pinecone">Pinecone</SelectItem>
                                <SelectItem value="milvus">Milvus</SelectItem>
                                <SelectItem value="qdrant">Qdrant</SelectItem>
                                <SelectItem value="weaviate">Weaviate</SelectItem>
                                <SelectItem value="elasticsearch">Elasticsearch</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription></FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                        control={form.control}
                        name="vectorDatabaseApiKey"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>API Key</FormLabel>
                            <FormControl>
                              <Input placeholder="输入API Key" {...field} />
                            </FormControl>
                            <FormDescription></FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="vectorDatabaseServiceUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>服务端点</FormLabel>
                            <FormControl>
                              <Input placeholder="输入服务端点URL" {...field} />
                            </FormControl>
                            <FormDescription></FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="vectorDatabaseIndexName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>索引名称</FormLabel>
                            <FormControl>
                              <Input placeholder="输入索引名称" {...field} />
                            </FormControl>
                            <FormDescription></FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card> */}

                  <Card>
                    <CardHeader>
                      <CardTitle>Embedding模型配置</CardTitle>
                      <CardDescription>选择并配置Embedding模型</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="embeddingModelsType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>向量数据库类型</FormLabel>
                            <FormControl>
                              <Select {...field} onValueChange={(value: string) => field.onChange(value)}>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择Embedding模型" />
                                </SelectTrigger>
                                <SelectContent>
                                  {embeddingInfo.map(item => <SelectItem key={item.value} value={[item.value, item.name].toString()}>{item.name}</SelectItem>)}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormDescription></FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="indexType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>选择检索默认方式</FormLabel>
                            <FormControl>
                              <Select {...field} onValueChange={(value: string) => { field.onChange(value); setIndexType(value) }}>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择检索方式" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="vector">向量检索</SelectItem>
                                  <SelectItem value="keyword">全文检索</SelectItem>
                                  <SelectItem value="hybrid">混合检索</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormDescription></FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      {/* <FormField
                          control={form.control}
                          name="embeddingModelsApiKey"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>API Key</FormLabel>
                              <FormControl>
                                <Input placeholder="输入模型API Key" {...field} />
                              </FormControl>
                              <FormDescription></FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="embeddingModelsServiceUrl"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>模型服务端点</FormLabel>
                              <FormControl>
                                <Input placeholder="输入模型服务端点URL（可选）" {...field} />
                              </FormControl>
                              <FormDescription></FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="embeddingModelsVectorDimension"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>向量维度</FormLabel>
                              <FormControl>
                                <Input placeholder="输入向量维度" type="number" {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                              </FormControl>
                              <FormDescription>OpenAI text-embedding-3默认为1536维</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        /> */}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>检索配置</CardTitle>
                      <CardDescription>设置检索方法</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="indexThreshold"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>score阈值</FormLabel>
                            <FormControl>
                              <Input placeholder="输入相似度阈值" type="number" step="0.1" {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                            </FormControl>
                            <FormDescription></FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="indexResults"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>top_k</FormLabel>
                            <FormControl>
                              <Input placeholder="输入检索结果数量" type="number" {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                            </FormControl>
                            <FormDescription></FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      {indexType === 'hybrid' &&
                        <FormField
                          control={form.control}
                          name="indexWeight"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>权重</FormLabel>
                              <FormControl>
                                {/* <Input placeholder="输入检索结果数量" type="number" {...field} onChange={(e) => field.onChange(Number(e.target.value))} /> */}
                                <Slider
                                  defaultValue={[field.value]}
                                  // value={[0.5]}
                                  max={1}
                                  step={0.1}
                                  className={"w-[100%]"}
                                  onValueChange={(value) => { field.onChange(value[0]); console.log('111', value) }}
                                // {...field}
                                />
                              </FormControl>
                              <FormDescription>向量检索{field.value}-全文检索{(1 - field.value).toFixed(1)}</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      }
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="advanced">
                <div className="grid gap-6 md:grid-cols-2">
                  <Card className="md:col-span-2">
                    <CardHeader>
                      <CardTitle>高级设置</CardTitle>
                      <CardDescription>配置知识库的高级参数</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="advancedSettingsForDocumentBlockSize"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>文档分块大小</FormLabel>
                              <FormControl>
                                <Input placeholder="输入文档分块大小" type="number" {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                              </FormControl>
                              <FormDescription>文档分块的字符数，默认为1000</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="advancedSettingsForDocumentChunkOverlap"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>分块重叠大小</FormLabel>
                              <FormControl>
                                <Input placeholder="输入分块重叠大小" type="number" {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                              </FormControl>
                              <FormDescription>相邻分块的重叠字符数，默认为200</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        {/* <FormField
                          control={form.control}
                          name="advancedSettingsSimilarityThreshold"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>相似度阈值</FormLabel>
                              <FormControl>
                                <Input placeholder="输入相似度阈值" type="number" step="0.1" {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                              </FormControl>
                              <FormDescription>检索时的相似度阈值，范围0-1，默认为0.7</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="advancedSettingsNumberOfSearchResults"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>检索结果数量</FormLabel>
                              <FormControl>
                                <Input placeholder="输入检索结果数量" type="number" {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                              </FormControl>
                              <FormDescription>检索返回的最大结果数量，默认为5</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        /> */}
                      </div>
                      {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-5">
                        <FormField
                          control={form.control}
                          name="advancedSettingsAutoUpdate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>自动更新索引</FormLabel>
                              <FormControl>
                                <Switch checked={field.value}
                                    onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <FormDescription>当文档更新时自动重建索引</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div> */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-5">
                        <FormField
                          control={form.control}
                          name="is_active"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>激活知识库</FormLabel>
                              <FormControl>
                                <Switch checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <FormDescription></FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>


              {/* <TabsContent value="documents">
                <Card>
                  <CardHeader>
                    <CardTitle>文档管理</CardTitle>
                    <CardDescription>上传和管理知识库文档</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <Label htmlFor="picture" className="border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center">
                      <Input id="picture" className="invisible" type="file" />
                      <Upload className="h-10 w-10 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium">拖放文件到此处或点击上传</h3>
                      <p className="text-sm text-muted-foreground mt-1 mb-4">
                        支持PDF、DOCX、TXT、MD等格式，单个文件最大50MB
                      </p>
                    </Label>
                    

                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-medium">已上传文件</h3>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            添加URL
                          </Button>
                          <Button variant="outline" size="sm">
                            全部删除
                          </Button>
                        </div>
                      </div>

                      <div className="border rounded-md divide-y">
                        <div className="p-4 flex justify-between items-center">
                          <div className="flex items-center space-x-4">
                            <div className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">PDF</span>
                            </div>
                            <div>
                              <p className="font-medium">产品手册.pdf</p>
                              <p className="text-xs text-muted-foreground">2.4MB · 上传于 2分钟前</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </div>
                        <div className="p-4 flex justify-between items-center">
                          <div className="flex items-center space-x-4">
                            <div className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">DOC</span>
                            </div>
                            <div>
                              <p className="font-medium">技术规格说明.docx</p>
                              <p className="text-xs text-muted-foreground">1.8MB · 上传于 2分钟前</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </div>
                        <div className="p-4 flex justify-between items-center">
                          <div className="flex items-center space-x-4">
                            <div className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">TXT</span>
                            </div>
                            <div>
                              <p className="font-medium">常见问题解答.txt</p>
                              <p className="text-xs text-muted-foreground">0.5MB · 上传于 2分钟前</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent> */}
            </Tabs>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" asChild>
                <Link href="/knowledge-bases">取消</Link>
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    提交中...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {
                      id ? '修改' : '创建'
                    }知识库
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </MainLayout>
  )
}
