
"use client"
import { useEffect, useState, use } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Empty } from "@/components/empty"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  ArrowLeft,
  Upload,
  MoreHorizontal,
  FileText,
  Trash2,
  Download,
  Loader
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { PaginationBox } from "@/components/pagination"

import { apiClient } from "@/lib/api/api"
import { useAuth } from "@/contexts/auth-context"
import dayjs from 'dayjs';

export default function KnowledgeBaseDocumentsPage({ params }: { params: Promise<{ id: string }> }) {
  const id = use(params)?.id;
  const { currentTenant } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [status, setStatus] = useState<string>('all')
  const [tableData, setTableData] = useState<any[] | undefined>([])
  const [paginationInfo, setPaginationInfo] = useState({
    totalPages: 0,
    current: 1,
    skip: 0,
    limit: 10,
  })
  const changePage = (page: number) => {
    setPaginationInfo({
      ...paginationInfo,
      current: page,
      skip: (page - 1) * paginationInfo.limit,
    })
  }
  const getData = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}/files`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          kb_id: id,
        },
        query: {
          skip: paginationInfo.skip,
          limit: paginationInfo.limit,
          status: status == 'all' ? undefined: status,
        }
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      setTableData(data.items)
      setPaginationInfo({
        ...paginationInfo,
        totalPages: Math.ceil(data.total / paginationInfo.limit),
      })
    }
    return data
  }

  const updateFile = async (e: any) => {
    const file = e.target.files[0];
    
    if (!file) {
      return;
    }

    const formData = new FormData();
    formData.append('file', file);
    formData.append('knowledge_base_id', id);
    setIsSubmitting(true)
    const { data, error } = await apiClient.POST(`/api/v1/tenants/{tenant_id}/knowledge/upload`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        }
      },
      body: formData as any,
    });
    setIsSubmitting(false)
    if (error) {
      console.log('error', error)
    } else {
      useToast().toast.success("上传成功")
      getData()
    }

  };

  const postFile = async (fileContent: any) => {
    const { data, error } = await apiClient.POST(`/api/v1/tenants/{tenant_id}/knowledge/files`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        }
      },
      body: {
        file_name: fileContent.file_name,
        file_type: fileContent.file_type,
        file_size: fileContent.file_size,
        meta_data: fileContent.meta_data,
        knowledge_base_id: fileContent.knowledge_base_id,
        file_path: fileContent.file_path,
      },
    });
    if (error) {
      console.log('error', error)
    } else {
      getData()
    }
  };

  const toDelete = async (id: string) => {
    const { data, error } = await apiClient.DELETE(`/api/v1/tenants/{tenant_id}/knowledge/files/{file_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          file_id: id,
        }
      }
    });
    getData()
    if (error) {
      console.log('error', error)
    } else {
      useToast().toast.success("提交成功", {
        description: `删除成功`
      })
    }
    getData()
  }

  const toDownload = async (id: string) => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/knowledge/files/{file_id}/download`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          file_id: id,
        }
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      const link = document.createElement('a');
      link.style.display = 'none';
      // 设置下载地址
      link.setAttribute('href', data.download_url);
      // 设置文件名
      link.setAttribute('download', data.file_name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  useEffect(() => {
    console.log('urlParams', id)
    document.title = "知识库文档管理 | 云知问"
    getData()
  }, [status, paginationInfo.current])
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href={`/knowledge-bases`}>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">文档管理</h1>
            <p className="text-muted-foreground">管理知识库中的文档</p>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            {/* <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input type="search" placeholder="搜索文档..." className="pl-8 w-[300px]" />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              <SlidersHorizontal className="h-4 w-4" />
            </Button> */}
          </div>
          <div className="flex space-x-2">
            {!isSubmitting ? <Label htmlFor="picture" className="relative">
              <Button variant="outline">
                <Upload className="mr-2 h-4 w-4" />
                上传文档
              </Button>
              <Input id="picture" onChange={e => updateFile(e)} className="absolute left-0 right-0 z-10 opacity-0" type="file" accept=".pdf, .docx, .xlsx, .pptx, .txt, .md" />
            </Label> : <Button variant="outline" onClick={() => setIsSubmitting(true)}>
              <Loader className="animate-spin" />
              上传中...
            </Button>}
          </div>
        </div>

        <Tabs value={status} onValueChange={(e) => setStatus(e)} className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">全部文档</TabsTrigger>
            <TabsTrigger value="indexed">已索引</TabsTrigger>
            <TabsTrigger value="processing">待处理</TabsTrigger>
            <TabsTrigger value="error">处理失败</TabsTrigger>
          </TabsList>

          <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[40px]">
                        {/* <Checkbox /> */}
                      </TableHead>
                      <TableHead>文档名称</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>大小(Byte)</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>上传时间</TableHead>
                      <TableHead>最后更新</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tableData?.map((item, idx) => <TableRow key={item.id + idx}>
                      <TableCell>
                        {/* <Checkbox /> */}
                      </TableCell>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4 text-blue-500" />
                          <span>{item.file_name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{item.file_type}</TableCell>
                      <TableCell>{item.file_size}</TableCell>
                      <TableCell>
                        {item.status == 'indexed' && <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                          已索引
                        </Badge>}
                        {item.status == 'processing' && <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100">
                          处理中
                        </Badge>}
                        {item.status == 'error' && <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">处理失败</Badge>}
                      </TableCell>
                      <TableCell>{dayjs(item?.tenant?.created_at).format('YYYY-MM-DD HH:mm:ss')}</TableCell>
                      <TableCell>{dayjs(item?.tenant?.updated_at).format('YYYY-MM-DD HH:mm:ss')}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {/* <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>查看</span>
                            </DropdownMenuItem> */}
                            <DropdownMenuItem onClick={() => toDownload(item.id)}>
                              <Download className="mr-2 h-4 w-4" />
                              <span>下载</span>
                            </DropdownMenuItem>
                            {/* <DropdownMenuItem>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              <span>重建索引</span>
                            </DropdownMenuItem> */}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive" onClick={() => toDelete(item.id)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>删除</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>)}
                  </TableBody>
                </Table>
                { !tableData?.length && <Empty /> }
              </CardContent>
            </Card>
            <PaginationBox totalPages={paginationInfo.totalPages} current={paginationInfo.current} changePage={changePage} />
        </Tabs>
      </div>
    </MainLayout>
  )
}
