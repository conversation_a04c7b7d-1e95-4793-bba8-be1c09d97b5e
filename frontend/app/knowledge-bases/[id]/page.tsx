"use client"
import { useEffect, useState, use } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Edit, FileText, Bot } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { apiClient } from "@/lib/api/api"
import { components } from "@/lib/api/strapi"
import dayjs from 'dayjs';
import { available } from "@/lib/utils"

type IUsageData = components["schemas"]["KnowledgeBaseUsageResponse"]

export default function KnowledgeBaseDetailPage({ params }: { params: Promise<{ id: string }> }) {
  // 这里可以根据ID获取知识库详情
  const id = use(params)?.id
  const { currentTenant } = useAuth()
  
  const [data, setData] = useState<any>()
  const [usageData, setUsageData] = useState<IUsageData>()

  const getData = async (id: string) => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}`, {
      params: {
      path: {
        tenant_id: currentTenant?.tenant_id ?? '',
        kb_id: id,
      }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      setData(data)
    }
  }

  const getUsageData = async (id: string) => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}/usage`, {
      params: {
      path: {
        tenant_id: currentTenant?.tenant_id ?? '',
        kb_id: id,
      }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      setUsageData(data)
    }
  }

  useEffect(() => {
    document.title = "知识库详情 | 云知问"
    id && getData(id.toString())
    id && getUsageData(id.toString())
  }, [])

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button variant="ghost" size="icon" asChild className="mr-2">
              <Link href="/knowledge-bases">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">知识库管理</h1>
              <p className="text-muted-foreground">知识库详情</p>
            </div>
          </div>
          {/* <div className="flex space-x-2">
            <Button variant="outline" asChild>
              <Link href={`/knowledge-bases/${id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Link>
            </Button>
            <Button variant="destructive">
              <Trash2 className="mr-2 h-4 w-4" />
              删除
            </Button>
          </div> */}
        </div>

        <div className="grid gap-6 md:grid-cols-[1fr_300px]">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
                <CardDescription>知识库的基本信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">知识库名称</h3>
                    <p>{data?.name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">文档数量</h3>
                    <p>--</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">创建时间</h3>
                    <p>{dayjs(data?.created_at).format('YYYY-MM-DD HH:mm:ss')}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">状态</h3>
                    <div>
                      {data?.is_active ? <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                        已激活
                      </Badge> : '未激活'}
                    </div>
                  </div>
                </div>
                <Separator />
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">描述</h3>
                  <p>{data?.description}</p>
                </div>
                {/* <div>
                  <h3 className="text-sm font-medium text-muted-foreground">标签</h3>
                  <div className="flex flex-wrap gap-2 mt-2">
                    { data?.config?.tag as string }
                  </div>
                </div> */}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>配置信息</CardTitle>
                <CardDescription>知识库的配置信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {/* <div>
                    <h3 className="text-sm font-medium text-muted-foreground">向量数据库</h3>
                    <p>{ data?.config?.vectorDatabaseType as string || '--'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">索引名称</h3>
                    <p>{ data?.config?.vectorDatabaseIndexName as string  || '--'}</p>
                  </div> */}
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Embedding模型</h3>
                    <p>{ data?.config?.embedding?.model_name as string  || '--'}</p>
                  </div>
                  {/* <div>
                    <h3 className="text-sm font-medium text-muted-foreground">向量维度</h3>
                    <p>{ data?.config?.embeddingModelsVectorDimension as string  || '--'}</p>
                  </div> */}
                </div>
                <Separator />
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">分块大小</h3>
                    <p>{ data?.config?.chunk_size as string}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">分块重叠</h3>
                    <p>{ data?.config?.chunk_overlap as string}</p>
                  </div>
                  {/* <div>
                    <h3 className="text-sm font-medium text-muted-foreground">相似度阈值</h3>
                    <p>{ data?.config?.advancedSettingsSimilarityThreshold as string}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">检索结果数量</h3>
                    <p>{ data?.config?.advancedSettingsNumberOfSearchResults as string}</p>
                  </div> */}
                </div>
                
              </CardContent>
            </Card>

            {/* <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>文档管理</CardTitle>
                    <CardDescription>管理知识库中的文档</CardDescription>
                  </div>
                  <Button asChild>
                    <Link href={`/knowledge-bases/${id}/documents`}>
                      <FileText className="mr-2 h-4 w-4" />
                      管理文档
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="recent" className="space-y-4">
                  <TabsList>
                    <TabsTrigger value="recent">最近添加</TabsTrigger>
                    <TabsTrigger value="popular">常用文档</TabsTrigger>
                  </TabsList>
                  <TabsContent value="recent">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-3 border rounded-md">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-blue-500" />
                          <div>
                            <p className="font-medium">产品手册.pdf</p>
                            <p className="text-xs text-muted-foreground">2.4MB · 上传于 2023-05-15</p>
                          </div>
                        </div>
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                          已索引
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center p-3 border rounded-md">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-blue-500" />
                          <div>
                            <p className="font-medium">技术规格说明.docx</p>
                            <p className="text-xs text-muted-foreground">1.8MB · 上传于 2023-05-10</p>
                          </div>
                        </div>
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                          已索引
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center p-3 border rounded-md">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-blue-500" />
                          <div>
                            <p className="font-medium">常见问题解答.txt</p>
                            <p className="text-xs text-muted-foreground">0.5MB · 上传于 2023-05-20</p>
                          </div>
                        </div>
                        <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100">
                          处理中
                        </Badge>
                      </div>
                    </div>
                  </TabsContent>
                  <TabsContent value="popular">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-3 border rounded-md">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-blue-500" />
                          <div>
                            <p className="font-medium">产品手册.pdf</p>
                            <p className="text-xs text-muted-foreground">被引用 245 次</p>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="flex justify-between items-center p-3 border rounded-md">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-blue-500" />
                          <div>
                            <p className="font-medium">技术规格说明.docx</p>
                            <p className="text-xs text-muted-foreground">被引用 178 次</p>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card> */}
          </div>

          <div className="space-y-6">
            {available(currentTenant) && <Card>
              <CardHeader>
                <CardTitle>操作</CardTitle>
                <CardDescription>知识库相关操作</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full" asChild>
                  <Link href={`/knowledge-bases/${id}/documents`}>
                    <FileText className="mr-2 h-4 w-4" />
                    管理文档
                  </Link>
                </Button>
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/knowledge-bases/create?id=${id}`}>
                    <Edit className="mr-2 h-4 w-4" />
                    编辑知识库
                  </Link>
                </Button>
                {/* <Button variant="outline" className="w-full">
                  <Settings className="mr-2 h-4 w-4" />
                  配置设置
                </Button>
                <Button variant="outline" className="w-full">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  重建索引
                </Button>
                <Button variant="destructive" className="w-full">
                  <Trash2 className="mr-2 h-4 w-4" />
                  删除知识库
                </Button> */}
              </CardContent>
            </Card>}

            <Card>
              <CardHeader>
                <CardTitle>关联Agent</CardTitle>
                <CardDescription>使用此知识库的Agent</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Agent活跃数:</span>
                  <span className="font-medium">{usageData?.active_agents}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Agent总数:</span>
                  <span className="font-medium">{usageData?.total_agents}</span>
                </div>
                <div className="space-y-2">
                  {usageData?.agents.map((item, idx) => <div key={item.agent_id + idx} className="flex items-center justify-between p-3 border rounded-md">
                    <div className="flex items-center space-x-2">
                      <Bot className="h-5 w-5 text-primary" />
                      <span>{item.agent_name}</span>
                    </div>
                    {item.is_active ? <Badge variant="outline" className="text-green-600 bg-green-100">已激活</Badge> : <Badge variant="outline" className="text-red-600 bg-red-100">未激活</Badge>}
                  </div>)}
                </div>
                {/* <Button variant="outline" size="sm" className="w-full">
                  <Bot className="mr-2 h-4 w-4" />
                  关联Agent
                </Button> */}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>知识库状态</CardTitle>
                <CardDescription>当前知识库状态信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">文档数量:</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">已索引文档:</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">处理中文档:</span>
                    <span className="font-medium">--</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">最后更新:</span>
                    <span className="font-medium">{dayjs(data?.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
