"use client"

import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { ArrowLeft, Save } from "lucide-react"
import { use, useEffect } from "react"

export default function EditKnowledgeBasePage({ params }: { params: Promise<{ id: string }> }) {
  const knowledgeBaseId = use(params)?.id
  useEffect(() => {
    document.title = "编辑知识库 | 云知问"
  }, [])

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href={`/knowledge-bases/${knowledgeBaseId}`}>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">编辑知识库</h1>
            <p className="text-muted-foreground">修改知识库配置</p>
          </div>
        </div>

        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="config">向量数据库配置</TabsTrigger>
            <TabsTrigger value="advanced">高级设置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
                <CardDescription>修改知识库的基本信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">知识库名称</Label>
                  <Input id="name" defaultValue="产品手册知识库" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">知识库描述</Label>
                  <Textarea id="description" defaultValue="产品使用手册与常见问题" rows={3} />
                </div>
                <div className="space-y-2">
                  <Label>可见范围</Label>
                  <RadioGroup defaultValue="tenant">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="tenant" id="tenant" />
                      <Label htmlFor="tenant">租户内可见</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="private" id="private" />
                      <Label htmlFor="private">仅创建者可见</Label>
                    </div>
                  </RadioGroup>
                </div>
                <div className="space-y-2">
                  <Label>标签</Label>
                  <div className="flex flex-wrap gap-2">
                    <div className="border rounded-md px-3 py-1">
                      <span className="text-sm">产品文档</span>
                    </div>
                    <div className="border rounded-md px-3 py-1">
                      <span className="text-sm">技术手册</span>
                    </div>
                    <div className="border rounded-md px-3 py-1">
                      <span className="text-sm">FAQ</span>
                    </div>
                    <Button variant="outline" size="sm">
                      编辑标签
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="config">
            <Card>
              <CardHeader>
                <CardTitle>向量数据库配置</CardTitle>
                <CardDescription>修改向量数据库配置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="vector-db">向量数据库类型</Label>
                  <Select defaultValue="pinecone">
                    <SelectTrigger id="vector-db">
                      <SelectValue placeholder="选择向量数据库" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pinecone">Pinecone</SelectItem>
                      <SelectItem value="milvus">Milvus</SelectItem>
                      <SelectItem value="qdrant">Qdrant</SelectItem>
                      <SelectItem value="weaviate">Weaviate</SelectItem>
                      <SelectItem value="elasticsearch">Elasticsearch</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="api-key">API Key</Label>
                  <Input id="api-key" type="password" defaultValue="••••••••••••••••••••••" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endpoint">服务端点</Label>
                  <Input id="endpoint" defaultValue="https://example-db.pinecone.io" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="index-name">索引名称</Label>
                  <Input id="index-name" defaultValue="product-manual-kb" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="embedding-model">Embedding模型</Label>
                  <Select defaultValue="openai">
                    <SelectTrigger id="embedding-model">
                      <SelectValue placeholder="选择Embedding模型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="openai">OpenAI text-embedding-3</SelectItem>
                      <SelectItem value="zhipu">智谱 embedding-2</SelectItem>
                      <SelectItem value="bge">BGE Embedding</SelectItem>
                      <SelectItem value="custom">自定义模型</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="model-api-key">模型API Key</Label>
                  <Input id="model-api-key" type="password" defaultValue="••••••••••••••••••••••" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="dimension">向量维度</Label>
                  <Input id="dimension" type="number" defaultValue="1536" />
                  <p className="text-xs text-muted-foreground">OpenAI text-embedding-3默认为1536维</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced">
            <Card>
              <CardHeader>
                <CardTitle>高级设置</CardTitle>
                <CardDescription>修改知识库的高级参数</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="chunk-size">文档分块大小</Label>
                    <Input id="chunk-size" type="number" defaultValue="1000" />
                    <p className="text-xs text-muted-foreground">文档分块的字符数，默认为1000</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="chunk-overlap">分块重叠大小</Label>
                    <Input id="chunk-overlap" type="number" defaultValue="200" />
                    <p className="text-xs text-muted-foreground">相邻分块的重叠字符数，默认为200</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="similarity-threshold">相似度阈值</Label>
                    <Input id="similarity-threshold" type="number" defaultValue="0.7" step="0.01" min="0" max="1" />
                    <p className="text-xs text-muted-foreground">检索时的相似度阈值，范围0-1，默认为0.7</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="top-k">检索结果数量</Label>
                    <Input id="top-k" type="number" defaultValue="5" />
                    <p className="text-xs text-muted-foreground">检索返回的最大结果数量，默认为5</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="auto-update">自动更新索引</Label>
                      <p className="text-sm text-muted-foreground">当文档更新时自动重建索引</p>
                    </div>
                    <Switch id="auto-update" defaultChecked />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="incremental-update">增量更新</Label>
                      <p className="text-sm text-muted-foreground">仅对更改的文档重建索引</p>
                    </div>
                    <Switch id="incremental-update" defaultChecked />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" asChild>
            <Link href={`/knowledge-bases/${knowledgeBaseId}`}>取消</Link>
          </Button>
          <Button>
            <Save className="mr-2 h-4 w-4" />
            保存更改
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
