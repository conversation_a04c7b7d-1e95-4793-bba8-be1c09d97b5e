import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/sonner"
import { cn } from "@/lib/utils"
import { AuthProvider } from "@/contexts/auth-context"
import { HttpInterceptor } from "@/lib/http-interceptor"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "云知问 ",
  description: "专业的多租户智能Agent管理平台",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={cn(inter.className, "min-h-screen bg-background")}>
        <AuthProvider>
            <HttpInterceptor />
            <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
              {children}
              <Toaster />
            </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
