"use client"

import React, { useEffect, useState } from "react"
import { ChatStreamWindow } from "@/components/chat-stream-window"
import { useSearchParams } from "next/navigation"

export default function EmbedChatPage() {
    const [agentId, setAgentId] = useState("")
    const searchParams = useSearchParams()
    const [config, setConfig] = useState({
        threadId: "default",
    })

    useEffect(() => {
        // 从 URL 参数中获取 agentId
        const agentIdFromUrl = searchParams.get("agentId")
        if (agentIdFromUrl) {
            setAgentId(agentIdFromUrl)
        }

        // 从 URL 参数中获取 threadId（如果有的话）
        const threadIdFromUrl = searchParams.get("threadId")
        if (threadIdFromUrl) {
            setConfig(prev => ({
                ...prev,
                threadId: threadIdFromUrl
            }))
        }
    }, [searchParams])

    // 如果没有提供 agentId，显示错误信息
    if (!agentId) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="text-center">
                    <h3 className="text-lg font-semibold mb-2">缺少必要参数</h3>
                    <p className="text-muted-foreground">请提供 agentId 参数</p>
                </div>
            </div>
        )
    }

    return (
        <div className="h-screen w-full">
            <ChatStreamWindow
                agentId={agentId}
                threadId={config.threadId}
            />
        </div>
    )
} 