"use client"
import { useEffect, useState } from "react"
import JsonSchemaForm from '@/components/json-schema-from';
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from 'next/navigation'
import { useToast } from "@/hooks/use-toast"
import { apiClient } from "@/lib/api/api"
import { components } from "@/lib/api/strapi";

type SystemTool = components["schemas"]["TenantTool"]
type Tool = components["schemas"]["Tool"]
export default function ToolEdit({ toolId }: { toolId?: string }) {
    const router = useRouter()
    const { currentTenant } = useAuth()
    const [tools, setTools] = useState<Tool[] | undefined>(undefined)
    const [schema, setSchema] = useState<Record<string, any> | null>()
    const [selectedTool, setSelectedTool] = useState<Tool | undefined>()
    const [_, setSystemTool] = useState<SystemTool | undefined>(undefined)
    const { toast } = useToast()

    const handleSubmit = (data: Record<string, any>) => {
        if (selectedTool?.id) {
            if (toolId) {
                putData(data, selectedTool?.id)
            } else {
                postData(data, selectedTool?.id)
            }
        }
    }

    const onValueChange = (id: string) => {
        try {
            const data = tools?.find(item => item.id === id)
            setSelectedTool(data)
            setSchema(data?.config_schema)
        } catch (error) {
            console.log(error)
        }
    }
    const postData = async (config: Record<string, any>, tool_id: string) => {
        const { data, error } = await apiClient.POST(`/api/v1/tenants/{tenant_id}/tools`, {
            params: {
                path: {
                    tenant_id: currentTenant?.tenant_id ?? ''
                }
            },
            body: {
                tenant_id: currentTenant?.tenant_id ?? '',
                tool_id: tool_id,
                config: config,
                is_active: true
            }
        });
        if (error) {
            console.log('error', error)
        } else {
            router.push('/tools')
            toast.success("提交成功", {
                description: `创建成功`
            })
        }
    }

    const putData = async (config: Record<string, any>, tool_id: string) => {
        if (!toolId) {
            return
        }
        const { data, error } = await apiClient.PUT(`/api/v1/tenants/{tenant_id}/tools/{tool_id}`, {
            params: {
                path: {
                    tenant_id: currentTenant?.tenant_id ?? '',
                    tool_id: toolId,
                }
            },
            body: {
                tenant_id: currentTenant?.tenant_id ?? '',
                tool_id: tool_id,
                config: config,
                is_active: true
            }
        });
        if (error) {
            console.log('error', error)
        } else {
            router.push('/tools')
            toast.success("提交成功", {
                description: `创建成功`
            })
        }
    }

    useEffect(() => {
        if (!toolId || !tools) {
            return
        }
        const fetchSystemTool = async () => {
            const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/tools/{tool_id}`, {
                params: {
                    path: {
                        tenant_id: currentTenant?.tenant_id ?? '',
                        tool_id: toolId,
                    }
                }
            });
            if (error) {
                console.log('error', error)
            } else {
                setSystemTool(data)
                setSelectedTool(data.tool ?? undefined)
                setSchema(data.tool?.config_schema)
            }
        }
        fetchSystemTool()
    }, [toolId, tools, currentTenant])

    useEffect(() => {
        const fetchTools = async () => {
            const { data, error } = await apiClient.GET(`/api/v1/tools`, {
                params: {}
            });
            if (error) {
                console.log('error', error)
            } else {
                setTools(data.items)
            }
        }
        fetchTools()
    }, [])

    return (
        <div className="container py-6 space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">MCP工具设置</h1>
                    <p className="text-muted-foreground">MCP工具参数设置</p>
                </div>
            </div>
            <Card>
                <CardContent className="space-y-4 flex">
                    <Select onValueChange={onValueChange} value={selectedTool?.id} >
                        <SelectTrigger>
                            <SelectValue placeholder="请选择工具类型" />
                        </SelectTrigger>
                        <SelectContent>
                            {tools?.map(item => <SelectItem key={item.id} value={item.id}>{item.name}</SelectItem>)}
                        </SelectContent>
                    </Select>
                </CardContent>
            </Card>
            {!!schema && <Card>
                <CardContent className="space-y-4">
                    <JsonSchemaForm schema={schema} onSubmit={handleSubmit} />
                </CardContent>
            </Card>}
        </div>
    )
}
