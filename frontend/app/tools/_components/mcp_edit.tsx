"use client"
import { useEffect, useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { ArrowLeft, Save, Plus, Trash2 } from "lucide-react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { apiClient } from "@/lib/api/api"
import { useRouter } from 'next/navigation'
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { components } from "@/lib/api/strapi"

type MCPServer = components["schemas"]["MCPServer"]

export default function MCPEdit({ serverId }: { serverId?: string }) {
  const router = useRouter()
  const { currentTenant } = useAuth()
  const [mcpServer, setMCPServer] = useState<MCPServer | undefined>()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const formSchema = z.object({
    name: z.string({
      message: "请输入内容",
    }).min(1, {
      message: "请输入名称",
    }),
    status: z.string({
      message: "请选择类型",
    }),
    transport_type: z.string({
      message: "请选择类型",
    }),
    endpoint: z.string().min(2, {
      message: "请输入url",
    }),
    meta_type: z
      .union([
        z.literal("").transform(() => undefined),
        z.string({
          message: "请选择类型",
        })
      ])
      .optional(),
    meta_data: z.array(z.object({
      key: z.string().min(2, {
        message: "请输入配置key",
      }),
      value: z.string().min(2, {
        message: "请输入配置value",
      }),
    }))
  })

  type FormData = z.infer<typeof formSchema>

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      transport_type: "",
      status: "",
      endpoint: "",
      meta_data: [],
    },
  })

  const onSubmit = (values: FormData) => {
    if (serverId) {
      putData(values)
    } else {
      postData(values)
    }
  }

  const postData = async (formData: FormData) => {
    setIsSubmitting(true)
    const { data, error } = await apiClient.POST(`/api/v1/tenants/{tenant_id}/mcp-servers`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? ''
        }
      },
      body: {
        name: formData.name,
        endpoint: formData.endpoint,
        transport_type: formData.transport_type,
        status: formData.status,
      }
    });
    setIsSubmitting(false)
    if (error) {
      console.log('error', error)
    } else {
      router.push('/tools')
      toast.success("提交成功", {
        description: `创建成功`
      })
    }
  }

  const putData = async (formData: FormData) => {
    if (!serverId) {
      return
    }
    setIsSubmitting(true)
    const { data, error } = await apiClient.PUT(`/api/v1/tenants/{tenant_id}/mcp-servers/{server_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          server_id: serverId
        }
      },
      body: {
        name: formData.name,
        endpoint: formData.endpoint,
        transport_type: formData.transport_type,
        status: 'status',
      }
    });
    setIsSubmitting(false)
    if (error) {
      console.log('error', error)
    } else {
      router.push('/tools')
      toast.success("提交成功", {
        description: `创建成功`
      })
    }
  }

  const getData = async (id: string) => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/mcp-servers/{server_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          server_id: id,
        }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      setMCPServer(data)
      data?.name && form.setValue("name", data.name, { shouldValidate: true })
      data?.endpoint && form.setValue("endpoint", data.endpoint, { shouldValidate: true })
      data?.transport_type && form.setValue("transport_type", data.transport_type, { shouldValidate: true })
      data?.status && form.setValue("status", data.status, { shouldValidate: true })
    }
  }
  const addAdvancedConfiguration = () => {
    const info = form.getValues('meta_data')
    form.setValue('meta_data', [...info, { key: '', value: '' }])
  }
  const setKey = (index: number, value: string) => {
    const info = form.getValues('meta_data')
    info[index].key = value
    form.setValue('meta_data', info)
  }
  const setValue = (index: number, value: string) => {
    const info = form.getValues('meta_data')
    info[index].value = value
    form.setValue('meta_data', info)
  }
  const removeAdvancedConfiguration = (index: number) => {
    const info = form.getValues('meta_data')
    info.splice(index, 1)
    form.setValue('meta_data', info)
  }

  useEffect(() => {
    serverId && getData(serverId)
  }, [serverId, currentTenant])

  return (
    <div className="container py-6 space-y-6">
      <div className="flex items-center">
        <Button variant="ghost" size="icon" asChild className="mr-2">
          <Link href="/tools">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">创建MCP服务</h1>
          <p className="text-muted-foreground">添加新的MCP服务</p>
        </div>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
              <CardDescription>设置MCP服务的基本信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>MCP服务名称</FormLabel>
                    <FormControl>
                      <Input placeholder="输入MCP服务名称，如：MCP" {...field} />
                    </FormControl>
                    <FormDescription>MCP服务名称必须唯一</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endpoint"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>端点</FormLabel>
                    <FormControl>
                      <Input placeholder="输入MCP服务端点，如：https://endpoint.example.com/1" {...field} />
                    </FormControl>
                    <FormDescription></FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="transport_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>工具类型</FormLabel>
                    <FormControl>
                      <Select {...field} onValueChange={(value: string) => field.onChange(value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择MCP服务类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="streamable_http">流式传输(Streaming)</SelectItem>
                          <SelectItem value="sse">Server-Sent Events(SSE)</SelectItem>
                          <SelectItem value="http">HTTP</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription></FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>激活状态</FormLabel>
                    <FormControl>
                      <Select {...field} onValueChange={(value: string) => field.onChange(value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择MCP服务状态" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">激活</SelectItem>
                          <SelectItem value="inactive">停用</SelectItem>
                          <SelectItem value="error">错误</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription></FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>高级配置信息</CardTitle>
              <CardDescription>设置MCP服务的配置信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="meta_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>配置类型</FormLabel>
                    <FormControl>
                      <Select {...field} onValueChange={(value: string) => field.onChange(value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择配置类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="header">Header</SelectItem>
                          <SelectItem value="query">Query</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription></FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="meta_data"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>高级配置</FormLabel>
                    <FormControl>
                      <div className="border rounded-md p-4 space-y-4">
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <Label>配置项</Label>
                            <Button variant="outline" onClick={() => addAdvancedConfiguration()} size="sm">
                              <Plus className="h-3 w-3 mr-1" />
                              添加配置
                            </Button>
                          </div>

                          <div className="space-y-2">
                            {field.value.map((item, index) => <div className="flex items-center space-x-2" key={index}>
                              <Input placeholder="配置项名称" value={item.key} onChange={e => setKey(index, e.target.value)} className="flex-1" />
                              <Input placeholder="配置项值" value={item.value} onChange={e => setValue(index, e.target.value)} className="flex-1" />
                              <Button variant="ghost" onClick={() => removeAdvancedConfiguration(index)} size="icon">
                                <Trash2 className="h-4 w-4 text-muted-foreground" />
                              </Button>
                            </div>)}
                          </div>
                        </div>
                      </div>
                    </FormControl>
                    <FormDescription>高级配置将以JSON格式存储</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" asChild>
              <Link href="/tools">取消</Link>
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              <Save className="mr-2 h-4 w-4" />
              {
                serverId ? '修改' : '创建'
              }工具
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
