"use client"
import { useEffect, useState } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, MoreHorizontal, Trash2, ExternalLink, Check } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { apiClient } from "@/lib/api/api"
import { useToast } from "@/hooks/use-toast"
import dayjs from 'dayjs';
import { available } from "@/lib/utils"

import { components } from "@/lib/api/strapi"

type SystemTool = components["schemas"]["TenantTool"]
type MCPServer = components["schemas"]["MCPServer"]
export default function ProvidersPage() {
  useEffect(() => {
    document.title = "工具管理 | 云知问"
  }, [])
  const { currentTenant } = useAuth()
  const [systemTools, setSystemTools] = useState<SystemTool[] | undefined>([])
  const [mcpServers, setMCPServers] = useState<MCPServer[] | undefined>([])
  const [active, setActive] = useState<string>('system')
  const { toast } = useToast()

  const fetchSystemTools = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/tools`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        }
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      setSystemTools(data.items)
    }
  }

  const fetchMCPServers = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/mcp-servers`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        }
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      setMCPServers(data.items)
    }
  }

  const toDelete = async (id: string) => {
    toast.info("", {
      description: active === 'system' ? `确认删除系统工具？` : `确认删除MCP服务？`,
      action: {
        label: "确认",
        onClick: async () => {
          active === 'system' ? await deleteTool(id) : await deleteMCPServer(id)
        },
      }
    })
  }
  const deleteTool = async (id: string) => {
    const { data, error } = await apiClient.DELETE(`/api/v1/tenants/{tenant_id}/tools/{tool_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          tool_id: id,
        }
      }
    })
    if (error) {
      toast.error("删除失败，请稍后再试")
    } else {
      fetchSystemTools()
    }
  }
  const deleteMCPServer = async (id: string) => {
    const { data, error } = await apiClient.DELETE(`/api/v1/tenants/{tenant_id}/mcp-servers/{server_id}`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
          server_id: id,
        }
      }
    })
    if (error) {
      toast.error("删除失败，请稍后再试")
    } else {
      fetchMCPServers()
    }
  }
  useEffect(() => {
    active === 'system' ? fetchSystemTools() : fetchMCPServers()
  }, [active, currentTenant])

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">工具管理</h1>
          </div>
          <div>
            {available(currentTenant) && <Button asChild>
              {active === "system" ?
                <Link href="/tools/system/create">
                  <Plus className="mr-2 h-4 w-4" />
                  添加系统工具
                </Link>
                :
                <Link href="/tools/mcp/create">
                  <Plus className="mr-2 h-4 w-4" />
                  添加MCP服务
                </Link>}
            </Button>}
          </div>
        </div>

        <Tabs value={active} onValueChange={e => setActive(e)} className="space-y-4">
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="system">系统工具</TabsTrigger>
              <TabsTrigger value="mcp">MCP服务</TabsTrigger>
            </TabsList>
            <div className="flex items-center space-x-2">
              {/* <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" placeholder="搜索提供商..." className="pl-8 w-[200px] md:w-[300px]" />
              </div> */}
            </div>
          </div>
          <TabsContent value="system" className="space-y-4" >
            <Card className="px-4">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[250px]">工具名称</TableHead>
                      <TableHead>标签</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>key</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {systemTools?.map((item) => <TableRow key={item.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <span>{item.tool?.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{item.tool?.tag}</Badge>
                      </TableCell>
                      <TableCell className="max-w-[300px] truncate">
                        {item.tool?.description}
                      </TableCell>
                      <TableCell>{item.tool?.key}</TableCell>
                      <TableCell>
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                          <Check className="mr-1 h-3 w-3" />
                          已激活
                        </Badge>
                      </TableCell>
                      <TableCell>{dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/tools/system/${item.tool_id}`}>
                                <ExternalLink className="mr-2 h-4 w-4" />
                                <span>查看详情</span>
                              </Link>
                            </DropdownMenuItem>
                            {available(currentTenant) && <DropdownMenuItem asChild>
                              <Link href={`/tools/system/${item.tool_id}/edit`}>
                                <ExternalLink className="mr-2 h-4 w-4" />
                                <span>编辑</span>
                              </Link>
                            </DropdownMenuItem>}
                            {available(currentTenant) && <DropdownMenuItem className="text-destructive" onClick={() => toDelete(item.tool_id)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>删除</span>
                            </DropdownMenuItem>}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>)}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="mcp" className="space-y-4" >
            <Card className="px-4">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[250px]">MCP名称</TableHead>
                      <TableHead>标签</TableHead>
                      <TableHead>端点</TableHead>
                      <TableHead>key</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mcpServers?.map((item) => <TableRow key={item.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <span>{item.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{item.id}</Badge>
                      </TableCell>
                      <TableCell className="max-w-[300px] truncate">
                        {item.endpoint}
                      </TableCell>
                      <TableCell>{item.transport_type}</TableCell>
                      <TableCell>
                        {item.status === 'active' &&
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                            <Check className="mr-1 h-3 w-3" />
                            已激活
                          </Badge>
                        }
                        {item.status === 'inactive' &&
                          <Badge className="bg-green-100 text-red-800 dark:bg-red-900 dark:text-red-100">
                            <Check className="mr-1 h-3 w-3" />
                            未激活
                          </Badge>
                        }
                        {item.status === 'error' &&
                          <Badge className="bg-green-100 text-red-800 dark:bg-red-900 dark:text-red-100">
                            <Check className="mr-1 h-3 w-3" />
                            错误
                          </Badge>
                        }
                      </TableCell>
                      <TableCell>{dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/tools/mcp/${item.id}`}>
                                <ExternalLink className="mr-2 h-4 w-4" />
                                <span>查看详情</span>
                              </Link>
                            </DropdownMenuItem>
                            {available(currentTenant) && <DropdownMenuItem asChild>
                              <Link href={`/tools/mcp/${item.id}/edit`}>
                                <ExternalLink className="mr-2 h-4 w-4" />
                                <span>编辑</span>
                              </Link>
                            </DropdownMenuItem>}
                            {available(currentTenant) && <DropdownMenuItem className="text-destructive" onClick={() => toDelete(item.id)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>删除</span>
                            </DropdownMenuItem>}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>)}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

        </Tabs>
      </div>
    </MainLayout>
  )
}
