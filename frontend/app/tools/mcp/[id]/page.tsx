"use client"

import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Edit, Trash2 } from "lucide-react"
import { use, useEffect, useState } from "react"
import { components } from "@/lib/api/strapi"
import { apiClient } from "@/lib/api/api"
import { useAuth } from "@/contexts/auth-context"

type MCPServer = components["schemas"]["MCPServer"]

export default function MCPServerDetailPage({ params }: { params: Promise<{ id: string }> }) {
  useEffect(() => {
    document.title = "MCP 服务详情 | 云知问"
  }, [])
  const serverId = use(params)?.id
  const { currentTenant } = useAuth()

  const [mcpServer, setMCPServer] = useState<MCPServer | undefined>()

  useEffect(() => {
    if (!serverId) {
      return
    }
    const fetchMCPServer = async () => {
      const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/mcp-servers/{server_id}`, {
        params: {
          path: {
            tenant_id: currentTenant?.tenant_id ?? '',
            server_id: serverId,
          }
        }
      });
      if (error) {
        console.log(error)
      } else {
        setMCPServer(data)
      }
    }
    fetchMCPServer()
  }, [serverId, currentTenant])

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button variant="ghost" size="icon" asChild className="mr-2">
              <Link href="/tools">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{mcpServer?.name}</h1>
              <p className="text-muted-foreground">MCP 服务名称</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" asChild>
              <Link href={`/tools/mcp/${serverId}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Link>
            </Button>
            <Button variant="destructive">
              <Trash2 className="mr-2 h-4 w-4" />
              删除
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-[1fr_300px]">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
                <CardDescription>MCP 服务的基本信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">创建时间</h3>
                    <p>{mcpServer?.created_at}</p>
                  </div>
                </div>
                <Separator />
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">端点</h3>
                  <p>{mcpServer?.endpoint}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>配置信息</CardTitle>
                <CardDescription>MCP 服务的配置信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Separator />
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">高级配置</h3>
                  <div className="mt-2 p-4 bg-muted/50 rounded-md">
                    <pre className="text-xs overflow-auto">{JSON.stringify(mcpServer?.meta_data, null, 2)}</pre>
                  </div>
                </div>
              </CardContent>
            </Card>

          </div>
        </div>
      </div>
    </MainLayout>
  )
}
