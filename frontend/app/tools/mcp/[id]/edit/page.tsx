"use client"
import { use, useEffect } from "react"
import { MainLayout } from "@/components/main-layout"
import MCPEdit from "@/app/tools/_components/mcp_edit";


export default function EditMCPServerPage({ params }: { params: Promise<{ id: string }> }) {
  useEffect(() => {
    document.title = "编辑 MCP 服务 | 云知问"
  }, [])
  const serverId = use(params)?.id

  return (
    <MainLayout>
      <MCPEdit serverId={serverId} />
    </MainLayout>
  )
}
