# 工具管理 - Router Interceptor 实现

## 功能概述

工具管理页面现在支持使用 Next.js Router Interceptor 模式进行无刷新编辑，确保编辑完成后页面保持当前状态而不是回到默认状态。

## 实现原理

### 1. Router Interceptor 路由结构

```
frontend/app/tools/
├── page.tsx                           # 主工具列表页面
├── (.)mcp/[id]/edit/page.tsx         # MCP 编辑拦截路由
├── (.)system/[id]/edit/page.tsx      # 系统工具编辑拦截路由
├── mcp/[id]/edit/page.tsx            # MCP 编辑常规路由（直接访问时使用）
└── system/[id]/edit/page.tsx         # 系统工具编辑常规路由（直接访问时使用）
```

### 2. 拦截机制

- 当用户从工具列表页面点击"编辑"时，会触发拦截路由 `(.)mcp/[id]/edit` 或 `(.)system/[id]/edit`
- 拦截路由以 Dialog 模式显示编辑表单，不会导致页面刷新
- 如果用户直接访问编辑 URL，则使用常规路由显示完整页面

### 3. 状态保持机制

- 编辑完成后使用 `router.back()` 而不是 `router.push('/tools')` 返回
- 通过 `CustomEvent` 通知父页面刷新数据
- 父页面监听相应事件并重新获取数据

## 使用方式

### 从列表页面编辑（拦截模式）
1. 在工具列表页面点击"编辑"按钮
2. 弹出 Dialog 编辑表单
3. 编辑完成后自动关闭 Dialog 并刷新列表数据
4. 页面状态（如当前选中的 Tab、滚动位置等）保持不变

### 直接访问编辑页面（常规模式）
1. 直接访问 `/tools/mcp/123/edit` 或 `/tools/system/456/edit`
2. 显示完整的编辑页面
3. 编辑完成后跳转回工具列表页面

## 技术实现细节

### 1. 组件接口扩展

```typescript
interface MCPEditProps {
  serverId?: string;
  onSuccess?: () => void;    // 成功回调
  onCancel?: () => void;     // 取消回调
  showHeader?: boolean;      // 是否显示页面头部
}

interface ToolEditProps {
  toolId?: string;
  onSuccess?: () => void;    // 成功回调
  onCancel?: () => void;     // 取消回调
  showHeader?: boolean;      // 是否显示页面头部
}
```

### 2. 事件通信

```typescript
// 编辑成功后触发事件
window.dispatchEvent(new CustomEvent('mcp-updated'))
window.dispatchEvent(new CustomEvent('system-tool-updated'))

// 父页面监听事件
window.addEventListener('mcp-updated', handleMCPUpdate)
window.addEventListener('system-tool-updated', handleSystemToolUpdate)
```

### 3. Dialog 实现

```typescript
const handleClose = () => {
  setIsOpen(false)
  router.back()  // 使用 back() 而不是 push() 保持状态
}

const handleSuccess = () => {
  setIsOpen(false)
  router.back()
  // 触发数据刷新事件
  window.dispatchEvent(new CustomEvent('mcp-updated'))
}
```

## 优势

1. **无刷新体验**: 编辑时不会丢失页面状态
2. **保持上下文**: 用户的操作上下文（如 Tab 选择、滚动位置）得以保持
3. **向后兼容**: 直接访问编辑 URL 仍然正常工作
4. **用户体验**: 更流畅的交互体验，符合现代 Web 应用标准

## 注意事项

1. 拦截路由只在从同一应用内导航时生效
2. 直接访问 URL 或刷新页面会使用常规路由
3. 需要确保 Dialog 组件正确处理键盘导航和焦点管理
4. 事件监听器需要在组件卸载时正确清理
