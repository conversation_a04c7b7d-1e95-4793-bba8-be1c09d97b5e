"use client"
import { use, useEffect } from "react"
import { MainLayout } from "@/components/main-layout"

import ToolEdit from "@/app/tools/_components/tool_edit";
export default function EditToolPage({ params }: { params: Promise<{ id: string }> }) {
  useEffect(() => {
    document.title = "编辑工具 | 云知问"
  }, [])
  const toolId = use(params)?.id
  return (
    <MainLayout>
      <ToolEdit toolId={toolId} />
    </MainLayout>
  )
}
