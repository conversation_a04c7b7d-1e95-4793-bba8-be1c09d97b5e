"use client"

import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Edit, Trash2 } from "lucide-react"
import { use, useEffect, useState } from "react"
import { components } from "@/lib/api/strapi"
import { apiClient } from "@/lib/api/api"
import { useAuth } from "@/contexts/auth-context"
type SystemTool = components["schemas"]["TenantTool"]

export default function ToolDetailPage({ params }: { params: Promise<{ id: string }> }) {
  useEffect(() => {
    document.title = "工具详情 | 云知问"
  }, [])
  const toolId = use(params)?.id
  const { currentTenant } = useAuth()

  const [systemTool, setSystemTool] = useState<SystemTool | undefined>(undefined)

  useEffect(() => {
    if (!toolId) {
      return
    }
    const fetchSystemTool = async () => {
      const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/tools/{tool_id}`, {
        params: {
          path: {
            tenant_id: currentTenant?.tenant_id ?? '',
            tool_id: toolId,
          }
        }
      });
      if (error) {
        console.log(error)
      } else {
        setSystemTool(data)
      }
    }
    fetchSystemTool()
  }, [currentTenant, toolId])

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button variant="ghost" size="icon" asChild className="mr-2">
              <Link href="/tools">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{systemTool?.tool?.name}</h1>
              <p className="text-muted-foreground">工具名称</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" asChild>
              <Link href={`/tools/system/${toolId}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Link>
            </Button>
            <Button variant="destructive">
              <Trash2 className="mr-2 h-4 w-4" />
              删除
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-[1fr_300px]">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
                <CardDescription>工具的基本信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">创建时间</h3>
                    <p>{systemTool?.created_at}</p>
                  </div>
                </div>
                <Separator />
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">描述</h3>
                  <p>{systemTool?.tool?.description}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>配置信息</CardTitle>
                <CardDescription>工具的配置信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Separator />
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">高级配置</h3>
                  <div className="mt-2 p-4 bg-muted/50 rounded-md">
                    <pre className="text-xs overflow-auto">{JSON.stringify(systemTool?.config, null, 2)}</pre>
                  </div>
                </div>
              </CardContent>
            </Card>

          </div>
        </div>
      </div>
    </MainLayout>
  )
}
