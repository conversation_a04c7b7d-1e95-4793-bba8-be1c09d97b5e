import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search, Plus, X, ShieldCheck, Edit } from "lucide-react"
import { Empty } from "@/components/empty"

export const metadata: Metadata = {
  title: "邀请与角色管理 | 云知问",
  description: "云知问多租户智能Agent平台邀请与角色管理",
}

export default function InvitationsPage() {
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">邀请与角色管理</h1>
            <p className="text-muted-foreground">管理用户邀请和角色权限</p>
          </div>
          <div>
            {/* <Button asChild>
              <Link href="/invitations/invite">
                <UserPlus className="mr-2 h-4 w-4" />
                邀请用户
              </Link>
            </Button> */}
          </div>
        </div>

        <Tabs defaultValue="invitations" className="space-y-4">
          <TabsList>
            <TabsTrigger value="invitations">邀请管理</TabsTrigger>
            <TabsTrigger value="roles">角色管理</TabsTrigger>
          </TabsList>

          <TabsContent value="invitations" className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" placeholder="搜索邀请..." className="pl-8 w-[300px]" />
              </div>
              <Button variant="outline" asChild>
                <Link href="/users/invite-user">
                  <Plus className="mr-2 h-4 w-4" />
                  新建邀请
                </Link>
              </Button>
            </div>

            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">邮箱</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>发送时间</TableHead>
                      <TableHead>过期时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {/* <TableRow>
                      <TableCell className="font-medium"><EMAIL></TableCell>
                      <TableCell>
                        <Badge variant="outline">Admin</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100">
                          待接受
                        </Badge>
                      </TableCell>
                      <TableCell>2023-05-15</TableCell>
                      <TableCell>2023-05-22</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              <span>重新发送</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>编辑</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <X className="mr-2 h-4 w-4" />
                              <span>撤销邀请</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium"><EMAIL></TableCell>
                      <TableCell>
                        <Badge variant="outline">Member</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                          已接受
                        </Badge>
                      </TableCell>
                      <TableCell>2023-05-10</TableCell>
                      <TableCell>2023-05-17</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem disabled>
                              <Mail className="mr-2 h-4 w-4" />
                              <span>重新发送</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem disabled>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>编辑</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem disabled>
                              <X className="mr-2 h-4 w-4" />
                              <span>撤销邀请</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium"><EMAIL></TableCell>
                      <TableCell>
                        <Badge variant="outline">Member</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">已过期</Badge>
                      </TableCell>
                      <TableCell>2023-04-20</TableCell>
                      <TableCell>2023-04-27</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              <span>重新邀请</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>删除记录</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium"><EMAIL></TableCell>
                      <TableCell>
                        <Badge variant="outline">Custom</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className="bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-100">
                          已拒绝
                        </Badge>
                      </TableCell>
                      <TableCell>2023-05-05</TableCell>
                      <TableCell>2023-05-12</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              <span>重新邀请</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>删除记录</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow> */}
                  </TableBody>
                </Table>
                <Empty />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="roles" className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" placeholder="搜索角色..." className="pl-8 w-[300px]" />
              </div>
              {/* <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建角色
              </Button> */}
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">Owner</CardTitle>
                      <CardDescription>租户所有者，拥有全部权限</CardDescription>
                    </div>
                    <Badge>系统</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-sm space-y-2">
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>用户与租户管理</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>邀请与角色管理</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>Agent管理（创建、编辑、删除）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>知识库管理（创建、编辑、删除）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>模型管理（创建、编辑、删除）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>系统设置</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" className="w-full" disabled>
                        <Edit className="mr-2 h-3 w-3" />
                        编辑
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">Admin</CardTitle>
                      <CardDescription>管理员，拥有大部分管理权限</CardDescription>
                    </div>
                    <Badge>系统</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-sm space-y-2">
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>用户管理（查看、编辑）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>邀请管理</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>Agent管理（创建、编辑、删除）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>知识库管理（创建、编辑、删除）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>模型管理（创建、编辑）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-600" />
                        <span>系统设置</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" className="w-full" disabled>
                        <Edit className="mr-2 h-3 w-3" />
                        编辑
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">Member</CardTitle>
                      <CardDescription>普通成员，拥有基本使用权限</CardDescription>
                    </div>
                    <Badge>系统</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-sm space-y-2">
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>Agent使用</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>知识库使用</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-600" />
                        <span>Agent管理（创建、编辑、删除）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-600" />
                        <span>知识库管理（创建、编辑、删除）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-600" />
                        <span>模型管理</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-600" />
                        <span>系统设置</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" className="w-full" disabled>
                        <Edit className="mr-2 h-3 w-3" />
                        编辑
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">Agent开发者</CardTitle>
                      <CardDescription>自定义角色，专注于Agent开发</CardDescription>
                    </div>
                    <Badge>自定义</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-sm space-y-2">
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>Agent使用</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>知识库使用</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>Agent管理（创建、编辑）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>知识库管理（使用）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-600" />
                        <span>模型管理</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-600" />
                        <span>系统设置</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" disabled className="w-full">
                        <Edit className="mr-2 h-3 w-3" />
                        编辑
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">知识库管理员</CardTitle>
                      <CardDescription>自定义角色，专注于知识库管理</CardDescription>
                    </div>
                    <Badge>自定义</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-sm space-y-2">
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>Agent使用</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>知识库使用</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-600" />
                        <span>Agent管理</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-4 w-4 text-green-600" />
                        <span>知识库管理（创建、编辑、删除）</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-600" />
                        <span>模型管理</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <X className="h-4 w-4 text-red-600" />
                        <span>系统设置</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" disabled className="w-full">
                        <Edit className="mr-2 h-3 w-3" />
                        编辑
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
