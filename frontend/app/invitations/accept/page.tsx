"use client"
import { useEffect, useState } from "react"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft } from "lucide-react"
import { useRouter, useSearchParams } from 'next/navigation'
import { apiClient } from "@/lib/api/api"
import { useToast } from "@/hooks/use-toast"
import { components } from "@/lib/api/strapi"
import dayjs from 'dayjs';
import { userRoleKey } from "@/lib/options"

type IData = components["schemas"]["InvitationDetailResponse"]
export default function InviteUserPage() {
  useEffect(() => {
    document.title = "邀请用户 | 云知问"
  }, [])
  const router = useRouter()
  const urlParams = useSearchParams();
  const token = urlParams.get('token');
  const [info, setInfo] = useState<IData>()
  const postInfo = async () => {
    const { error } = await apiClient.POST(`/api/v1/auth/accept-invitation`, {
      body: {
        invitation_token: token ?? '',
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      router.push('/')
      useToast().toast.success("提交成功", {
        description: `创建用户成功`
      })
    }
  }
  const getData = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/auth/invitations/{invitation_token}`, {
      params: {
        path: {
          invitation_token: token ?? '',
        }
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      setInfo(data)
    }
  }
  useEffect(() => {
    getData()
  }, [])
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href="/invitations">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">邀请</h1>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{info?.inviter_name}邀请您加入</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">租户名称</p>
                <p>{info?.tenant_name}</p>
              </div>
              <div>
                <p className="text-muted-foreground">租户描述</p>
                <p>{info?.tenant_description}</p>
              </div>
              <div>
                <p className="text-muted-foreground">角色</p>
                <p>{info?.invitation?.role_key as string in userRoleKey ? userRoleKey[info?.invitation?.role_key as keyof typeof userRoleKey] : '未知角色'}</p>
              </div>
              <div>
                <p className="text-muted-foreground">有效期至</p>
                <p>{dayjs(info?.invitation?.expires_at).format('YYYY-MM-DD HH:mm:ss')}</p>
              </div>
            </div>
            <Separator />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" asChild>
                <Link href="/invitations">取消</Link>
              </Button>
              {info?.invitation?.status === 'pending' ? <Button onClick={postInfo}>
                接受邀请
              </Button> : (info?.invitation.status == 'accepted' ? <Button variant="outline" disabled>已完成</Button> : <Button variant="outline" disabled>已过期</Button>)
              }
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
