import type { Metadata } from "next"
import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Send, Plus, Trash2 } from "lucide-react"

export const metadata: Metadata = {
  title: "邀请用户 | 云知问",
  description: "云知问多租户智能Agent平台邀请用户",
}

export default function InviteUserPage() {
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link href="/invitations">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">邀请用户</h1>
            <p className="text-muted-foreground">邀请新用户加入您的租户</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>邀请详情</CardTitle>
            <CardDescription>填写邀请信息，邀请新成员加入</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>邀请至租户</Label>
                <Select defaultValue="abc-tech">
                  <SelectTrigger>
                    <SelectValue placeholder="选择租户" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="personal">个人空间</SelectItem>
                    <SelectItem value="abc-tech">ABC科技有限公司</SelectItem>
                    <SelectItem value="xyz-research">XYZ研究院</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>邀请邮箱</Label>
                  <Button variant="ghost" size="sm">
                    <Plus className="h-3 w-3 mr-1" />
                    批量添加
                  </Button>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Input placeholder="输入邮箱地址" type="email" className="flex-1" />
                    <Select defaultValue="admin">
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="选择角色" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="member">Member</SelectItem>
                        <SelectItem value="agent-dev">Agent开发者</SelectItem>
                        <SelectItem value="kb-admin">知识库管理员</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="ghost" size="icon">
                      <Trash2 className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Input placeholder="输入邮箱地址" type="email" className="flex-1" />
                    <Select defaultValue="member">
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="选择角色" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="member">Member</SelectItem>
                        <SelectItem value="agent-dev">Agent开发者</SelectItem>
                        <SelectItem value="kb-admin">知识库管理员</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="ghost" size="icon">
                      <Trash2 className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </div>
                  <Button variant="outline" className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    添加更多邮箱
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">邀请信息</Label>
                <Textarea
                  id="message"
                  placeholder="添加个性化邀请信息（可选）"
                  rows={4}
                  defaultValue="我邀请您加入ABC科技有限公司的云知问平台，我们可以在这里协作管理智能Agent和知识库。"
                />
              </div>

              <div className="space-y-2">
                <Label>邀请有效期</Label>
                <Select defaultValue="7">
                  <SelectTrigger>
                    <SelectValue placeholder="选择有效期" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1天</SelectItem>
                    <SelectItem value="3">3天</SelectItem>
                    <SelectItem value="7">7天</SelectItem>
                    <SelectItem value="14">14天</SelectItem>
                    <SelectItem value="30">30天</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            <div className="flex justify-end space-x-2">
              <Button variant="outline" asChild>
                <Link href="/invitations">取消</Link>
              </Button>
              <Button>
                <Send className="mr-2 h-4 w-4" />
                发送邀请
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
