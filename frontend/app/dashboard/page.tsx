"use client"
import { MainLayout } from "@/components/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Bot, Database, Users, Layers, Activity, BarChart3, ArrowUpRight, Plus, ExternalLink, Edit } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useEffect, useState } from "react"
import { components } from "@/lib/api/strapi"
import { apiClient } from "@/lib/api/api"
import Link from "next/link"
import dayjs from 'dayjs';

type Agent = components["schemas"]["Agent"]
export default function DashboardPage() {
  useEffect(() => {
    document.title = "仪表盘 | 云知问"
  }, [])

  const { currentTenant } = useAuth()
  const [agents, setAgents] = useState<Agent[] | undefined>([])
  const getData = async () => {
    const { data } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/agents`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        },
        query: {
          skip: 0,
          limit: 3,
        }
      }
    });
    setAgents(data?.items)
  }

  useEffect(() => {
    currentTenant?.tenant_id && getData()
  }, [currentTenant])


  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">仪表盘</h1>
            <p className="text-muted-foreground">欢迎回来，这是您的平台概览</p>
          </div>
          <div className="flex space-x-2">
            <Button asChild>
              <Link href="/agents/create/wizard">
                <Plus className="mr-2 h-4 w-4" />
                创建Agent
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Agent总数</CardTitle>
              <Bot className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">
                较上月 <span className="text-green-500">+2</span>
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">知识库总数</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-muted-foreground">
                较上月 <span className="text-green-500">+3</span>
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">用户总数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">24</div>
              <p className="text-xs text-muted-foreground">
                较上月 <span className="text-green-500">+5</span>
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">模型总数</CardTitle>
              <Layers className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">6</div>
              <p className="text-xs text-muted-foreground">
                较上月 <span className="text-green-500">+1</span>
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="agents" className="space-y-4">
          <TabsList>
            <TabsTrigger value="agents">Agent概览</TabsTrigger>
            <TabsTrigger value="usage">使用情况</TabsTrigger>
            {/* <TabsTrigger value="activity">最近活动</TabsTrigger> */}
          </TabsList>
          <TabsContent value="agents" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {agents?.map((agent) => (
                <Card key={agent.id}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg font-medium flex items-center justify-between">
                      <span>{agent.name}</span>
                      <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 px-2 py-1 rounded-full">
                        已发布
                      </span>
                    </CardTitle>
                    <CardDescription>{agent.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">类型:</span>
                        <span>{agent.agent_type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">更新时间:</span>
                        <span>{dayjs(agent.updated_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                      </div>
                      {/* <div className="flex justify-between">
                        <span className="text-muted-foreground">关联知识库:</span>
                        <span>{agent.knowledge_base_config?.max_results}</span>
                      </div> */}
                    </div>
                    <div className="mt-4 flex space-x-2">
                      <Link href={`/agents/create/wizard?id=${agent.id}`} className="w-full">
                        <Button variant="outline" size="sm" className="w-full">
                          <Edit className="mr-2 h-3 w-3" />
                          编辑
                        </Button>
                      </Link>
                      <Link href={`/agents/${agent.id}`} className="w-full">
                        <Button size="sm" className="w-full">
                          <ExternalLink className="mr-2 h-3 w-3" />
                          访问
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="flex justify-center">
              <Link href="/agents">
                <Button variant="outline" >
                  查看全部
                  <ArrowUpRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </TabsContent>
          <TabsContent value="usage" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>使用情况</CardTitle>
                <CardDescription>过去30天的平台使用情况</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <div className="h-[300px] flex items-center justify-center border-2 border-dashed rounded-md">
                  <div className="text-center">
                    <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-2 text-lg font-semibold">使用统计图表</h3>
                    <p className="text-sm text-muted-foreground">这里将显示Agent调用、知识库访问等使用统计图表</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>最近活动</CardTitle>
                <CardDescription>平台上的最近操作和事件</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex">
                    <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                      <Activity className="h-5 w-5" />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium leading-none">客服Agent更新至v1.2版本</p>
                      <p className="text-sm text-muted-foreground">2小时前 · 由 John Doe 操作</p>
                    </div>
                  </div>
                  <div className="flex">
                    <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                      <Database className="h-5 w-5" />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium leading-none">新增产品手册知识库</p>
                      <p className="text-sm text-muted-foreground">昨天 · 由 Jane Smith 操作</p>
                    </div>
                  </div>
                  <div className="flex">
                    <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                      <Users className="h-5 w-5" />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium leading-none">新用户 Zhang Wei 加入团队</p>
                      <p className="text-sm text-muted-foreground">2天前 · 系统自动处理</p>
                    </div>
                  </div>
                  <div className="flex">
                    <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                      <Bot className="h-5 w-5" />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium leading-none">创建意图分析Agent</p>
                      <p className="text-sm text-muted-foreground">3天前 · 由 John Doe 操作</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
