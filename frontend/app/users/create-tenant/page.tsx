"use client"
import { useEffect, useState } from "react"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Save, Loader2 } from "lucide-react"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { useToast } from "@/hooks/use-toast"
import { apiClient } from "@/lib/api/api"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from "@/contexts/auth-context"

export default function UsersPage() {
  useEffect(() => {
    document.title = "用户与租户管理 | 云知问"
  }, [])
  const router = useRouter()
  const urlParams = useSearchParams();
  const id = urlParams.get('id');
  const { getUser } = useAuth()

  const formSchema = z.object({
    name: z.string().min(2, {
      message: "请输入信息",
    }),
    description: z.string().min(2, {
      message: "请输入信息",
    }),
    // type: z.string().min(2, {
    //   message: "请选择类型",
    // }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      // type: ""
    },
  })

  const [isSubmitting, setIsSubmitting] = useState(false);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      if (id) {
        await putData(values)
      } else {
        await postData(values)
      }
    } finally {
      setIsSubmitting(false);
    }
  }

  const putData = async (values: { name: string; description: string }) => {
    const { error } = await apiClient.PUT(`/api/v1/tenants/{tenant_id}`, {
      params: {
        path: {
          tenant_id: id as string,
        }
      },
      body: {
        name: values.name,
        description: values.description,
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      getUser()
      router.push('/users?tabActive=tenants')
      useToast().toast.success("提交成功", {
        description: `修改租户成功`
      })
    }
  }
  const postData = async (values: { name: string; description: string }) => {
    const { error } = await apiClient.POST(`/api/v1/auth/tenants`, {
      body: {
        name: values.name,
        description: values.description,
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      getUser()
      router.push('/users?tabActive=tenants')
      useToast().toast.success("提交成功", {
        description: `创建租户成功`
      })
    }
  }

  const getTenantData = async (id: string) => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}`, {
      params: {
        path: {
          tenant_id: id,
        }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      form.setValue('name', data.name)
      form.setValue('description', data.description as string)
    }
  }
  useEffect(() => {
    id && getTenantData(id.toString())
  }, [id])
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">用户与租户管理</h1>
            <p className="text-muted-foreground">{id ? '编辑租户信息' : '新建租户信息'}</p>
          </div>
        </div>

        <Card>
          <CardTitle className="px-5">租户信息</CardTitle>
          <CardContent className="px-5">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>租户名称</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入租户名称" {...field} />
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>说明信息</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入说明" {...field} />
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>租户类型</FormLabel>
                      <FormControl>
                        <Select {...field} onValueChange={(value: string) => field.onChange(value)}>
                          <SelectTrigger id="agent_type">
                            <SelectValue placeholder="选择租户类型" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="personal">个人</SelectItem>
                            <SelectItem value="company">公司</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                /> */}
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      提交中...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      提交数据
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
