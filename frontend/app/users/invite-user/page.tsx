"use client"
import { useEffect, useState } from "react"
import { MainLayout } from "@/components/main-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Save } from "lucide-react"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { useToast } from "@/hooks/use-toast"
import { apiClient } from "@/lib/api/api"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useRouter } from 'next/navigation'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { userRoleKey } from "@/lib/options"
import { useAuth } from "@/contexts/auth-context"

export default function UsersPage() {
  useEffect(() => {
    document.title = "用户与租户管理 | 云知问"
  }, [])
  const router = useRouter()
  const { currentTenant } = useAuth()
  const [roleKey] = useState<string>('member')
  const [tenantId, setTenantId] = useState<string>(currentTenant?.tenant_id ?? '')
  const list = JSON.parse(window.localStorage.getItem("ls-user") || '{}')
  const formSchema = z.object({
    name: z.string().min(2, {
      message: "",
    }),
    email: z.string().email("请输入正确邮箱地址"),
    role_key: z.string().min(2, {
      message: "请选择角色",
    }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: tenantId,
      email: "",
      role_key: roleKey,
    },
  })

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    postData(values)
  }
  const postData = async (values: { email: string; role_key: string; }) => {
    const { error } = await apiClient.POST(`/api/v1/auth/tenants/{tenant_id}/invite`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        }
      },
      body: {
        email: values.email,
        role_key: values.role_key,
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      router.push('/users')
      useToast().toast.success("提交成功", {
        description: `邀请成功`
      })
    }
  }
  useEffect(() => {
    setTenantId(currentTenant?.tenant_id ?? '')
  }, [currentTenant])
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">用户与租户管理</h1>
            <p className="text-muted-foreground">邀请用户</p>
          </div>
        </div>

        <Card>
          <CardTitle className="px-5">填写邀请信息，邀请新成员加入</CardTitle>
          <CardContent className="px-5">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>邀请至租户</FormLabel>
                      <FormControl>
                        <Select {...field} value={tenantId} disabled onValueChange={(value: string) => field.onChange(value)}>
                          <SelectTrigger id="role_key">
                            <SelectValue placeholder="选择租户" />
                          </SelectTrigger>
                          <SelectContent>
                            {list?.tenants?.map((item: any) => (
                              <SelectItem key={item?.tenant_id} value={item?.tenant_id}>{item?.tenant?.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>邮箱</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入邮箱" {...field} />
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="role_key"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>角色信息</FormLabel>
                      <FormControl>
                        <Select {...field} onValueChange={(value: string) => field.onChange(value)}>
                          <SelectTrigger id="role_key">
                            <SelectValue placeholder="选择类型" />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.entries(userRoleKey).map(([key, value]) => (
                              <SelectItem value={key} key={key}>{value}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit">
                  <Save className="mr-2 h-4 w-4" />
                  提交邀请
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
