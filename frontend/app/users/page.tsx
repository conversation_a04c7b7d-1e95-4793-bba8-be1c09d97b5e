"use client"
import { useEffect, useState, useRef } from "react"

import Link from "next/link"
import { MainLayout } from "@/components/main-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Empty } from "@/components/empty"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { UpdateUserRole } from "@/components/update-user-role"
import { useSearchParams } from 'next/navigation'

import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Users, Search, Plus, MoreHorizontal, Edit, Trash2, UserPlus, CircleUser } from "lucide-react"
import dayjs from 'dayjs';
import { useAuth } from "@/contexts/auth-context"
import { userRoleKey } from "@/lib/options"
// export const metadata: Metadata = {
//   title: "用户与租户管理 | 云知问",
//   description: "云知问多租户智能Agent平台用户与租户管理",
// }
import { PaginationBox } from "@/components/pagination"
import { apiClient } from "@/lib/api/api"
import { components } from "@/lib/api/strapi"
type IUser = components["schemas"]["UserWithRole"]
type ITenants = components["schemas"]["UserDetail"]
type UserTenantRole = components["schemas"]["UserTenantRole"]
import { debounce } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"
import { available } from "@/lib/utils"

export default function UsersPage() {
  useEffect(() => {
    document.title = "用户与租户管理 | 云知问"
  }, [])
  const urlParams = useSearchParams();
  const active = urlParams.get('tabActive');
  const { getUser, currentTenant, setCurrentTenant } = useAuth()
  const [searchUserInfo, setSearchUserInfo] = useState<{ username: string; is_active: string }>({
    username: '',
    is_active: 'all',
  })
  const [userList, setUserList] = useState<IUser[] | undefined>([])
  const [paginationInfo, setPaginationInfo] = useState({
    totalPages: 0,
    current: 1,
    skip: 0,
    limit: 10,
  })
  const [tabActive, setTabActive] = useState(active || 'users')

  const debouncedSearch = useRef(
    debounce((value: string) => {
      setSearchUserInfo({ ...searchUserInfo, username: value });
      getUserData(); // 触发数据请求
    }, 500)
  ).current
  const getUserData = async () => {
    const query: { username?: string; is_active?: boolean } = {}
    if (searchUserInfo) {
      query.username = searchUserInfo.username
    }
    if (searchUserInfo.is_active != 'all') {
      query.is_active = searchUserInfo.is_active == 'true' ? true : false
    }
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/users`, {
      params: {
        path: {
          tenant_id: currentTenant?.tenant_id ?? '',
        },
        query: {
          skip: paginationInfo.skip,
          limit: paginationInfo.limit,
          ...query
        }
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      setUserList(data.items)
      setPaginationInfo({
        ...paginationInfo,
        totalPages: Math.ceil(data.total / paginationInfo.limit),
      })
    }
    return data
  }
  const delUser = async (item: { username?: string; id: any; }) => {
    useToast().toast.info("删除用户", {
      description: `确认删除用户${item.username}`,
      action: {
        label: "确认",
        onClick: async () => {
          const { error } = await apiClient.DELETE(`/api/v1/tenants/{tenant_id}/users/{user_id}`, {
            params: {
              path: {
                tenant_id: currentTenant?.tenant_id ?? '',
                user_id: item.id,
              }
            }
          })
          if (error) {
            console.log('error', error)
          } else {
            getUserData()
          }
        },
      }
    })
  }
  const changePage = (page: number) => {
    setPaginationInfo({
      ...paginationInfo,
      current: page,
      skip: (page - 1) * paginationInfo.limit,
    })
  }
  useEffect(() => {
    getUserData()
  }, [searchUserInfo, currentTenant, paginationInfo.current])

  const [searchName] = useState<string>('')
  const [tenantsInfo, setTenantsInfo] = useState<ITenants | undefined>()
  const getTenantsData = async () => {
    const { data, error } = await apiClient.GET(`/api/v1/users/me/detail`);
    if (error) {
      console.log('error', error)
    } else {
      setTenantsInfo(data)
    }
  }

  const handleTenantChange = (tenant?: UserTenantRole) => {
    setTabActive('users')
    setCurrentTenant(tenant)
  }

  const toDeleteTenant = async (id: string) => {
    if (currentTenant?.tenant_id == id) {
      return useToast().toast.error("不能删除当前租户")
    }
    const { error } = await apiClient.DELETE(`/api/v1/tenants/{tenant_id}`, {
      params: {
        path: {
          tenant_id: id,
        }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      getUser()
      getTenantsData()
    }
  }

  useEffect(() => {
    getTenantsData()
  }, [searchName])
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">用户与租户管理</h1>
            <p className="text-muted-foreground">管理用户、租户和权限</p>
          </div>
          <div className="flex space-x-2">
            {available(currentTenant) && <Button variant="outline" asChild>
              <Link href="/users/invite-user">
                <UserPlus className="mr-2 h-4 w-4" />
                邀请用户
              </Link>
            </Button>}
          </div>
        </div>

        <Tabs value={tabActive} onValueChange={(e) => setTabActive(e)} className="space-y-4">
          <TabsList>
            <TabsTrigger value="users">用户管理</TabsTrigger>
            <TabsTrigger value="tenants">租户管理</TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="relative w-[300px]">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" onChange={(e) => debouncedSearch(e.target.value)} placeholder="搜索用户..." className="pl-8 block" />
              </div>
              <div className="relative flex items-center justify-between ml-5">
                <Label htmlFor="streaming" className="mr-2">用户状态:</Label>
                <RadioGroup defaultValue="all" onValueChange={(e) => setSearchUserInfo({ ...searchUserInfo, is_active: e })} className="flex items-center space-x-2">
                  <span className="flex items-center space-x-2">
                    <RadioGroupItem value="all" id="r1" />
                    <Label htmlFor="r1">全部</Label>
                  </span>
                  <span className="flex items-center space-x-2">
                    <RadioGroupItem value="true" id="r2" />
                    <Label htmlFor="r2">活跃</Label>
                  </span>
                  <span className="flex items-center space-x-2">
                    <RadioGroupItem value="false" id="r3" />
                    <Label htmlFor="r3">未激活</Label>
                  </span>
                </RadioGroup>
              </div>
              <div className="flex-1/4"></div>
              {/* <Link href="/users/create-user">
                <Button variant="outline">
                  <Plus className="mr-2 h-4 w-4" />
                  添加用户
                </Button>
              </Link> */}
            </div>

            <Card>
              <CardContent className="px-5">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">用户</TableHead>
                      <TableHead>姓名</TableHead>
                      <TableHead>邮箱</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>加入时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {userList && userList.map((item) => (<TableRow key={item.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage />
                            <AvatarFallback>
                              <CircleUser className="h-8 w-8"/>
                            </AvatarFallback>
                          </Avatar>
                          <span>{item.username}</span>
                        </div>
                      </TableCell>
                      <TableCell>{item.display_name}</TableCell>
                      <TableCell>{item.email}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{item?.role_key in userRoleKey ? userRoleKey[item.role_key as keyof typeof userRoleKey] : '未知角色'}</Badge>
                      </TableCell>
                      <TableCell>
                        {item.is_active ? <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                          活跃
                        </Badge> : <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100">
                          未激活
                        </Badge>}
                      </TableCell>
                      <TableCell>{dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')}</TableCell>
                      <TableCell className="text-right">
                        {available(currentTenant) && <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <UpdateUserRole userId={item.id} getUserData={() => getUserData()} />

                            {/* <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              <span>发送邮件</span>
                            </DropdownMenuItem> */}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Button variant="ghost" onClick={() => delUser(item)} className="w-full">
                                <Trash2 className="mr-2 h-4 w-4" />
                                <span>删除</span>
                              </Button>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>}
                      </TableCell>
                    </TableRow>))}
                  </TableBody>
                </Table>
                {!userList?.length && <Empty />}
              </CardContent>
            </Card>
            <PaginationBox totalPages={paginationInfo.totalPages} current={paginationInfo.current} changePage={changePage} />
          </TabsContent>

          <TabsContent value="tenants" className="space-y-4">
            <div className="flex justify-between items-center">
              <div></div>
              {/* <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" onChange={(e) => setSearchName(e.target.value)} placeholder="搜索租户..." className="pl-8 w-[300px]" />
              </div> */}
              <Link href="/users/create-tenant">
                <Button variant="outline">
                  <Plus className="mr-2 h-4 w-4" />
                  添加租户
                </Button>
              </Link>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {tenantsInfo?.tenants?.map((item) => (<Card key={item?.tenant?.id}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex justify-between items-center">
                    <span>{item?.tenant?.name}</span>
                    {/* <Badge>个人</Badge>
                      <Badge>企业</Badge> */}
                    {item.role_key == 'owner' && <Badge>{item?.role?.name}</Badge>}
                    {item.role_key == 'member' && <Badge variant="outline">{item?.role?.name}</Badge>}
                    {item.role_key == 'admin' && <Badge>{item?.role?.name}</Badge>}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      {item.role_key == 'owner' && <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleTenantChange(item)}>
                          <Users className="mr-2 h-3 w-3" />
                          <span>管理成员</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive" onClick={() => toDeleteTenant(item?.tenant?.id as string)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          <span>删除</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>}
                    </DropdownMenu>
                  </CardTitle>
                  <CardDescription>{item?.tenant?.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-sm space-y-2">
                      {/* <div className="flex justify-between">
                        <span className="text-muted-foreground">创建者:</span>
                        <span>--</span>
                      </div> */}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">创建时间:</span>
                        <span>{dayjs(item?.tenant?.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">更新时间:</span>
                        <span>{dayjs(item?.tenant?.updated_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                      </div>
                      {/* <div className="flex justify-between">
                        <span className="text-muted-foreground">成员数:</span>
                        <span>--</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Agent数:</span>
                        <span>--</span>
                      </div> */}
                    </div>
                    <div className="flex space-x-2">
                      {currentTenant?.tenant_id != item?.tenant?.id && item.role_key == 'owner' ? <Link href={`/users/create-tenant?id=${item?.tenant?.id}`} className="w-full block">
                        <Button variant="outline" size="sm" className="w-full">
                          <Edit className="mr-2 h-3 w-3" />
                          编辑
                        </Button>
                      </Link> : <Button disabled className="bg-[#ccc] w-full">编辑</Button>}
                      <Link href={`/users/tenant-detail?id=${item?.tenant?.id}`} className="w-full block">
                        <Button size="sm" className="w-full">
                          查看详情
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
