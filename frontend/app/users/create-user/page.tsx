"use client"
import { useEffect } from "react"
import { MainLayout } from "@/components/main-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Save } from "lucide-react"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { useToast } from "@/hooks/use-toast"
import { apiClient } from "@/lib/api/api"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useRouter } from 'next/navigation'
import { UserFormData, userSchema } from "@/lib/validations/auth"

export default function UsersPage() {
  useEffect(() => {
    document.title = "用户与租户管理 | 云知问"
  }, [])
  const router = useRouter()

  const form = useForm<UserFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(userSchema),
    defaultValues: {
      username: "",
      email: "",
      password: "",
      display_name: "",
    },
  })

  const onSubmit = (values: UserFormData) => {
    postUserData(values)
  }
  const postUserData = async (values: { username: string; email: string; password: string; display_name?: string }) => {
    const { error } = await apiClient.POST(`/api/v1/auth/register`, {
      body: {
        username: values.username,
        email: values.email,
        password: values.password,
        display_name: values.display_name
      }
    });
    if (error) {
      console.log('error', error)
    } else {
      router.push('/users')
      useToast().toast.success("提交成功", {
        description: `创建用户成功`
      })
    }
  }

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">用户与租户管理</h1>
            <p className="text-muted-foreground">新建用户信息</p>
          </div>
        </div>

        <Card>
          <CardTitle className="px-5">用户信息</CardTitle>
          <CardContent className="px-5">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>用户名</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入用户名" {...field} />
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>邮箱</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入邮箱" {...field} />
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="display_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>用户姓名</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入用户姓名" {...field} />
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>密码</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入密码" {...field} />
                      </FormControl>
                      <FormDescription></FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit">
                  <Save className="mr-2 h-4 w-4" />
                  提交数据
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
