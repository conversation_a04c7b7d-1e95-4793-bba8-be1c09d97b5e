"use client"
import { useEffect, useState } from "react"
import { MainLayout } from "@/components/main-layout"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { apiClient } from "@/lib/api/api"
import { useSearchParams } from 'next/navigation'
import { components } from "@/lib/api/strapi"
import dayjs from 'dayjs';

type IData = components["schemas"]["TenantWithStats"]
export default function UsersPage() {
  useEffect(() => {
    document.title = "用户与租户管理 | 云知问"
  }, [])
  const urlParams = useSearchParams();
  const id = urlParams.get('id');
  const [data, setData] = useState<IData>()

  const getTenantData = async (id: string) => {
    const { data, error } = await apiClient.GET(`/api/v1/tenants/{tenant_id}/stats`, {
      params: {
        path: {
          tenant_id: id,
        }
      }
    })
    if (error) {
      console.log('error', error)
    } else {
      console.log('data', data)
      setData(data)
    }
  }
  useEffect(() => {
    id && getTenantData(id.toString())
  }, [id])
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">用户与租户管理</h1>
            <p className="text-muted-foreground mr-2">租户详情</p>
          </div>
        </div>

        <Card>
          <CardTitle className="px-5">租户信息</CardTitle>
          <CardContent className="px-5">
            <div className="space-y-4">
              <div className="text-sm space-y-2 flex justify-between flex-wrap">
                <div className="flex flex-1/2">
                  <span className="text-muted-foreground mr-2">租户名称:</span>
                  <span>{data?.name}</span>
                </div>
                {/* <div className="flex flex-1/2">
                  <span className="text-muted-foreground mr-2">创建者:</span>
                  <span>创建者</span>
                </div> */}
                <div className="flex flex-1/2">
                  <span className="text-muted-foreground mr-2">租户描述:</span>
                  <span>{data?.description}</span>
                </div>
                {/* <div className="flex flex-1/2">
                  <span className="text-muted-foreground mr-2">类型:</span>
                  <span>企业、个人</span>
                </div> */}
                <div className="flex flex-1/2">
                  <span className="text-muted-foreground mr-2">创建时间:</span>
                  <span>{dayjs(data?.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                </div>
                <div className="flex flex-1/2">
                  <span className="text-muted-foreground mr-2">更新时间:</span>
                  <span>{dayjs(data?.updated_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                </div>
                <div className="flex flex-1/2">
                  <span className="text-muted-foreground mr-2">成员数:</span>
                  <span>{data?.user_count}</span>
                </div>
                <div className="flex flex-1/2">
                  <span className="text-muted-foreground mr-2">Agent数:</span>
                  <span>{data?.agent_count}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
