"use client"

import { useState, useEffect } from "react"

const <PERSON><PERSON><PERSON><PERSON>_BREAKPOINT = 768

export function useMobile() {
  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined)

  useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}


// "use client"

// import { useState, useEffect } from "react"

// export const useMobile = () => {
//   const [isMobile, setIsMobile] = useState(false)

//   useEffect(() => {
//     const handleResize = () => {
//       setIsMobile(window.innerWidth < 768) // Adjust breakpoint as needed
//     }

//     // Set initial value
//     handleResize()

//     // Listen for window resize events
//     window.addEventListener("resize", handleResize)

//     // Remove event listener on cleanup
//     return () => window.removeEventListener("resize", handleResize)
//   }, [])

//   return isMobile
// }
