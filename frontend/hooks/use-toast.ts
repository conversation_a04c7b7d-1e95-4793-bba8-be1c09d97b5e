
import { components } from '@/lib/api/strapi';
import { toast as sonner } from 'sonner';

type ToastError = components["schemas"]["ValidationError"]

export function useToast() {
    return {
        toast: sonner,
        toastError: (detail: ToastError[] | string | undefined) => {
            const error = typeof detail === 'string' ? detail : detail?.map((item) => item.msg).join('\n') || '操作失败，请稍后再试'
            sonner.error(error);
        }
    };
}
