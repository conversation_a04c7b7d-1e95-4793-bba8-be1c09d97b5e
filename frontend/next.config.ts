import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  async headers() {
    return [
      {
        source: '/api/:path*', 
        headers: [
          {
            key: 'env', 
            value: 'dev', 
          },
        ],
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*', 
        destination: 'http://localhost:8000/api/:path*',
      },
    ];
  },
};

export default nextConfig;
