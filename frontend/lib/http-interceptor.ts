"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { apiClient } from "./api/api"
import { useAuth } from "@/contexts/auth-context"
import { Middleware } from "openapi-fetch";

export function HttpInterceptor() {

  const router = useRouter()
  const { toast } = useToast()
  const { token } = useAuth();

  const UNPROTECTED_ROUTES = ["/api/v1/auth/login", "/api/v1/auth/register", "/v1/public/"];

  const responseHandler = async (response: Response) => {

    if (!response.ok) {
      let errorMessage = "请求失败";
      try {
        const data = await response.clone().json();
        if (data?.detail) {
          errorMessage = data.detail;
        }
      } catch {
        try {
          const text = await response.clone().text();
          if (text) {
            errorMessage = text;
          }
        } catch {
          // 忽略解析错误，使用默认消息
        }
      }
      switch (response.status) {
        case 308:
          errorMessage = "请求的资源已被永久移动"
          break
        case 401:
          {
            errorMessage = "会话已过期，请重新登录";
            router.replace("/login");
          }
          break
        case 404:
          errorMessage = "请求的资源不存在"
          break
        case 500:
          errorMessage = "服务器内部错误"
          break
        case 502:
          errorMessage = "网关错误"
          break
        default:
          break
      }
      toast.error(errorMessage)
    }
  }

  const middleware: Middleware = {
    async onRequest({ schemaPath, request }) {
      if (UNPROTECTED_ROUTES.some((pathname) => schemaPath.startsWith(pathname))) {
        return undefined;
      }
      if (token) {
        request.headers.set("Authorization", `${token?.token_type} ${token?.access_token}`);
      }
      return request;
    },
    async onResponse({ response }) {
      responseHandler(response)
      return response;
    },
    async onError({ error }) {
      return new Error("Oops, fetch failed", { cause: error });
    },
  }

  useEffect(() => {
    apiClient.use(middleware);
    const originalFetch = window.fetch
    window.fetch = async (input, init) => {
      try {
        const headers = new Headers(init?.headers || {})

        if (token) {
          headers.set("Authorization", `${token?.token_type} ${token?.access_token}`)
        }

        const updatedInit = {
          ...init,
          headers
        }

        const response = await originalFetch(input, updatedInit)
        responseHandler(response)
        return response
      } catch (error) {
        toast.error("网络错误，请稍后再试");
        throw error
      }
    }

    // 清理函数
    return () => {
      apiClient.eject(middleware);
      window.fetch = originalFetch
    }
  }, [router, toast, token, middleware, responseHandler])
  return null;
}
