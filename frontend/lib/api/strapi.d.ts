/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/v1/auth/register": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 用户注册
         * @description 注册新用户并创建个人组织空间
         */
        post: operations["register_api_v1_auth_register_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 用户登录
         * @description 用户登录并获取访问令牌
         *
         *     使用OAuth2兼容表单：
         *     - username: 用户名或邮箱
         *     - password: 密码
         */
        post: operations["login_api_v1_auth_login_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/tenants": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 创建组织
         * @description 创建新组织
         */
        post: operations["create_tenant_api_v1_auth_tenants_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/tenants/{tenant_id}/invite": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 邀请用户
         * @description 邀请用户加入组织
         */
        post: operations["invite_user_api_v1_auth_tenants__tenant_id__invite_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/invitations/{invitation_token}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取邀请详情
         * @description 获取邀请详情（无需认证）
         */
        get: operations["get_invitation_details_api_v1_auth_invitations__invitation_token__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/accept-invitation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 接受邀请
         * @description 接受组织邀请
         */
        post: operations["accept_invitation_api_v1_auth_accept_invitation_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取我的租户列表
         * @description 获取租户列表
         */
        get: operations["list_tenants_api_v1_tenants_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取租户详情
         * @description 获取租户详情
         */
        get: operations["get_tenant_api_v1_tenants__tenant_id__get"];
        /**
         * 更新租户
         * @description 更新租户信息
         */
        put: operations["update_tenant_api_v1_tenants__tenant_id__put"];
        post?: never;
        /**
         * 删除租户
         * @description 删除租户
         */
        delete: operations["delete_tenant_api_v1_tenants__tenant_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/stats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取租户统计信息
         * @description 获取租户详情及统计信息
         */
        get: operations["get_tenant_stats_api_v1_tenants__tenant_id__stats_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取租户下的用户列表
         * @description 获取租户下的用户列表，包含每个用户的角色信息
         */
        get: operations["list_tenant_users_api_v1_tenants__tenant_id__users_get"];
        put?: never;
        /**
         * 添加用户到租户
         * @description 将用户添加到租户
         */
        post: operations["add_user_to_tenant_api_v1_tenants__tenant_id__users_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/users/{user_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取租户中的用户详情
         * @description 获取租户中用户的详情，包含角色信息
         */
        get: operations["get_tenant_user_api_v1_tenants__tenant_id__users__user_id__get"];
        /**
         * 更新用户角色
         * @description 更新用户在租户中的角色
         */
        put: operations["update_user_role_api_v1_tenants__tenant_id__users__user_id__put"];
        post?: never;
        /**
         * 将用户从租户中移除
         * @description 将用户从租户中移除
         */
        delete: operations["remove_user_from_tenant_api_v1_tenants__tenant_id__users__user_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/users/me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取当前用户信息
         * @description 获取当前登录用户的信息
         */
        get: operations["get_current_user_info_api_v1_users_me_get"];
        /**
         * 更新当前用户信息
         * @description 更新当前登录用户的信息
         */
        put: operations["update_current_user_api_v1_users_me_put"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/users/me/detail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取当前用户详细信息
         * @description 获取当前登录用户的详细信息，包括所属租户和角色
         */
        get: operations["get_current_user_detail_api_v1_users_me_detail_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/users/me/change-password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 修改当前用户密码
         * @description 修改当前登录用户的密码，需要提供原密码进行验证
         */
        post: operations["change_current_user_password_api_v1_users_me_change_password_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/roles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取所有可用角色
         * @description 获取所有可用的租户角色
         */
        get: operations["list_all_roles_api_v1_roles_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/roles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取租户角色列表
         * @description 获取租户下的角色列表（固定角色）
         */
        get: operations["list_tenant_roles_api_v1_tenants__tenant_id__roles_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/roles/{role_key}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取角色详情
         * @description 获取角色详情和权限
         */
        get: operations["get_role_api_v1_tenants__tenant_id__roles__role_key__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/templates": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent Templates
         * @description 获取Agent创建模板
         *
         *     返回可用的Agent模板列表，包含预设配置和向导步骤
         */
        get: operations["get_agent_templates_api_v1_tenants__tenant_id__agents_templates_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agents
         * @description 获取Agent列表
         */
        get: operations["get_agents_api_v1_tenants__tenant_id__agents_get"];
        put?: never;
        /**
         * Create Agent
         * @description 创建Agent - 草稿模式
         *
         *     **创建方式：**
         *
         *     1. **基于模板创建（推荐）**：
         *        - 指定 `template_id`，系统会基于模板的默认配置创建Agent草稿
         *
         *     2. **自定义创建**：
         *        - 不指定 `template_id`，自定义所有配置
         *        - 必须指定 `agent_type`（supervisor、general）
         *
         *     **创建流程：**
         *     - 所有Agent创建后都处于草稿状态
         *     - 可以立即修改基本信息（name、description等）
         *     - 配置信息需要通过草稿接口完善后发布
         *
         *     **后续操作：**
         *     1. 修改基本信息：`PUT /agents/{agent_id}/basic-info`（立即生效）
         *     2. 完善配置：`PUT /agents/{agent_id}/draft`（草稿保存）
         *     3. 验证配置：`POST /agents/{agent_id}/validate`
         *     4. 发布Agent：`POST /agents/{agent_id}/publish`
         */
        post: operations["create_agent_api_v1_tenants__tenant_id__agents_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent
         * @description 获取Agent详情，包含版本信息和模型信息
         */
        get: operations["get_agent_api_v1_tenants__tenant_id__agents__agent_id__get"];
        /**
         * Update Agent
         * @description 更新Agent信息 - 兼容性接口
         *
         *     ⚠️ **推荐使用专门的接口**：
         *     - 基本信息：`PUT /agents/{agent_id}/basic-info`
         *     - 配置信息：`PUT /agents/{agent_id}/draft`
         *
         *     此接口保留向后兼容性，但建议迁移到专门接口。
         */
        put: operations["update_agent_api_v1_tenants__tenant_id__agents__agent_id__put"];
        post?: never;
        /**
         * Delete Agent
         * @description 删除/停用Agent
         */
        delete: operations["delete_agent_api_v1_tenants__tenant_id__agents__agent_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/basic-info": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update Agent Basic Info
         * @description 更新Agent基本信息 - 立即生效
         *
         *     **基本信息**（立即更新，无需发布）：
         *     - `name`：Agent名称
         *     - `description`：Agent描述
         *     - `is_active`：是否激活
         *
         *     **注意**：
         *     - 基本信息修改立即生效，不影响配置版本
         *     - 配置相关信息请使用 `/draft` 接口
         *     - 图标管理请使用专门的图标接口
         */
        put: operations["update_agent_basic_info_api_v1_tenants__tenant_id__agents__agent_id__basic_info_put"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/icon": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Upload Agent Icon
         * @description 上传Agent图标
         *
         *     - **支持格式**: JPEG、PNG、GIF、SVG、WebP
         *     - **文件大小**: 最大2MB
         *     - **建议尺寸**: 256x256像素，正方形
         *     - **存储路径**: `{tenant_id}/agents/{agent_id}/icon.{ext}`
         *
         *     上传成功后会自动删除之前的图标文件（如果存在）
         */
        post: operations["upload_agent_icon_api_v1_tenants__tenant_id__agents__agent_id__icon_post"];
        /**
         * Delete Agent Icon
         * @description 删除Agent图标
         *
         *     删除Agent的图标文件和相关元数据
         */
        delete: operations["delete_agent_icon_api_v1_tenants__tenant_id__agents__agent_id__icon_delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/icon-url": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent Icon Url
         * @description 获取Agent图标的访问URL
         *
         *     返回可直接访问的图标URL，用于前端展示
         */
        get: operations["get_agent_icon_url_api_v1_tenants__tenant_id__agents__agent_id__icon_url_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/draft": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent Draft
         * @description 获取Agent配置信息 - 草稿模式
         *
         *     **配置信息**包括：
         *     - `model_id`：LLM模型ID
         *     - `prompt`：提示词配置
         *     - `knowledge_base_ids`：知识库关联
         *     - `tool_ids`：工具关联
         *     - `mcp_server_ids`：MCP服务器关联
         *     - `llm_model_config`：LLM模型参数（温度、token数等）
         *     - `knowledge_base_config`：知识库检索配置
         *
         *     **说明**：
         *     - 如果没有草稿，会基于当前发布版本创建草稿
         *     - 配置修改通过草稿->发布流程，确保质量
         *     - 基本信息（name、description）请使用 `/basic-info` 接口
         *     - 使用结构化配置字段，后端自动处理JSON存储
         */
        get: operations["get_agent_draft_api_v1_tenants__tenant_id__agents__agent_id__draft_get"];
        /**
         * Update Agent Draft
         * @description 更新Agent配置信息 - 草稿保存
         *
         *     **可更新的配置信息**：
         *     - `model_id`：LLM模型ID
         *     - `prompt`：提示词内容
         *     - `knowledge_base_ids`：关联知识库列表
         *     - `tool_ids`：关联工具列表
         *     - `mcp_server_ids`：关联MCP服务器列表
         *     - `llm_model_config`：LLM模型参数配置
         *     - `knowledge_base_config`：知识库检索配置
         *
         *     **结构化配置说明**：
         *     - 使用结构化字段而非原始JSON，提升开发体验
         *     - 后端自动将配置合并到数据库的config字段
         *     - 前端无需关心底层JSON结构
         *
         *     **草稿模式特点**：
         *     - 自动保存，不影响当前运行版本
         *     - 支持增量更新，只传入需要修改的字段
         *     - 更新后需要调用 `/publish` 发布才生效
         *     - 可以随时通过 `/validate` 验证配置完整性
         *
         *     **注意**：
         *     - 此接口只处理配置信息，不处理基本信息
         *     - 基本信息（name、description）请使用 `/basic-info` 接口
         */
        put: operations["update_agent_draft_api_v1_tenants__tenant_id__agents__agent_id__draft_put"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/publish": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Publish Agent Config
         * @description 发布Agent配置 - 草稿生效
         *
         *     **发布流程**：
         *     1. 验证草稿配置完整性
         *     2. 创建新的配置版本
         *     3. 将新版本设为当前生效版本
         *     4. 基于新版本创建新草稿（供下次修改）
         *
         *     **版本管理**：
         *     - 自动生成语义化版本号（如 1.0.0 -> 1.1.0）
         *     - 保留历史版本，支持回滚
         *     - 发布后立即生效，Agent开始使用新配置
         *
         *     **说明**：
         *     - 只影响配置信息，不影响基本信息
         *     - 基本信息修改立即生效，无需发布
         */
        post: operations["publish_agent_config_api_v1_tenants__tenant_id__agents__agent_id__publish_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/history": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent History
         * @description 获取发布历史
         *
         *     获取所有已发布的版本历史
         */
        get: operations["get_agent_history_api_v1_tenants__tenant_id__agents__agent_id__history_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/rollback/{version_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Rollback Agent Version
         * @description 回滚到指定版本
         *
         *     回滚到指定历史版本，立即生效并创建新草稿
         */
        post: operations["rollback_agent_version_api_v1_tenants__tenant_id__agents__agent_id__rollback__version_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/validate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Validate Agent Config
         * @description 验证Agent配置
         *
         *     检查当前草稿配置是否完整和有效
         */
        post: operations["validate_agent_config_api_v1_tenants__tenant_id__agents__agent_id__validate_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/wizard": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Config Wizard
         * @description 获取配置向导步骤
         *
         *     返回Agent配置的向导步骤和当前进度
         */
        get: operations["get_config_wizard_api_v1_tenants__tenant_id__agents__agent_id__wizard_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/available-knowledge-bases": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Knowledge Bases
         * @description 获取租户下可用的知识库列表
         *
         *     用于Agent创建和编辑时选择关联的知识库
         */
        get: operations["get_available_knowledge_bases_api_v1_tenants__tenant_id__agents_available_knowledge_bases_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/available-tools": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Tools
         * @description 获取租户下可用的工具列表
         *
         *     用于Agent创建和编辑时选择关联的工具
         */
        get: operations["get_available_tools_api_v1_tenants__tenant_id__agents_available_tools_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/available-mcp-servers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Mcp Servers
         * @description 获取租户下可用的MCP服务器列表
         *
         *     用于Agent创建和编辑时选择关联的MCP服务器
         */
        get: operations["get_available_mcp_servers_api_v1_tenants__tenant_id__agents_available_mcp_servers_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tools": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Tools
         * @description 获取工具列表
         */
        get: operations["list_tools_api_v1_tools_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tools/{tool_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Tool
         * @description 获取工具详情
         */
        get: operations["get_tool_api_v1_tools__tool_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/tools": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** List Tenant Tools */
        get: operations["list_tenant_tools_api_v1_tenants__tenant_id__tools_get"];
        put?: never;
        /**
         * Create Tenant Tool
         * @description 为租户配置工具
         */
        post: operations["create_tenant_tool_api_v1_tenants__tenant_id__tools_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/tools/{tool_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Tenant Tool */
        get: operations["get_tenant_tool_api_v1_tenants__tenant_id__tools__tool_id__get"];
        /**
         * Update Tenant Tool
         * @description 更新租户工具配置
         */
        put: operations["update_tenant_tool_api_v1_tenants__tenant_id__tools__tool_id__put"];
        post?: never;
        /**
         * Delete Tenant Tool
         * @description 删除租户工具配置
         */
        delete: operations["delete_tenant_tool_api_v1_tenants__tenant_id__tools__tool_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/tools-available": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Tenant Available Tools
         * @description 获取租户可用工具列表
         */
        get: operations["list_tenant_available_tools_api_v1_tenants__tenant_id__tools_available_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/mcp-servers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Mcp Servers
         * @description 获取MCP服务器列表
         */
        get: operations["list_mcp_servers_api_v1_tenants__tenant_id__mcp_servers_get"];
        put?: never;
        /**
         * Create Mcp Server
         * @description 创建MCP服务器
         */
        post: operations["create_mcp_server_api_v1_tenants__tenant_id__mcp_servers_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/mcp-servers/{server_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Mcp Server
         * @description 获取MCP服务器详情
         */
        get: operations["get_mcp_server_api_v1_tenants__tenant_id__mcp_servers__server_id__get"];
        /**
         * Update Mcp Server
         * @description 更新MCP服务器信息
         */
        put: operations["update_mcp_server_api_v1_tenants__tenant_id__mcp_servers__server_id__put"];
        post?: never;
        /**
         * Delete Mcp Server
         * @description 删除MCP服务器
         */
        delete: operations["delete_mcp_server_api_v1_tenants__tenant_id__mcp_servers__server_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/mcp-servers-available": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Tenant Available Mcp Servers
         * @description 获取租户可用的MCP服务器列表
         */
        get: operations["list_tenant_available_mcp_servers_api_v1_tenants__tenant_id__mcp_servers_available_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/{tool_ids}/test/{server_ids}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Check Mcp Server Is Available */
        get: operations["check_mcp_server_is_available_api_v1_tenants__tenant_id___tool_ids__test__server_ids__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/models": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Models
         * @description 获取租户模型列表
         */
        get: operations["list_models_api_v1_tenants__tenant_id__models_get"];
        put?: never;
        /**
         * Create Model
         * @description 创建模型
         */
        post: operations["create_model_api_v1_tenants__tenant_id__models_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/models/{model_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Model
         * @description 获取租户模型详情
         */
        get: operations["get_model_api_v1_tenants__tenant_id__models__model_id__get"];
        /**
         * Update Model
         * @description 更新租户模型信息
         */
        put: operations["update_model_api_v1_tenants__tenant_id__models__model_id__put"];
        post?: never;
        /**
         * Delete Model
         * @description 删除租户模型
         */
        delete: operations["delete_model_api_v1_tenants__tenant_id__models__model_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/model-types": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Model Types
         * @description 获取支持的模型类型列表
         */
        get: operations["get_model_types_api_v1_model_types_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/available-providers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Providers
         * @description 获取可供租户启用的所有提供商列表（包括内置和自定义OpenAI兼容提供商）
         */
        get: operations["get_available_providers_api_v1_tenants__tenant_id__available_providers_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/enabled-providers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Enabled Providers
         * @description 获取租户已启用的提供商配置列表（同一提供商可能有多个不同配置）
         */
        get: operations["get_enabled_providers_api_v1_tenants__tenant_id__enabled_providers_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/enable-provider/{provider_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Enable Provider
         * @description 为租户启用提供商（添加新的配置）
         *     同一个提供商可以启用多次，使用不同的配置
         *
         *     请求体格式：
         *     {
         *         "name": "配置名称",
         *         "credentials": {
         *             "api_key": "your-api-key",
         *             // 其他认证信息
         *         }
         *     }
         */
        post: operations["enable_provider_api_v1_tenants__tenant_id__enable_provider__provider_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/disable-provider/{credential_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Disable Provider
         * @description 禁用租户的特定提供商配置
         */
        delete: operations["disable_provider_api_v1_tenants__tenant_id__disable_provider__credential_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/provider-credentials/{credential_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Edit Provider Config
         * @description 编辑已启用的提供商配置
         *
         *     支持部分更新：
         *     - name: 更新配置名称
         *     - credentials: 更新凭证信息
         *     - is_active: 启用/禁用配置
         *
         *     可选验证：
         *     - validate_config=true: 验证更新后的配置是否有效
         *     - resync_models=true: 重新同步模型列表
         */
        put: operations["edit_provider_config_api_v1_tenants__tenant_id__provider_credentials__credential_id__put"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/test-provider-config": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Test Provider Config
         * @description 测试提供商配置是否有效（在正式启用前测试）
         *
         *     请求体格式：
         *     {
         *         "provider_id": "提供商ID",
         *         "credentials": {
         *             "api_key": "your-api-key",
         *             // 其他认证信息
         *         }
         *     }
         */
        post: operations["test_provider_config_api_v1_tenants__tenant_id__test_provider_config_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/provider-models/{credential_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Provider Models
         * @description 获取特定提供商配置下的模型列表（带10分钟内存缓存）
         */
        get: operations["get_provider_models_api_v1_tenants__tenant_id__provider_models__credential_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/provider-config-schema/{provider_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Provider Config Schema
         * @description 获取特定提供商的配置 Schema，用于前端动态生成配置表单
         */
        get: operations["get_provider_config_schema_api_v1_tenants__tenant_id__provider_config_schema__provider_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 超级管理员查看所有用户
         * @description 超级管理员查看系统中的所有用户
         */
        get: operations["admin_list_all_users_api_v1_admin_users_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/users/{user_id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * 超级管理员更改用户状态
         * @description 超级管理员更改用户的激活状态
         */
        put: operations["admin_update_user_status_api_v1_admin_users__user_id__status_put"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/make-super-admin": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 设置超级管理员
         * @description 将用户设置为超级管理员
         */
        post: operations["make_super_admin_api_v1_admin_make_super_admin_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/remove-super-admin": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 移除超级管理员
         * @description 移除用户的超级管理员权限
         */
        post: operations["remove_super_admin_api_v1_admin_remove_super_admin_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/tenants": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 超级管理员查看所有租户
         * @description 超级管理员查看系统中的所有租户列表
         */
        get: operations["admin_list_all_tenants_api_v1_admin_tenants_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/tenants/{tenant_id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * 超级管理员更改租户状态
         * @description 超级管理员更改租户的激活状态
         */
        put: operations["admin_update_tenant_status_api_v1_admin_tenants__tenant_id__status_put"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/admin/tenants/{tenant_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * 超级管理员强制删除租户
         * @description 超级管理员强制删除租户（包括所有关联数据）
         */
        delete: operations["admin_force_delete_tenant_api_v1_admin_tenants__tenant_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/bases": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Knowledge Bases
         * @description 获取知识库列表
         *
         *     - **tenant_id**: 租户ID
         *     - **skip**: 分页起始位置
         *     - **limit**: 分页大小
         *     - **is_active**: 是否激活（可选）
         */
        get: operations["list_knowledge_bases_api_v1_tenants__tenant_id__knowledge_bases_get"];
        put?: never;
        /**
         * Create Knowledge Base
         * @description 创建知识库
         *
         *     - **tenant_id**: 租户ID
         *     - **name**: 知识库名称
         *     - **description**: 知识库描述（可选）
         *     - **config**: 知识库配置（可选）
         */
        post: operations["create_knowledge_base_api_v1_tenants__tenant_id__knowledge_bases_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Knowledge Base
         * @description 获取知识库详情
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         */
        get: operations["get_knowledge_base_api_v1_tenants__tenant_id__knowledge_bases__kb_id__get"];
        /**
         * Update Knowledge Base
         * @description 更新知识库
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *     - **name**: 知识库名称（可选）
         *     - **description**: 知识库描述（可选）
         *     - **config**: 知识库配置（可选，包含embedding和retrieval等设置）
         *     - **is_active**: 是否激活（可选）
         *
         *     知识库配置可以包含以下内容：
         *     1. embedding: Embedding模型配置
         *        - model_name: 模型名称
         *        - model_id: 模型ID
         *     2. retrieval: 检索配置
         *        - default_search_type: 默认检索类型 (vector/keyword/hybrid)
         *        - vector_search: 向量检索配置
         *        - keyword_search: 关键词检索配置
         *        - hybrid_search: 混合检索配置
         *     3. chunk_size: 分块大小
         *     4. chunk_overlap: 分块重叠大小
         */
        put: operations["update_knowledge_base_api_v1_tenants__tenant_id__knowledge_bases__kb_id__put"];
        post?: never;
        /**
         * Delete Knowledge Base
         * @description 删除知识库（软删除）
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         */
        delete: operations["delete_knowledge_base_api_v1_tenants__tenant_id__knowledge_bases__kb_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}/usage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Knowledge Base Usage
         * @description 获取知识库使用情况
         *
         *     返回使用该知识库的Agent列表及统计信息
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *
         *     返回信息包括：
         *     - 知识库基本信息
         *     - 使用该知识库的Agent总数
         *     - 激活状态的Agent数量
         *     - 详细的Agent列表（包括版本信息）
         */
        get: operations["get_knowledge_base_usage_api_v1_tenants__tenant_id__knowledge_bases__kb_id__usage_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}/versions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Knowledge Base Versions
         * @description 获取知识库版本历史
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *     - **skip**: 分页起始位置
         *     - **limit**: 分页大小
         */
        get: operations["list_knowledge_base_versions_api_v1_tenants__tenant_id__knowledge_bases__kb_id__versions_get"];
        put?: never;
        /**
         * Create Knowledge Base Version
         * @description 创建知识库版本
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *     - **version_name**: 版本名称
         *     - **description**: 版本描述（可选）
         */
        post: operations["create_knowledge_base_version_api_v1_tenants__tenant_id__knowledge_bases__kb_id__versions_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/files": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Knowledge File
         * @description 创建知识文件（通过已有路径）
         *
         *     - **tenant_id**: 租户ID
         *     - **knowledge_base_id**: 知识库ID
         *     - **file_name**: 文件名称
         *     - **file_path**: 文件路径
         *     - **file_type**: 文件类型（可选）
         *     - **file_size**: 文件大小（可选）
         *     - **meta_data**: 元数据（可选）
         */
        post: operations["create_knowledge_file_api_v1_tenants__tenant_id__knowledge_files_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/upload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Upload Knowledge File
         * @description 上传知识文件到S3并创建记录
         *
         *     - **tenant_id**: 租户ID
         *     - **file**: 要上传的文件（multipart/form-data）
         *     - **knowledge_base_id**: 知识库ID（form字段）
         */
        post: operations["upload_knowledge_file_api_v1_tenants__tenant_id__knowledge_upload_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/files/{file_id}/download": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get File Download Url
         * @description 获取文件下载URL
         *
         *     - **tenant_id**: 租户ID
         *     - **file_id**: 文件ID
         *     - **expires_in**: URL有效期（秒）
         */
        get: operations["get_file_download_url_api_v1_tenants__tenant_id__knowledge_files__file_id__download_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}/files": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Knowledge Files
         * @description 获取知识库文件列表
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *     - **skip**: 分页起始位置
         *     - **limit**: 分页大小
         *     - **status**: 文件状态（可选）
         */
        get: operations["list_knowledge_files_api_v1_tenants__tenant_id__knowledge_bases__kb_id__files_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/files/{file_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Knowledge File
         * @description 获取知识文件详情
         *
         *     - **tenant_id**: 租户ID
         *     - **file_id**: 文件ID
         */
        get: operations["get_knowledge_file_api_v1_tenants__tenant_id__knowledge_files__file_id__get"];
        /**
         * Update Knowledge File
         * @description 更新知识文件
         *
         *     - **tenant_id**: 租户ID
         *     - **file_id**: 文件ID
         *     - **file_name**: 文件名称（可选）
         *     - **meta_data**: 元数据（可选）
         *     - **status**: 文件状态（可选）
         */
        put: operations["update_knowledge_file_api_v1_tenants__tenant_id__knowledge_files__file_id__put"];
        post?: never;
        /**
         * Delete Knowledge File
         * @description 删除知识文件（包括从S3删除）
         *
         *     - **tenant_id**: 租户ID
         *     - **file_id**: 文件ID
         */
        delete: operations["delete_knowledge_file_api_v1_tenants__tenant_id__knowledge_files__file_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/files/{file_id}/chunks": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Knowledge Chunks
         * @description 获取文件的知识分块列表
         *
         *     - **tenant_id**: 租户ID
         *     - **file_id**: 文件ID
         *     - **skip**: 分页起始位置
         *     - **limit**: 分页大小
         */
        get: operations["list_knowledge_chunks_api_v1_tenants__tenant_id__knowledge_files__file_id__chunks_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/{kb_id}/search": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Search Knowledge
         * @description 搜索知识库
         *
         *     - **tenant_id**: 租户ID
         *     - **knowledge_base_id**: 知识库ID
         *     - **query**: 搜索查询
         *     - **session_id**: 会话ID（可选）
         *     - **search_params**: 搜索参数（可选）
         */
        get: operations["search_knowledge_api_v1_tenants__tenant_id__knowledge__kb_id__search_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/bases/{kb_id}/search-history": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Knowledge Search History
         * @description 获取知识库搜索历史
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *     - **session_id**: 会话ID（可选）
         *     - **skip**: 分页起始位置
         *     - **limit**: 分页大小
         */
        get: operations["get_knowledge_search_history_api_v1_tenants__tenant_id__knowledge_bases__kb_id__search_history_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/{kb_id}/retrieve": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Retrieve Knowledge Documents
         * @description 从知识库中检索文档
         *
         *     使用RAG模块的retrieval功能直接检索文档内容。
         *     如果不指定搜索参数，将使用知识库中配置的默认参数。
         *     如果知识库中没有配置检索设置，默认使用关键词检索。
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *     - **query**: 搜索查询
         *     - **top_k**: 返回结果数量，如不指定则使用知识库默认配置
         *     - **session_id**: 会话ID（可选）
         *     - **save_search**: 是否保存搜索记录
         */
        get: operations["retrieve_knowledge_documents_api_v1_tenants__tenant_id__knowledge__kb_id__retrieve_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/{kb_id}/reindex-task": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Reindex Task
         * @description 创建知识库重索引任务
         *
         *     这个操作会创建一个任务队列中的任务，用于重索引知识库中的所有文件。
         *     适用于大型知识库或需要在后台执行的情况。
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *     - **chunk_size**: 文本块大小（字符数）
         *     - **chunk_overlap**: 文本块重叠大小（字符数）
         */
        post: operations["create_reindex_task_api_v1_tenants__tenant_id__knowledge__kb_id__reindex_task_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/{kb_id}/reindex": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Reindex Knowledge Base
         * @description 重新索引知识库中的所有文件
         *
         *     这个操作会删除现有的索引，并对知识库中的所有文件重新进行分块和向量化。
         *     由于操作可能耗时较长，任务会在后台运行。
         *     适用于小型知识库。对于大型知识库，建议使用 /reindex-task 接口。
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *     - **chunk_size**: 文本块大小（字符数）
         *     - **chunk_overlap**: 文本块重叠大小（字符数）
         */
        post: operations["reindex_knowledge_base_api_v1_tenants__tenant_id__knowledge__kb_id__reindex_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/{kb_id}/reindex-status/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Reindex Task Status
         * @description 获取知识库重索引任务状态
         *
         *     用于检查通过 /reindex-task 接口创建的重索引任务的执行状态。
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *     - **task_id**: 任务ID
         */
        get: operations["get_reindex_task_status_api_v1_tenants__tenant_id__knowledge__kb_id__reindex_status__task_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/knowledge/config/{kb_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Knowledge Base Config
         * @description 获取知识库配置
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *
         *     返回知识库的完整配置信息，包括：
         *     1. embedding: Embedding模型配置
         *     2. retrieval: 检索配置
         *     3. chunk_size: 分块大小
         *     4. chunk_overlap: 分块重叠大小
         */
        get: operations["get_knowledge_base_config_api_v1_tenants__tenant_id__knowledge_config__kb_id__get"];
        /**
         * Update Knowledge Base Config
         * @description 更新知识库配置
         *
         *     - **tenant_id**: 租户ID
         *     - **kb_id**: 知识库ID
         *     - **config_update**: 知识库配置更新信息
         *
         *     配置更新可以包含以下内容：
         *     1. embedding: Embedding模型配置（必选）
         *        - model_name: 模型名称
         *        - model_id: 模型ID
         *     2. retrieval: 检索配置（可选，默认使用关键词检索）
         *        - default_search_type: 默认检索类型 (vector/keyword/hybrid)
         *        - vector_search: 向量检索配置
         *          - top_k: 返回结果数量
         *          - score_threshold: 分数阈值
         *          - rerank: Rerank模型配置
         *        - keyword_search: 关键词检索配置
         *          - top_k: 返回结果数量
         *          - score_threshold: 分数阈值
         *          - rerank: Rerank模型配置
         *        - hybrid_search: 混合检索配置
         *          - top_k: 返回结果数量
         *          - score_threshold: 分数阈值
         *          - rerank: Rerank模型配置
         *          - vector_weight: 向量检索权重
         *          - keyword_weight: 关键词检索权重
         *     3. chunk_size: 分块大小
         *     4. chunk_overlap: 分块重叠大小
         */
        put: operations["update_knowledge_base_config_api_v1_tenants__tenant_id__knowledge_config__kb_id__put"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/chat": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Chat With Agent
         * @description 与指定Agent进行对话
         *
         *     - **message**: 用户消息内容
         *     - **thread_id**: 会话线程ID，默认为"default"
         *     - **user_id**: 用户ID（可选）
         *     - **user_name**: 用户名（可选）
         *
         *     返回包含Agent响应的结果以及会话信息。
         */
        post: operations["chat_with_agent_api_v1_tenants__tenant_id__agents__agent_id__chat_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/{agent_id}/chat/stream": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Chat With Agent Stream
         * @description 与指定Agent进行流式对话
         *
         *     返回Server-Sent Events (SSE)格式的流式响应。
         *     每个事件包含增量内容或状态信息。
         */
        post: operations["chat_with_agent_stream_api_v1_tenants__tenant_id__agents__agent_id__chat_stream_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/sessions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Sessions
         * @description 列出指定租户的会话
         *
         *     可通过Agent ID、用户ID、状态等条件进行过滤。
         */
        get: operations["list_sessions_api_v1_tenants__tenant_id__sessions_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/sessions/{session_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Session
         * @description 获取指定会话的详细信息
         */
        get: operations["get_session_api_v1_tenants__tenant_id__sessions__session_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/agents/cache/clear": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Clear Agent Cache
         * @description 清理Agent实例缓存
         *
         *     可以清理指定Agent的缓存，或清理整个租户的所有Agent缓存。
         */
        post: operations["clear_agent_cache_api_v1_tenants__tenant_id__agents_cache_clear_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/execution/stats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Execution Stats
         * @description 获取Agent执行服务的统计信息
         *
         *     包括缓存Agent实例数量、活跃会话数等统计数据。
         */
        get: operations["get_execution_stats_api_v1_execution_stats_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tenants/{tenant_id}/sessions/cleanup": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Cleanup Expired Sessions
         * @description 清理指定租户的过期会话
         */
        post: operations["cleanup_expired_sessions_api_v1_tenants__tenant_id__sessions_cleanup_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/sessions/cleanup/all": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Cleanup All Expired Sessions
         * @description 清理所有租户的过期会话（管理员功能）
         */
        post: operations["cleanup_all_expired_sessions_api_v1_sessions_cleanup_all_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Health Check
         * @description Agent执行服务健康检查
         */
        get: operations["health_check_api_v1_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Health Check */
        get: operations["health_check_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /** AcceptInvitationRequest */
        AcceptInvitationRequest: {
            /**
             * Invitation Token
             * @description 邀请令牌
             */
            invitation_token: string;
        };
        /** AcceptInvitationResponse */
        AcceptInvitationResponse: {
            /**
             * Success
             * @description 是否成功
             */
            success: boolean;
            /**
             * Message
             * @description 响应消息
             */
            message: string;
            /**
             * Tenant Id
             * @description 租户ID
             */
            tenant_id: string;
        };
        /** Agent */
        Agent: {
            /**
             * Name
             * @description Agent名称
             */
            name: string;
            /** @description Agent类型 */
            agent_type?: components["schemas"]["AgentType"] | null;
            /**
             * Description
             * @description Agent描述
             */
            description?: string | null;
            /**
             * Icon Url
             * @description 图标S3存储路径
             */
            icon_url?: string | null;
            /** Id */
            id: string;
            /** Tenant Id */
            tenant_id: string;
            /** Open Id */
            open_id: string;
            /** Is Active */
            is_active: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /**
             * Config
             * @description 当前版本的配置
             */
            config?: {
                [key: string]: unknown;
            } | null;
            /**
             * Model Id
             * @description 当前版本的模型ID
             */
            model_id?: string | null;
            /**
             * Prompt
             * @description 当前版本的提示词
             */
            prompt?: string | null;
            /**
             * Icon Access Url
             * @description 图标访问URL
             */
            icon_access_url?: string | null;
            /**
             * Icon Metadata
             * @description 图标元数据
             */
            icon_metadata?: {
                [key: string]: unknown;
            } | null;
            /**
             * Is Supervisor
             * @description 是否为监督者类型
             */
            readonly is_supervisor: boolean;
            /** @description 从config中提取模型配置 */
            readonly llm_model_config: components["schemas"]["AgentLLMModelConfig"] | null;
            /** @description 从config中提取知识库配置 */
            readonly knowledge_base_config: components["schemas"]["AgentKnowledgeBaseConfig"] | null;
        };
        /**
         * AgentBasicInfoUpdate
         * @description Agent基本信息更新 - 立即生效，无需发布
         */
        AgentBasicInfoUpdate: {
            /**
             * Name
             * @description Agent名称
             */
            name?: string | null;
            /**
             * Description
             * @description Agent描述
             */
            description?: string | null;
            /**
             * Is Active
             * @description 是否激活
             */
            is_active?: boolean | null;
        };
        /**
         * AgentConfigValidation
         * @description Agent配置验证结果
         */
        AgentConfigValidation: {
            /**
             * Is Valid
             * @description 配置是否有效
             */
            is_valid: boolean;
            /**
             * Errors
             * @description 错误列表
             */
            errors?: string[];
            /**
             * Warnings
             * @description 警告列表
             */
            warnings?: string[];
            /**
             * Suggestions
             * @description 建议列表
             */
            suggestions?: string[];
        };
        /**
         * AgentConfigWizardResponse
         * @description 配置向导响应
         */
        AgentConfigWizardResponse: {
            /**
             * Wizard Steps
             * @description 向导步骤
             */
            wizard_steps: {
                [key: string]: unknown;
            }[];
            /**
             * Current Progress
             * @description 当前进度
             */
            current_progress: {
                [key: string]: unknown;
            };
            /**
             * Template Info
             * @description 模板信息
             */
            template_info: {
                [key: string]: unknown;
            };
        };
        /**
         * AgentCreateRequest
         * @description 统一的Agent创建请求
         */
        AgentCreateRequest: {
            /**
             * Name
             * @description Agent名称
             */
            name: string;
            /**
             * Description
             * @description Agent描述
             */
            description?: string | null;
            /**
             * Template Id
             * @description 使用的模板ID，如果指定则为模板创建
             */
            template_id?: string | null;
            /** @description Agent类型 */
            agent_type: components["schemas"]["AgentType"];
            /**
             * Model Id
             * @description LLM模型ID
             */
            model_id: string;
            /**
             * Prompt
             * @description 提示词
             */
            prompt: string;
            /**
             * Knowledge Base Ids
             * @description 知识库ID列表
             */
            knowledge_base_ids?: string[] | null;
            /**
             * Tool Ids
             * @description 工具ID列表
             */
            tool_ids?: string[] | null;
            /**
             * Mcp Server Ids
             * @description MCP服务器ID列表
             */
            mcp_server_ids?: string[] | null;
            /** @description LLM模型配置 */
            llm_model_config?: components["schemas"]["AgentLLMModelConfig"] | null;
            /** @description 知识库配置 */
            knowledge_base_config?: components["schemas"]["AgentKnowledgeBaseConfig"] | null;
        };
        /**
         * AgentCreateResponse
         * @description Agent创建响应
         */
        AgentCreateResponse: {
            /** @description 创建的Agent信息 */
            agent: components["schemas"]["Agent"];
            /** @description 初始草稿配置 */
            draft_config: components["schemas"]["AgentDraftConfig"];
            /**
             * Next Steps
             * @description 下一步操作提示
             */
            next_steps: string[];
        };
        /** AgentDetail */
        AgentDetail: {
            /**
             * Name
             * @description Agent名称
             */
            name: string;
            /** @description Agent类型 */
            agent_type?: components["schemas"]["AgentType"] | null;
            /**
             * Description
             * @description Agent描述
             */
            description?: string | null;
            /**
             * Icon Url
             * @description 图标S3存储路径
             */
            icon_url?: string | null;
            /** Id */
            id: string;
            /** Tenant Id */
            tenant_id: string;
            /** Open Id */
            open_id: string;
            /** Is Active */
            is_active: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /**
             * Config
             * @description 当前版本的配置
             */
            config?: {
                [key: string]: unknown;
            } | null;
            /**
             * Model Id
             * @description 当前版本的模型ID
             */
            model_id?: string | null;
            /**
             * Prompt
             * @description 当前版本的提示词
             */
            prompt?: string | null;
            /**
             * Icon Access Url
             * @description 图标访问URL
             */
            icon_access_url?: string | null;
            /**
             * Icon Metadata
             * @description 图标元数据
             */
            icon_metadata?: {
                [key: string]: unknown;
            } | null;
            /** Versions */
            versions: components["schemas"]["AgentVersion"][];
            /**
             * Is Supervisor
             * @description 是否为监督者类型
             */
            readonly is_supervisor: boolean;
            /** @description 从config中提取模型配置 */
            readonly llm_model_config: components["schemas"]["AgentLLMModelConfig"] | null;
            /** @description 从config中提取知识库配置 */
            readonly knowledge_base_config: components["schemas"]["AgentKnowledgeBaseConfig"] | null;
        };
        /**
         * AgentDraftConfig
         * @description Agent草稿配置
         */
        AgentDraftConfig: {
            /**
             * Model Id
             * @description LLM模型ID
             */
            model_id: string;
            /**
             * Prompt
             * @description 提示词
             */
            prompt: string;
            /**
             * Knowledge Base Ids
             * @description 知识库ID列表
             */
            knowledge_base_ids?: string[] | null;
            /**
             * Tool Ids
             * @description 工具ID列表
             */
            tool_ids?: string[] | null;
            /**
             * Mcp Server Ids
             * @description MCP服务器ID列表
             */
            mcp_server_ids?: string[] | null;
            /** @description LLM模型配置 */
            llm_model_config?: components["schemas"]["AgentLLMModelConfig"] | null;
            /** @description 知识库配置 */
            knowledge_base_config?: components["schemas"]["AgentKnowledgeBaseConfig"] | null;
            /**
             * Has Changes
             * @description 是否有未发布的更改
             * @default false
             */
            has_changes: boolean;
            /**
             * Is Ready To Publish
             * @description 是否准备好发布
             * @default false
             */
            is_ready_to_publish: boolean;
            /**
             * Validation Errors
             * @description 配置验证错误
             */
            validation_errors?: string[];
            /**
             * Last Saved At
             * @description 最后保存时间
             */
            last_saved_at?: string | null;
            /**
             * Last Published At
             * @description 最后发布时间
             */
            last_published_at?: string | null;
        };
        /**
         * AgentDraftUpdateRequest
         * @description Agent草稿更新请求
         */
        AgentDraftUpdateRequest: {
            /**
             * Model Id
             * @description LLM模型ID
             */
            model_id?: string | null;
            /**
             * Prompt
             * @description 提示词
             */
            prompt?: string | null;
            /**
             * Knowledge Base Ids
             * @description 知识库ID列表
             */
            knowledge_base_ids?: string[] | null;
            /**
             * Tool Ids
             * @description 工具ID列表
             */
            tool_ids?: string[] | null;
            /**
             * Mcp Server Ids
             * @description MCP服务器ID列表
             */
            mcp_server_ids?: string[] | null;
            /** @description LLM模型配置 */
            llm_model_config?: components["schemas"]["AgentLLMModelConfig"] | null;
            /** @description 知识库配置 */
            knowledge_base_config?: components["schemas"]["AgentKnowledgeBaseConfig"] | null;
        };
        /**
         * AgentDraftUpdateResponse
         * @description Agent草稿更新响应
         */
        AgentDraftUpdateResponse: {
            /**
             * Success
             * @description 是否更新成功
             */
            success: boolean;
            /**
             * Has Changes
             * @description 是否有未发布的更改
             */
            has_changes: boolean;
            /**
             * Is Ready To Publish
             * @description 是否准备好发布
             */
            is_ready_to_publish: boolean;
            /**
             * Validation Errors
             * @description 配置验证错误
             */
            validation_errors?: string[];
            /**
             * Last Saved At
             * Format: date-time
             * @description 最后保存时间
             */
            last_saved_at: string;
        };
        /**
         * AgentHistoryResponse
         * @description 版本历史响应
         */
        AgentHistoryResponse: {
            /**
             * Versions
             * @description 版本历史列表
             */
            versions: components["schemas"]["AgentVersionHistory"][];
        };
        /**
         * AgentIconDeleteResponse
         * @description 图标删除响应
         */
        AgentIconDeleteResponse: {
            /**
             * Success
             * @description 是否删除成功
             */
            success: boolean;
            /**
             * Message
             * @description 响应消息
             */
            message: string;
        };
        /**
         * AgentIconUploadResponse
         * @description 图标上传响应
         */
        AgentIconUploadResponse: {
            /**
             * Success
             * @description 是否上传成功
             */
            success: boolean;
            /**
             * Message
             * @description 响应消息
             */
            message: string;
            /**
             * Icon Url
             * @description 图标S3存储路径
             */
            icon_url?: string | null;
            /**
             * Icon Access Url
             * @description 图标访问URL
             */
            icon_access_url?: string | null;
            /**
             * Metadata
             * @description 图标元数据
             */
            metadata?: {
                [key: string]: unknown;
            } | null;
        };
        /**
         * AgentKnowledgeBaseConfig
         * @description 知识库配置
         */
        AgentKnowledgeBaseConfig: {
            /**
             * Max Results
             * @description 最大返回结果数
             * @default 5
             */
            max_results: number | null;
            /**
             * Min Score
             * @description 最小得分
             * @default 0.2
             */
            min_score: number | null;
        };
        /**
         * AgentLLMModelConfig
         * @description LLM模型配置
         */
        AgentLLMModelConfig: {
            /**
             * Temperature
             * @description 温度
             * @default 0.5
             */
            temperature: number | null;
            /**
             * Top P
             * @description Top-P
             * @default 0.5
             */
            top_p: number | null;
            /**
             * Max Tokens
             * @description 最大Token数
             * @default 2048
             */
            max_tokens: number | null;
            /**
             * Max Retries
             * @description 最大重试次数
             * @default 3
             */
            max_retries: number | null;
            /**
             * Timeout
             * @description 超时时间
             * @default 10
             */
            timeout: number | null;
        };
        /** AgentListResponse */
        AgentListResponse: {
            /** Total */
            total: number;
            /** Items */
            items: components["schemas"]["Agent"][];
        };
        /**
         * AgentPublishRequest
         * @description 发布Agent配置
         */
        AgentPublishRequest: {
            /**
             * Release Notes
             * @description 发布说明
             */
            release_notes?: string | null;
        };
        /**
         * AgentPublishResponse
         * @description 发布响应
         */
        AgentPublishResponse: {
            /**
             * Success
             * @description 是否发布成功
             */
            success: boolean;
            /** @description 发布的版本信息 */
            version: components["schemas"]["AgentVersionHistory"];
            /** @description Agent信息 */
            agent: components["schemas"]["Agent"];
        };
        /**
         * AgentRollbackRequest
         * @description 回滚请求
         */
        AgentRollbackRequest: {
            /**
             * Reason
             * @description 回滚原因
             */
            reason?: string | null;
        };
        /**
         * AgentRollbackResponse
         * @description 回滚响应
         */
        AgentRollbackResponse: {
            /**
             * Success
             * @description 是否回滚成功
             */
            success: boolean;
            /**
             * Current Version
             * @description 当前版本号
             */
            current_version: string;
            /**
             * Rollback Reason
             * @description 回滚原因
             */
            rollback_reason?: string | null;
            /**
             * New Draft Created
             * @description 是否创建了新草稿
             */
            new_draft_created: boolean;
        };
        /**
         * AgentTemplate
         * @description Agent创建模板
         */
        AgentTemplate: {
            /**
             * Template Id
             * @description 模板ID
             */
            template_id: string;
            /**
             * Name
             * @description 模板名称
             */
            name: string;
            /** @description Agent类型 */
            agent_type: components["schemas"]["AgentType"];
            /**
             * Description
             * @description 模板描述
             */
            description: string;
            /**
             * Icon Url
             * @description 模板图标
             */
            icon_url?: string | null;
            /**
             * Default Config
             * @description 默认配置，包含model_id、prompt、knowledge_base_ids等
             */
            default_config?: {
                [key: string]: unknown;
            };
            /**
             * Suggested Model Ids
             * @description 推荐模型ID列表
             */
            suggested_model_ids?: string[];
            /**
             * Suggested Tool Ids
             * @description 推荐工具ID列表
             */
            suggested_tool_ids?: string[];
            /**
             * Suggested Knowledge Base Ids
             * @description 推荐知识库ID列表
             */
            suggested_knowledge_base_ids?: string[];
            /**
             * Config Wizard
             * @description 配置向导步骤，包含字段类型和选项来源
             */
            config_wizard?: {
                [key: string]: unknown;
            }[];
        };
        /**
         * AgentTemplatesResponse
         * @description 模板列表响应
         */
        AgentTemplatesResponse: {
            /**
             * Templates
             * @description 模板列表
             */
            templates: components["schemas"]["AgentTemplate"][];
            /** @description 可用资源 */
            available_resources: components["schemas"]["AvailableResources"];
        };
        /**
         * AgentType
         * @enum {string}
         */
        AgentType: "supervisor" | "general";
        /** AgentUpdate */
        AgentUpdate: {
            /**
             * Name
             * @description Agent名称
             */
            name: string;
            /** @description Agent类型 */
            agent_type?: components["schemas"]["AgentType"] | null;
            /**
             * Description
             * @description Agent描述
             */
            description?: string | null;
            /**
             * Icon Url
             * @description 图标S3存储路径
             */
            icon_url?: string | null;
            /**
             * Is Active
             * @description 是否激活
             */
            is_active?: boolean | null;
        };
        /** AgentUsageInfo */
        AgentUsageInfo: {
            /**
             * Agent Id
             * @description Agent ID
             */
            agent_id: string;
            /**
             * Agent Name
             * @description Agent名称
             */
            agent_name: string;
            /**
             * Agent Type
             * @description Agent类型
             */
            agent_type?: string | null;
            /**
             * Description
             * @description Agent描述
             */
            description?: string | null;
            /**
             * Is Active
             * @description Agent是否激活
             */
            is_active: boolean;
            /**
             * Version Number
             * @description 使用该知识库的版本号
             */
            version_number: string;
            /**
             * Version Id
             * @description 版本ID
             */
            version_id: string;
            /**
             * Is Current Version
             * @description 是否为当前版本
             */
            is_current_version: boolean;
            /**
             * Created At
             * Format: date-time
             * @description Agent创建时间
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             * @description Agent更新时间
             */
            updated_at: string;
        };
        /** AgentVersion */
        AgentVersion: {
            /**
             * Version Number
             * @description 版本号
             */
            version_number: string;
            /**
             * Description
             * @description 版本描述
             */
            description?: string | null;
            /**
             * Model Id
             * @description LLM模型ID
             */
            model_id?: string | null;
            /**
             * Knowledge Base Ids
             * @description 关联的知识库ID列表
             */
            knowledge_base_ids?: string[] | null;
            /**
             * Tool Ids
             * @description 关联的工具ID列表
             */
            tool_ids?: string[] | null;
            /**
             * Mcp Server Ids
             * @description 关联的MCP服务器ID列表
             */
            mcp_server_ids?: string[] | null;
            /** Id */
            id: string;
            /** Agent Id */
            agent_id: string;
            /** Config */
            config: {
                [key: string]: unknown;
            } | null;
            /** Prompt */
            prompt: string | null;
            /** Is Current */
            is_current: boolean;
            /** Is Published */
            is_published: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** @description 从config中提取模型配置 */
            readonly llm_model_config: components["schemas"]["AgentLLMModelConfig"] | null;
            /** @description 从config中提取知识库配置 */
            readonly knowledge_base_config: components["schemas"]["AgentKnowledgeBaseConfig"] | null;
        };
        /**
         * AgentVersionHistory
         * @description Agent版本历史
         */
        AgentVersionHistory: {
            /**
             * Version Id
             * @description 版本ID
             */
            version_id: string;
            /**
             * Version Number
             * @description 版本号
             */
            version_number: string;
            /**
             * Release Notes
             * @description 发布说明
             */
            release_notes?: string | null;
            /**
             * Published At
             * Format: date-time
             * @description 发布时间
             */
            published_at: string;
            /**
             * Is Current
             * @description 是否为当前运行版本
             */
            is_current: boolean;
            /**
             * Config Summary
             * @description 配置摘要
             */
            config_summary: {
                [key: string]: unknown;
            };
        };
        /**
         * AvailableKnowledgeBase
         * @description 可用知识库信息
         */
        AvailableKnowledgeBase: {
            /**
             * Id
             * @description 知识库ID
             */
            id: string;
            /**
             * Name
             * @description 知识库名称
             */
            name: string;
            /**
             * Description
             * @description 知识库描述
             */
            description?: string | null;
            /**
             * Document Count
             * @description 文档数量
             * @default 0
             */
            document_count: number;
            /**
             * Created At
             * Format: date-time
             * @description 创建时间
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             * @description 更新时间
             */
            updated_at: string;
        };
        /**
         * AvailableMCPServer
         * @description 可用MCP服务器信息
         */
        AvailableMCPServer: {
            /**
             * Id
             * @description MCP服务器ID
             */
            id: string;
            /**
             * Name
             * @description 服务器名称
             */
            name: string;
            /**
             * Endpoint
             * @description 服务器端点
             */
            endpoint: string;
            /**
             * Transport Type
             * @description 传输类型
             */
            transport_type: string;
            /**
             * Status
             * @description 服务器状态
             */
            status: string;
            /**
             * Last Heartbeat
             * @description 最后心跳时间
             */
            last_heartbeat?: string | null;
        };
        /**
         * AvailableModel
         * @description 可用模型信息
         */
        AvailableModel: {
            /**
             * Id
             * @description 模型ID
             */
            id: string;
            /**
             * Name
             * @description 模型名称
             */
            name: string;
            /**
             * Key
             * @description 模型标识符
             */
            key: string;
            /**
             * Description
             * @description 模型描述
             */
            description?: string | null;
            /**
             * Model Type
             * @description 模型类型
             */
            model_type: string;
        };
        /**
         * AvailableProviderItem
         * @description 可用提供商项目
         */
        AvailableProviderItem: {
            /** Id */
            id: string;
            /** Name */
            name: string;
            /** Key */
            key: string;
            /** Provider Type */
            provider_type: string;
            /** Auth Type */
            auth_type: string;
            /** Description */
            description?: string | null;
            /** Website */
            website?: string | null;
            /** Icon */
            icon?: string | null;
            /** Api Base */
            api_base?: string | null;
            /** Config Schema */
            config_schema?: {
                [key: string]: unknown;
            } | null;
        };
        /**
         * AvailableProvidersResponse
         * @description 获取可用提供商列表响应
         */
        AvailableProvidersResponse: {
            /**
             * Total
             * @default 0
             */
            total: number;
            /** Items */
            items: components["schemas"]["AvailableProviderItem"][];
        };
        /**
         * AvailableResources
         * @description 可用资源集合
         */
        AvailableResources: {
            /**
             * Models
             * @description 可用模型列表
             */
            models?: components["schemas"]["AvailableModel"][];
            /**
             * Tools
             * @description 可用工具列表
             */
            tools?: components["schemas"]["AvailableTool"][];
            /**
             * Knowledge Bases
             * @description 可用知识库列表
             */
            knowledge_bases?: components["schemas"]["AvailableKnowledgeBase"][];
            /**
             * Mcp Servers
             * @description 可用MCP服务器列表
             */
            mcp_servers?: components["schemas"]["AvailableMCPServer"][];
        };
        /**
         * AvailableTool
         * @description 可用工具信息
         */
        AvailableTool: {
            /**
             * Id
             * @description 工具ID
             */
            id: string;
            /**
             * Tenant Tool Id
             * @description 租户工具ID
             */
            tenant_tool_id: string;
            /**
             * Name
             * @description 工具名称
             */
            name: string;
            /**
             * Key
             * @description 工具标识符
             */
            key: string;
            /**
             * Description
             * @description 工具描述
             */
            description?: string | null;
            /**
             * Tool Type
             * @description 工具类型
             */
            tool_type: string;
            /**
             * Config Schema
             * @description 配置Schema
             */
            config_schema?: {
                [key: string]: unknown;
            } | null;
            /**
             * Tenant Config
             * @description 租户配置
             */
            tenant_config?: {
                [key: string]: unknown;
            } | null;
        };
        /** Body_login_api_v1_auth_login_post */
        Body_login_api_v1_auth_login_post: {
            /** Grant Type */
            grant_type?: string | null;
            /** Username */
            username: string;
            /** Password */
            password: string;
            /**
             * Scope
             * @default
             */
            scope: string;
            /** Client Id */
            client_id?: string | null;
            /** Client Secret */
            client_secret?: string | null;
        };
        /** Body_upload_agent_icon_api_v1_tenants__tenant_id__agents__agent_id__icon_post */
        Body_upload_agent_icon_api_v1_tenants__tenant_id__agents__agent_id__icon_post: {
            /**
             * Icon
             * Format: binary
             * @description 图标文件
             */
            icon: string;
        };
        /** Body_upload_knowledge_file_api_v1_tenants__tenant_id__knowledge_upload_post */
        Body_upload_knowledge_file_api_v1_tenants__tenant_id__knowledge_upload_post: {
            /**
             * File
             * Format: binary
             */
            file: string;
            /** Knowledge Base Id */
            knowledge_base_id: string;
        };
        /**
         * CacheClearRequest
         * @description 缓存清理请求模型
         */
        CacheClearRequest: {
            /** Agent Id */
            agent_id?: string | null;
        };
        /**
         * ChatRequest
         * @description 对话请求模型
         */
        ChatRequest: {
            /** Message */
            message: string;
            /**
             * Thread Id
             * @default default
             */
            thread_id: string;
            /** User Id */
            user_id?: string | null;
            /** User Name */
            user_name?: string | null;
        };
        /**
         * ChatResponse
         * @description 对话响应模型
         */
        ChatResponse: {
            /** Success */
            success: boolean;
            /** Response */
            response?: string | null;
            /** Error */
            error?: string | null;
            /** Session Id */
            session_id: string;
            /** Tenant Id */
            tenant_id: string;
            /** Agent Id */
            agent_id: string;
            /** Thread Id */
            thread_id: string;
            /** User Id */
            user_id?: string | null;
        };
        /**
         * ChatStreamRequest
         * @description 流式对话请求模型
         */
        ChatStreamRequest: {
            /** Message */
            message: string;
            /**
             * Thread Id
             * @default default
             */
            thread_id: string;
            /** User Id */
            user_id?: string | null;
            /** User Name */
            user_name?: string | null;
        };
        /**
         * DisableProviderResponse
         * @description 禁用提供商响应
         */
        DisableProviderResponse: {
            /** Success */
            success: boolean;
            /** Message */
            message: string;
            /** Disabled Models */
            disabled_models: number;
        };
        /**
         * EditProviderConfigRequest
         * @description 编辑提供商配置请求
         */
        EditProviderConfigRequest: {
            /**
             * Name
             * @description 配置名称
             */
            name?: string | null;
            /**
             * Credentials
             * @description 凭证信息
             */
            credentials?: {
                [key: string]: unknown;
            } | null;
            /**
             * Is Active
             * @description 是否激活
             */
            is_active?: boolean | null;
        };
        /**
         * EditProviderConfigResponse
         * @description 编辑提供商配置响应
         */
        EditProviderConfigResponse: {
            /** Success */
            success: boolean;
            /** Message */
            message: string;
            /** Credential Id */
            credential_id: string;
            /** Credential Name */
            credential_name: string;
            provider: components["schemas"]["ProviderBasicInfo"];
            /**
             * Updated Fields
             * @description 更新的字段列表
             */
            updated_fields?: string[];
            /**
             * Validation Performed
             * @description 是否执行了配置验证
             * @default false
             */
            validation_performed: boolean;
            /**
             * Models Resynced
             * @description 重新同步的模型数量
             */
            models_resynced?: number | null;
        };
        /**
         * EmbeddingConfig
         * @description Embedding模型配置
         */
        EmbeddingConfig: {
            /**
             * Model Name
             * @description Embedding模型名称
             */
            model_name: string;
            /**
             * Model Id
             * @description Embedding模型ID
             */
            model_id: string;
        };
        /**
         * EnableProviderResponse
         * @description 启用提供商响应
         */
        EnableProviderResponse: {
            /** Success */
            success: boolean;
            /** Message */
            message: string;
            /** Credential Id */
            credential_id: string;
            /** Credential Name */
            credential_name: string;
            provider: components["schemas"]["ProviderBasicInfo"];
            /** Models Discovered */
            models_discovered: number;
        };
        /**
         * EnabledProviderItem
         * @description 已启用提供商项目
         */
        EnabledProviderItem: {
            /** Credential Id */
            credential_id: string;
            /** Credential Name */
            credential_name: string;
            provider: components["schemas"]["ProviderBasicInfo"];
            /** Model Count */
            model_count: number;
            /** Credentials */
            credentials: {
                [key: string]: unknown;
            };
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Updated At */
            updated_at?: string | null;
        };
        /**
         * EnabledProvidersResponse
         * @description 获取已启用提供商列表响应
         */
        EnabledProvidersResponse: {
            /**
             * Total
             * @default 0
             */
            total: number;
            /** Items */
            items: components["schemas"]["EnabledProviderItem"][];
        };
        /**
         * GenericProviderConfigRequest
         * @description 通用的提供商配置请求（支持动态类型）
         */
        GenericProviderConfigRequest: {
            /**
             * Name
             * @description 配置名称
             */
            name: string;
            /**
             * Credentials
             * @description 凭证信息
             */
            credentials: {
                [key: string]: unknown;
            };
        };
        /**
         * GenericProviderTestRequest
         * @description 通用的提供商测试请求（支持动态类型）
         */
        GenericProviderTestRequest: {
            /**
             * Provider Id
             * @description 提供商ID
             */
            provider_id: string;
            /**
             * Credentials
             * @description 凭证信息
             */
            credentials: {
                [key: string]: unknown;
            };
        };
        /** HTTPValidationError */
        HTTPValidationError: {
            /** Detail */
            detail?: components["schemas"]["ValidationError"][];
        };
        /**
         * HybridSearchConfig
         * @description 混合检索配置
         */
        HybridSearchConfig: {
            /**
             * Top K
             * @description 返回结果数量
             * @default 5
             */
            top_k: number;
            /**
             * Score Threshold
             * @description 分数阈值
             * @default 0.5
             */
            score_threshold: number;
            /** @description Rerank模型配置 */
            rerank?: components["schemas"]["RerankModelConfig"] | null;
            /**
             * Vector Weight
             * @description 向量检索权重
             * @default 0.7
             */
            vector_weight: number;
            /**
             * Keyword Weight
             * @description 关键词检索权重
             * @default 0.3
             */
            keyword_weight: number;
        };
        /** InvitationDetailResponse */
        InvitationDetailResponse: {
            /** @description 邀请详情 */
            invitation: components["schemas"]["InvitationInfo"];
            /**
             * Tenant Name
             * @description 租户名称
             */
            tenant_name: string;
            /**
             * Tenant Description
             * @description 租户描述
             */
            tenant_description?: string | null;
            /**
             * Inviter Name
             * @description 邀请人姓名
             */
            inviter_name: string;
            /**
             * Is Expired
             * @description 是否已过期
             */
            is_expired: boolean;
        };
        /** InvitationInfo */
        InvitationInfo: {
            /**
             * Id
             * @description 邀请ID
             */
            id: string;
            /**
             * Token
             * @description 邀请令牌
             */
            token: string;
            /**
             * Email
             * Format: email
             * @description 被邀请者邮箱
             */
            email: string;
            /**
             * Role Key
             * @description 角色键值
             */
            role_key: string;
            /**
             * Expires At
             * Format: date-time
             * @description 过期时间
             */
            expires_at: string;
            /**
             * Invitation Link
             * @description 邀请链接
             */
            invitation_link: string;
            /** @description 租户信息 */
            tenant_info?: components["schemas"]["TenantInfo"] | null;
            /**
             * Inviter Name
             * @description 邀请人姓名
             */
            inviter_name?: string | null;
            /**
             * Status
             * @description 邀请状态
             */
            status: string;
        };
        /** InvitationResponse */
        InvitationResponse: {
            /**
             * Message
             * @description 响应消息
             */
            message: string;
            /** @description 邀请信息 */
            invitation: components["schemas"]["InvitationInfo"];
        };
        /** InviteUserRequest */
        InviteUserRequest: {
            /**
             * Email
             * Format: email
             * @description 被邀请者邮箱
             */
            email: string;
            /**
             * Role Key
             * @description 角色键值
             */
            role_key: string;
        };
        /**
         * KeywordSearchConfig
         * @description 关键词检索配置
         */
        KeywordSearchConfig: {
            /**
             * Top K
             * @description 返回结果数量
             * @default 5
             */
            top_k: number;
            /**
             * Score Threshold
             * @description 分数阈值
             * @default 0.5
             */
            score_threshold: number;
            /** @description Rerank模型配置 */
            rerank?: components["schemas"]["RerankModelConfig"] | null;
        };
        /**
         * KnowledgeBaseConfigUpdate
         * @description 知识库配置更新请求
         */
        KnowledgeBaseConfigUpdate: {
            /** @description Embedding模型配置 */
            embedding?: components["schemas"]["EmbeddingConfig"] | null;
            /** @description 检索配置 */
            retrieval?: components["schemas"]["RetrievalConfig"] | null;
            /**
             * Chunk Size
             * @description 分块大小
             */
            chunk_size?: number | null;
            /**
             * Chunk Overlap
             * @description 分块重叠大小
             */
            chunk_overlap?: number | null;
        };
        /** KnowledgeBaseCreate */
        KnowledgeBaseCreate: {
            /**
             * Tenant Id
             * @description 租户ID
             */
            tenant_id: string;
            /**
             * Name
             * @description 知识库名称
             */
            name: string;
            /**
             * Description
             * @description 知识库描述
             */
            description?: string | null;
            /** @description 知识库配置，包含embedding和retrieval等设置 */
            config?: components["schemas"]["KnowledgeBaseFullConfig"] | null;
        };
        /**
         * KnowledgeBaseFullConfig
         * @description 知识库完整配置
         */
        KnowledgeBaseFullConfig: {
            embedding: components["schemas"]["EmbeddingConfig"];
            /** @description 检索配置 */
            retrieval?: components["schemas"]["RetrievalConfig"] | null;
            /**
             * Chunk Size
             * @description 分块大小
             * @default 1000
             */
            chunk_size: number;
            /**
             * Chunk Overlap
             * @description 分块重叠大小
             * @default 200
             */
            chunk_overlap: number;
        };
        /** KnowledgeBaseListResponse */
        KnowledgeBaseListResponse: {
            /** Items */
            items: components["schemas"]["KnowledgeBaseResponse"][];
            /**
             * Total
             * @description 总数
             */
            total: number;
            /**
             * Page
             * @description 当前页码
             */
            page: number;
            /**
             * Size
             * @description 每页大小
             */
            size: number;
        };
        /** KnowledgeBaseResponse */
        KnowledgeBaseResponse: {
            /**
             * Name
             * @description 知识库名称
             */
            name: string;
            /**
             * Description
             * @description 知识库描述
             */
            description?: string | null;
            /**
             * Config
             * @description 知识库配置
             */
            config?: {
                [key: string]: unknown;
            } | null;
            /**
             * Id
             * @description 知识库ID
             */
            id: string;
            /**
             * Tenant Id
             * @description 租户ID
             */
            tenant_id: string;
            /**
             * Is Active
             * @description 是否激活
             */
            is_active: boolean;
            /**
             * Created At
             * Format: date-time
             * @description 创建时间
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             * @description 更新时间
             */
            updated_at: string;
        };
        /** KnowledgeBaseUpdate */
        KnowledgeBaseUpdate: {
            /**
             * Name
             * @description 知识库名称
             */
            name?: string | null;
            /**
             * Description
             * @description 知识库描述
             */
            description?: string | null;
            /**
             * Is Active
             * @description 是否激活
             */
            is_active?: boolean | null;
            /** @description 知识库配置，包含embedding和retrieval等设置 */
            config?: components["schemas"]["KnowledgeBaseFullConfig"] | null;
        };
        /** KnowledgeBaseUsageResponse */
        KnowledgeBaseUsageResponse: {
            /**
             * Knowledge Base Id
             * @description 知识库ID
             */
            knowledge_base_id: string;
            /**
             * Knowledge Base Name
             * @description 知识库名称
             */
            knowledge_base_name: string;
            /**
             * Total Agents
             * @description 使用该知识库的Agent总数
             */
            total_agents: number;
            /**
             * Active Agents
             * @description 激活状态的Agent数量
             */
            active_agents: number;
            /**
             * Agents
             * @description 使用该知识库的Agent列表
             */
            agents: components["schemas"]["AgentUsageInfo"][];
        };
        /** KnowledgeBaseVersionCreate */
        KnowledgeBaseVersionCreate: {
            /**
             * Knowledge Base Id
             * @description 知识库ID
             */
            knowledge_base_id: string;
            /**
             * Version Name
             * @description 版本名称
             */
            version_name: string;
            /**
             * Description
             * @description 版本描述
             */
            description?: string | null;
        };
        /** KnowledgeBaseVersionListResponse */
        KnowledgeBaseVersionListResponse: {
            /** Items */
            items: components["schemas"]["KnowledgeBaseVersionResponse"][];
            /**
             * Total
             * @description 总数
             */
            total: number;
            /**
             * Page
             * @description 当前页码
             */
            page: number;
            /**
             * Size
             * @description 每页大小
             */
            size: number;
        };
        /** KnowledgeBaseVersionResponse */
        KnowledgeBaseVersionResponse: {
            /**
             * Id
             * @description 版本ID
             */
            id: string;
            /**
             * Knowledge Base Id
             * @description 知识库ID
             */
            knowledge_base_id: string;
            /**
             * Version Name
             * @description 版本名称
             */
            version_name: string;
            /**
             * Description
             * @description 版本描述
             */
            description?: string | null;
            /**
             * Created At
             * Format: date-time
             * @description 创建时间
             */
            created_at: string;
        };
        /** KnowledgeChunkResponse */
        KnowledgeChunkResponse: {
            /**
             * Content
             * @description 分块内容
             */
            content: string;
            /**
             * Meta Data
             * @description 元数据
             */
            meta_data?: {
                [key: string]: unknown;
            } | null;
            /**
             * Embedding
             * @description 向量嵌入
             */
            embedding?: number[] | null;
            /**
             * Id
             * @description 分块ID
             */
            id: string;
            /**
             * File Id
             * @description 文件ID
             */
            file_id: string;
            /**
             * Created At
             * Format: date-time
             * @description 创建时间
             */
            created_at: string;
        };
        /** KnowledgeFileCreate */
        KnowledgeFileCreate: {
            /**
             * File Name
             * @description 文件名称
             */
            file_name: string;
            /**
             * File Type
             * @description 文件类型
             */
            file_type?: string | null;
            /**
             * File Size
             * @description 文件大小(字节)
             */
            file_size?: number | null;
            /**
             * Meta Data
             * @description 元数据
             */
            meta_data?: {
                [key: string]: unknown;
            } | null;
            /**
             * Knowledge Base Id
             * @description 知识库ID
             */
            knowledge_base_id: string;
            /**
             * File Path
             * @description 文件路径
             */
            file_path: string;
        };
        /** KnowledgeFileListResponse */
        KnowledgeFileListResponse: {
            /** Items */
            items: components["schemas"]["KnowledgeFileResponse"][];
            /**
             * Total
             * @description 总数
             */
            total: number;
            /**
             * Page
             * @description 当前页码
             */
            page: number;
            /**
             * Size
             * @description 每页大小
             */
            size: number;
        };
        /** KnowledgeFileResponse */
        KnowledgeFileResponse: {
            /**
             * File Name
             * @description 文件名称
             */
            file_name: string;
            /**
             * File Type
             * @description 文件类型
             */
            file_type?: string | null;
            /**
             * File Size
             * @description 文件大小(字节)
             */
            file_size?: number | null;
            /**
             * Meta Data
             * @description 元数据
             */
            meta_data?: {
                [key: string]: unknown;
            } | null;
            /**
             * Id
             * @description 文件ID
             */
            id: string;
            /**
             * Knowledge Base Id
             * @description 知识库ID
             */
            knowledge_base_id: string;
            /**
             * File Path
             * @description 文件路径
             */
            file_path: string;
            /**
             * Status
             * @description 文件状态
             */
            status: string;
            /**
             * Chunk Count
             * @description 分块数量
             */
            chunk_count: number;
            /**
             * Created At
             * Format: date-time
             * @description 创建时间
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             * @description 更新时间
             */
            updated_at: string;
        };
        /** KnowledgeFileUpdate */
        KnowledgeFileUpdate: {
            /**
             * File Name
             * @description 文件名称
             */
            file_name?: string | null;
            /**
             * Meta Data
             * @description 元数据
             */
            meta_data?: {
                [key: string]: unknown;
            } | null;
            /**
             * Status
             * @description 文件状态
             */
            status?: string | null;
        };
        /** KnowledgeFileUrlResponse */
        KnowledgeFileUrlResponse: {
            /**
             * Id
             * @description 文件ID
             */
            id: string;
            /**
             * File Name
             * @description 文件名称
             */
            file_name: string;
            /**
             * Download Url
             * @description 文件下载URL
             */
            download_url: string;
            /**
             * Expires At
             * Format: date-time
             * @description URL过期时间
             */
            expires_at: string;
        };
        /** KnowledgeSearchResponse */
        KnowledgeSearchResponse: {
            /**
             * Id
             * @description 搜索ID
             */
            id: string;
            /**
             * Knowledge Base Id
             * @description 知识库ID
             */
            knowledge_base_id: string;
            /**
             * Query
             * @description 搜索查询
             */
            query: string;
            /**
             * Session Id
             * @description 会话ID
             */
            session_id?: string | null;
            /**
             * Results
             * @description 搜索结果
             */
            results?: {
                [key: string]: unknown;
            }[] | null;
            /**
             * Created At
             * Format: date-time
             * @description 创建时间
             */
            created_at: string;
        };
        /** MCPServer */
        MCPServer: {
            /**
             * Name
             * @description 服务器名称
             */
            name: string;
            /**
             * Endpoint
             * @description 服务器地址
             */
            endpoint: string;
            /**
             * Transport Type
             * @description 传输类型
             */
            transport_type: string;
            /**
             * Status
             * @description 状态
             * @default active
             */
            status: string | null;
            /**
             * Meta Data
             * @description 元数据
             */
            meta_data?: {
                [key: string]: unknown;
            } | null;
            /** Id */
            id: string;
            /** Last Heartbeat */
            last_heartbeat: string | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /** MCPServerCreate */
        MCPServerCreate: {
            /**
             * Name
             * @description 服务器名称
             */
            name: string;
            /**
             * Endpoint
             * @description 服务器地址
             */
            endpoint: string;
            /**
             * Transport Type
             * @description 传输类型
             */
            transport_type: string;
            /**
             * Status
             * @description 状态
             * @default active
             */
            status: string | null;
            /**
             * Meta Data
             * @description 元数据
             */
            meta_data?: {
                [key: string]: unknown;
            } | null;
        };
        /** MCPServerListResponse */
        MCPServerListResponse: {
            /** Total */
            total: number;
            /** Items */
            items: components["schemas"]["MCPServer"][];
        };
        /** MCPServerUpdate */
        MCPServerUpdate: {
            /**
             * Name
             * @description 服务器名称
             */
            name?: string | null;
            /**
             * Endpoint
             * @description 服务器地址
             */
            endpoint?: string | null;
            /**
             * Transport Type
             * @description 传输类型
             */
            transport_type?: string | null;
            /**
             * Status
             * @description 状态
             */
            status?: string | null;
            /**
             * Meta Data
             * @description 元数据
             */
            meta_data?: {
                [key: string]: unknown;
            } | null;
        };
        /** Model */
        Model: {
            /** Id */
            id: string;
            /** Tenant Id */
            tenant_id: string;
            /** Provider Id */
            provider_id: string;
            /** Provider Access Credential Id */
            provider_access_credential_id: string;
            /** Name */
            name: string;
            /** Key */
            key: string;
            /** Model Type */
            model_type: string;
            /** Description */
            description?: string | null;
            /**
             * Is Active
             * @default true
             */
            is_active: boolean;
            /** Extra Config */
            extra_config?: {
                [key: string]: unknown;
            } | null;
        };
        /** ModelCreate */
        ModelCreate: {
            /**
             * Name
             * @description 模型名称
             */
            name: string;
            /**
             * Key
             * @description 模型标识
             */
            key: string;
            /**
             * Model Type
             * @description 模型类型
             */
            model_type: string;
            /**
             * Provider Access Credential Id
             * @description 提供商访问凭证ID
             */
            provider_access_credential_id: string;
            /**
             * Extra Config
             * @description 模型额外配置
             */
            extra_config?: {
                [key: string]: unknown;
            } | null;
        };
        /**
         * ModelInfo
         * @description 模型信息
         */
        ModelInfo: {
            /** Id */
            id: string;
            /** Key */
            key: string;
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** Model Type */
            model_type?: string | null;
        };
        /** ModelListResponse */
        ModelListResponse: {
            /** Total */
            total: number;
            /** Items */
            items: components["schemas"]["Model"][];
        };
        /** ModelType */
        ModelType: {
            /**
             * Key
             * @description 模型类型标识
             */
            key: string;
            /**
             * Name
             * @description 模型类型名称
             */
            name: string;
            /**
             * Description
             * @description 模型类型描述
             */
            description?: string | null;
        };
        /** ModelTypeListResponse */
        ModelTypeListResponse: {
            /** Items */
            items: components["schemas"]["ModelType"][];
        };
        /** ModelUpdate */
        ModelUpdate: {
            /**
             * Name
             * @description 模型名称
             */
            name?: string | null;
            /**
             * Key
             * @description 模型标识
             */
            key?: string | null;
            /**
             * Model Type
             * @description 模型类型
             */
            model_type?: string | null;
            /**
             * Provider Access Credential Id
             * @description 提供商访问凭证ID
             */
            provider_access_credential_id?: string | null;
            /**
             * Extra Config
             * @description 模型额外配置
             */
            extra_config?: {
                [key: string]: unknown;
            } | null;
            /**
             * Is Active
             * @description 是否激活
             */
            is_active?: boolean | null;
        };
        /** OperationResponse */
        OperationResponse: {
            /** Success */
            success: boolean;
            /** Message */
            message: string;
        };
        /** PasswordReset */
        PasswordReset: {
            /**
             * Current Password
             * @description 当前密码
             */
            current_password: string;
            /**
             * New Password
             * @description 新密码
             */
            new_password: string;
        };
        /** PersonalTenantInfo */
        PersonalTenantInfo: {
            /**
             * Id
             * @description 租户ID
             */
            id: string;
            /**
             * Name
             * @description 租户名称
             */
            name: string;
        };
        /**
         * ProviderBasicInfo
         * @description 提供商基本信息
         */
        ProviderBasicInfo: {
            /** Id */
            id: string;
            /** Name */
            name: string;
            /** Key */
            key: string;
            /** Provider Type */
            provider_type: string;
            /** Auth Type */
            auth_type: string;
            /** Description */
            description?: string | null;
            /** Website */
            website?: string | null;
            /** Icon */
            icon?: string | null;
            /** Api Base */
            api_base?: string | null;
            /** Config Schema */
            config_schema?: {
                [key: string]: unknown;
            } | null;
        };
        /**
         * ProviderModelsResponse
         * @description 提供商模型列表响应
         */
        ProviderModelsResponse: {
            /** Models */
            models: components["schemas"]["ModelInfo"][];
            /**
             * Total
             * @default 0
             */
            total: number;
        };
        /**
         * ProviderType
         * @description 提供商类型
         * @enum {string}
         */
        ProviderType: "BUILTIN" | "CUSTOM_OPENAI";
        /**
         * RerankModelConfig
         * @description Rerank模型配置
         */
        RerankModelConfig: {
            /**
             * Enabled
             * @description 是否启用Rerank模型
             * @default false
             */
            enabled: boolean;
            /**
             * Model Name
             * @description Rerank模型名称
             */
            model_name?: string | null;
            /**
             * Model Id
             * @description Rerank模型ID
             */
            model_id?: string | null;
        };
        /**
         * RetrievalConfig
         * @description 检索配置
         */
        RetrievalConfig: {
            /**
             * Default Search Type
             * @description 默认检索类型：向量检索、关键词检索、混合检索
             * @default keyword
             * @enum {string}
             */
            default_search_type: "vector" | "keyword" | "hybrid";
            /** @description 向量检索配置 */
            vector_search?: components["schemas"]["VectorSearchConfig"] | null;
            /** @description 关键词检索配置 */
            keyword_search?: components["schemas"]["KeywordSearchConfig"] | null;
            /** @description 混合检索配置 */
            hybrid_search?: components["schemas"]["HybridSearchConfig"] | null;
        };
        /** Role */
        Role: {
            /**
             * Key
             * @description 角色标识
             */
            key: string;
            /**
             * Name
             * @description 角色名称
             */
            name: string;
            /**
             * Description
             * @description 角色描述
             */
            description?: string | null;
        };
        /** RoleListResponse */
        RoleListResponse: {
            /** Total */
            total: number;
            /** Items */
            items: components["schemas"]["Role"][];
        };
        /** RoleWithPermissions */
        RoleWithPermissions: {
            /**
             * Key
             * @description 角色标识
             */
            key: string;
            /**
             * Name
             * @description 角色名称
             */
            name: string;
            /**
             * Description
             * @description 角色描述
             */
            description?: string | null;
            /**
             * Permissions
             * @description 权限列表
             * @default {}
             */
            permissions: {
                [key: string]: boolean;
            };
        };
        /** SessionInfo */
        SessionInfo: {
            /** Session Id */
            session_id: string;
            /** Tenant Id */
            tenant_id: string;
            /** Agent Id */
            agent_id: string;
            /** Thread Id */
            thread_id: string;
            /** User Id */
            user_id?: string | null;
            /** User Name */
            user_name?: string | null;
            /** @default active */
            status: components["schemas"]["SessionStatus"];
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Last Active
             * Format: date-time
             */
            last_active?: string;
            /**
             * Message Count
             * @default 0
             */
            message_count: number;
            /** Metadata */
            metadata?: {
                [key: string]: unknown;
            };
        };
        /**
         * SessionStatus
         * @description 会话状态枚举
         * @enum {string}
         */
        SessionStatus: "active" | "idle" | "expired" | "terminated";
        /** Tenant */
        Tenant: {
            /**
             * Name
             * @description 租户名称
             */
            name: string;
            /**
             * Description
             * @description 租户描述
             */
            description?: string | null;
            /** Id */
            id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /** TenantBase */
        TenantBase: {
            /**
             * Name
             * @description 租户名称
             */
            name: string;
            /**
             * Description
             * @description 租户描述
             */
            description?: string | null;
        };
        /** TenantCreate */
        TenantCreate: {
            /**
             * Name
             * @description 组织名称
             */
            name: string;
            /**
             * Description
             * @description 组织描述
             */
            description?: string | null;
        };
        /** TenantCreateInfo */
        TenantCreateInfo: {
            /**
             * Id
             * @description 租户ID
             */
            id: string;
            /**
             * Name
             * @description 租户名称
             */
            name: string;
            /**
             * Description
             * @description 租户描述
             */
            description?: string | null;
        };
        /** TenantCreateResponse */
        TenantCreateResponse: {
            /**
             * Message
             * @description 响应消息
             */
            message: string;
            /** @description 租户信息 */
            tenant: components["schemas"]["TenantCreateInfo"];
        };
        /** TenantInfo */
        TenantInfo: {
            /**
             * Id
             * @description 租户ID
             */
            id: string;
            /**
             * Name
             * @description 租户名称
             */
            name: string;
        };
        /** TenantListResponse */
        TenantListResponse: {
            /** Total */
            total: number;
            /** Items */
            items: components["schemas"]["Tenant"][];
        };
        /** TenantTool */
        TenantTool: {
            /**
             * Tenant Id
             * @description 租户ID
             */
            tenant_id: string;
            /**
             * Tool Id
             * @description 工具ID
             */
            tool_id: string;
            /**
             * Config
             * @description 配置信息
             */
            config?: {
                [key: string]: unknown;
            } | null;
            /**
             * Is Active
             * @description 是否激活
             * @default true
             */
            is_active: boolean | null;
            /** Id */
            id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            tool?: components["schemas"]["Tool"] | null;
        };
        /** TenantToolCreate */
        TenantToolCreate: {
            /**
             * Tenant Id
             * @description 租户ID
             */
            tenant_id: string;
            /**
             * Tool Id
             * @description 工具ID
             */
            tool_id: string;
            /**
             * Config
             * @description 配置信息
             */
            config?: {
                [key: string]: unknown;
            } | null;
            /**
             * Is Active
             * @description 是否激活
             * @default true
             */
            is_active: boolean | null;
        };
        /** TenantToolListResponse */
        TenantToolListResponse: {
            /** Total */
            total: number;
            /** Items */
            items: components["schemas"]["TenantTool"][];
        };
        /** TenantToolUpdate */
        TenantToolUpdate: {
            /**
             * Config
             * @description 配置信息
             */
            config?: {
                [key: string]: unknown;
            } | null;
            /**
             * Is Active
             * @description 是否激活
             */
            is_active?: boolean | null;
        };
        /** TenantUpdate */
        TenantUpdate: {
            /**
             * Name
             * @description 租户名称
             */
            name?: string | null;
            /**
             * Description
             * @description 租户描述
             */
            description?: string | null;
        };
        /** TenantWithStats */
        TenantWithStats: {
            /**
             * Name
             * @description 租户名称
             */
            name: string;
            /**
             * Description
             * @description 租户描述
             */
            description?: string | null;
            /** Id */
            id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /**
             * User Count
             * @description 用户数量
             * @default 0
             */
            user_count: number;
            /**
             * Agent Count
             * @description Agent数量
             * @default 0
             */
            agent_count: number;
        };
        /**
         * TestProviderConfigResponse
         * @description 测试提供商配置响应
         */
        TestProviderConfigResponse: {
            /** Success */
            success: boolean;
            /** Message */
            message: string;
            provider?: components["schemas"]["ProviderBasicInfo"] | null;
            /** Models Available */
            models_available?: number | null;
            /** Models */
            models?: {
                [key: string]: unknown;
            }[] | null;
            /** Error Details */
            error_details?: string | null;
        };
        /** Token */
        Token: {
            /** Access Token */
            access_token: string;
            /**
             * Token Type
             * @default bearer
             */
            token_type: string;
            /**
             * Expires At
             * Format: date-time
             */
            expires_at: string;
            /** User Id */
            user_id: string;
            /** Username */
            username: string;
        };
        /** Tool */
        Tool: {
            /**
             * Name
             * @description 工具名称
             */
            name: string;
            /**
             * Key
             * @description 工具标识
             */
            key: string;
            /**
             * Description
             * @description 工具描述
             */
            description?: string | null;
            /**
             * Config Schema
             * @description 配置架构
             */
            config_schema?: {
                [key: string]: unknown;
            } | null;
            /**
             * Param Schema
             * @description 参数架构
             */
            param_schema?: {
                [key: string]: unknown;
            } | null;
            /**
             * Tag
             * @description 工具分类
             */
            tag?: string | null;
            /** Is Active */
            is_active: boolean;
            /** Id */
            id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /** ToolDetail */
        ToolDetail: {
            /**
             * Name
             * @description 工具名称
             */
            name: string;
            /**
             * Key
             * @description 工具标识
             */
            key: string;
            /**
             * Description
             * @description 工具描述
             */
            description?: string | null;
            /**
             * Config Schema
             * @description 配置架构
             */
            config_schema?: {
                [key: string]: unknown;
            } | null;
            /**
             * Param Schema
             * @description 参数架构
             */
            param_schema?: {
                [key: string]: unknown;
            } | null;
            /**
             * Tag
             * @description 工具分类
             */
            tag?: string | null;
            /** Is Active */
            is_active: boolean;
            /** Id */
            id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /** ToolListResponse */
        ToolListResponse: {
            /** Total */
            total: number;
            /** Items */
            items: components["schemas"]["Tool"][];
        };
        /** User */
        User: {
            /**
             * Username
             * @description 用户名
             */
            username: string;
            /**
             * Email
             * Format: email
             * @description 邮箱
             */
            email: string;
            /**
             * Display Name
             * @description 显示名称
             */
            display_name?: string | null;
            /** Id */
            id: string;
            /** Is Active */
            is_active: boolean;
            /** Last Login */
            last_login: string | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Extra */
            extra: {
                [key: string]: unknown;
            } | null;
        };
        /** UserDetail */
        UserDetail: {
            /**
             * Username
             * @description 用户名
             */
            username: string;
            /**
             * Email
             * Format: email
             * @description 邮箱
             */
            email: string;
            /**
             * Display Name
             * @description 显示名称
             */
            display_name?: string | null;
            /** Id */
            id: string;
            /** Is Active */
            is_active: boolean;
            /** Last Login */
            last_login: string | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Extra */
            extra: {
                [key: string]: unknown;
            } | null;
            /** Tenants */
            tenants: components["schemas"]["UserTenantRole"][];
        };
        /** UserListResponse */
        UserListResponse: {
            /** Total */
            total: number;
            /** Items */
            items: components["schemas"]["User"][];
        };
        /** UserRegister */
        UserRegister: {
            /**
             * Username
             * @description 用户名
             */
            username: string;
            /**
             * Email
             * Format: email
             * @description 邮箱
             */
            email: string;
            /**
             * Password
             * @description 密码
             */
            password: string;
            /**
             * Display Name
             * @description 显示名称
             */
            display_name?: string | null;
            /**
             * Invitation Token
             * @description 邀请令牌（可选）
             */
            invitation_token?: string | null;
        };
        /** UserRegisterInfo */
        UserRegisterInfo: {
            /**
             * Id
             * @description 用户ID
             */
            id: string;
            /**
             * Username
             * @description 用户名
             */
            username: string;
            /**
             * Email
             * Format: email
             * @description 邮箱
             */
            email: string;
            /**
             * Is Verified
             * @description 是否已验证
             */
            is_verified: boolean;
        };
        /** UserRegisterResponse */
        UserRegisterResponse: {
            /**
             * Message
             * @description 响应消息
             */
            message: string;
            /** @description 用户信息 */
            user: components["schemas"]["UserRegisterInfo"];
            /** @description 个人租户信息 */
            personal_tenant: components["schemas"]["PersonalTenantInfo"];
            /**
             * Invitation Accepted
             * @description 是否处理了邀请
             */
            invitation_accepted?: boolean | null;
            /** @description 通过邀请加入的租户 */
            joined_tenant?: components["schemas"]["TenantInfo"] | null;
        };
        /** UserTenantAssociationCreate */
        UserTenantAssociationCreate: {
            /**
             * User Id
             * @description 用户ID
             */
            user_id: string;
            /**
             * Tenant Id
             * @description 租户ID
             */
            tenant_id: string;
            /**
             * Role Key
             * @description 角色标识
             */
            role_key: string;
        };
        /** UserTenantAssociationResponse */
        UserTenantAssociationResponse: {
            /** User Id */
            user_id: string;
            /** Tenant Id */
            tenant_id: string;
            /** Role Key */
            role_key: string;
            /** Success */
            success: boolean;
            /** Message */
            message: string;
        };
        /** UserTenantAssociationUpdate */
        UserTenantAssociationUpdate: {
            /**
             * Role Key
             * @description 角色标识
             */
            role_key: string;
        };
        /** UserTenantRole */
        UserTenantRole: {
            /** User Id */
            user_id: string;
            /** Tenant Id */
            tenant_id: string;
            /** Role Key */
            role_key: string;
            tenant: components["schemas"]["Tenant"] | null;
            role: components["schemas"]["Role"] | null;
        };
        /** UserUpdate */
        UserUpdate: {
            /**
             * Username
             * @description 用户名
             */
            username?: string | null;
            /**
             * Email
             * @description 邮箱
             */
            email?: string | null;
            /**
             * Display Name
             * @description 显示名称
             */
            display_name?: string | null;
            /**
             * Is Active
             * @description 是否激活
             */
            is_active?: boolean | null;
            /**
             * Extra
             * @description 额外信息
             */
            extra?: {
                [key: string]: unknown;
            } | null;
        };
        /** UserWithRole */
        UserWithRole: {
            /**
             * Username
             * @description 用户名
             */
            username: string;
            /**
             * Email
             * Format: email
             * @description 邮箱
             */
            email: string;
            /**
             * Display Name
             * @description 显示名称
             */
            display_name?: string | null;
            /** Id */
            id: string;
            /** Is Active */
            is_active: boolean;
            /** Last Login */
            last_login: string | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Extra */
            extra: {
                [key: string]: unknown;
            } | null;
            /**
             * Role Key
             * @description 在当前租户中的角色标识
             */
            role_key: string;
        };
        /** UserWithRoleListResponse */
        UserWithRoleListResponse: {
            /** Total */
            total: number;
            /** Items */
            items: components["schemas"]["UserWithRole"][];
        };
        /** ValidationError */
        ValidationError: {
            /** Location */
            loc: (string | number)[];
            /** Message */
            msg: string;
            /** Error Type */
            type: string;
        };
        /**
         * VectorSearchConfig
         * @description 向量检索配置
         */
        VectorSearchConfig: {
            /**
             * Top K
             * @description 返回结果数量
             * @default 5
             */
            top_k: number;
            /**
             * Score Threshold
             * @description 分数阈值
             * @default 0.5
             */
            score_threshold: number;
            /** @description Rerank模型配置 */
            rerank?: components["schemas"]["RerankModelConfig"] | null;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    register_api_v1_auth_register_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserRegister"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserRegisterResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    login_api_v1_auth_login_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/x-www-form-urlencoded": components["schemas"]["Body_login_api_v1_auth_login_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Token"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_tenant_api_v1_auth_tenants_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TenantCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantCreateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    invite_user_api_v1_auth_tenants__tenant_id__invite_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["InviteUserRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InvitationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_invitation_details_api_v1_auth_invitations__invitation_token__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                invitation_token: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InvitationDetailResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    accept_invitation_api_v1_auth_accept_invitation_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AcceptInvitationRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AcceptInvitationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_tenants_api_v1_tenants_get: {
        parameters: {
            query?: {
                /** @description 跳过记录数 */
                skip?: number;
                /** @description 返回记录数 */
                limit?: number;
                /** @description 按名称过滤 */
                name?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_tenant_api_v1_tenants__tenant_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantBase"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_tenant_api_v1_tenants__tenant_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["TenantUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantBase"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_tenant_api_v1_tenants__tenant_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: unknown;
                    };
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_tenant_stats_api_v1_tenants__tenant_id__stats_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantWithStats"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_tenant_users_api_v1_tenants__tenant_id__users_get: {
        parameters: {
            query?: {
                /** @description 跳过记录数 */
                skip?: number;
                /** @description 返回记录数 */
                limit?: number;
                /** @description 按用户名过滤 */
                username?: string;
                /** @description 按状态过滤 */
                is_active?: boolean;
            };
            header?: never;
            path: {
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserWithRoleListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    add_user_to_tenant_api_v1_tenants__tenant_id__users_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["UserTenantAssociationCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserTenantAssociationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_tenant_user_api_v1_tenants__tenant_id__users__user_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 用户ID */
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserWithRole"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_user_role_api_v1_tenants__tenant_id__users__user_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description 用户ID */
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["UserTenantAssociationUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserTenantAssociationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    remove_user_from_tenant_api_v1_tenants__tenant_id__users__user_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description 用户ID */
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OperationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_current_user_info_api_v1_users_me_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["User"];
                };
            };
        };
    };
    update_current_user_api_v1_users_me_put: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["UserUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["User"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_current_user_detail_api_v1_users_me_detail_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDetail"];
                };
            };
        };
    };
    change_current_user_password_api_v1_users_me_change_password_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PasswordReset"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OperationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_all_roles_api_v1_roles_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Role"][];
                };
            };
        };
    };
    list_tenant_roles_api_v1_tenants__tenant_id__roles_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RoleListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_role_api_v1_tenants__tenant_id__roles__role_key__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description 角色标识 */
                role_key: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RoleWithPermissions"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_agent_templates_api_v1_tenants__tenant_id__agents_templates_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentTemplatesResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_agents_api_v1_tenants__tenant_id__agents_get: {
        parameters: {
            query?: {
                /** @description 跳过记录数 */
                skip?: number;
                /** @description 返回记录数 */
                limit?: number;
                /** @description 按名称过滤 */
                name?: string | null;
                /** @description 按类型过滤 */
                agent_type?: string | null;
                /** @description 按状态过滤 */
                is_active?: boolean | null;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_agent_api_v1_tenants__tenant_id__agents_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentCreateRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentCreateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_agent_api_v1_tenants__tenant_id__agents__agent_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentDetail"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_agent_api_v1_tenants__tenant_id__agents__agent_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Agent"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_agent_api_v1_tenants__tenant_id__agents__agent_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_agent_basic_info_api_v1_tenants__tenant_id__agents__agent_id__basic_info_put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentBasicInfoUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Agent"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    upload_agent_icon_api_v1_tenants__tenant_id__agents__agent_id__icon_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "multipart/form-data": components["schemas"]["Body_upload_agent_icon_api_v1_tenants__tenant_id__agents__agent_id__icon_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentIconUploadResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_agent_icon_api_v1_tenants__tenant_id__agents__agent_id__icon_delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentIconDeleteResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_agent_icon_url_api_v1_tenants__tenant_id__agents__agent_id__icon_url_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_agent_draft_api_v1_tenants__tenant_id__agents__agent_id__draft_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentDraftConfig"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_agent_draft_api_v1_tenants__tenant_id__agents__agent_id__draft_put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentDraftUpdateRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentDraftUpdateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    publish_agent_config_api_v1_tenants__tenant_id__agents__agent_id__publish_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentPublishRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentPublishResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_agent_history_api_v1_tenants__tenant_id__agents__agent_id__history_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentHistoryResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    rollback_agent_version_api_v1_tenants__tenant_id__agents__agent_id__rollback__version_id__post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
                /** @description 版本ID */
                version_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentRollbackRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentRollbackResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    validate_agent_config_api_v1_tenants__tenant_id__agents__agent_id__validate_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentConfigValidation"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_config_wizard_api_v1_tenants__tenant_id__agents__agent_id__wizard_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentConfigWizardResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_available_knowledge_bases_api_v1_tenants__tenant_id__agents_available_knowledge_bases_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_available_tools_api_v1_tenants__tenant_id__agents_available_tools_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_available_mcp_servers_api_v1_tenants__tenant_id__agents_available_mcp_servers_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_tools_api_v1_tools_get: {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                name?: string | null;
                is_active?: boolean | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ToolListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_tool_api_v1_tools__tool_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 工具ID */
                tool_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ToolDetail"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_tenant_tools_api_v1_tenants__tenant_id__tools_get: {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                is_active?: boolean | null;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantToolListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_tenant_tool_api_v1_tenants__tenant_id__tools_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TenantToolCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantTool"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_tenant_tool_api_v1_tenants__tenant_id__tools__tool_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 工具ID */
                tool_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantTool"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_tenant_tool_api_v1_tenants__tenant_id__tools__tool_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 工具ID */
                tool_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TenantToolUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantTool"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_tenant_tool_api_v1_tenants__tenant_id__tools__tool_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 工具ID */
                tool_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_tenant_available_tools_api_v1_tenants__tenant_id__tools_available_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantTool"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_mcp_servers_api_v1_tenants__tenant_id__mcp_servers_get: {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                name?: string | null;
                status?: string | null;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MCPServerListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_mcp_server_api_v1_tenants__tenant_id__mcp_servers_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MCPServerCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MCPServer"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_mcp_server_api_v1_tenants__tenant_id__mcp_servers__server_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 服务器ID */
                server_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MCPServer"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_mcp_server_api_v1_tenants__tenant_id__mcp_servers__server_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 服务器ID */
                server_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MCPServerUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MCPServer"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_mcp_server_api_v1_tenants__tenant_id__mcp_servers__server_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 服务器ID */
                server_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_tenant_available_mcp_servers_api_v1_tenants__tenant_id__mcp_servers_available_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MCPServer"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    check_mcp_server_is_available_api_v1_tenants__tenant_id___tool_ids__test__server_ids__get: {
        parameters: {
            query: {
                /** @description 要执行的内容 */
                content: string;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 工具ID */
                tool_ids: string;
                /** @description 服务器ID */
                server_ids: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_models_api_v1_tenants__tenant_id__models_get: {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                name?: string | null;
                provider_id?: string | null;
                model_type?: string | null;
                is_active?: boolean | null;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ModelListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_model_api_v1_tenants__tenant_id__models_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ModelCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Model"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_model_api_v1_tenants__tenant_id__models__model_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 模型ID */
                model_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Model"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_model_api_v1_tenants__tenant_id__models__model_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 模型ID */
                model_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ModelUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Model"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_model_api_v1_tenants__tenant_id__models__model_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 模型ID */
                model_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_model_types_api_v1_model_types_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ModelTypeListResponse"];
                };
            };
        };
    };
    get_available_providers_api_v1_tenants__tenant_id__available_providers_get: {
        parameters: {
            query?: {
                /** @description 筛选提供商类型 */
                provider_type?: components["schemas"]["ProviderType"] | null;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AvailableProvidersResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_enabled_providers_api_v1_tenants__tenant_id__enabled_providers_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EnabledProvidersResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    enable_provider_api_v1_tenants__tenant_id__enable_provider__provider_id__post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 提供商ID */
                provider_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["GenericProviderConfigRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EnableProviderResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    disable_provider_api_v1_tenants__tenant_id__disable_provider__credential_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 提供商配置ID */
                credential_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DisableProviderResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    edit_provider_config_api_v1_tenants__tenant_id__provider_credentials__credential_id__put: {
        parameters: {
            query?: {
                /** @description 是否验证配置有效性 */
                validate_config?: boolean;
                /** @description 是否重新同步模型 */
                resync_models?: boolean;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 提供商配置ID */
                credential_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["EditProviderConfigRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EditProviderConfigResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    test_provider_config_api_v1_tenants__tenant_id__test_provider_config_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["GenericProviderTestRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TestProviderConfigResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_provider_models_api_v1_tenants__tenant_id__provider_models__credential_id__get: {
        parameters: {
            query?: {
                /** @description 是否强制刷新缓存 */
                refresh?: boolean;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 提供商配置ID */
                credential_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ProviderModelsResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_provider_config_schema_api_v1_tenants__tenant_id__provider_config_schema__provider_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 提供商ID */
                provider_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: unknown;
                    };
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    admin_list_all_users_api_v1_admin_users_get: {
        parameters: {
            query?: {
                /** @description 跳过记录数 */
                skip?: number;
                /** @description 返回记录数 */
                limit?: number;
                /** @description 按用户名过滤 */
                username?: string;
                /** @description 按邮箱过滤 */
                email?: string;
                /** @description 按状态过滤 */
                is_active?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    admin_update_user_status_api_v1_admin_users__user_id__status_put: {
        parameters: {
            query: {
                /** @description 是否激活 */
                is_active: boolean;
            };
            header?: never;
            path: {
                /** @description 用户ID */
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["User"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    make_super_admin_api_v1_admin_make_super_admin_post: {
        parameters: {
            query: {
                /** @description 用户ID */
                user_id: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OperationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    remove_super_admin_api_v1_admin_remove_super_admin_post: {
        parameters: {
            query: {
                /** @description 用户ID */
                user_id: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OperationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    admin_list_all_tenants_api_v1_admin_tenants_get: {
        parameters: {
            query?: {
                /** @description 跳过记录数 */
                skip?: number;
                /** @description 返回记录数 */
                limit?: number;
                /** @description 按名称过滤 */
                name?: string;
                /** @description 按状态过滤 */
                is_active?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    admin_update_tenant_status_api_v1_admin_tenants__tenant_id__status_put: {
        parameters: {
            query: {
                /** @description 是否激活 */
                is_active: boolean;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TenantBase"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    admin_force_delete_tenant_api_v1_admin_tenants__tenant_id__delete: {
        parameters: {
            query?: {
                /** @description 是否强制删除 */
                force?: boolean;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: unknown;
                    };
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_knowledge_bases_api_v1_tenants__tenant_id__knowledge_bases_get: {
        parameters: {
            query?: {
                /** @description 分页起始位置 */
                skip?: number;
                /** @description 分页大小 */
                limit?: number;
                /** @description 是否激活 */
                is_active?: boolean | null;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeBaseListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_knowledge_base_api_v1_tenants__tenant_id__knowledge_bases_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KnowledgeBaseCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeBaseResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_knowledge_base_api_v1_tenants__tenant_id__knowledge_bases__kb_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeBaseResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_knowledge_base_api_v1_tenants__tenant_id__knowledge_bases__kb_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KnowledgeBaseUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeBaseResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_knowledge_base_api_v1_tenants__tenant_id__knowledge_bases__kb_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_knowledge_base_usage_api_v1_tenants__tenant_id__knowledge_bases__kb_id__usage_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeBaseUsageResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_knowledge_base_versions_api_v1_tenants__tenant_id__knowledge_bases__kb_id__versions_get: {
        parameters: {
            query?: {
                /** @description 分页起始位置 */
                skip?: number;
                /** @description 分页大小 */
                limit?: number;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeBaseVersionListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_knowledge_base_version_api_v1_tenants__tenant_id__knowledge_bases__kb_id__versions_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KnowledgeBaseVersionCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeBaseVersionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_knowledge_file_api_v1_tenants__tenant_id__knowledge_files_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KnowledgeFileCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeFileResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    upload_knowledge_file_api_v1_tenants__tenant_id__knowledge_upload_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "multipart/form-data": components["schemas"]["Body_upload_knowledge_file_api_v1_tenants__tenant_id__knowledge_upload_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeFileResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_file_download_url_api_v1_tenants__tenant_id__knowledge_files__file_id__download_get: {
        parameters: {
            query?: {
                /** @description URL有效期（秒） */
                expires_in?: number;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 文件ID */
                file_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeFileUrlResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_knowledge_files_api_v1_tenants__tenant_id__knowledge_bases__kb_id__files_get: {
        parameters: {
            query?: {
                /** @description 分页起始位置 */
                skip?: number;
                /** @description 分页大小 */
                limit?: number;
                /** @description 文件状态 */
                status?: string | null;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeFileListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_knowledge_file_api_v1_tenants__tenant_id__knowledge_files__file_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 文件ID */
                file_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeFileResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_knowledge_file_api_v1_tenants__tenant_id__knowledge_files__file_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 文件ID */
                file_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KnowledgeFileUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeFileResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_knowledge_file_api_v1_tenants__tenant_id__knowledge_files__file_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 文件ID */
                file_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_knowledge_chunks_api_v1_tenants__tenant_id__knowledge_files__file_id__chunks_get: {
        parameters: {
            query?: {
                /** @description 分页起始位置 */
                skip?: number;
                /** @description 分页大小 */
                limit?: number;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 文件ID */
                file_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeChunkResponse"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    search_knowledge_api_v1_tenants__tenant_id__knowledge__kb_id__search_get: {
        parameters: {
            query: {
                /** @description 搜索查询 */
                query: string;
                /** @description 返回结果数量，如不指定则使用知识库默认配置 */
                top_k?: number | null;
                /** @description 会话ID */
                session_id?: string | null;
                /** @description 是否保存搜索记录 */
                save_search?: boolean;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeSearchResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_knowledge_search_history_api_v1_tenants__tenant_id__knowledge_bases__kb_id__search_history_get: {
        parameters: {
            query?: {
                /** @description 会话ID */
                session_id?: string | null;
                /** @description 分页起始位置 */
                skip?: number;
                /** @description 分页大小 */
                limit?: number;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeSearchResponse"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    retrieve_knowledge_documents_api_v1_tenants__tenant_id__knowledge__kb_id__retrieve_get: {
        parameters: {
            query: {
                /** @description 搜索查询 */
                query: string;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeSearchResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_reindex_task_api_v1_tenants__tenant_id__knowledge__kb_id__reindex_task_post: {
        parameters: {
            query?: {
                /** @description 文本块大小 */
                chunk_size?: number;
                /** @description 文本块重叠大小 */
                chunk_overlap?: number;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: unknown;
                    };
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    reindex_knowledge_base_api_v1_tenants__tenant_id__knowledge__kb_id__reindex_post: {
        parameters: {
            query?: {
                /** @description 文本块大小 */
                chunk_size?: number;
                /** @description 文本块重叠大小 */
                chunk_overlap?: number;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: unknown;
                    };
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_reindex_task_status_api_v1_tenants__tenant_id__knowledge__kb_id__reindex_status__task_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
                /** @description 任务ID */
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: unknown;
                    };
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_knowledge_base_config_api_v1_tenants__tenant_id__knowledge_config__kb_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: unknown;
                    };
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_knowledge_base_config_api_v1_tenants__tenant_id__knowledge_config__kb_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 知识库ID */
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KnowledgeBaseConfigUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeBaseResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    chat_with_agent_api_v1_tenants__tenant_id__agents__agent_id__chat_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ChatRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ChatResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    chat_with_agent_stream_api_v1_tenants__tenant_id__agents__agent_id__chat_stream_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description Agent ID */
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ChatStreamRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_sessions_api_v1_tenants__tenant_id__sessions_get: {
        parameters: {
            query?: {
                /** @description 按Agent ID过滤 */
                agent_id?: string | null;
                /** @description 按用户ID过滤 */
                user_id?: string | null;
                /** @description 按状态过滤 */
                status?: components["schemas"]["SessionStatus"] | null;
                /** @description 跳过记录数 */
                skip?: number;
                /** @description 返回记录数 */
                limit?: number;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SessionInfo"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_session_api_v1_tenants__tenant_id__sessions__session_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
                /** @description 会话ID */
                session_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SessionInfo"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    clear_agent_cache_api_v1_tenants__tenant_id__agents_cache_clear_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CacheClearRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_execution_stats_api_v1_execution_stats_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    cleanup_expired_sessions_api_v1_tenants__tenant_id__sessions_cleanup_post: {
        parameters: {
            query?: {
                /** @description 会话过期时间（小时） */
                expiry_hours?: number;
            };
            header?: never;
            path: {
                /** @description 租户ID */
                tenant_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    cleanup_all_expired_sessions_api_v1_sessions_cleanup_all_post: {
        parameters: {
            query?: {
                /** @description 会话过期时间（小时） */
                expiry_hours?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    health_check_api_v1_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    health_check_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
}
