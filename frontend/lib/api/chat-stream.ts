import { components } from "./strapi"

type SSEMessage = components["schemas"]["SessionInfo"] & {
  type: string
  data: string
  accumulated: string
  message_type: string
}

type ChatRequest = components["schemas"]["ChatRequest"] & {
  tenant_id: string
  agent_id: string
}

// SSE 聊天服务
export class ChatStreamService {
  private baseUrl =  process.env.NODE_ENV === "development"
      ? "http://localhost:8000/api/v1"
      : "/api/v1";
  private eventSource: EventSource | null = null

  // 发送聊天消息并建立SSE连接
  async sendMessage(
    request: ChatRequest,
    onMessage: (message: SSEMessage) => void,
    onError: (error: Error) => void,
    onComplete: () => void,
  ): Promise<void> {
    try {
      // 构建URL
      const url = `${this.baseUrl}/tenants/${request.tenant_id}/agents/${request.agent_id}/chat/stream`

      // 方法1：使用 EventSource 处理 SSE（适用于 GET 请求）
      // 由于我们需要发送 POST 请求，这里使用 fetch API

      // 方法2：使用 fetch API 发送 POST 请求并处理 SSE 响应
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Accept": "text/event-stream",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: request.message,
          thread_id: request.thread_id,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 检查响应是否为SSE流
      const contentType = response.headers.get("content-type")
      if (!contentType?.includes("text/event-stream")) {
        throw new Error("Response is not a valid SSE stream")
      }

      // 处理流式响应
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error("Failed to get response reader")
      }

      const decoder = new TextDecoder()
      let buffer = ""

      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) {
            onComplete()
            break
          }

          // 解码数据
          buffer += decoder.decode(value, { stream: true })

          // 处理SSE数据
          const lines = buffer.split("\n")
          buffer = lines.pop() || "" // 保留最后一行（可能不完整）

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = line.slice(6) // 移除 "data: " 前缀
                if (data.trim() === "[DONE]") {
                  onComplete()
                  return
                }

                const sseMessage: SSEMessage = JSON.parse(data)
                onMessage(sseMessage)
              } catch (parseError) {
                console.warn("Failed to parse SSE message:", line, parseError)
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }
    } catch (error) {
      onError(error instanceof Error ? error : new Error("Unknown error occurred"))
    }
  }

  // 断开连接
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
  }
}

// 创建单例实例
export const chatStreamService = new ChatStreamService()
