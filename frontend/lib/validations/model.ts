import { z } from "zod"

// 创建模型表单验证
export const createModelSchema = z.object({
  name: z
    .string()
    .min(2, "模型名称至少需要2个字符")
    .max(100, "模型名称不能超过100个字符"),
    // .regex(/^[\u4e00-\u9fa5a-zA-Z0-9\s_/-]+$/, "模型名称只能包含中文、字母、数字、空格、下划线、连字符和斜杠"),

  key: z
    .string()
    .min(2, "模型标识至少需要2个字符")
    .max(50, "模型标识不能超过50个字符"),
    // .regex(/^[\u4e00-\u9fa5a-zA-Z0-9\s_/-]+$/, "模型标识只能包含中文、字母、数字、空格、下划线、连字符和斜杠")
    // .refine((val) => !val.startsWith("_") && !val.endsWith("_"), "模型标识不能以下划线开头或结尾"),

  model_type: z.string().min(1, "请选择模型类型"),

  provider_access_credential_id: z.string().min(1, "请选择提供商凭据"),


  // extra_config: z.record(z.any()).optional(),

  // // 额外配置字段
  // max_tokens: z.number().min(1).max(100000).optional(),
  // temperature: z.number().min(0).max(2).optional(),
  // top_p: z.number().min(0).max(1).optional(),
  // frequency_penalty: z.number().min(-2).max(2).optional(),
  // presence_penalty: z.number().min(-2).max(2).optional(),
})

export type CreateModelFormData = z.infer<typeof createModelSchema>

