import { z } from "zod"
import { components } from "../api/strapi"

export const createAgentSchema = z.object({
  name: z
    .string()
    .min(2, "Agent 名称至少需要 2 个字符")
    .max(50, "Agent 名称不能超过 50 个字符")
    .regex(/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/, "Agent 名称只能包含字母、数字、中文、下划线和连字符"),

  description: z.string().min(10, "描述至少需要 10 个字符").max(500, "描述不能超过 500 个字符"),

  template_id: z.string().optional(),

  model_id: z.string().min(1, "请选择一个模型"),

  is_active: z.boolean(),

  prompt: z.string().min(20, "系统提示至少需要 20 个字符").max(2000, "系统提示不能超过 2000 个字符"),

  agent_type: z.custom<components["schemas"]["AgentType"]>().refine((val) => val !== null, "请选择模型类型"),
  // agent_type: z.string().min(1, "请选择模型类型"),

  tenant_id: z.string().min(1, "请选择租户"),

  is_supervisor: z.boolean(),

  knowledge_base_ids: z.array(z.string()).max(100, "关联知识库不能超过 100 个").optional(),
  knowledge_min_score: z.number().min(0, "最小得分不能小于 0").max(100, "最小得分不能大于 100"),
  knowledge_max_results: z.number().min(0, "最大返回结果数不能小于 0").max(100, "最大返回结果数不能大于 100"),

  tool_ids: z.array(z.string()).max(100, "关联知识库不能超过 100 个").optional(),

  mcp_server_ids: z.array(z.string()).max(100, "关联知识库不能超过 100 个").optional(),

  temperature: z.number().min(0, "温度不能小于 0").max(1, "温度不能大于 1"),
  top_p: z.number().min(0, "top_p 不能小于 0").max(1, "top_p 不能大于 1"),
  max_tokens: z.number().min(0, "max_tokens 不能小于 0"),
  max_retries: z.number().min(0, "max_retries 不能小于 0"),
  timeout: z.number().min(0, "timeout 不能小于 0"),

  release_notes: z.string().min(0, "描述至少需要 0 个字符").max(500, "描述不能超过 500 个字符"),

})

export type CreateAgentFormData = z.infer<typeof createAgentSchema>

export const chatMessageSchema = z.object({
  message: z.string().min(1, "消息不能为空").max(1000, "消息不能超过 1000 个字符"),
})

export type ChatMessageFormData = z.infer<typeof chatMessageSchema>
