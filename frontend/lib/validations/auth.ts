import { z } from "zod"

// 可复用的验证规则
const usernameValidation = z
  .string()
  .min(3, "用户名至少需要 3 个字符")
  .max(64, "用户名不能超过 64 个字符")
  .regex(/^[a-zA-Z0-9_-]+$/, "用户名只能包含字母、数字、下划线和连字符")

const emailValidation = z
  .string()
  .email("请输入有效的电子邮箱地址")
  .min(5, "邮箱地址太短")
  .max(254, "邮箱地址太长")

const passwordValidation = z
  .string()
  .min(6, "密码至少需要 6 个字符")
  .max(64, "密码不能超过 64 个字符")
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "密码必须包含大小写字母和数字")

const displayNameValidation = z
  .union([
    z.literal("").transform(() => undefined),
    z
      .string()
      .min(1, "显示名称至少需要 1 个字符")
      .max(64, "显示名称不能超过 50 个字符")
      .regex(
        /^[a-zA-Z0-9\u4e00-\u9fa5_-\s]+$/,
        "显示名称可以包含字母、数字、中文、空格、下划线和连字符",
      ),
  ])
  .optional()

export const registerSchema = z
  .object({
    username: usernameValidation,
    email: emailValidation,
    password: passwordValidation,
    confirm_password: z.string(),
    display_name: displayNameValidation,
    invitation_token: z.string().optional(),
    agree_to_terms: z.boolean().refine((val) => val === true, {
      message: "请同意服务条款和隐私政策",
    }),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: "两次输入的密码不一致",
    path: ["confirm_password"],
  })

export type RegisterFormData = z.infer<typeof registerSchema>


export const userSchema = z.object({
  username: usernameValidation,
  email: emailValidation,
  password: passwordValidation,
  display_name: displayNameValidation,
})

export type UserFormData = z.infer<typeof userSchema>

export const profileSchema = z.object({
  email: emailValidation,
  display_name: displayNameValidation,
})

export type ProfileSchema = z.infer<typeof profileSchema>

export const changePasswordSchema = z
  .object({
    current_password: passwordValidation,
    password: passwordValidation,
    confirm_password: z.string(),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: "两次输入的密码不一致",
    path: ["confirm_password"],
  })

export type ChangePasswordSchema = z.infer<typeof changePasswordSchema>
