import { z } from "zod"

export const registerSchema = z
  .object({
    username: z
      .string()
      .min(3, "用户名至少需要 3 个字符")
      .max(64, "用户名不能超过 64 个字符")
      .regex(/^[a-zA-Z0-9_-]+$/, "用户名只能包含字母、数字、下划线和连字符"),

    email: z.string().email("请输入有效的电子邮箱地址").min(5, "邮箱地址太短").max(254, "邮箱地址太长"),

    password: z
      .string()
      .min(6, "密码至少需要 6 个字符")
      .max(64, "密码不能超过 64 个字符")
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "密码必须包含大小写字母和数字"),

    confirm_password: z.string(),

    // display_name: z.preprocess(
    //   (val) => (val === "" ? undefined : val),
    //   z
    //     .string()
    //     .min(2, "显示名称至少需要 2 个字符")
    //     .max(50, "显示名称不能超过 50 个字符")
    //     .regex(/^[a-zA-Z0-9\u4e00-\u9fa5_-\s]+$/, "显示名称可以包含字母、数字、中文、空格、下划线和连字符")
    //     .optional(),
    // ),

    display_name: z
      .union([
        z.literal("").transform(() => undefined),
        z
          .string()
          .min(1, "显示名称至少需要 1 个字符")
          .max(64, "显示名称不能超过 50 个字符")
          .regex(
            /^[a-zA-Z0-9\u4e00-\u9fa5_-\s]+$/,
            "显示名称可以包含字母、数字、中文、空格、下划线和连字符",
          ),
      ])
      .optional(),

    invitation_token: z.string().optional(),

    agree_to_terms: z.boolean().refine((val) => val === true, {
      message: "请同意服务条款和隐私政策",
    }),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: "两次输入的密码不一致",
    path: ["confirm_password"],
  })

  export const modifySchema = registerSchema.
export type RegisterFormData = z.infer<typeof registerSchema>