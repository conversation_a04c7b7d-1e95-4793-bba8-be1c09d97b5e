import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const debounce = function (fn: Function, t: number): Function {
  let timeId: string | number | NodeJS.Timeout | undefined | null = null;
  const delay = t || 500;
  return function (this: unknown, ...args: any[]) {
      if (timeId) {
      clearTimeout(timeId);
      }
      timeId = setTimeout(() => {
      timeId = null;
      fn.apply(this, args);
      }, delay);
  };
};

export const available = (currentTenant: any) => {
  return ['owner', 'admin'].includes(currentTenant?.role_key)
}