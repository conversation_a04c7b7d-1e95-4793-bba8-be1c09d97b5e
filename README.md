# 云知问平台设计

## 一、系统架构设计
### 1. 整体架构图

```text
┌─────────────────────────────────────────┐
│              客户端接入层                │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ │
│  │ Web嵌入  │ │ API接入  │ │ SDK接入  │ │
│  └────┬─────┘ └────┬─────┘ └────┬─────┘ │
│       │            │            │        │
│  ┌────▼────────────▼────────────▼────┐  │
│  │           管理后台                 │  │
│  └────────────────┬──────────────────┘  │
└───────────────────┼─────────────────────┘
                    │
┌───────────────────▼───────────────────┐
│              API网关层                │
│  认证、鉴权、限流、日志、监控、路由    │
└───────────────────┬───────────────────┘
                    │
┌───────────────────▼───────────────────┐
│              应用服务层                │
│ ┌─────────────┐  ┌─────────────────┐  │
│ │  认证服务   │  │    Agent服务    │  │
│ └─────────────┘  └─────────────────┘  │
│ ┌─────────────┐  ┌─────────────────┐  │
│ │  租户服务   │  │    会话服务     │  │
│ └─────────────┘  └─────────────────┘  │
│ ┌─────────────┐  ┌─────────────────┐  │
│ │  知识库服务  │  │    模型服务     │  │
│ └─────────────┘  └─────────────────┘  │
│ ┌─────────────┐  ┌─────────────────┐  │
│ │  通知服务   │  │   统计分析服务   │  │
│ └─────────────┘  └─────────────────┘  │
└───────────────────┬───────────────────┘
                    │
┌───────────────────▼───────────────────┐
│            LangGraph引擎层             │
│   Agent编排、RAG、意图分析、多轮对话   │
└───────────────────┬───────────────────┘
                    │
┌───────────────────▼───────────────────┐
│              存储服务层                │
│ ┌─────────┐ ┌────────┐ ┌───────────┐  │
│ │PostgreSQL│ │向量数据库│ │对象存储   │  │
│ └─────────┘ └────────┘ └───────────┘  │
└─────────────────────────────────────┘
```

## 二、数据库表设计

### 1. 租户与用户相关表
```sql
-- 租户表
CREATE TABLE tenant (
    id SERIAL PRIMARY KEY,
    name VARCHAR(64) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE "user" (
    id SERIAL PRIMARY KEY,
    username VARCHAR(64) NOT NULL UNIQUE,
    email VARCHAR(128) NOT NULL UNIQUE,
    password_hash VARCHAR(256) NOT NULL,
    display_name VARCHAR(64),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    extra JSONB
);

-- 用户-租户-角色关联表
CREATE TABLE user_tenant_association (
    user_id INTEGER REFERENCES "user"(id),
    tenant_id INTEGER REFERENCES tenant(id),
    role_id INTEGER REFERENCES role(id),
    PRIMARY KEY (user_id, tenant_id)
);

-- 角色表
CREATE TABLE role (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenant(id),
    name VARCHAR(64) NOT NULL,
    key VARCHAR(64) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Agent相关表

```sql
-- Agent表（合并了agent_type）
CREATE TABLE agent (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenant(id) NOT NULL,
    name VARCHAR(64) NOT NULL UNIQUE,
    agent_type VARCHAR(32),
    is_supervisor BOOLEAN DEFAULT false,  -- 标识是否为supervisor角色
    open_id VARCHAR(64) NOT NULL UNIQUE,
    current_version_id INTEGER,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    web_url VARCHAR(256),
    embed_code TEXT
);

-- Agent版本表（保持不变）
CREATE TABLE agent_version (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER REFERENCES agent(id) NOT NULL,
    version VARCHAR(32) NOT NULL,
    config JSONB,
    prompt TEXT,
    description TEXT,
    is_published BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Agent调用关系表（简化的层次结构）
CREATE TABLE agent_call_association (
    supervisor_id INTEGER REFERENCES agent(id),
    expert_id INTEGER REFERENCES agent(id),
    priority INTEGER DEFAULT 0,  -- 调用优先级
    description TEXT,
    PRIMARY KEY (supervisor_id, expert_id)
);

-- 工具定义表(工具广场)
CREATE TABLE tool (
    id SERIAL PRIMARY KEY,
    name VARCHAR(64) NOT NULL,
    key VARCHAR(64) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(128),
    schema JSONB,  -- 工具输入参数schema定义
    output_schema JSONB, -- 输出参数schema定义
    tool_type VARCHAR(32) NOT NULL, -- 'mcp', 'builtin', 'custom'
    is_public BOOLEAN DEFAULT true, -- 是否在广场公开可用
    version VARCHAR(32) DEFAULT '1.0',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- MCP服务器表
CREATE TABLE mcp_server (
    id SERIAL PRIMARY KEY,
    name VARCHAR(64) NOT NULL,
    server_url VARCHAR(256) NOT NULL,
    transport_type VARCHAR(32) DEFAULT 'streamable_http', -- 'streamable_http', 'sse', 'stdio'
    auth_type VARCHAR(32) DEFAULT 'api_key', -- 'api_key', 'oauth', 'none'
    auth_config JSONB,  -- 认证配置
    status VARCHAR(32) DEFAULT 'active', -- 'active', 'inactive', 'error'
    last_heartbeat TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- MCP工具映射表
CREATE TABLE mcp_tool (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER REFERENCES tool(id) NOT NULL,
    mcp_server_id INTEGER REFERENCES mcp_server(id) NOT NULL,
    mcp_tool_key VARCHAR(128) NOT NULL,  -- MCP系统中的工具key
    is_active BOOLEAN DEFAULT true,
    config JSONB,  -- 特定配置
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 租户工具表
CREATE TABLE tenant_tool (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenant(id) NOT NULL,
    tool_id INTEGER REFERENCES tool(id) NOT NULL,
    display_name VARCHAR(64),  -- 租户自定义显示名称
    display_description TEXT,  -- 租户自定义描述
    is_enabled BOOLEAN DEFAULT true,
    quota_daily INTEGER, -- 每日配额
    quota_monthly INTEGER, -- 每月配额
    config JSONB, -- 租户特定配置
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, tool_id)
);

-- Agent-Tool关联表（保持不变）
CREATE TABLE agent_tool (
    agent_id INTEGER REFERENCES agent(id),
    tenant_tool_id INTEGER REFERENCES tenant_tool(id),
    is_enabled BOOLEAN DEFAULT true,
    display_name VARCHAR(64),  -- Agent专用的工具显示名称
    display_description TEXT,  -- Agent专用的工具描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (agent_id, tenant_tool_id)
);

-- 工具执行日志表
CREATE TABLE tool_execution_log (
    id SERIAL PRIMARY KEY,
    tool_id INTEGER REFERENCES tool(id) NOT NULL,
    tenant_id INTEGER REFERENCES tenant(id) NOT NULL,
    agent_id INTEGER REFERENCES agent(id),
    session_id INTEGER REFERENCES session(id),
    user_id INTEGER REFERENCES "user"(id),
    input_params JSONB,
    output_result TEXT,
    execution_time INTEGER, -- 执行时间（毫秒）
    token_usage INTEGER, -- Token使用量
    status VARCHAR(32) DEFAULT 'success', -- 'success', 'failed', 'timeout'
    error_msg TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 会话相关表
```sql
-- 会话表
CREATE TABLE session (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER REFERENCES agent(id) NOT NULL,
    tenant_id INTEGER REFERENCES tenant(id) NOT NULL,
    user_id INTEGER REFERENCES "user"(id),
    external_user_id VARCHAR(64),
    external_user_info JSONB,
    title VARCHAR(128),
    status VARCHAR(32) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 会话消息表（整合Agent间消息）
CREATE TABLE session_message (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES session(id) NOT NULL,
    sender VARCHAR(32),
    user_id INTEGER REFERENCES "user"(id),
    external_user_id VARCHAR(64),
    sender_agent_id INTEGER REFERENCES agent(id),  -- 发送Agent（可选）
    receiver_agent_id INTEGER REFERENCES agent(id),  -- 接收Agent（可选）
    content TEXT NOT NULL,
    message_type VARCHAR(32) DEFAULT 'text',
    is_internal BOOLEAN DEFAULT false,  -- 标识是否为Agent内部消息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    model_call_id INTEGER REFERENCES model_call_log(id)
);
```

### 4. 知识库相关表
```sql
-- 知识库表
CREATE TABLE knowledge_base (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenant(id) NOT NULL,
    name VARCHAR(64) NOT NULL UNIQUE,
    description TEXT,
    vector_db_type VARCHAR(32),
    config JSONB,
    embedding_model_id INTEGER REFERENCES model(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 知识库文件表
CREATE TABLE knowledge_file (
    id SERIAL PRIMARY KEY,
    kb_id INTEGER REFERENCES knowledge_base(id),
    file_name VARCHAR(256),
    file_type VARCHAR(32),
    file_path VARCHAR(512),
    file_size BIGINT,  -- 文件大小（字节）
    status VARCHAR(32),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    embedding_model_id INTEGER REFERENCES model(id)
);
```

### 5. 模型与调用记录表
```sql
-- 服务商表
CREATE TABLE provider (
    id SERIAL PRIMARY KEY,
    name VARCHAR(64) NOT NULL UNIQUE,
    provider_type VARCHAR(32),
    config JSONB,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 模型表
CREATE TABLE model (
    id SERIAL PRIMARY KEY,
    name VARCHAR(128) NOT NULL,
    provider_id INTEGER REFERENCES provider(id) NOT NULL,
    model_type VARCHAR(32),
    config JSONB,
    description TEXT,
    input_token_price DECIMAL(10,6),  -- 每1000个输入token的价格
    output_token_price DECIMAL(10,6), -- 每1000个输出token的价格
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 模型调用日志表
CREATE TABLE model_call_log (
    id SERIAL PRIMARY KEY,
    model_id INTEGER REFERENCES model(id) NOT NULL,
    provider_id INTEGER REFERENCES provider(id) NOT NULL,
    agent_id INTEGER REFERENCES agent(id),
    session_id INTEGER REFERENCES session(id),
    user_id INTEGER REFERENCES "user"(id),
    request_data JSONB,              -- 完整的请求数据
    response_data JSONB,             -- 完整的响应数据
    tokens_input INTEGER,           -- 输入token数量
    tokens_output INTEGER,          -- 输出token数量
    tokens_total INTEGER,           -- 总token数量
    status VARCHAR(32) DEFAULT 'success',
    error_msg TEXT,
    request_time TIMESTAMP,         -- 请求时间
    response_time TIMESTAMP,        -- 响应时间
    latency INTEGER,                -- 延迟（毫秒）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 6. 安全认证相关表

```sql
-- 访问凭证表
CREATE TABLE access_credential (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenant(id) NOT NULL,
    agent_id INTEGER REFERENCES agent(id) NOT NULL,
    app_id VARCHAR(64) UNIQUE NOT NULL,
    app_secret VARCHAR(256) NOT NULL,
    allowed_domains JSONB,
    allowed_ips JSONB,
    rate_limit INTEGER,
    token_expire_hours INTEGER DEFAULT 24,
    sign_type VARCHAR(32) DEFAULT 'hmac_sha256',
    sign_version VARCHAR(16) DEFAULT 'v1',
    nonce_check BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 访问令牌表
CREATE TABLE access_token (
    id SERIAL PRIMARY KEY,
    credential_id INTEGER REFERENCES access_credential(id),
    token VARCHAR(256) UNIQUE NOT NULL,
    expired_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```


## 三、项目结构

```text
YunZhiWen/
├── backend/                # FastAPI后端服务
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 配置、安全、初始化
│   │   ├── db/             # 数据库相关
│   │   ├── models/         # SQLAlchemy模型
│   │   ├── schemas/        # Pydantic模型
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── main.py         # FastAPI入口
│   ├── tests/              # 后端测试
│   ├── requirements.txt
│   ├── Dockerfile
│   └── alembic/            # 数据库迁移
├── frontend/               # Next.js前端（管理后台+Web嵌入）
│   ├── src/
│   │   ├── pages/          # Next.js页面
│   │   ├── components/     # 通用组件
│   │   ├── modules/        # 业务模块（如Agent、知识库、会话等）
│   │   ├── styles/         # 样式
│   │   ├── utils/          # 工具
│   │   ├── hooks/          # React hooks
│   │   └── sdk/            # JS/TS SDK（可单独npm包）
│   ├── public/             # 静态资源
│   ├── tests/              # 前端测试
│   ├── package.json
│   ├── next.config.js
│   └── Dockerfile
├── sdk/                    # 可选：独立的前端SDK（如npm包，便于第三方集成）
│   ├── src/
│   ├── package.json
│   └── README.md
├── deploy/                 # 部署与CI/CD脚本
│   ├── docker-compose.yml
│   ├── nginx.conf
│   └── k8s/                # 可选：Kubernetes部署
├── .env                    # 环境变量
├── README.md
└── LICENSE
```

### 1. backend（FastAPI）
只负责API、业务逻辑、数据库、认证、模型/向量库等。
可独立运行，支持热重载（如uvicorn app.main:app --reload）。
提供RESTful API、WebSocket、SSE等接口。
可通过Docker单独部署。

### 2. frontend（Next.js）
统一管理后台、Web嵌入页面、对话组件等。
可通过/admin、/embed、/chat等路由区分不同前端应用。
业务模块化，便于团队协作和复用。
可通过Docker单独部署，或与后端共用Nginx反向代理。

### 3. sdk（可选）
提供独立的JS/TS SDK，便于第三方网站快速集成Agent对话、会话等功能。
可发布到npm，文档与示例独立维护。

### 4. deploy
集中管理所有部署相关脚本（Docker Compose、Nginx、K8s等）。
支持一键本地开发、测试、生产部署。


## 四、开发与部署

### 1. 本地开发
#### 后端
- 安装 python 依赖
```bash
cd backend 
python3 -m venv venv
pip install -r requirements.txt
```

- 配置文件
```bash
cd backend 
cp .env.exmaple .env
```

- 启动运行
```bash
cd backend
uvicorn app.main:app --reload
或
python3 -m app.main
```

- swagger-ui
[ Swagger UI 地址 ](http://localhost:8000/docs)

- OpenAPI
[ OpenAPI 地址 ](http://localhost:8000/openapi.json)

#### 前端

```bash
cd frontend && npm run dev
```

SDK开发：cd sdk && npm run dev
前后端通过.env或next.config.js配置API地址，支持本地跨域。

### 2. 生产部署
推荐用Docker Compose或K8s编排，Nginx统一反向代理。
目录结构便于CI/CD自动化部署。
可将前端静态资源构建后由Nginx托管，API由Nginx转发到FastAPI。

### 3. 代码复用与协作
前端/sdk目录可与主项目解耦，便于独立维护和版本发布。
后端API文档建议用OpenAPI/Swagger自动生成，便于前后端联调。
业务模块（如Agent、知识库、会话、通知等）前后端目录结构尽量一致，便于协作。
